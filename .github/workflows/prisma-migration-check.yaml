name: Prisma Migration Check

on:
  pull_request:
    branches:
      - staging
    paths:
      - 'prisma/**/schema.prisma'

permissions:
  contents: read
  pull-requests: write

jobs:
  check-prisma-migrations:
    name: Check Prisma Schema Changes and Migrations
    runs-on: self-hosted
    
    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Create schema comparison script
        run: |
          cat > compare_schemas.js << 'EOF'
          const fs = require('fs');
          const path = require('path');
          const { execSync } = require('child_process');
          const crypto = require('crypto');

          // Function to sanitize schema content
          function sanitizeSchema(content) {
            return content
              // Remove single-line comments (// comments)
              .replace(/\/\/.*$/gm, '')
              // Remove multi-line comments (/* comments */)
              .replace(/\/\*[\s\S]*?\*\//g, '')
              // Remove all whitespace and newlines
              .replace(/\s+/g, '')
              // Remove empty lines
              .split('\n')
              .filter(line => line.trim() !== '')
              .join('');
          }

          // Function to calculate directory checksum
          function calculateDirectoryChecksum(dirPath) {
            if (!fs.existsSync(dirPath)) {
              return 'DIRECTORY_NOT_EXISTS';
            }
            
            const files = [];
            
            function walkDir(dir) {
              const items = fs.readdirSync(dir, { withFileTypes: true });
              for (const item of items) {
                const fullPath = path.join(dir, item.name);
                if (item.isDirectory()) {
                  walkDir(fullPath);
                } else {
                  files.push(fullPath);
                }
              }
            }
            
            walkDir(dirPath);
            files.sort(); // Ensure consistent ordering
            
            const hash = crypto.createHash('md5');
            for (const file of files) {
              const relativePath = path.relative(dirPath, file);
              const content = fs.readFileSync(file, 'utf8');
              // Normalize line endings for consistent checksums
              const normalizedContent = content.replace(/\r\n/g, '\n');
              hash.update(relativePath + ':' + normalizedContent);
            }
            
            return hash.digest('hex');
          }

          // Find all schema.prisma files
          function findSchemaFiles() {
            const schemas = [];
            const prismaDir = 'prisma';
            
            if (!fs.existsSync(prismaDir)) {
              console.log('No prisma directory found');
              return schemas;
            }
            
            function walkDir(dir) {
              const items = fs.readdirSync(dir, { withFileTypes: true });
              for (const item of items) {
                const fullPath = path.join(dir, item.name);
                if (item.isDirectory()) {
                  walkDir(fullPath);
                } else if (item.name === 'schema.prisma') {
                  schemas.push(fullPath);
                }
              }
            }
            
            walkDir(prismaDir);
            return schemas;
          }

          async function main() {
            console.log('🔍 Finding Prisma schema files...');
            const schemaFiles = findSchemaFiles();
            
            if (schemaFiles.length === 0) {
              console.log('✅ No schema.prisma files found, skipping check');
              return;
            }
            
            console.log(`📋 Found ${schemaFiles.length} schema files:`, schemaFiles);
            
            let hasSchemaChanges = false;
            let migrationErrors = [];
            
            for (const schemaFile of schemaFiles) {
              console.log(`\n🔍 Checking schema: ${schemaFile}`);
              
              // Get current (PR) schema content
              const currentSchema = fs.readFileSync(schemaFile, 'utf8');
              const sanitizedCurrent = sanitizeSchema(currentSchema);
              
              // Get target branch schema content
              let targetSchema = '';
              try {
                targetSchema = execSync(`git show origin/${{ github.event.pull_request.base.ref }}:${schemaFile}`, { encoding: 'utf8' });
              } catch (error) {
                console.log(`⚠️  Could not find ${schemaFile} in target branch, treating as new file`);
                targetSchema = '';
              }
              
              const sanitizedTarget = sanitizeSchema(targetSchema);
              
              // Compare sanitized schemas
              if (sanitizedCurrent !== sanitizedTarget) {
                console.log(`📦 Schema changes detected in ${schemaFile}`);
                hasSchemaChanges = true;
                
                // Get the corresponding migrations directory
                const schemaDir = path.dirname(schemaFile);
                const migrationsDir = path.join(schemaDir, 'migrations');
                
                console.log(`🔍 Checking migrations directory: ${migrationsDir}`);
                
                // Calculate checksums for migrations directory
                const currentMigrationsChecksum = calculateDirectoryChecksum(migrationsDir);
                
                let targetMigrationsChecksum = 'DIRECTORY_NOT_EXISTS';
                try {
                  // Create a temporary directory to checkout target branch migrations
                  const tempDir = `/tmp/target_migrations_${Date.now()}`;
                  execSync(`mkdir -p ${tempDir}`);
                  
                  try {
                    execSync(`git archive origin/${{ github.event.pull_request.base.ref }} ${migrationsDir} | tar -x -C ${tempDir}`, { stdio: 'pipe' });
                    targetMigrationsChecksum = calculateDirectoryChecksum(path.join(tempDir, migrationsDir));
                  } catch (archiveError) {
                    console.log(`⚠️  Could not find migrations directory in target branch: ${migrationsDir}`);
                  }
                  
                  // Cleanup
                  execSync(`rm -rf ${tempDir}`);
                } catch (error) {
                  console.log(`⚠️  Error checking target migrations: ${error.message}`);
                }
                
                console.log(`📍 Current migrations checksum: ${currentMigrationsChecksum}`);
                console.log(`📍 Target migrations checksum: ${targetMigrationsChecksum}`);
                
                // If checksums are equal, migrations weren't updated
                if (currentMigrationsChecksum === targetMigrationsChecksum) {
                  const error = `❌ Schema changed in ${schemaFile} but migrations directory ${migrationsDir} was not updated!`;
                  console.log(error);
                  migrationErrors.push({
                    schema: schemaFile,
                    migrationsDir: migrationsDir,
                    error: error
                  });
                } else {
                  console.log(`✅ Migrations directory ${migrationsDir} was properly updated`);
                }
              } else {
                console.log(`✅ No changes detected in ${schemaFile}`);
              }
            }
            
            if (migrationErrors.length > 0) {
              console.log('\n❌ Migration validation failed!');
              console.log('\n📋 Errors found:');
              migrationErrors.forEach(error => {
                console.log(`  - ${error.error}`);
              });
              
              console.log('\n🔧 To fix this issue:');
              console.log('1. Run `pnpm migrate-all` locally to generate new migration files');
              console.log('2. Commit the generated migration files');
              console.log('3. Push the changes to your PR branch');
              
              process.exit(1);
            } else if (hasSchemaChanges) {
              console.log('\n✅ All schema changes have corresponding migration updates');
            } else {
              console.log('\n✅ No schema changes detected');
            }
          }

          main().catch(error => {
            console.error('❌ Script failed:', error);
            process.exit(1);
          });
          EOF

      - name: Run schema comparison and migration check
        run: node compare_schemas.js

      - name: Comment on PR if migrations are missing
        if: failure()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## ❌ Prisma Migration Check Failed')
            );

            const commentBody = `## ❌ Prisma Migration Check Failed

            **Schema changes detected but migrations were not updated!**

            This PR contains changes to Prisma schema files, but the corresponding migration files were not generated or updated.

            ### 🔧 Required Actions:

            1. **Run migrations locally:**
               \`\`\`bash
               pnpm migrate-all
               \`\`\`

            2. **Commit the generated migration files:**
               \`\`\`bash
               git add prisma/*/migrations/
               git commit -m "feat: add migrations for schema changes"
               \`\`\`

            3. **Push the changes:**
               \`\`\`bash
               git push
               \`\`\`

            ### 📋 Why this check exists:

            - Ensures database schema changes are properly versioned
            - Prevents deployment issues from missing migrations  
            - Maintains consistency between schema definitions and migration files
            - Protects against data loss from untracked schema changes

            This check compares the sanitized schema content (without comments/whitespace) between your PR branch and the target branch, then verifies that migration directories have been updated when schema changes are detected.`;

            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: commentBody
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentBody
              });
            }

      - name: Remove comment on PR (Success)
        if: success()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## ❌ Prisma Migration Check Failed')
            );

            if (existingComment) {
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id
              });
            }
