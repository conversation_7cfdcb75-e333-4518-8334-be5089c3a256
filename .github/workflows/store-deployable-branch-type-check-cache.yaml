name: Store deployable branch type check cache
run-name: Store type check cache for ${{ github.ref_name }}
'on':
  push:
    branches:
      - dev
      - staging
      - main
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
env:
  ACTIONS_STEP_DEBUG: true
jobs:
  cache-type-check:
    name: Cache Type Check Results
    uses: ./.github/workflows/pull-requests.yaml
    with:
      is_store_cache_only: true
    secrets: inherit