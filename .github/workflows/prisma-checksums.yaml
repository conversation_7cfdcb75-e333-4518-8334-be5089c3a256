name: Prisma Schema Checksums

on:
  workflow_call:

permissions:
  contents: read
  pull-requests: write

jobs:
  check-prisma-checksums:
    name: Check Prisma Schema Checksums
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.13.0
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --ignore-scripts

      - name: Verify Prisma schema checksums
        run: pnpm verify-checksums

      - name: Comment on PR if checksums don't match
        if: failure()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## ⚠️ Prisma Schema Changes Detected')
            );

            const commentBody = `## ⚠️ Prisma Schema Changes Detected

            Changes to Prisma schema files were detected, but the generated types have not been updated.

            ### Required Actions:

            1. Run \`pnpm codegen\` to update the generated types
            2. Commit both the schema changes and the generated types
            3. Push the changes to your branch

            This check ensures that your database schema changes are properly reflected in the TypeScript types used throughout the codebase.

            The checksum verification system prevents inconsistencies between schema definitions and generated types, which could lead to runtime errors.`;

            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: commentBody
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentBody
              });
            }

      - name: Remove comment on PR (Success)
        if: success()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## ⚠️ Prisma Schema Changes Detected')
            );

            if (existingComment) {
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id
              });
            }
