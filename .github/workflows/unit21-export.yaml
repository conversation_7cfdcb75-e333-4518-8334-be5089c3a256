name: Scheduled Unit21 Export

on:
  schedule:
    - cron: '0 0 1 * *'  # Monthly
  workflow_dispatch:
env:
  DB_HOST: localhost
  DB_USER: migrations
  DB_PW: secrets.RISE_SQL_MIGRATIONS_PROD
  DB_PORT: 3306
  DB_SCHEMA: rise
  IS_CI: true
  SOCKET_PATH: /data/sql/production/sock/riseworksprod:us-central1:rise
jobs:
  install_and_run:
    runs-on:
      - self-hosted
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: main
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.13.0
          cache: pnpm
      - run: pnpm install --ignore-scripts
      - name: Get current date
        id: date
        run: |
          START_DATE=$(date -d "-1 month" +%Y-%m-%d)
          echo "START_DATE=$START_DATE" >> $GITHUB_ENV
      - run: touch ./packages/scripts/.env && pnpm script unit21Export --save --startDate ${{env.START_DATE}}