name: OpenAPI Visual Diff
on:
  workflow_call:
    outputs:
      diff_exists:
        description: 'Whether differences were found in the OpenAPI spec'
        value: ${{ jobs.openapi-diff.outputs.diff_exists }}

permissions:
  contents: read
  pull-requests: write

jobs:
  openapi-diff:
    name: Generate OpenAPI Diff
    runs-on:
      - self-hosted
    outputs:
      diff_exists: ${{ steps.check-changes.outputs.has_changes == 'true' && steps.diff.outputs.diff_exists || 'false' }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Check for OpenAPI spec changes
        id: check-changes
        run: |
          echo "openapi_files=$(git diff --name-only --diff-filter=AM ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -E 'packages/contracts/src/codegen/openapi/rise.yaml$' | tr '\n' ' ')" >> $GITHUB_OUTPUT
          if [ -z "$(git diff --name-only --diff-filter=AM ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -E 'packages/contracts/src/codegen/openapi/rise.yaml$')" ]; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "diff_exists=false" >> $GITHUB_OUTPUT
          else
            echo "has_changes=true" >> $GITHUB_OUTPUT
          fi

      - name: Skip if no changes
        if: steps.check-changes.outputs.has_changes == 'false'
        run: |
          echo "No changes detected in OpenAPI spec file. Skipping diff generation."
          # Set the output for the job to indicate no diff exists
          echo "diff_exists=false" >> $GITHUB_OUTPUT

      - name: Checkout base branch
        if: steps.check-changes.outputs.has_changes == 'true'
        run: |
          git fetch origin ${{ github.base_ref }}
          git checkout -b base_branch origin/${{ github.base_ref }}

      - name: Setup Node.js
        if: steps.check-changes.outputs.has_changes == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          
      - name: Cache oasdiff
        id: cache-oasdiff
        uses: actions/cache@v3
        with:
          path: $HOME/.local/bin/oasdiff
          key: oasdiff-1.11.1-linux-amd64

      - name: Install oasdiff
        if: steps.check-changes.outputs.has_changes == 'true' && steps.cache-oasdiff.outputs.cache-hit != 'true'
        run: |
          curl -sSL https://github.com/Tufin/oasdiff/releases/download/v1.11.1/oasdiff_1.11.1_linux_amd64.tar.gz -o oasdiff.tar.gz
          tar -xzf oasdiff.tar.gz
          chmod +x oasdiff
          mkdir -p $HOME/.local/bin
          mv oasdiff $HOME/.local/bin/
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Add oasdiff to PATH
        if: steps.check-changes.outputs.has_changes == 'true' && steps.cache-oasdiff.outputs.cache-hit == 'true'
        run: |
          chmod +x $HOME/.local/bin/oasdiff
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Generate OpenAPI diff
        id: diff
        if: steps.check-changes.outputs.has_changes == 'true'
        run: |
          # First, make sure we're on the PR branch
          git checkout ${{ github.event.pull_request.head.ref }}
          echo "On PR branch: $(git branch --show-current)"
          
          # Save the PR branch OpenAPI file
          if [ -f packages/contracts/src/codegen/openapi/rise.yaml ]; then
            cp packages/contracts/src/codegen/openapi/rise.yaml $GITHUB_WORKSPACE/pr_openapi.yaml
            echo "PR branch OpenAPI file copied to $GITHUB_WORKSPACE/pr_openapi.yaml"
            echo "PR file hash: $(sha256sum $GITHUB_WORKSPACE/pr_openapi.yaml | cut -d' ' -f1)"
          else
            echo "Error: OpenAPI file not found in PR branch"
            exit 1
          fi
          
          # Now checkout the base branch
          git fetch origin ${{ github.base_ref }}
          git checkout origin/${{ github.base_ref }}
          echo "On base branch: $(git branch --show-current)"
          
          # Save the base branch OpenAPI file
          if [ -f packages/contracts/src/codegen/openapi/rise.yaml ]; then
            cp packages/contracts/src/codegen/openapi/rise.yaml $GITHUB_WORKSPACE/base_openapi.yaml
            echo "Base branch OpenAPI file copied to $GITHUB_WORKSPACE/base_openapi.yaml"
            echo "Base file hash: $(sha256sum $GITHUB_WORKSPACE/base_openapi.yaml | cut -d' ' -f1)"
            
            # Debug: Compare file sizes
            echo "PR file size: $(wc -c < $GITHUB_WORKSPACE/pr_openapi.yaml) bytes"
            echo "Base file size: $(wc -c < $GITHUB_WORKSPACE/base_openapi.yaml) bytes"
            
            # Debug: Show diff of the files
            echo "File diff summary:"
            diff -q $GITHUB_WORKSPACE/base_openapi.yaml $GITHUB_WORKSPACE/pr_openapi.yaml || echo "Files are different"
            
            # Switch back to PR branch
            git checkout ${{ github.event.pull_request.head.ref }}
            echo "Switched back to PR branch: $(git branch --show-current)"
            
            # Generate diff report using oasdiff
            echo "Running oasdiff..."
            oasdiff diff $GITHUB_WORKSPACE/base_openapi.yaml $GITHUB_WORKSPACE/pr_openapi.yaml --format markdown > raw_diff.md || echo "oasdiff detected changes (exit code: $?)"
            
            # Add emojis to the output
            sed -e 's/^### Added/### ✨ Added/g' \
                -e 's/^### Deleted/### 🗑️ Deleted/g' \
                -e 's/^### Modified/### 🔄 Modified/g' \
                -e 's/^#### Added/#### ✅ Added/g' \
                -e 's/^#### Deleted/#### ❌ Deleted/g' \
                -e 's/^#### Modified/#### 📝 Modified/g' \
                -e 's/^##### Added/##### ➕ Added/g' \
                -e 's/^##### Deleted/##### ➖ Deleted/g' \
                -e 's/^##### Modified/##### 🔁 Modified/g' raw_diff.md > openapi_diff.md
            
            # Check if there are any changes
            if [ -s raw_diff.md ]; then
              echo "DIFF_EXISTS=true" >> $GITHUB_ENV
              echo "diff_exists=true" >> $GITHUB_OUTPUT
              # Add header to the diff file
              sed -i '1i## 📊 OpenAPI Changes\n' openapi_diff.md
            else
              echo -e "## 📊 OpenAPI Changes\n\n✅ No significant changes detected" > openapi_diff.md
              echo "DIFF_EXISTS=false" >> $GITHUB_ENV
              echo "diff_exists=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "Base branch does not have the OpenAPI file"
            echo -e "## 📊 OpenAPI Changes\n\nNew OpenAPI file added in this PR" > openapi_diff.md
            echo "DIFF_EXISTS=false" >> $GITHUB_ENV
            echo "diff_exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Create or update PR comment
        if: ${{ steps.check-changes.outputs.has_changes == 'true' }}
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            const diffContent = fs.readFileSync('openapi_diff.md', 'utf8');
            
            const commentBody = diffContent;
            
            // Find existing comment
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });
            
            const existingComment = comments.find(comment => 
              comment.body.includes('## 📊 OpenAPI Changes')
            );
            
            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: commentBody
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentBody
              });
            }
