name: Apps
run-name: Deploy app to ${{ github.ref_name }}
'on':
  push:
    branches:
      - dev
      - staging
      - main
permissions:
  contents: write
  pull-requests: write
  id-token: write
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
env:
  MIGRATION_ENV: >-
    ${{contains(github.ref, 'dev') && 'development' || contains(github.ref,
    'staging') && 'staging' || contains(github.ref, 'main') && 'production'}}
  ACTIONS_STEP_DEBUG: true
  DB_HOST: localhost
  DB_USER: migrations
  DB_PW: >-
    ${{contains(github.ref, 'dev') && secrets.RISE_SQL_MIGRATIONS_DEV ||
    contains(github.ref, 'staging') && secrets.RISE_SQL_MIGRATIONS_STAGING ||
    contains(github.ref,'main') && secrets.RISE_SQL_MIGRATIONS_PROD}}
  DB_PORT: 3306
  DB_SCHEMA: rise
  SOCKET_PATH: >-
    /data/sql${{contains(github.ref,'dev') &&
    '/dev/sock/riseworksdev:us-central1:rise-tf-primary' || contains(github.ref,
    'staging') && '/staging/sock/riseworksstaging:us-central1:rise-tf-primary'
    || contains(github.ref, 'main')
    &&'/production/sock/riseworksprod:us-central1:rise'}}
jobs:
  pre-deploy:
    outputs:
      changed-services: ${{ steps.modified-services.outputs.services }}
    if: >-
      github.actor != 'github-actions' &&
      !contains(github.event.head_commit.message, 'release-please')
    name: Pre deploy 
    runs-on:
      - self-hosted
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history to allow commit comparison 
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.13.0
          cache: pnpm
      - run: pnpm install --ignore-scripts && pnpm add -g concurrently prisma
      - name: Publish to Cloudflare Pages
        if: contains(github.ref, 'staging')
        uses: cloudflare/wrangler-action@v3
        continue-on-error: true
        with:
          apiToken: ${{ secrets.CLOUDFLARE_PAGES_API_TOKEN }}
          accountId: c4c0629f6236a738da924864c998dd8a
          packageManager: pnpm
          workingDirectory: packages/contracts
          command: >-
            pages deploy ./src/codegen/scalar --project-name=docs-dev
            --commit-dirty=true
            
      - name: Apply Prisma Migrations
        run: concurrently "DATABASE_URL=mysql://migrations:${{contains(github.ref, 'dev') && secrets.RISE_SQL_MIGRATIONS_DEV || contains(github.ref, 'staging') && secrets.RISE_SQL_MIGRATIONS_STAGING || secrets.RISE_SQL_MIGRATIONS_PROD}}@localhost/rise_audit?socket=/data/sql${{contains(github.ref, 'dev') && '/dev/sock/riseworksdev:us-central1:rise-tf-primary' || contains(github.ref, 'staging') && '/staging/sock/riseworksstaging:us-central1:rise-tf-primary' || contains(github.ref, 'main') && '/production/sock/riseworksprod:us-central1:rise'}} prisma migrate deploy --schema=./prisma/rise_audit/schema.prisma" "DATABASE_URL=mysql://migrations:${{contains(github.ref, 'dev') && secrets.RISE_SQL_MIGRATIONS_DEV || contains(github.ref, 'staging') && secrets.RISE_SQL_MIGRATIONS_STAGING || secrets.RISE_SQL_MIGRATIONS_PROD}}@localhost/rise_private?socket=/data/sql${{contains(github.ref, 'dev') && '/dev/sock/riseworksdev:us-central1:rise-tf-primary' || contains(github.ref, 'staging') && '/staging/sock/riseworksstaging:us-central1:rise-tf-primary' || contains(github.ref, 'main') && '/production/sock/riseworksprod:us-central1:rise'}} prisma migrate deploy --schema=./prisma/rise_private/schema.prisma" "DATABASE_URL=mysql://migrations:${{contains(github.ref, 'dev') && secrets.RISE_SQL_MIGRATIONS_DEV || contains(github.ref, 'staging') && secrets.RISE_SQL_MIGRATIONS_STAGING || secrets.RISE_SQL_MIGRATIONS_PROD}}@localhost/mastertax?socket=/data/sql${{contains(github.ref, 'dev') && '/dev/sock/riseworksdev:us-central1:rise-tf-primary' || contains(github.ref, 'staging') && '/staging/sock/riseworksstaging:us-central1:rise-tf-primary' || contains(github.ref, 'main') && '/production/sock/riseworksprod:us-central1:rise'}} prisma migrate deploy --schema=./prisma/mastertax/schema.prisma" "DATABASE_URL=mysql://migrations:${{contains(github.ref, 'dev') && secrets.RISE_SQL_MIGRATIONS_DEV || contains(github.ref, 'staging') && secrets.RISE_SQL_MIGRATIONS_STAGING || secrets.RISE_SQL_MIGRATIONS_PROD}}@localhost/rise?socket=/data/sql${{contains(github.ref, 'dev') && '/dev/sock/riseworksdev:us-central1:rise-tf-primary' || contains(github.ref, 'staging') && '/staging/sock/riseworksstaging:us-central1:rise-tf-primary' || contains(github.ref, 'main') && '/production/sock/riseworksprod:us-central1:rise'}} prisma migrate deploy --schema=./prisma/rise/schema.prisma"
          
      - name: Run database seeding
        run: NODE_ENV=localhost GOOGLE_PROJECT_NUMBER=NULL DB_CONNECTION_LIMIT=10 prisma db seed

      - name: Get modified API services
        id: modified-services
        run: |
          apis=$(pnpm --filter="./apps/*" --filter='!dev' --filter='!testing' ls --json  | jq -r '.[] | .name' | jq -R -s -c 'split("\n") | map(select(length > 0))' )
          echo $apis

          modified_packages=$(pnpm --filter="...[${{ github.event.before }}...${{ github.event.after }}]" ls --json  | jq -r '.[] | .name' | jq -R -s -c 'split("\n") | map(select(length > 0))' )
          echo $modified_packages

          SERVICES=$(jq -c -n --argjson a "$apis" --argjson b "$modified_packages" '
            $a | map(select(. as $item | $b | index($item) != null))
          ')
          echo $SERVICES
          if [ -z "$SERVICES" ] || [ "$SERVICES" = "null" ] || [ "$SERVICES" = "" ]; then
            SERVICES="[]"
          fi
          echo "services=$SERVICES" >> $GITHUB_OUTPUT

  build-and-push:
    needs:
      - pre-deploy
    if: >-
      github.actor != 'github-actions' &&
      !contains(github.event.head_commit.message, 'release-please') &&
      needs.pre-deploy.outputs.changed-services != '[]'
    uses: ./.github/workflows/build-and-push.yaml

  deploy:
    needs:
      - pre-deploy
      - build-and-push
    if: >-
      github.actor != 'github-actions' &&
      !contains(github.event.head_commit.message, 'release-please') &&
      needs.pre-deploy.outputs.changed-services != '[]'
    strategy:
      # if any of the deployments fail we want to keep up with the queued ones
      fail-fast: false
      matrix: 
        changed-services : ${{ fromJson(needs.pre-deploy.outputs.changed-services) }}
    uses: ./.github/workflows/deploy-to-cloudrun.yaml
    with:
      APP_NAME:  ${{ matrix.changed-services }}