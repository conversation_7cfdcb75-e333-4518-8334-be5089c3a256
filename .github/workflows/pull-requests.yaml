name: Pull Request checks
'on':
  pull_request:
    branches:
      - dev
      - staging
      - main
  workflow_call:
    inputs:
      is_store_cache_only:
        description: 'Skip all steps except for storing the TypeScript cache'
        required: false
        type: boolean
        default: false

permissions:
  contents: write
  pull-requests: write
  id-token: write
# Use a different concurrency group when running in cache-only mode to avoid deadlocks
concurrency:
  group: ${{ inputs.is_store_cache_only == true && format('{0}-cache-only-{1}', github.workflow, github.run_id) || format('{0}-{1}', github.workflow, github.head_ref || github.run_id) }}
  cancel-in-progress: true
env:
  ACTIONS_STEP_DEBUG: true
  TARGET_BRANCH: "${{ github.base_ref }}"
  SOURCE_BRANCH: "${{ github.head_ref }}"
  IS_CI: true
jobs:
  lint-and-typecheck:
    name: Lint and typecheck
    runs-on:
      - self-hosted
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.13.0
          cache: pnpm
      - run: pnpm install --ignore-scripts

      - run: pnpm run lint-all
        if: ${{ github.event_name != 'workflow_dispatch' && inputs.is_store_cache_only != true }}


      - name: Restore Typescript Cache
        uses: Attest/typescript-cache-action@main
        with:
          # If it's a PR  will use target branch typecheck cache
          # If it's a cache store will restore previous version cache
          cache-base-key: ${{ github.event.pull_request.base.sha || github.event.before }}
          cache-key: ${{ format('{0}{1}', github.base_ref || github.ref_name, (github.event_name == 'pull_request' && format('-{0}-{1}', github.head_ref, github.event.pull_request.head.sha) || '')) }} 
          base-ref: ${{ github.event.pull_request.base.sha }}
          files: |
            ./apps/*/dist/**/*.d.ts
            ./apps/*/dist/tsconfig.tsbuildinfo
            ./packages/*/dist/**/*.d.ts
            ./packages/*/dist/tsconfig.tsbuildinfo

      - run: pnpm run typecheck-all

      - name: Save Typescript Cache
        uses: Attest/typescript-cache-action/save@main
        with:
          cache-base-key: ${{ github.event.pull_request.base.sha || github.event.after }}
          cache-key: ${{ format('{0}{1}', github.base_ref || github.ref_name, (github.event_name == 'pull_request' && format('-{0}-{1}', github.head_ref, github.event.pull_request.head.sha) || '')) }} 
          files: |
            ./apps/*/dist/**/*.d.ts
            ./apps/*/dist/tsconfig.tsbuildinfo
            ./packages/*/dist/**/*.d.ts
            ./packages/*/dist/tsconfig.tsbuildinfo

      - name: Run secretlint on changed files
        if: ${{ inputs.is_store_cache_only != true }}
        run: |
          CHANGED_FILES=$(git diff --name-only --diff-filter=d ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | tr '\n' ' ')
          if [ -n "$CHANGED_FILES" ]; then
            echo "Running secretlint on changed files"
            pnpm secretlint $CHANGED_FILES
          else
            echo "No files changed, skipping secretlint"
          fi

      - run: pnpm test:unit
        if: ${{ inputs.is_store_cache_only != true }}

  validate-sql:
    name: Validate SQL Files
    uses: ./.github/workflows/validate-sql.yaml
    if: inputs.is_store_cache_only != true
    secrets: inherit

  openapi-diff:
    name: OpenAPI Visual Diff
    uses: ./.github/workflows/openapi-visual-diff.yaml
    if: inputs.is_store_cache_only != true
    secrets: inherit

  prisma-checksums:
    name: Prisma Schema Checksums
    uses: ./.github/workflows/prisma-checksums.yaml
    if: inputs.is_store_cache_only != true
    secrets: inherit

  success:
    name: Success
    runs-on:
      - self-hosted
    needs: [lint-and-typecheck, validate-sql, openapi-diff, prisma-checksums]
    # Skip the openapi-diff requirement if it wasn't run
    if: |
      inputs.is_store_cache_only != true &&
      (always() &&
        (needs.lint-and-typecheck.result == 'success') &&
        (needs.validate-sql.result == 'success') &&
        (needs.openapi-diff.result == 'success' || needs.openapi-diff.result == 'skipped') &&
        (needs.prisma-checksums.result == 'success' || needs.prisma-checksums.result == 'skipped')
      )
    steps:
      - name: Report Success
        run: echo "All required checks passed!"
