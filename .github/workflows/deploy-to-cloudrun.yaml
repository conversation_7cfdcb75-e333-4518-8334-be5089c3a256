name: CloudRun Deploy
# https://cloud.google.com/community/tutorials/cicd-cloud-run-github-actions

on:
  workflow_call:
    inputs:
      ENV_NAME:
        required: false
        type: string
        default: ${{ contains(github.ref, 'main') && 'prod' || contains(github.ref, 'staging') && 'staging' || 'dev'}}
      GCP_PROJECT_ID:
        required: false
        type: string
        default: riseworks${{ contains(github.ref, 'main') && 'prod' || contains(github.ref, 'staging') && 'staging' || 'dev'}}
      GCP_PROJECT_NUMBER:
        required: false
        type: string
        default: ${{ contains(github.ref, 'main') && '23957376505' || contains(github.ref, 'staging') && '103829732552' || '841056449880'}}
      NODE_ENV:
        required: false
        type: string
        default: ${{ contains(github.ref, 'main') && 'production' || contains(github.ref, 'staging') && 'staging' || 'development'}}
      APP_NAME:
        required: true
        type: string
      ENV_DEPLOY:
        required: false
        type: boolean
        default: false
        
env:
  GCP_REGION: us-central1
  GCP_ARTIFACT_REGISTRY: us-docker.pkg.dev/${{ inputs.GCP_PROJECT_ID }}/rise-docker-registry/rise-apis
  GCP_BUILD_FOLDER: ./apps/${{ inputs.APP_NAME }}/cloudrun/${{ inputs.ENV_NAME }}
  
  # EXAMPLES
  # https://github.com/antonputra/lesson-087/blob/main/.github/workflows/gcp.yml

jobs:
  deploy:
    name: Deploy
    runs-on: [self-hosted, cloudrun]

    # Add "id-token" with the intended permissions.
    permissions:
      contents: 'read'
      id-token: 'write'

    env:
      SENTRY_AUTH_TOKEN: ****************************************************************

    steps:

      # MUST come before auth
      - name: Checkout
        uses: actions/checkout@v4

      # Configure Workload Identity Federation and generate an access token.
      # https://github.com/google-github-actions/auth#authenticating-via-service-account-key-json-1
      - id: 'auth'
        name: 'Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: 'projects/${{ inputs.GCP_PROJECT_NUMBER }}/locations/global/workloadIdentityPools/build-actions/providers/github'
          service_account: 'github-action@${{ inputs.GCP_PROJECT_ID }}.iam.gserviceaccount.com'

      # Setup gcloud CLI
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
      
      - name: Deploy
        timeout-minutes: 5
        run: |-
          cd $GCP_BUILD_FOLDER
          kustomize edit set image service-image="$GCP_ARTIFACT_REGISTRY:$GITHUB_SHA"
          kustomize build . -o ./service.yml
          cat ./service.yml
          gcloud run services replace ./service.yml --region "$GCP_REGION"