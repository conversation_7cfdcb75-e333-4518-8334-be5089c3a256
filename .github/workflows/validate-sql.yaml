name: Validate SQL Files
'on':
  workflow_call:
    outputs:
      validation_status:
        description: 'Status of SQL validation'
        value: ${{ jobs.validate-sql.outputs.status }}

permissions:
  contents: read
  pull-requests: write

jobs:
  validate-sql:
    name: Validate SQL Files
    runs-on:
      - self-hosted

    outputs:
      status: ${{ steps.validate.outputs.status }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Find changed SQL files
        id: find-sql
        run: echo "sql_files=$(git diff --name-only --diff-filter=AM ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -E 'prisma/.*/migrations/.*\.sql$' | tr '\n' ' ')" >> $GITHUB_OUTPUT

      - name: Check for SQL changes
        id: check-changes
        run: |
          if [ -z "${{ steps.find-sql.outputs.sql_files }}" ]; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "No SQL files to validate"
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "has_changes=true" >> $GITHUB_OUTPUT
            echo "Found SQL files to validate: ${{ steps.find-sql.outputs.sql_files }}"
          fi

      - name: Create requirements file if it doesn't exist
        if: steps.check-changes.outputs.has_changes == 'true'
        run: |
          mkdir -p .github/workflows
          if [ ! -f .github/workflows/requirements-sqlfluff.txt ]; then
            echo "sqlfluff==3.4.0" > .github/workflows/requirements-sqlfluff.txt
          fi

      - name: Set up Python
        if: steps.check-changes.outputs.has_changes == 'true'
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'
          cache-dependency-path: '.github/workflows/requirements-sqlfluff.txt'

      - name: Install SQLFluff
        if: steps.check-changes.outputs.has_changes == 'true'
        run: pip install -r .github/workflows/requirements-sqlfluff.txt

      - name: Validate SQL files
        id: validate
        if: steps.check-changes.outputs.has_changes == 'true'
        run: |
          set +e
          ERRORS=0

          for file in ${{ steps.find-sql.outputs.sql_files }}; do
            echo "Validating $file"
            sqlfluff lint "$file" --dialect mysql --exclude-rules layout,AM04
            if [ $? -ne 0 ]; then
              ERRORS=1
              echo "::error file=$file::SQL validation failed"
            fi
          done

          if [ $ERRORS -eq 0 ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

          exit $ERRORS

      - name: Comment on PR (Failure)
        if: failure()
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## SQL Validation Results')
            );

            const commentBody = `## SQL Validation Results

            ⚠️ Some SQL files in this PR have validation issues. Please check the workflow logs for details.

            You can run \`sqlfluff lint $file --dialect mysql --exclude-rules layout,AM04\` locally to fix these issues before pushing again.`;

            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: commentBody
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentBody
              });
            }

      - name: Remove comment on PR (Success)
        if: success() && steps.check-changes.outputs.has_changes == 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('## SQL Validation Results')
            );

            if (existingComment) {
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id
              });
            }
