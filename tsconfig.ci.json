{"extends": "./tsconfig.json", "compilerOptions": {"composite": false, "incremental": false, "noEmit": true, "sourceMap": false, "paths": {"backend": ["./packages/backend/src/index.ts"], "@riseworks/contracts": ["./packages/contracts/src/index.ts"], "db": ["./packages/db/src/index.ts"], "features": ["./packages/features/src/index.ts"], "repositories": ["./packages/repositories/src/index.ts"], "utils": ["./packages/utils/src/index.ts"]}}}