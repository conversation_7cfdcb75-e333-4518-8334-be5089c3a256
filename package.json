{"name": "api-monorepo", "version": "1.7.0", "description": "Rise API Services", "repository": "**************:riseworks/api-monorepo.git", "license": "PROPRIETARY", "private": true, "engines": {"node": "22"}, "os": ["darwin", "linux"], "scripts": {"script": "pnpm --filter scripts script", "prepare": "npx -y husky install", "preinstall": "npx -y only-allow pnpm", "codegen": "MIGRATION_ENV=development LOG_LEVEL=error pnpm --filter codegen start --all && biome format --write .", "codegen:openapi": "LOG_LEVEL=error pnpm --filter codegen start --openapi", "codegen:kysely": "LOG_LEVEL=error pnpm --filter codegen start --kysely", "codegen:contracts": "LOG_LEVEL=error pnpm --filter codegen start --contracts-merge", "codegen:smart-contracts": "LOG_LEVEL=error MIGRATION_ENV=development pnpm --filter codegen start --smart-contracts", "codegen:dev": "pnpm --filter codegen watch", "codegen:events": "LOG_LEVEL=error pnpm --filter codegen start --events-merge", "codegen:endpoints": "LOG_LEVEL=error pnpm --filter codegen start --endpoints-merge", "generate:repo": "pnpm --filter codegen generate --repo", "generate:endpoint": "pnpm --filter codegen generate --endpoint", "generate:service": "pnpm --filter codegen generate --service", "generate:app": "pnpm --filter codegen generate --service", "generate:event": "pnpm --filter codegen generate --event", "start-db": "bash ./packages/scripts/initdb.sh", "serve": "pnpm --filter contracts serve", "dev-all": "pnpm run -w --parallel \"/^dev:.*/\"", "dev:inngest": "inngest-cli dev --no-discovery --no-poll", "dev:start": "pnpm --filter dev run start", "test-all": "pnpm --filter testing test", "lint-all": "pnpm --filter !dev --filter !testing --filter !scripts --parallel lint", "typecheck-all": "pnpm --filter !dev --filter !testing --filter !scripts typecheck", "typecheck:dev": "tsc -b ./tsconfig.build.json --watch", "build-all": "pnpm -r build", "clean-all": "pnpm -r clean", "secretlint": "secretlint", "migrate-all": "pnpm migrate:rise && pnpm migrate:rise_private && pnpm migrate:rise_audit && pnpm migrate:mastertax && pnpm --filter testing db:reset && pnpm seed-and-generate", "seed-and-generate": "MIGRATION_ENV=localhost LOG_LEVEL=error pnpm --filter db seed && pnpm codegen", "migrate:rise": "MIGRATION_ENV=development prisma migrate dev --schema=./prisma/rise/schema.prisma --skip-generate --skip-seed", "migrate:rise_private": "MIGRATION_ENV=development prisma migrate dev --schema=./prisma/rise_private/schema.prisma --skip-generate --skip-seed", "migrate:rise_audit": "MIGRATION_ENV=development prisma migrate dev --schema=./prisma/rise_audit/schema.prisma --skip-generate --skip-seed", "migrate:mastertax": "MIGRATION_ENV=development prisma migrate dev --schema=./prisma/mastertax/schema.prisma --skip-generate --skip-seed", "deploy-all": "prisma migrate deploy --schema=./prisma/rise/schema.prisma && prisma migrate deploy --schema=./prisma/rise_private/schema.prisma && prisma migrate deploy --schema=./prisma/rise_audit/schema.prisma && prisma migrate deploy --schema=./prisma/mastertax/schema.prisma", "test:unit": "vitest --config vitest.unit.config.ts", "verify-checksums": "pnpm --filter codegen verify-checksums", "db:copy": "pnpm script copyRemoteDatabase"}, "prisma": {"seed": "pnpm --filter db seed"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@hkdobrev/run-if-changed": "^0.6.3", "@secretlint/secretlint-rule-filter-comments": "^9.3.4", "@secretlint/secretlint-rule-preset-recommend": "^9.3.4", "husky": "^9.1.7", "prisma": "^6.9.0", "secretlint": "^9.3.4", "typescript": "^5.8.3", "vitest": "^3.2.3", "wrangler": "3.90.0"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "inngest-cli"], "overrides": {"google-auth-library@9.15.1": "^10.1.0", "@openzeppelin/contracts": "^5.3.0", "esbuild-linux-64": "^0.25.0", "esbuild": "^0.25.0", "ws": "^8.7.0", "glob": "^10.4.5", "elliptic": "^6.6.0", "@sentry/node": "^8.5.0", "axios": "^1.9.0"}}, "run-if-changed": {"pnpm-lock.yaml": "pnpm i", "prisma/rise/migrations/**/*.sql": "pnpm migrate-all", "prisma/rise_private/migrations/**/*.sql": "pnpm migrate-all", "prisma/rise_audit/migrations/**/*.sql": "pnpm migrate-all", "prisma/mastertax/migrations/**/*.sql": "pnpm migrate-all", "packages/db/src/seed.ts": "pnpm seed-and-generate", "packages/db/src/helpers/contracts.ts": "pnpm seed-and-generate", "packages/db/src/helpers/*.json": "pnpm seed-and-generate"}, "dependencies": {"inngest-cli": "^1.6.4"}, "packageManager": "pnpm@10.12.1"}