/*
  Warnings:

  - A unique constraint covering the columns `[user_nanoid,team_nanoid,count]` on the table `retirement_payroll_settings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `count` to the `retirement_payroll_settings` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX `retirement_payroll_settings_user_nanoid_team_nanoid_retireme_key` ON `retirement_payroll_settings`;

-- AlterTable
ALTER TABLE `retirement_payroll_settings` ADD COLUMN `count` INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `retirement_payroll_settings_user_nanoid_team_nanoid_count_key` ON `retirement_payroll_settings`(`user_nanoid`, `team_nanoid`, `count`);
