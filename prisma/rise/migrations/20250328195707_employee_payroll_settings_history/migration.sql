/*
  Data migration strategy:
  - First add new columns as nullable
  - Populate data from related tables
    - For created by, use team owners first, then team admins, then empty string
  - Make columns NOT NULL
  - Then remove old foreign key columns
*/

-- Add nullable columns first for employee_payroll_pauses
ALTER TABLE `employee_payroll_pauses` 
    ADD COLUMN `created_by` VARCHAR(191) NULL,
    ADD COLUMN `team_nanoid` VARCHAR(191) NULL,
    ADD COLUMN `user_nanoid` VARCHAR(191) NULL;

-- Add nullable columns first for one_off_bonuses
ALTER TABLE `one_off_bonuses` 
    ADD COLUMN `created_by` VARCHAR(191) NULL,
    ADD COLUMN `team_nanoid` VARCHAR(191) NULL,
    ADD COLUMN `user_nanoid` VARCHAR(191) NULL;

-- Add nullable created_by to employee_payroll_settings
ALTER TABLE `employee_payroll_settings` 
    ADD COLUMN `created_by` VARCHAR(191) NULL,
    ADD COLUMN `effective_start` DATE NULL,
    ADD COLUMN `effective_end` DATE NULL;

-- Populate data for employee_payroll_pauses
UPDATE `employee_payroll_pauses` p
JOIN `employee_payroll_settings` s ON p.employee_payroll_settings_id = s.id
LEFT JOIN (
    -- Find team owners (company entities that own the team)
    SELECT 
        team.nanoid AS team_nanoid,
        company.nanoid AS company_nanoid
    FROM rise_entities team
    JOIN rise_entities company ON team.parent_riseid = company.riseid
    WHERE team.type = 'team' AND company.type = 'company'
) team_company ON s.team_nanoid = team_company.team_nanoid
LEFT JOIN (
    -- Find company owners (users that own the company)
    SELECT 
        re.nanoid AS company_nanoid,
        re2.nanoid AS owner_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.parent_riseid = re2.riseid
    WHERE re.type = 'company' AND re2.type = 'user'
) company_owner ON team_company.company_nanoid = company_owner.company_nanoid
LEFT JOIN (
    -- Find team admins (users with team_admin role)
    SELECT 
        re.parent_riseid AS team_riseid,
        re2.nanoid AS admin_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.riseid = re2.riseid
    WHERE re.type = 'team_admin' AND re2.type = 'user'
) team_admin ON s.team_nanoid = team_admin.team_riseid
SET p.user_nanoid = s.nanoid, 
    p.team_nanoid = s.team_nanoid,
    p.created_by = COALESCE(company_owner.owner_nanoid, team_admin.admin_nanoid, '');

-- Populate data for one_off_bonuses
UPDATE `one_off_bonuses` b
JOIN `employee_payroll_settings` s ON b.employee_payroll_settings_id = s.id
LEFT JOIN (
    -- Find team owners (company entities that own the team)
    SELECT 
        team.nanoid AS team_nanoid,
        company.nanoid AS company_nanoid
    FROM rise_entities team
    JOIN rise_entities company ON team.parent_riseid = company.riseid
    WHERE team.type = 'team' AND company.type = 'company'
) team_company ON s.team_nanoid = team_company.team_nanoid
LEFT JOIN (
    -- Find company owners (users that own the company)
    SELECT 
        re.nanoid AS company_nanoid,
        re2.nanoid AS owner_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.parent_riseid = re2.riseid
    WHERE re.type = 'company' AND re2.type = 'user'
) company_owner ON team_company.company_nanoid = company_owner.company_nanoid
LEFT JOIN (
    -- Find team admins (users with team_admin role)
    SELECT 
        re.parent_riseid AS team_riseid,
        re2.nanoid AS admin_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.riseid = re2.riseid
    WHERE re.type = 'team_admin' AND re2.type = 'user'
) team_admin ON s.team_nanoid = team_admin.team_riseid
SET b.user_nanoid = s.nanoid, 
    b.team_nanoid = s.team_nanoid,
    b.created_by = COALESCE(company_owner.owner_nanoid, team_admin.admin_nanoid, '');

-- Populate created_by for employee_payroll_settings
UPDATE `employee_payroll_settings` s
LEFT JOIN (
    -- Find team owners (company entities that own the team)
    SELECT 
        team.nanoid AS team_nanoid,
        company.nanoid AS company_nanoid
    FROM rise_entities team
    JOIN rise_entities company ON team.parent_riseid = company.riseid
    WHERE team.type = 'team' AND company.type = 'company'
) team_company ON s.team_nanoid = team_company.team_nanoid
LEFT JOIN (
    -- Find company owners (users that own the company)
    SELECT 
        re.nanoid AS company_nanoid,
        re2.nanoid AS owner_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.parent_riseid = re2.riseid
    WHERE re.type = 'company' AND re2.type = 'user'
) company_owner ON team_company.company_nanoid = company_owner.company_nanoid
LEFT JOIN (
    -- Find team admins (users with team_admin role)
    SELECT 
        re.parent_riseid AS team_riseid,
        re2.nanoid AS admin_nanoid
    FROM rise_entities re
    JOIN rise_entities re2 ON re.riseid = re2.riseid
    WHERE re.type = 'team_admin' AND re2.type = 'user'
) team_admin ON s.team_nanoid = team_admin.team_riseid
SET s.created_by = COALESCE(company_owner.owner_nanoid, team_admin.admin_nanoid, '');

-- Populate effective_start for employee_payroll_settings
UPDATE `employee_payroll_settings` s
SET s.effective_start = s.start_date;

-- Make columns NOT NULL after populating data
ALTER TABLE `employee_payroll_pauses` 
    MODIFY `created_by` VARCHAR(191) NOT NULL,
    MODIFY `team_nanoid` VARCHAR(191) NOT NULL,
    MODIFY `user_nanoid` VARCHAR(191) NOT NULL;

ALTER TABLE `one_off_bonuses` 
    MODIFY `created_by` VARCHAR(191) NOT NULL,
    MODIFY `team_nanoid` VARCHAR(191) NOT NULL,
    MODIFY `user_nanoid` VARCHAR(191) NOT NULL;

ALTER TABLE `employee_payroll_settings` 
    MODIFY `created_by` VARCHAR(191) NOT NULL,
    MODIFY `effective_start` DATE NOT NULL;

-- DropForeignKey
ALTER TABLE `employee_payroll_pauses` DROP FOREIGN KEY `employee_payroll_pauses_employee_payroll_settings_id_fkey`;

-- DropForeignKey
ALTER TABLE `one_off_bonuses` DROP FOREIGN KEY `one_off_bonuses_employee_payroll_settings_id_fkey`;

-- DropIndex
DROP INDEX `employee_payroll_pauses_employee_payroll_settings_id_start_d_key` ON `employee_payroll_pauses`;

-- DropIndex
DROP INDEX `employee_payroll_settings_nanoid_team_nanoid_key` ON `employee_payroll_settings`;

-- DropIndex
DROP INDEX `healthcare_payroll_settings_nanoid_team_nanoid_type_start_da_key` ON `healthcare_payroll_settings`;

-- DropIndex
DROP INDEX `one_off_bonuses_employee_payroll_settings_id_fkey` ON `one_off_bonuses`;

-- DropIndex
DROP INDEX `retirement_payroll_settings_user_nanoid_team_nanoid_retireme_key` ON `retirement_payroll_settings`;

-- Now drop the old ID columns after data migration
ALTER TABLE `employee_payroll_pauses` DROP COLUMN `employee_payroll_settings_id`;
ALTER TABLE `one_off_bonuses` DROP COLUMN `employee_payroll_settings_id`;

-- Rename columns
ALTER TABLE `employee_payroll_settings` 
    RENAME COLUMN `nanoid` TO `user_nanoid`,
    DROP COLUMN `updated_at`,
    ALTER COLUMN `rise_account` DROP DEFAULT;

-- AlterTable healthcare_payroll_settings
ALTER TABLE `healthcare_payroll_settings` 
    RENAME COLUMN `nanoid` TO `user_nanoid`,
    RENAME COLUMN `start_date` TO `effective_start`,
    RENAME COLUMN `end_date` TO `effective_end`,
    DROP COLUMN `updated_at`;
ALTER TABLE `healthcare_payroll_settings`
    MODIFY `effective_end` DATE NULL;

-- AlterTable retirement_payroll_settings
ALTER TABLE `retirement_payroll_settings` 
    RENAME COLUMN `start_date` TO `effective_start`,
    RENAME COLUMN `end_date` TO `effective_end`,
    DROP COLUMN `updated_at`;
ALTER TABLE `retirement_payroll_settings`
    MODIFY `effective_start` DATE NOT NULL,
    MODIFY `effective_end` DATE NULL;

-- Create indexes
CREATE UNIQUE INDEX `employee_payroll_pauses_user_nanoid_team_nanoid_start_date_e_key` ON `employee_payroll_pauses`(`user_nanoid`, `team_nanoid`, `start_date`, `end_date`);

CREATE UNIQUE INDEX `employee_payroll_settings_user_nanoid_team_nanoid_effective__key` ON `employee_payroll_settings`(`user_nanoid`, `team_nanoid`, `effective_start`, `effective_end`);

CREATE UNIQUE INDEX `healthcare_payroll_settings_user_nanoid_team_nanoid_type_eff_key` ON `healthcare_payroll_settings`(`user_nanoid`, `team_nanoid`, `type`, `effective_start`, `effective_end`);

CREATE UNIQUE INDEX `retirement_payroll_settings_user_nanoid_team_nanoid_retireme_key` ON `retirement_payroll_settings`(`user_nanoid`, `team_nanoid`, `retirement_type`, `effective_start`, `effective_end`);
