-- CreateTable
CREATE TABLE `users_rsk` (
    `nanoid` VARCHAR(191) NOT NULL,
    `has_passkey` BOOLEAN NOT NULL DEFAULT false,
    `has_iframe_rsk` B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    `has_email_passkey` B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `reset_status` ENUM('started', 'pending', 'completed', 'failed') NOT NULL DEFAULT 'completed',
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `verified_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `last_modified_by` VARCHAR(191) NULL,

    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
