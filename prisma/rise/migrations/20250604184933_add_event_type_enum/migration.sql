/*
  Warnings:

  - You are about to alter the column `event_type` on the `webhook_events` table. The data in that column could be lost. The data in that column will be cast from `VarChar(128)` to `Enum(EnumId(91))`.

*/
-- AlterTable
ALTER TABLE `webhook_endpoints` ADD COLUMN `is_removed` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE `webhook_events` MODIFY `event_type` ENUM('account_duplicated.detected', 'deposit.deposit_received', 'invites.invite_accepted', 'payment.payment_sent', 'pay_schedules.pay_schedule_created', 'payee.riseid_address_updated') NOT NULL;
