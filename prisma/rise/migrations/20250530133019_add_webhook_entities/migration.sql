-- CreateTable
CREATE TABLE `webhook_endpoints` (
    `nanoid` VARCHAR(191) NOT NULL,
    `company_nanoid` VARCHAR(191) NOT NULL,
    `team_nanoid` VARCHAR(191) NULL,
    `created_by` VA<PERSON>HA<PERSON>(191) NOT NULL,
    `last_modified_by` VARCHAR(191) NULL,
    `url` TEXT NOT NULL,
    `secret_encrypted` TEXT NOT NULL,
    `events` JSON NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `webhook_endpoints_company_nanoid_idx`(`company_nanoid`),
    INDEX `webhook_endpoints_team_nanoid_idx`(`team_nanoid`),
    INDEX `webhook_endpoints_is_active_idx`(`is_active`),
    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `webhook_events` (
    `nanoid` VARCHAR(191) NOT NULL,
    `event_type` TEXT NOT NULL,
    `payload` JSON NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `webhook_events_event_type_idx`(`event_type`(255)),
    INDEX `webhook_events_created_at_idx`(`created_at`),
    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `webhook_deliveries` (
    `nanoid` VARCHAR(191) NOT NULL,
    `webhook_nanoid` VARCHAR(191) NOT NULL,
    `event_nanoid` VARCHAR(191) NOT NULL,
    `status` ENUM('queued', 'success', 'failed', 'retrying') NOT NULL DEFAULT 'queued',
    `response_code` INTEGER NULL,
    `error_message` TEXT NULL,
    `response_body` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `webhook_deliveries_webhook_nanoid_idx`(`webhook_nanoid`),
    INDEX `webhook_deliveries_event_nanoid_idx`(`event_nanoid`),
    INDEX `webhook_deliveries_status_idx`(`status`),
    INDEX `webhook_deliveries_created_at_idx`(`created_at`),
    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `webhook_deliveries` ADD CONSTRAINT `webhook_deliveries_webhook_nanoid_fkey` FOREIGN KEY (`webhook_nanoid`) REFERENCES `webhook_endpoints`(`nanoid`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `webhook_deliveries` ADD CONSTRAINT `webhook_deliveries_event_nanoid_fkey` FOREIGN KEY (`event_nanoid`) REFERENCES `webhook_events`(`nanoid`) ON DELETE RESTRICT ON UPDATE CASCADE;
