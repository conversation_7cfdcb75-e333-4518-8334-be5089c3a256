-- AlterTable
ALTER TABLE `entity_withdraw_options` MODIFY `ramp` ENUM('domestic_usd', 'international_exchange', 'international_usd', 'international_usd_default', 'europe', 'gbp', 'ngn', 'arbitrum_wallet', 'avalanche_wallet', 'base_wallet', 'ethereum_wallet', 'optimism_wallet', 'polygon_wallet', 'coinbase', 'coinbase_wallet', 'token_swap') NOT NULL;

-- AlterTable
ALTER TABLE `withdraw_options` MODIFY `ramp` ENUM('domestic_usd', 'international_exchange', 'international_usd', 'international_usd_default', 'europe', 'gbp', 'ngn', 'arbitrum_wallet', 'avalanche_wallet', 'base_wallet', 'ethereum_wallet', 'optimism_wallet', 'polygon_wallet', 'coinbase', 'coinbase_wallet', 'token_swap') NOT NULL;

-- AlterTable
ALTER TABLE `withdraw_options_currency` MODIFY `ramp` ENUM('domestic_usd', 'international_exchange', 'international_usd', 'international_usd_default', 'europe', 'gbp', 'ngn', 'arbitrum_wallet', 'avalanche_wallet', 'base_wallet', 'ethereum_wallet', 'optimism_wallet', 'polygon_wallet', 'coinbase', 'coinbase_wallet', 'token_swap') NOT NULL;
