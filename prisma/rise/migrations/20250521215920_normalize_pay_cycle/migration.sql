UPDATE rise.symmetry_audit
SET pay_cycle = CONCAT(
  LEFT(pay_cycle, 4), '-',                      -- YYYY
  LPAD(SUBSTRING_INDEX(SUBSTRING_INDEX(pay_cycle, '-', 2), '-', -1), 2, '0'), '-', -- M<PERSON> padded
  CONCAT('P', RIGHT(pay_cycle, 1))              -- P1 or P2
)
WHERE pay_cycle NOT REGEXP '^[0-9]{4}-[0-9]{2}-P[12]$';
-- This migration normalizes the pay_cycle format in the symmetry_audit table.