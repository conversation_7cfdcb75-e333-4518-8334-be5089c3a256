/*
  Warnings:

  - The primary key for the `chainflip_swaps` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `deposit_error` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `deposit_failed_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `estimated_completion_time` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `estimated_output_value` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `input_address` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `input_received_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `input_slippage_tolerance_percent` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `input_transaction_id` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `output_transaction_id` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `recommended_slippage_tolerance_percent` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `refund_confirmed_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `refund_error` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `refund_failed_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `refund_sent_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `refund_transaction_id` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_change_address` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_change_locations` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_change_value` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_deposit_address` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_deposit_locations` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `source_deposit_value` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_address` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_error` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_expiry_time` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_failed_at` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_id` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_minimum_rate` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_slippage_tolerance_percent` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to drop the column `swap_status` on the `chainflip_swaps` table. All the data in the column will be lost.
  - You are about to alter the column `input_value` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `output_value` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `fee_btc` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `fee_eth` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `fee_flip` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `fee_usdc` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - You are about to alter the column `refund_value` on the `chainflip_swaps` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Int`.
  - Added the required column `channel_address` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `channel_expires_at` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `channel_id` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `input_addresses` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `input_network_fee` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `input_txid` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `previous_swap_ids` to the `chainflip_swaps` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `chainflip_swaps` DROP PRIMARY KEY,
    DROP COLUMN `deposit_error`,
    DROP COLUMN `deposit_failed_at`,
    DROP COLUMN `estimated_completion_time`,
    DROP COLUMN `estimated_output_value`,
    DROP COLUMN `id`,
    DROP COLUMN `input_address`,
    DROP COLUMN `input_received_at`,
    DROP COLUMN `input_slippage_tolerance_percent`,
    DROP COLUMN `input_transaction_id`,
    DROP COLUMN `output_transaction_id`,
    DROP COLUMN `recommended_slippage_tolerance_percent`,
    DROP COLUMN `refund_confirmed_at`,
    DROP COLUMN `refund_error`,
    DROP COLUMN `refund_failed_at`,
    DROP COLUMN `refund_sent_at`,
    DROP COLUMN `refund_transaction_id`,
    DROP COLUMN `source_change_address`,
    DROP COLUMN `source_change_locations`,
    DROP COLUMN `source_change_value`,
    DROP COLUMN `source_deposit_address`,
    DROP COLUMN `source_deposit_locations`,
    DROP COLUMN `source_deposit_value`,
    DROP COLUMN `swap_address`,
    DROP COLUMN `swap_error`,
    DROP COLUMN `swap_expiry_time`,
    DROP COLUMN `swap_failed_at`,
    DROP COLUMN `swap_id`,
    DROP COLUMN `swap_minimum_rate`,
    DROP COLUMN `swap_slippage_tolerance_percent`,
    DROP COLUMN `swap_status`,
    ADD COLUMN `channel_address` VARCHAR(191) NOT NULL,
    ADD COLUMN `channel_expires_at` DATETIME(3) NOT NULL,
    ADD COLUMN `channel_id` VARCHAR(191) NOT NULL,
    ADD COLUMN `completed_at` DATETIME(3) NULL,
    ADD COLUMN `error` ENUM('DEPOSIT_IGNORED', 'DEPOSIT_REJECTED', 'SWAP_OUTPUT_TOO_SMALL', 'SENDING_FAILED', 'REFUND_OUTPUT_TOO_SMALL') NULL,
    ADD COLUMN `failed_at` DATETIME(3) NULL,
    ADD COLUMN `fee_dot` INTEGER NULL,
    ADD COLUMN `fee_sol` INTEGER NULL,
    ADD COLUMN `fee_usdt` INTEGER NULL,
    ADD COLUMN `input_addresses` JSON NOT NULL,
    ADD COLUMN `input_network_fee` INTEGER NOT NULL,
    ADD COLUMN `input_txid` VARCHAR(191) NOT NULL,
    ADD COLUMN `output_txid` VARCHAR(191) NULL,
    ADD COLUMN `previous_swap_ids` JSON NOT NULL,
    ADD COLUMN `refund_received_at` DATETIME(3) NULL,
    ADD COLUMN `refund_txid` VARCHAR(191) NULL,
    ADD COLUMN `status` ENUM('WAITING', 'RECEIVING', 'SWAPPING', 'SENDING', 'SENT', 'COMPLETED', 'FAILED') NULL,
    MODIFY `input_value` INTEGER NOT NULL,
    MODIFY `output_value` INTEGER NULL,
    MODIFY `fee_btc` INTEGER NULL,
    MODIFY `fee_eth` INTEGER NULL,
    MODIFY `fee_flip` INTEGER NULL,
    MODIFY `fee_usdc` INTEGER NULL,
    MODIFY `refund_value` INTEGER NULL,
    ADD PRIMARY KEY (`channel_id`);
