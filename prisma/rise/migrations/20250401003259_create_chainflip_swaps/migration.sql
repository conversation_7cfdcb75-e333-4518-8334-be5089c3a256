-- CreateTable
CREATE TABLE `chainflip_swaps` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `entity_nanoid` VARCHAR(191) NOT NULL,
    `input_asset` ENUM('BTC') NOT NULL,
    `input_chain` ENUM('Bitcoin') NOT NULL,
    `input_value` BIGINT NOT NULL,
    `input_transaction_id` VARCHAR(191) NOT NULL,
    `input_address` VARCHAR(191) NOT NULL,
    `refund_address` VARCHAR(191) NOT NULL,
    `input_sent_at` DATETIME(3) NOT NULL,
    `input_received_at` DATETIME(3) NULL,
    `input_confirmed_at` DATETIME(3) NULL,
    `input_slippage_tolerance_percent` INTEGER NULL,
    `output_asset` ENUM('USDC') NOT NULL,
    `output_chain` ENUM('Arbitrum') NOT NULL,
    `output_value` BIGINT NULL,
    `output_transaction_id` VARCHAR(191) NULL,
    `output_address` VARCHAR(191) NOT NULL,
    `output_sent_at` DATETIME(3) NULL,
    `quote_type` ENUM('regular', 'boosted') NOT NULL,
    `estimated_output_value` BIGINT NOT NULL,
    `estimated_completion_time` DATETIME(3) NOT NULL,
    `recommended_slippage_tolerance_percent` INTEGER NOT NULL,
    `swap_id` VARCHAR(191) NOT NULL,
    `swap_address` VARCHAR(191) NOT NULL,
    `swap_minimum_rate` BIGINT NOT NULL,
    `swap_slippage_tolerance_percent` INTEGER NOT NULL,
    `swap_status` ENUM('WAITING', 'RECEIVING', 'SWAPPING', 'SENDING', 'SENT', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'WAITING',
    `swap_expiry_time` DATETIME(3) NOT NULL,
    `fee_btc` BIGINT NULL,
    `fee_eth` BIGINT NULL,
    `fee_flip` BIGINT NULL,
    `fee_usdc` BIGINT NULL,
    `refund_value` BIGINT NULL,
    `refund_transaction_id` VARCHAR(191) NULL,
    `refund_sent_at` DATETIME(3) NULL,
    `refund_confirmed_at` DATETIME(3) NULL,
    `swap_error` ENUM('DEPOSIT_IGNORED', 'DEPOSIT_REJECTED', 'SWAP_OUTPUT_TOO_SMALL', 'SENDING_FAILED', 'REFUND_OUTPUT_TOO_SMALL') NULL,
    `swap_failed_at` DATETIME(3) NULL,
    `deposit_error` ENUM('DEPOSIT_IGNORED', 'DEPOSIT_REJECTED', 'SWAP_OUTPUT_TOO_SMALL', 'SENDING_FAILED', 'REFUND_OUTPUT_TOO_SMALL') NULL,
    `deposit_failed_at` DATETIME(3) NULL,
    `refund_error` ENUM('DEPOSIT_IGNORED', 'DEPOSIT_REJECTED', 'SWAP_OUTPUT_TOO_SMALL', 'SENDING_FAILED', 'REFUND_OUTPUT_TOO_SMALL') NULL,
    `refund_failed_at` DATETIME(3) NULL,
    `source_deposit_address` VARCHAR(191) NULL,
    `source_deposit_locations` JSON NULL,
    `source_deposit_value` BIGINT NULL,
    `source_change_address` VARCHAR(191) NULL,
    `source_change_locations` JSON NULL,
    `source_change_value` BIGINT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
