-- CreateTable
CREATE TABLE `payroll_healthcheck_logs` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `run_id` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `team_nanoid` VARCHAR(191) NOT NULL,
    `payroll_program` ENUM('riseworks_inc', 'riseworks_eor_us', 'riseworks_pps_us') NOT NULL,
    `user_nanoid` VARCHAR(191) NULL,
    `target_account` ENUM('employee_payroll', 'team_payroll') NOT NULL,
    `rise_account` VARCHAR(191) NOT NULL,
    `pay_cycle_year` INTEGER NOT NULL,
    `pay_cycle_month` INTEGER NOT NULL,
    `pay_cycle_period` INTEGER NOT NULL,
    `success` BOOLEAN NOT NULL,
    `errors` JSON NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
