-- AlterTable
ALTER TABLE `withdrawals` MODIFY `transaction_nanoid` VARCHAR(191) NULL,
    MODIFY `transaction_index` VARCHAR(191) NULL,
    MODIFY `status` ENUM('draft', 'blockchain_pending', 'blockchain_sent', 'blockchain_failed', 'blockchain_completed', 'blockchain_reversed', 'fiat_sent', 'fiat_failed', 'fiat_reversed', 'fiat_pending', 'completed', 'failed') NOT NULL;

-- CreateTable
CREATE TABLE `activity_history` (
    `nanoid` VARCHAR(191) NOT NULL,
    `entity_nanoid` VARCHAR(191) NOT NULL,
    `entity_type` ENUM('org_admin', 'org_finance_admin', 'org_viewer', 'team_admin', 'team_viewer', 'team_finance_admin', 'contractor', 'team_employee', 'user', 'company', 'team') NOT NULL,
    `workspace_nanoid` VARCHAR(191) NOT NULL,
    `workspace_type` ENUM('org_admin', 'org_finance_admin', 'org_viewer', 'team_admin', 'team_viewer', 'team_finance_admin', 'contractor', 'team_employee', 'user', 'company', 'team') NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
