-- CreateTable
CREATE TABLE `entities_multi_currency_configurations` (
    `nanoid` VARCHAR(191) NOT NULL,
    `currency_id` INTEGER NOT NULL,
    `deposits` BOOLEAN NOT NULL DEFAULT false,
    `invoices` <PERSON>O<PERSON><PERSON>N NOT NULL DEFAULT false,
    `rise_balance` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `is_main_currency` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `entities_multi_currency_configurations_nanoid_currency_id_key`(`nanoid`, `currency_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
