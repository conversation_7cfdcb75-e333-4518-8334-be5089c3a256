/*
  Warnings:

  - Added the required column `count` to the `employee_payroll_settings` table.
  - Existing rows in `employee_payroll_settings` will be populated with a sequential count.
    For each pair of (`user_nanoid`, `team_nanoid`), the `count` will be assigned starting from 0
    for the earliest entry (based on `created_at` ASC, then `id` ASC as a tie-breaker), incrementing for subsequent entries.
  - A unique constraint covering the columns `[user_nanoid,team_nanoid,count]` on the table `employee_payroll_settings` will be added.
    This ensures that after the update, the combination of these three fields is unique for every row.

*/
-- Step 1: Add the count column as nullable temporarily
ALTER TABLE `employee_payroll_settings` ADD COLUMN `count` INTEGER;

-- Step 2: Update the count column for existing rows
-- This statement populates the 'count' for existing records based on their creation order within each (user_nanoid, team_nanoid) group.
-- The oldest record gets count = 0, the next oldest gets count = 1, and so on.
-- 'id' is used as a tie-breaker in ordering if 'created_at' is identical for multiple records within the same group.
WITH `RankedSettings` AS (
    SELECT
        `id`,
        `user_nanoid`,
        `team_nanoid`,
        `created_at`,
        ROW_NUMBER() OVER (PARTITION BY `user_nanoid`, `team_nanoid` ORDER BY `created_at` ASC, `id` ASC) - 1 AS `new_count`
    FROM
        `employee_payroll_settings`
)
UPDATE `employee_payroll_settings` AS `eps`
INNER JOIN `RankedSettings` AS `rs` ON `eps`.`id` = `rs`.`id`
SET `eps`.`count` = `rs`.`new_count`;

-- Step 3: Modify the count column to be NOT NULL
ALTER TABLE `employee_payroll_settings` MODIFY COLUMN `count` INTEGER NOT NULL;

-- Step 4: Create the unique index
CREATE UNIQUE INDEX `employee_payroll_settings_user_nanoid_team_nanoid_count_key` ON `employee_payroll_settings`(`user_nanoid`, `team_nanoid`, `count`);
