-- CreateTable
CREATE TABLE `supported_currencies` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(128) NOT NULL,
    `symbol` VARCHAR(16) NOT NULL,
    `type` ENUM('FIAT', 'CRYPTO') NOT NULL,
    `rise_erc20_token` VARCHAR(255) NOT NULL,
    `active` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `supported_currencies_rise_erc20_token_key`(`rise_erc20_token`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
