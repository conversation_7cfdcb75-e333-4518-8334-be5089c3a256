-- remove comments from activity_history nanoid column

SET @preparedStatement = (SELECT IF(EXISTS
  (
    SELECT table_name 
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_schema = 'rise'
      AND table_name = 'activity_history'
      AND column_name = 'nanoid'
  ),
  CONCAT("ALTER TABLE activity_history MODIFY COLUMN nanoid VARCHAR(191) NOT NULL COMMENT ''"),
  "SELECT 1"
));
PREPARE alterIfExists FROM @preparedStatement;
EXECUTE alterIfExists;
DEALLOCATE PREPARE alterIfExists;