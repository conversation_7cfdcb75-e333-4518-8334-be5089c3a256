DROP TRIGGER IF EXISTS rise_audit_blockchain_addresses_update;
DROP TRIGGER IF EXISTS rise_audit_blockchain_addresses_insert;
DROP TRIGGER IF EXISTS rise_audit_agreements_update;
DROP TRIGGER IF EXISTS rise_audit_agreements_insert;
DROP TRIGGER IF EXISTS rise_audit_companies_data_update;
DROP TRIGGER IF EXISTS rise_audit_companies_data_insert;
DROP TRIGGER IF EXISTS rise_audit_invites_update;
DROP TRIGGER IF EXISTS rise_audit_invites_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_agreements_update;
DROP TRIGGER IF EXISTS rise_audit_rise_agreements_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_entities_update;
DROP TRIGGER IF EXISTS rise_audit_rise_entities_insert;
DROP TRIGGER IF EXISTS rise_audit_teams_data_update;
DROP TRIGGER IF EXISTS rise_audit_teams_data_insert;
DROP TRIGGER IF EXISTS rise_audit_time_entries_insert;
DROP TRIGGER IF EXISTS rise_audit_time_entries_update;
DROP TRIGGER IF EXISTS rise_audit_users_data_update;
DROP TRIGGER IF EXISTS rise_audit_users_data_insert;
DROP TRIGGER IF EXISTS rise_audit_users_onboarding_update;
DROP TRIGGER IF EXISTS rise_audit_users_onboarding_insert;
DROP TRIGGER IF EXISTS rise_audit_users_certifications_update;
DROP TRIGGER IF EXISTS rise_audit_users_certifications_insert;
