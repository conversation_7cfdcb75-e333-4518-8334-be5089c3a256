datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model rise_entities {
  riseid           String /// @kysely(CompanyRiseid | UserRiseid | TeamRiseid) @zod(companyRiseid.or(userRiseid).or(teamRiseid))
  type             rise_entities_type
  parent_riseid    String /// @kysely(CompanyRiseid | UserRiseid | TeamRiseid) @zod(companyRiseid.or(userRiseid).or(teamRiseid))
  nanoid           String             @unique /// @kysely(AllNanoids) @zod(allNanoids)
  avatar           String             @default("")
  last_modified_by String? /// @kysely(UserNanoid | null) @zod(userNanoid.nullish())
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())

  @@id([riseid, parent_riseid])
}

model entity_permissions {
  nanoid           String   @id /// @kysely(UserNanoid)
  pay_app          Boolean  @default(false)
  document_app     <PERSON>olean  @default(false)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model agreements {
  id               Int      @id @default(autoincrement())
  version          String   @db.VarChar(100)
  agreements_url   String   @db.VarChar(1000)
  active           Boolean  @default(true)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model companies_data {
  nanoid                String                       @id /// @kysely(CompanyNanoid)
  rise_account          String                       @default("") /// @kysely(CompanyRiseAccount) @zod(companyRiseAccount)
  name                  String                       @db.VarChar(255)
  country               String                       @default("") @db.VarChar(45)
  state                 String                       @default("") @db.VarChar(45)
  incorporation_country String                       @default("") @db.VarChar(2)
  incorporation_type    companies_incorporation_type @default(sole_proprietorship)
  is_dao                Boolean                      @default(false)
  website               String                       @default("") @db.VarChar(1000)
  size                  companies_size               @default(medium)
  phone                 String                       @db.VarChar(45)
  doing_business_as     String                       @default("")
  payroll_enabled       Boolean                      @default(false)
  created_at            DateTime                     @default(now())
  updated_at            DateTime                     @default(now())
  last_modified_by      String? /// @kysely(UserNanoid | null)
  v1_id                 String                       @default("") @db.VarChar(100)
}

model invites {
  email            String         @db.VarChar(320)
  role             invites_role
  status           invites_status
  nanoid           String         @id /// @kysely(InviteNanoid)
  anonymous        Boolean        @default(false)
  prefill          Json
  invited_by       String /// @kysely(UserNanoid)
  invited_to       String /// @kysely(CompanyNanoid | TeamNanoid)
  riseid           String?
  expires_at       DateTime
  created_at       DateTime       @default(now())
  updated_at       DateTime       @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model invites_riseid {
  riseid     String
  email      String   @id @db.VarChar(320)
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

model blockchain_addresses {
  nanoid           String? /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid | null) @zod(userNanoid.or(companyNanoid).or(teamNanoid).nullable())
  address          String
  owner_address    String
  parent_account   String?
  salt             String
  network          String
  type             riseid_types
  tx_hash          String         @default("")
  purpose          riseid_purpose @default(generic)
  created_at       DateTime       @default(now())
  updated_at       DateTime       @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)

  @@id([address, network])
  @@index([type, nanoid])
  @@index([address])
}

model memo_codes {
  nanoid String @id /// @kysely(CompanyNanoid | TeamNanoid)
  code   String
}

model smart_contracts {
  id               Int      @id @default(autoincrement())
  active           Boolean  @default(false)
  name             String   @db.VarChar(255)
  alias            String?  @db.VarChar(255)
  version          String   @db.VarChar(10)
  address          String   @db.Char(42)
  bytecode         String?  @db.LongText
  network          Int
  abi              Json
  environment      String   @default("development") @db.VarChar(45)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)

  @@unique([name, version, network, environment])
  @@index([name])
}

model teams_data {
  nanoid           String   @id /// @kysely(TeamNanoid)
  name             String   @default("") @db.VarChar(45)
  rise_account     String   @default("") /// @kysely(TeamRiseAccount) @zod(teamRiseAccount)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model users_data {
  nanoid            String           @id /// @kysely(UserNanoid)
  rise_account      String           @default("") /// @kysely(UserRiseAccount) @zod(userRiseAccount)
  email             String           @unique(map: "email_UNIQUE") @db.VarChar(255)
  first_name        String           @db.VarChar(45)
  middle_name       String           @default("") @db.VarChar(45)
  last_name         String           @db.VarChar(45)
  country           String           @default("") @db.VarChar(45)
  state             String           @default("") @db.VarChar(45)
  recovery_email    String           @default("") @db.VarChar(255)
  occupation        String           @default("") @db.VarChar(100)
  phone             String           @default("") @db.VarChar(45)
  dob               DateTime?        @db.Date
  alias             String           @default("") @db.VarChar(100)
  linkedin          String           @default("") @db.VarChar(100)
  discord           String           @default("") @db.VarChar(100)
  x                 String           @default("") @db.VarChar(100)
  website           String           @default("") @db.VarChar(100)
  created_at        DateTime         @default(now())
  updated_at        DateTime         @default(now())
  last_modified_by  String? /// @kysely(UserNanoid | null)
  account_status    account_statuses @default(created)
  advanced_security Boolean          @default(false)
  v1_id             String           @default("") @db.VarChar(100)
}

model users_certifications {
  nanoid           String   @id /// @kysely(CertificationNanoid)
  user_nanoid      String /// @kysely(UserNanoid)
  title            String   @default("") @db.VarChar(100)
  website          String   @default("") @db.VarChar(1000)
  year             Int
  file             String   @default("") @db.VarChar(1000)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model enodes {
  url      String     @id
  active   Boolean    @default(true)
  chain_id Int
  network  String
  type     enode_type @default(normal)

  @@index([chain_id])
}

model rise_agreements {
  agreement_id     Int
  nanoid           String /// @kysely(UserNanoid)
  ip               String   @default("") @db.VarChar(60)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)

  @@id([agreement_id, nanoid])
}

model users_onboarding {
  nanoid                   String                               @id /// @kysely(UserNanoid)
  step                     users_onboarding_step                @default(initial)
  role                     users_onboarding_role                @default(viewer)
  moderation_status        users_onboarding_moderation_status   @default(pending)
  moderation_internal_note String                               @default("")
  moderation_public_note   String                               @default("")
  accepted_invites         Boolean                              @default(false)
  register_business        Boolean                              @default(false)
  ext_inquiry_status       users_onboarding_ext_inquiry_status?
  ext_inquiry_id           String?
  created_at               DateTime                             @default(now())
  updated_at               DateTime                             @default(now())
  last_modified_by         String? /// @kysely(UserNanoid | null)
}

model superadmin_settings {
  id                             Int      @id @default(autoincrement())
  company_nanoid                 String   @default("") /// @kysely(CompanyNanoid) @zod(companyNanoid)
  transaction_processing_enabled Boolean
  created_at                     DateTime @default(now())
  updated_at                     DateTime @default(now())
}

model company_role_settings {
  company_nanoid                    String /// @kysely(CompanyNanoid)
  user_nanoid                       String /// @kysely(UserNanoid)
  payment_threshold_per_day         Decimal  @default(0) @db.Decimal(10, 2)
  payment_threshold_per_transaction Decimal  @default(0) @db.Decimal(10, 2)
  created_at                        DateTime @default(now())
  updated_at                        DateTime @default(now())
  last_modified_by                  String? /// @kysely(UserNanoid | null)

  @@id([company_nanoid, user_nanoid])
}

enum team_role_status {
  active
  inactive
}

model team_role_settings {
  team_nanoid      String /// @kysely(TeamNanoid)
  user_nanoid      String /// @kysely(UserNanoid)
  payable_company  String? /// @kysely(CompanyNanoid | null)
  rise_account     String? /// @kysely(UserRiseAccount | null) @zod(userRiseAccount.nullish())
  payment_handler  String?
  anonymous        Boolean          @default(false)
  created_at       DateTime         @default(now())
  updated_at       DateTime         @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
  status           team_role_status @default(active)

  @@id([team_nanoid, user_nanoid])
}

model company_settings {
  nanoid                   String   @id /// @kysely(CompanyNanoid)
  default_signer           String /// @kysely(UserNanoid | null) @zod(userNanoid.nullish())
  payment_delay_in_minutes Int      @default(0)
  invoicing_enabled        Boolean  @default(true)
  enabled_document_types   Json /// @kysely(JSONColumnType<CompanyDocumentTypes[]>) @zod(companyDocumentTypes)
  anonymous_users          Boolean  @default(true)
  custom_psa_nanoid        String? /// @kysely(TemplateNanoid | null)
  created_at               DateTime @default(now())
  updated_at               DateTime @default(now())
  last_modified_by         String? /// @kysely(UserNanoid | null)
  enabled_payroll_programs Json? /// @kysely(JSONColumnType<string[]>) @zod(z.string().array())
  require_signed_psa       Boolean  @default(false)
  require_sow              Boolean  @default(false)
}

model team_settings {
  nanoid                   String   @id /// @kysely(TeamNanoid)
  payment_delay_in_minutes Int      @default(0)
  created_at               DateTime @default(now())
  updated_at               DateTime @default(now())
  last_modified_by         String? /// @kysely(UserNanoid | null)
}

model foo {
  id Int @id
}

model withdraw_options {
  id          Int                         @id @default(autoincrement())
  ramp        withdraw_option_ramp
  type        withdraw_option_type
  country     String                      @db.Char(2)
  entity_type withdraw_option_entity_type
  enabled     Boolean                     @default(true)
  created_at  DateTime                    @default(now())
  updated_at  DateTime                    @default(now())

  @@unique([ramp, country, entity_type])
}

model countries {
  code           String   @id @db.Char(2)
  name           String   @db.VarChar(100)
  full_name      String   @db.VarChar(100)
  iso3           String   @db.Char(3)
  number         String   @db.VarChar(10)
  continent_code String   @db.Char(2)
  supported      Boolean  @default(true)
  created_at     DateTime @default(now())
  updated_at     DateTime @default(now())
}

model continents {
  code       String   @id @db.Char(2)
  name       String   @db.VarChar(100)
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

model withdrawals {
  nanoid                         String          @id /// @kysely(WithdrawNanoid) @zod(withdrawNanoid)
  external_reference             String          @default("") @db.VarChar(255)
  account_nanoid                 String /// @kysely(WithdrawAccountNanoid) @zod(withdrawAccountNanoid)
  transaction_nanoid             String? /// @kysely(TransactionNanoid | null) @zod(transactionNanoid.nullable())
  transaction_index              String?
  invoice_number                 String
  source_currency                String
  destination_currency           String
  source_amount_cents            Int
  destination_amount_cents       Int
  source_fees_cents              Int
  source_fees_sponsor            String? /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid | null) @zod(userNanoid.or(companyNanoid).or(teamNanoid).nullish())
  source_fees_cents_sponsored    Int             @default(0)
  sponsor_fee_payment_onchain_id String?
  conversion_rate                Float
  status                         withdraw_status
  estimated_delivery_date        DateTime?
  error_message                  String?         @db.VarChar(5000)
  invoice                        String?         @db.VarChar(500)
  created_at                     DateTime        @default(now())
  updated_at                     DateTime        @default(now())
  last_modified_by               String? /// @kysely(UserNanoid | null)

  @@index(account_nanoid)
}

enum cctp_withdraw_status {
  pending
  complete
  failed
}

model cctp_withdrawals {
  nanoid      String               @id /// @ts(WithdrawNanoid) @zod(withdrawNanoid)
  tx_nanoid   String? /// @ts(TransactionNanoid | null) @zod(transactionNanoid.nullable())
  tx_hash     String?
  domain      String
  status      cctp_withdraw_status @default(pending)
  created_at  DateTime             @default(now())
  updated_at  DateTime             @default(now())
  attestation String?              @db.Text
}

model deposits {
  nanoid                    String @id /// @kysely(DepositNanoid) @zod(depositNanoid)
  entity_nanoid             String /// @kysely(CompanyNanoid | TeamNanoid) @zod(companyNanoid.or(teamNanoid))
  entity_deposit_account_id Int

  provider_deposit_id String            @unique
  provider_entity_id  String?
  provider_wallet_id  String?
  provider_name       deposit_providers @default(manual)

  source_currency          String
  source_amount_cents      Int
  destination_currency     String
  destination_amount_cents Int

  transaction_date          DateTime
  purpose_of_payment        String?
  reference                 String?
  trace                     String?
  funding_blockchain_txn    String?
  blockchain_reference      String
  deposit_bank_account_seen Int?
  status                    deposit_status
  created_at                DateTime       @default(now())
  updated_at                DateTime       @default(now())
}

model provider_withdrawals {
  nanoid               String            @id /// @kysely(WithdrawNanoid)
  name                 withdraw_provider
  id                   String?
  reference            String?
  document_uploaded_at DateTime?
  status               String?
  failure_reason       String?           @db.VarChar(5000)
  failure_code         String?
  created_at           DateTime          @default(now())
  updated_at           DateTime          @default(now())
}

model provider_withdrawals_remittance {
  nanoid            String   @id
  id                String
  reference         String
  status            String?
  failure_reason    String?  @db.VarChar(5000)
  failure_code      String?
  source_created_at DateTime
  created_at        DateTime @default(now())
}

model erc20_tokens {
  address          String   @db.VarChar(100)
  network          String
  symbol           String   @db.VarChar(100)
  logo             String   @db.VarChar(5000)
  withdraw_enabled Boolean  @default(true)
  deposit_enabled  Boolean  @default(true)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  chain_id         Int?
  name             String?  @db.VarChar(100)
  decimals         Int?

  @@id([symbol, network])
}

model documents {
  nanoid            String              @id /// @kysely(DocumentNanoid)
  template_nanoid   String? /// @kysely(TemplateNanoid | null)
  is_pre_signed     Boolean?            @default(false)
  created_at        DateTime            @default(now())
  created_by        String /// @kysely(UserNanoid)
  updated_at        DateTime?           @updatedAt
  updated_by        String? /// @kysely(UserNanoid | null)
  deleted_by        String? /// @kysely(UserNanoid | null)
  deleted_at        DateTime?
  start_date        DateTime?
  end_date          DateTime?
  content           Json
  name              String
  document_versions document_versions[] @relation("document_versions")
}

model document_versions {
  nanoid          String           @id /// @kysely(DocumentVersionNanoid)
  document_nanoid String /// @kysely(DocumentNanoid)
  document        documents        @relation("document_versions", fields: [document_nanoid], references: [nanoid])
  modifications   Json
  version_number  Int
  status          documents_status @default(draft)
  status_reason   String?
  created_at      DateTime         @default(now())
  created_by      String /// @kysely(UserNanoid)
  updated_at      DateTime?        @updatedAt
  updated_by      String? /// @kysely(UserNanoid | null)
  deleted_by      String? /// @kysely(UserNanoid | null)
  deleted_at      DateTime?
  signatures      signatures[]     @relation("signatures")
  base64String    String?          @db.MediumText
  signedHash      String?          @db.MediumText
  content         Bytes?           @db.MediumBlob
  hashed_document String?

  @@index([document_nanoid])
}

model document_versions_user_map {
  id                 Int                @id @default(autoincrement())
  doc_version_nanoid String /// @kysely(DocumentVersionNanoid)
  user_nanoid        String /// @kysely(UserNanoid)
  role               document_user_role @default(viewer)

  @@index([doc_version_nanoid, user_nanoid])
  @@index([doc_version_nanoid])
  @@index([user_nanoid])
}

enum signer_type {
  rise
  service_requester
  service_provider
}

model signatures {
  nanoid             String            @id /// @kysely(DocumentSignatureNanoid)
  doc_version_nanoid String /// @kysely(DocumentVersionNanoid)
  document_versions  document_versions @relation("signatures", fields: [doc_version_nanoid], references: [nanoid])
  user_nanoid        String /// @kysely(UserNanoid)
  created_at         DateTime          @default(now())
  created_by         String /// @kysely(UserNanoid)
  updated_at         DateTime?         @updatedAt
  updated_by         String? /// @kysely(UserNanoid | null)
  status             signatures_status @default(pending)
  deleted_by         String? /// @kysely(UserNanoid | null)
  deleted_at         DateTime?
  modifications      Json?
  base64String       String?           @db.MediumText
  signedHash         String?           @db.MediumText
  ip                 String?
  signAddress        String?
  is_company         Boolean?          @default(false)
  company_name       String?
  address            String
  device             String?
  signer_type        signer_type?
  hashed_document    String?

  @@index([doc_version_nanoid, user_nanoid])
  @@index([doc_version_nanoid])
  @@index([user_nanoid])
}

model templates_signatures {
  nanoid          String            @id /// @kysely(TemplateSignatureNanoid)
  template_nanoid String /// @kysely(TemplateNanoid)
  templates       templates         @relation("template_signatures", fields: [template_nanoid], references: [nanoid])
  user_nanoid     String /// @kysely(UserNanoid)
  created_at      DateTime          @default(now())
  created_by      String /// @kysely(UserNanoid)
  updated_at      DateTime?         @updatedAt
  updated_by      String? /// @kysely(UserNanoid | null)
  status          signatures_status @default(pending)
  deleted_by      String? /// @kysely(UserNanoid | null)
  deleted_at      DateTime?
  base64String    String?           @db.MediumText
  signedHash      String?           @db.MediumText
  ip              String?
  signAddress     String?
  is_company      Boolean?          @default(false)
  company_name    String?
  address         String
  device          String?
  signer_type     signer_type?

  @@index([template_nanoid, user_nanoid])
  @@index([template_nanoid])
}

model templates {
  nanoid                 String                 @id /// @kysely(TemplateNanoid)
  content                Json
  name                   String
  isSystemTemplate       Boolean                @default(false)
  parent_template_nanoid String? /// @kysely(TemplateNanoid | null)
  color                  String?
  description            String?
  type                   template_type? /// @kysely('psa' | 'msa_c' | 'msa_sp' | 'sow' | 'ea_us' | 'ea_rise' | null)
  created_at             DateTime               @default(now())
  created_by             String? /// @kysely(UserNanoid | null)
  updated_at             DateTime?              @updatedAt
  updated_by             String? /// @kysely(UserNanoid | null)
  deleted_by             String? /// @kysely(UserNanoid | null)
  deleted_at             DateTime?
  template_signatures    templates_signatures[] @relation("template_signatures")
  status                 template_status        @default(draft)
  base64String           String?                @db.MediumText
  signedHash             String?                @db.MediumText
  entity_nanoid          String? /// @kysely(CompanyNanoid | TeamNanoid | null) @zod(companyNanoid.or(teamNanoid).nullish())
}

model templates_users_map {
  id              Int     @id @default(autoincrement())
  template_nanoid String /// @kysely(TemplateNanoid)
  user_nanoid     String /// @kysely(UserNanoid)
  isOwner         Boolean @default(false)

  @@index([template_nanoid, user_nanoid])
  @@index([template_nanoid])
  @@index([user_nanoid])
}

model document_activities {
  id              Int                     @id @default(autoincrement())
  document_nanoid String /// @kysely(DocumentNanoid)
  user_nanoid     String /// @kysely(UserNanoid)
  version_nanoid  String /// @kysely(DocumentVersionNanoid)
  event           document_activity_event
  ip              String?
  sign_address    String?
  created_at      DateTime                @default(now())
  created_by      String /// @kysely(UserNanoid)

  @@index([user_nanoid, document_nanoid])
  @@index([user_nanoid])
  @@index([document_nanoid])
}

model time_entries {
  id               Int      @id @default(autoincrement())
  nanoid           String /// @kysely(UserNanoid)
  company_nanoid   String /// @kysely(CompanyNanoid)
  pay_intent_id    Int
  notes            String   @default("") @db.VarChar(1000)
  date             DateTime @db.Date
  hours            Decimal  @db.Decimal(10, 2)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)

  @@unique([nanoid, company_nanoid, date])
}

model pay_intents {
  id               Int                @id @default(autoincrement())
  nanoid           String /// @kysely(UserNanoid)
  company_nanoid   String /// @kysely(CompanyNanoid)
  hourly_rate      Decimal            @db.Decimal(10, 2)
  status           pay_intents_status @default(draft)
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model ip_whitelist {
  nanoid     String /// @kysely(UserNanoid)
  ip         String   @db.VarChar(100)
  created_at DateTime @default(now())

  @@id([nanoid, ip])
}

model payment_groups {
  nanoid     String   @id /// @kysely(PaymentGroupNanoid) @zod(paymentGroupNanoid)
  onchain_id String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

model payment {
  nanoid               String   @id /// @kysely(PaymentNanoid) @zod(paymentNanoid)
  onchain_id           String   @unique
  payment_group_nanoid String /// @kysely(PaymentGroupNanoid) @zod(paymentGroupNanoid)
  group_onchain_id     String
  creation_transaction String? /// @kysely(TransactionNanoid | null) @zod(transactionNanoid.nullable())
  process_transaction  String? /// @kysely(TransactionNanoid | null) @zod(transactionNanoid.nullable())
  invoice_nanoid       String /// @kysely(InvoiceNanoid) @zod(invoiceNanoid)
  created_at           DateTime @default(now())
  updated_at           DateTime @default(now())
}

model payment_invoice_data {
  nanoid String @id /// @kysely(InvoiceNanoid) @zod(invoiceNanoid)

  recipient_nanoid String /// @kysely(UserNanoid | CompanyNanoid) @zod(userNanoid.or(companyNanoid))
  payer_nanoid     String /// @kysely(TeamNanoid) @zod(teamNanoid)

  group_nanoid     String /// @kysely(PaymentGroupNanoid) @zod(paymentGroupNanoid)
  group_onchain_id String

  type payment_invoice_type

  // time entry
  documents_ids         Json /// @kysely(ColumnType<string[], string, string>) @zod(z.array(z.string()))
  // TODO: this column name should be migrated to hourly_rate_cents to comply with multi-currency configurations
  hourly_rate_usd_cents Int
  time_entries          Json /// @kysely(ColumnType<{ date: string, hours: number, minutes: number, notes: string }[], string, string>) @zod(z.array(z.object({ date: z.string(), hours: z.number().int(), minutes: z.number().int(), notes: z.string() })))

  recurrence payment_recurrence?

  role_description    String? @db.VarChar(500)
  invoice_description String? @db.VarChar(5000)
  rise_sow            Boolean
  external_sow_url    String? @db.VarChar(1000)
  sow_document_nanoid String? /// @kysely(DocumentNanoid | null)

  flat_type payment_invoice_flat_type?

  invoice_number String? @default("") @db.VarChar(100)

  amount_cents Int    @default(0)
  currency     String

  invoice_status payment_invoice_status @default(draft)

  payroll_program payroll_programs?

  pay_intent_time Int @default(0)

  created_at DateTime @default(now())
  updated_at DateTime @default(now())

  @@index([payer_nanoid])
}

model payment_invoice_counter {
  nanoid String @id /// @kysely(PaymentNanoid) @zod(paymentNanoid)
  count  Int
}

model pay_schedules {
  nanoid           String             @id /// @kysely(PayScheduleNanoid)
  team_nanoid      String /// @kysely(TeamNanoid)
  user_nanoid      String /// @kysely(UserNanoid)
  type             pay_schedule_types
  amount           Decimal            @db.Decimal(10, 2)
  payments_amount  Int
  start_date       DateTime
  end_date         DateTime
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
}

model countries_risk {
  code             String      @id
  risk_level       risk_levels
  rise_restriction Boolean     @default(false)
  factor           String      @db.VarChar(1000)
  sub_factor       String      @db.VarChar(1000)
  created_at       DateTime    @default(now())
  updated_at       DateTime    @default(now())
}

model withdraw_options_currency {
  id                               Int                  @id @default(autoincrement())
  ramp                             withdraw_option_ramp
  country                          String               @db.Char(2)
  currency                         String               @db.Char(3)
  private_base_fee_hundreths       Int
  private_margin_one_fee_hundreths Int
  rate_percent_hundreths           Int
  rate_flat_hundreths              Int
  limit_min_dest_currency          Int
  limit_max_dest_currency          Int
  wait_time                        String               @db.VarChar(400)
  wait_minutes                     Int?
  cut_off_hour_gmt                 Int?
  exclude_weekends                 Boolean              @default(false)
  exclude_holidays                 Boolean              @default(false)
  exclude_fri_sat                  Boolean              @default(false)
  enabled                          Boolean              @default(true)
  created_at                       DateTime             @default(now())
  updated_at                       DateTime             @default(now())

  @@unique([ramp, country, currency])
}

model entity_withdraw_options {
  nanoid     String               @id
  ramp       withdraw_option_ramp
  enabled    Boolean              @default(true)
  comment    String?              @default("") @db.VarChar(1000)
  created_at DateTime             @default(now())
  updated_at DateTime             @default(now())
}

model entity_deposit_accounts {
  id                      Int               @id @default(autoincrement())
  entity_nanoid           String /// @kysely(CompanyNanoid | TeamNanoid) @zod(companyNanoid.or(teamNanoid))
  account_id              String            @unique
  account_name            String
  payment_handler_address String
  provider                deposit_providers @default(manual)
  created_at              DateTime          @default(now())
  updated_at              DateTime          @default(now())
}

model deposit_payment_handlers {
  payment_handler String
  entity_nanoid   String /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid) @zod(userNanoid.or(companyNanoid).or(teamNanoid))
  token_address   String
  network         String
  created_at      DateTime @default(now())
  updated_at      DateTime @default(now())

  @@id([payment_handler, token_address, network])
}

model admin_blockchain_transactions {
  id                Int                                 @id @default(autoincrement())
  to                String
  calldata          String                              @db.VarChar(5000)
  email             String
  contract_name     String
  contract_function String
  contract_params   Json
  status            admin_blockchain_transaction_status @default(pending)
  tx_hash           String?
  created_at        DateTime                            @default(now())
  updated_at        DateTime                            @default(now())
  last_modified_by  String? /// @kysely(UserNanoid | null)
}

model routefusion_entities {
  nanoid      String                   @id /// @kysely(UserNanoid | CompanyNanoid)
  entity_type routefusion_entity_types
  entity_id   String
}

model withdraw_account_conflict {
  nanoid                  String                           @id
  conflict_account_nanoid String
  status                  withdraw_account_conflict_status
  date                    DateTime
  created_at              DateTime                         @default(now())
  updated_at              DateTime                         @default(now())
}

model blockchain_transactions {
  id            String                                 @id /// @kysely(TransactionNanoid)
  friendly_type blockchain_transactions_friendly_types @default(unknown)
  network       String
  status        blockchain_transactions_status         @default(waiting)
  metadata      Json? /// @kysely(JSONColumnType<BlockchainTransactionsMetadata>) @zod(blockchainTransactionsMetadata)
  nanoid        String?
  hash          String?
  error         String                                 @default("")
  created_at    DateTime                               @default(now())
  updated_at    DateTime                               @default(now())
}

enum activity_history_type {
  withdraw
  deposit
  payment
  invoice
}

model activity_history {
  nanoid           String
  type             activity_history_type @default(withdraw)
  entity_nanoid    String /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid) @zod(userNanoid.or(companyNanoid).or(teamNanoid))
  entity_type      rise_entities_type
  workspace_nanoid String /// @kysely(UserNanoid | TeamNanoid | CompanyNanoid | TeamRoleNanoid) @zod(userNanoid.or(teamNanoid).or(companyNanoid).or(teamRoleNanoid))
  workspace_type   rise_entities_type
  created_at       DateTime              @default(now())
  updated_at       DateTime              @default(now())

  @@id([nanoid, entity_nanoid])
}

model account_ledger {
  nanoid   String /// @kysely(TransactionNanoid) @zod(transactionNanoid)
  hash     String
  tx_index Int

  payment_onchain_id String?
  withdraw_id        String? /// @kysely(WithdrawNanoid | null) @zod(withdrawNanoid.nullish())
  deposit_id         String? /// @kysely(DepositNanoid | null) @zod(depositNanoid.nullish())

  account_address String
  from_address    String
  to_address      String
  amount          String
  amount_units    Int
  currency        String
  balance_before  String
  balance_after   String
  token_address   String
  type            ledger_types
  tx_timestamp    Int
  network         String
  created_at      DateTime     @default(now())
  updated_at      DateTime     @default(now())

  @@id([nanoid, tx_index, account_address])
  @@index([account_address, tx_timestamp(sort: Desc), tx_index(sort: Asc)], name: "idx_account_address_timestamp_txindex")
}

model ethereum_transactions {
  nanoid          String                         @id /// @kysely(TransactionNanoid)
  hash            String                         @db.Char(66)
  chain_id        Int
  block           String                         @db.VarChar(191)
  gas_price       String                         @db.VarChar(191)
  transaction_fee String                         @db.VarChar(191)
  data            String                         @db.Text
  from            String                         @db.VarChar(191)
  to              String                         @db.VarChar(191)
  value           String                         @db.VarChar(191)
  contract_method String                         @db.VarChar(191)
  contract_params Json
  is_forwarder    Boolean
  status          blockchain_transactions_status @default(waiting)
  tx_timestamp    Int
  created_at      DateTime                       @default(now())
  updated_at      DateTime                       @default(now())

  @@unique([hash])
}

model ethereum_transaction_forwarder_calls {
  nanoid            String                         @id /// @kysely(TransactionNanoid)
  hash              String                         @db.Char(66)
  chain_id          Int
  forwarder_index   Int
  forwarder_address String                         @db.VarChar(191)
  contract_name     String                         @db.VarChar(191)
  signer            String                         @db.VarChar(191)
  from              String                         @db.VarChar(191)
  to                String                         @db.VarChar(191)
  data              String                         @db.VarChar(10000)
  input_method      String                         @db.VarChar(191)
  input_params      Json
  status            blockchain_transactions_status @default(waiting)
  tx_timestamp      Int
  created_at        DateTime                       @default(now())
  updated_at        DateTime                       @default(now())

  @@unique([hash, forwarder_index])
}

model ethereum_transaction_events {
  nanoid           String /// @kysely(TransactionNanoid)
  hash             String   @db.Char(66)
  chain_id         Int
  log_index        Int
  block_number     Int
  contract_address String   @db.VarChar(191)
  contract_name    String   @db.VarChar(191)
  event_name       String   @db.VarChar(191)
  param_1_name     String?  @db.VarChar(191)
  param_1_value    String?  @db.VarChar(1000)
  param_2_name     String?  @db.VarChar(191)
  param_2_value    String?  @db.VarChar(1000)
  param_3_name     String?  @db.VarChar(191)
  param_3_value    String?  @db.VarChar(1000)
  data_name        String?  @db.VarChar(191)
  data_value       String?  @db.VarChar(1000)
  params           Json
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())

  @@id([nanoid, log_index])
}

model blockchain_transaction_seen {
  hash       String   @id
  network    String
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

model action_items {
  nanoid          String             @id /// @kysely(ActionItemNanoid)
  entity_nanoid   String /// @kysely(UserNanoid | TeamNanoid | CompanyNanoid)
  workspace       String? /// @kysely(TeamNanoid | CompanyNanoid | null) @zod(companyNanoid.or(teamNanoid).nullish())
  type            action_item_types
  title           String
  description     String
  status          action_item_status @default(pending)
  icon            action_item_icons
  idempotency_key String             @db.VarChar(191)
  created_at      DateTime           @default(now())
  updated_at      DateTime           @default(now())

  @@unique([entity_nanoid, idempotency_key])
}

model withdraw_downtimes {
  id           Int      @id @default(autoincrement())
  country_code String   @db.VarChar(2)
  from         DateTime
  to           DateTime
  description  String   @db.VarChar(1000)
  created_at   DateTime @default(now())
  updated_at   DateTime @default(now())
}

model compliance_provider_data {
  nanoid        String   @id /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid) @zod(userNanoid.or(companyNanoid).or(teamNanoid))
  compliance_id String
  external_id   String?  @db.VarChar(100)
  created_at    DateTime @default(now())
  updated_at    DateTime @default(now())
}

model v1_riseid_payment_handler {
  riseid          String   @id
  payment_handler String
  created_at      DateTime @default(now())
  updated_at      DateTime @default(now())
}

model user_notifications {
  nanoid        String                   @id @db.VarChar(200) /// @kysely(NotificationNanoid) @zod(notificationNanoid)
  user_nanoid   String                   @db.VarChar(200) /// @kysely(UserNanoid) @zod(userNanoid.nullish())
  type          user_notification_type
  entity_nanoid String                   @db.VarChar(200) /// @kysely(TransactionNanoid) @zod(transactionNanoid.nullish())
  text          String                   @db.VarChar(500)
  state         user_notification_status
  data          Json /// @kysely(JSONColumnType<NotificationData>) @zod(notificationData)
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @default(now())
}

enum enode_type {
  normal
  archive
}

enum tx_status_type {
  pending
  confirmed
}

enum user_notification_type {
  transaction
}

enum user_notification_status {
  read
  unread
}

model v1_v2_migration_entity {
  entity_v1_id              Int
  entity_v2_nanoid          String /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid) @zod(userNanoid.or(companyNanoid).or(teamNanoid))
  entity_type               v1_entity_type
  riseid                    String
  rise_account              String /// @kysely(AllRiseAccounts) @zod(allRiseAccounts)
  migrated_clerk_ids        Boolean                       @default(false)
  migrated_sumsub_ids       Boolean                       @default(false)
  migrated_completed        Boolean                       @default(false)
  created_at                DateTime                      @default(now())
  updated_at                DateTime                      @default(now())
  migration_status          migration_status_type?
  failure_step              String?
  failure_reason            String?                       @db.VarChar(5000)
  owner_id                  Int?
  owner_type                v1_owner_type?
  company_relationship_id   Int?
  company_relationship_type v1_company_relationship_type?
  inngest_event_id          String?

  @@id([entity_v1_id, entity_type])
}

model team_payroll {
  nanoid          String /// @kysely(TeamNanoid) @zod(teamNanoid)
  country_code    String                @default("US")
  payroll_program payroll_programs      @default(riseworks_eor_us)
  rise_account    String /// @kysely(TeamPayrollRiseAccount) @zod(teamPayrollRiseAccount)
  pay_schedule    payroll_pay_schedules @default(bimonthly)
  created_at      DateTime              @default(now())
  updated_at      DateTime              @default(now())

  @@id([nanoid, payroll_program])
}

model rise_payroll_company {
  payroll_program payroll_programs
  company_nanoid  String /// @kysely(CompanyNanoid) @zod(companyNanoid)
  created_at      DateTime         @default(now())
  updated_at      DateTime         @default(now())

  @@id([payroll_program, company_nanoid])
}

model employee_payroll_settings {
  id                        Int                       @id @default(autoincrement())
  user_nanoid               String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid               String /// @kysely(TeamNanoid) @zod(teamNanoid)
  count                     Int
  payroll_program           payroll_programs          @default(riseworks_eor_us)
  country_code              String                    @default("US")
  rise_account              String /// @kysely(EmployeePayrollRiseAccount) @zod(employeePayrollRiseAccount)
  employment_type           String
  start_date                DateTime                  @db.Date()
  end_date                  DateTime?                 @db.Date()
  work_hours_per_week       Int
  job_title                 String
  annual_base_salary        Decimal                   @db.Decimal(10, 2)
  currency                  String /// @kysely(PayrollCurrencies) @zod(payrollCurrencies)
  job_scope                 String                    @db.VarChar(255) /// @zod(z.string().min(1).max(200))
  signing_bonus_amount      Decimal                   @db.Decimal(10, 2)
  variable_compensations    Json /// @kysely(JSONColumnType<VariableCompensations>) @zod(variableCompensations)
  stipends                  Json /// @kysely(JSONColumnType<Stipends>) @zod(stipends)
  time_off                  String
  status                    employee_payroll_statuses @default(active)
  created_at                DateTime                  @default(now())
  notice_period_days        Int                       @default(0)
  restricted_period_days    Int                       @default(0)
  dispute_resolution_method String                    @default("")
  effective_start           DateTime                  @db.Date()
  effective_end             DateTime?                 @db.Date()
  created_by                String /// @kysely(UserNanoid) @zod(userNanoid)

  @@unique([user_nanoid, team_nanoid, count])
}

model employee_payroll_pauses {
  id          Int       @id @default(autoincrement())
  user_nanoid String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid String /// @kysely(TeamNanoid) @zod(teamNanoid)
  start_date  DateTime  @db.Date
  end_date    DateTime? @db.Date
  created_at  DateTime  @default(now())
  created_by  String /// @kysely(UserNanoid) @zod(userNanoid)

  @@unique([user_nanoid, team_nanoid, start_date, end_date])
}

model one_off_bonuses {
  id               Int       @id @default(autoincrement())
  user_nanoid      String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid      String /// @kysely(TeamNanoid) @zod(teamNanoid)
  type             String
  amount           Decimal   @db.Decimal(10, 2)
  pay_at           DateTime  @db.Date
  created_at       DateTime  @default(now())
  created_by       String /// @kysely(UserNanoid) @zod(userNanoid)
  updated_at       DateTime  @default(now())
  deleted_at       DateTime?
  last_modified_by String?
}

model employee_payroll_past_wh {
  nanoid      String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid String /// @kysely(TeamNanoid) @zod(teamNanoid)
  month_wh    Decimal  @db.Decimal(10, 2)
  year_wh     Decimal  @db.Decimal(10, 2)
  quarter_wh  Decimal  @db.Decimal(10, 2)
  last_date   DateTime @db.Date
  updated_at  DateTime @default(now())
  created_at  DateTime @default(now())

  @@id([nanoid, team_nanoid])
}

model employee_payroll_taxes_past_wh {
  nanoid       String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid  String /// @kysely(TeamNanoid) @zod(teamNanoid)
  country_code String   @default("US")
  tax_id       String
  month_wh     Decimal  @db.Decimal(10, 2)
  year_wh      Decimal  @db.Decimal(10, 2)
  quarter_wh   Decimal  @db.Decimal(10, 2)
  last_date    DateTime @db.Date
  updated_at   DateTime @default(now())
  created_at   DateTime @default(now())

  @@id([nanoid, team_nanoid, tax_id])
}

model healthcare_payroll_settings {
  id                        Int              @id @default(autoincrement())
  user_nanoid               String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid               String /// @kysely(TeamNanoid) @zod(teamNanoid)
  effective_start           DateTime         @db.Date
  effective_end             DateTime?        @db.Date
  healthcare_employer_value Decimal          @db.Decimal(10, 2)
  healthcare_employee_value Decimal          @db.Decimal(10, 2)
  type                      healthcare_types
  created_at                DateTime         @default(now())

  @@unique([user_nanoid, team_nanoid, type, effective_start, effective_end])
}

model retirement_payroll_settings {
  id                          Int               @id @default(autoincrement())
  user_nanoid                 String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid                 String /// @kysely(TeamNanoid) @zod(teamNanoid)
  count                       Int
  effective_start             DateTime          @db.Date
  effective_end               DateTime?         @db.Date
  retirement_type             retirement_types
  ee_contribution_value       Decimal           @default(0) @db.Decimal(10, 2)
  ee_contribution_type        contribution_type @default(fixed)
  er_match_contribution_value Decimal           @default(0) @db.Decimal(10, 2)
  er_match_contribution_type  contribution_type @default(percentage)
  er_grant_contribution_value Decimal           @default(0) @db.Decimal(10, 2)
  er_grant_contribution_type  contribution_type @default(percentage)
  repayment_amount            Decimal?          @db.Decimal(10, 2)
  created_at                  DateTime          @default(now())

  @@unique([user_nanoid, team_nanoid, retirement_type, count])
}

enum contribution_type {
  percentage
  fixed
}

model retirement_plans_contribution_limits {
  id                 Int              @id @default(autoincrement())
  retirement_type    retirement_types
  year               Int
  contribution_limit Decimal          @db.Decimal(10, 2)
  catch_up_50        Decimal          @db.Decimal(10, 2)
  catch_up_60        Decimal          @db.Decimal(10, 2)

  @@unique([year, retirement_type])
}

model employee_tax_questionnaires {
  id                   Int      @id @default(autoincrement())
  country_code         String   @default("US")
  nanoid               String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid          String /// @kysely(TeamNanoid) @zod(teamNanoid)
  questionnaire_values Json /// @kysely(JSONColumnType<TaxQuestionnaireValues>) @zod(taxQuestionnaireValues)
  dependents_count     Int      @default(0)
  created_at           DateTime @default(now())
}

model symmetry_audit {
  id                   Int                   @id @default(autoincrement())
  employee_nanoid      String /// @kysely(UserNanoid) @zod(userNanoid)
  team_nanoid          String /// @kysely(TeamNanoid) @zod(teamNanoid)
  payroll_program      payroll_programs
  payroll_pay_schedule payroll_pay_schedules
  gross_wages          Decimal               @db.Decimal(10, 2)
  subject_wages        Decimal               @db.Decimal(10, 2)
  ee_taxes             Decimal               @db.Decimal(10, 2)
  er_taxes             Decimal               @db.Decimal(10, 2)
  ee_retirement        Decimal               @db.Decimal(10, 2)
  er_retirement        Decimal               @db.Decimal(10, 2)
  json_input           Json
  json_output          Json
  pay_cycle            String
  cache_key            String                @default("")
  cache_hits           Int                   @default(0)
  created_at           DateTime              @default(now())

  @@index([cache_key])
}

model ccip_configurations {
  network        String   @id
  chain_selector String
  router_address String
  created_at     DateTime @default(now())
  updated_at     DateTime @default(now())
}

model bucket_accounts {
  id         String   @id @db.VarChar(255)
  name       String   @default("") @db.VarChar(255)
  account    String
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

model payroll_documents {
  country_code String
  file_name    String
  file_url     String
  preview_url  String

  @@id([country_code, file_name])
}

model notification_tokens {
  id          Int                         @id @default(autoincrement())
  user_nanoid String? /// @kysely(UserNanoid | null)
  ext_user_id String?
  token       String                      @db.VarChar(512)
  app_type    notification_token_app_type @default(web)
  status      notification_token_status   @default(active)
  created_at  DateTime                    @default(now())
  updated_at  DateTime?                   @updatedAt

  @@unique([user_nanoid, token])
}

model integration_conn {
  connection_id      String                    @unique
  status             integration_conn_status
  category           integration_conn_category
  ext_app_name       integration_app_name
  data               Json /// @kysely(JSONColumnType<IntegrationConnData>) @zod(integrationConnData)
  rise_entity_nanoid String /// @kysely(UserNanoid | CompanyNanoid | TeamNanoid) @zod(userNanoid.or(companyNanoid).or(teamNanoid))
  last_modified_by   String /// @kysely(UserNanoid) @zod(userNanoid)
  created_at         DateTime                  @default(now())
  updated_at         DateTime                  @default(now())
}

model integration_transaction_data {
  connection_id   String
  invoice_id      String /// @kysely(InvoiceNanoid) @zod(invoiceNanoid)
  ext_entity_type integration_tran_ent_type
  ext_entity_id   String
  updated_at      DateTime                  @default(now())
  created_at      DateTime                  @default(now())

  @@id([connection_id, invoice_id])
}

model transfer_needs_document {
  country    String   @id @db.VarChar(2)
  created_at DateTime @default(now())
  updated_at DateTime @default(now())
}

enum integration_tran_ent_type {
  expense
  invoice
}

enum employee_payroll_statuses {
  active
  inactive
  terminated
}

enum integration_conn_status {
  active
  inactive
}

enum integration_conn_category {
  accounting
  email_invoicing
}

enum integration_app_name {
  xero
  quickbooks
  risepay
}

enum notification_token_status {
  active
  inactive
}

enum notification_token_app_type {
  web
  mobile
}

enum healthcare_types {
  medical
  dental
  vision
  life_add
}

enum retirement_types {
  traditional_401k
  roth_401k
  loan_repayment
}

enum payroll_programs {
  riseworks_inc
  riseworks_eor_us
  riseworks_pps_us
}

enum payroll_pay_schedules {
  bimonthly /// Paid **twice a month** (24 times a year)
  biweekly /// Paid **every two weeks** (26 times a year)
  weekly /// Paid **once a week** (52 times a year)
  monthly /// Paid **once a month** (12 times a year)
  bi_monthly /// Paid **every two months** (6 times a year)
  quartely /// Paid **once every three months** (4 times a year)
  six_months /// Paid **once every six months** (2 times a year)
  annually /// Paid **once a year**
}

enum v1_entity_type {
  user
  company
  team
}

enum v1_owner_type {
  user
  company
}

enum v1_company_relationship_type {
  company
  team
}

enum migration_status_type {
  in_progress
  completed
  failed
}

enum deposit_providers {
  manual
  routefusion
  bitcoin
  rise_pay_handler
}

enum payment_recurrence {
  daily
  weekly
  bi_weekly
  four_weeks
  monthly
  bi_monthly
  quartely
  six_months
  annually
}

enum payment_invoice_type {
  time_entry
  flat
  paynow
  payonce
  recurring
  milestone
  batch
  payroll_cash_requirement
}

enum payment_invoice_flat_type {
  reimbursement
  product
  service
}

enum payment_invoice_status {
  draft
  open
  closed
  open_canceled
  canceled
}

enum deposit_status {
  pending
  complete
  failed
  rejected
  reverted
}

enum account_statuses {
  created
  activated
  blocked
  expired
  deactivated
}

enum blockchain_transactions_friendly_types {
  unknown
  fiat_deposit
  fund
  withdraw
  payschedule_create
  payment_create
  payment_execute
  payment_intent
  payment_invoice
}

enum action_item_icons {
  shield_alt
  user
  user_shield
  user_plus
  lock
  file_contract
  file_signature
  id_card
  key
  exclamation
  star
  exclamation_circle
}

enum action_item_types {
  w4form
}

enum action_item_status {
  pending
  completed
  failed
}

enum blockchain_transactions_status {
  waiting
  sent
  confirmed
  failed
}

enum ledger_types {
  subscription_fee
  chargeback_fee
  payment
  transfer
  withdraw
  withdraw_fee
  deposit
  refund
}

enum risk_levels {
  low
  medium
  high
}

enum withdraw_account_conflict_status {
  pending
  accepted
  rejected
}

enum routefusion_entity_types {
  business
  personal
}

enum admin_blockchain_transaction_status {
  pending
  confirmed
  canceled
  failed
}

enum pay_intents_status {
  draft
  pending
  approved
  rejected
}

enum pay_schedule_types {
  recurring
  milestone
}

enum withdraw_provider {
  Orum
  Routefusion
  CitiBank
  WellsFargo
  Unblock
}

enum withdraw_status {
  draft
  blockchain_pending
  blockchain_sent
  blockchain_failed
  blockchain_completed
  blockchain_reversed
  fiat_sent
  fiat_failed
  fiat_reversed
  fiat_pending
  completed
  failed
}

enum withdraw_option_type {
  fiat
  crypto
}

enum withdraw_option_ramp {
  domestic_usd
  international_exchange
  international_usd
  international_usd_default
  europe
  gbp
  ngn
  arbitrum_wallet
  avalanche_wallet
  base_wallet
  ethereum_wallet
  optimism_wallet
  polygon_wallet
  coinbase
  coinbase_wallet
  token_swap
}

enum withdraw_option_entity_type {
  individual
  business
}

enum users_onboarding_moderation_status {
  pending
  submitted
  approved
  rejected
}

enum users_onboarding_ext_inquiry_status {
  created
  pending
  completed
  expired
  failed
  needs_review
  approved
  declined
}

enum riseid_types {
  user
  company
  team
  pay_handler
  rise_account
  rise_riseid_deposit
}

enum riseid_purpose {
  generic
  deposit
  withdraw
  payment
  main
  team_payroll
  employee_payroll
  tax_account
  rise
}

enum rise_entities_type {
  org_admin
  org_finance_admin
  org_viewer
  team_admin
  team_viewer
  team_finance_admin
  contractor
  team_employee
  user
  company
  team
}

enum companies_size {
  micro_sized
  small_sized
  medium
  large_sized
}

enum companies_incorporation_type {
  sole_proprietorship
  private_limited_company
  limited_liability_company
  s_corporation                        @map("s-corporation")
  c_corporation                        @map("c-corporation")
  public_limited_company
  limited_partnership
  holding_company
  non_government_organisation
  statutory_company
  subsidiary_company
  unlimited_partnership
  charitable_incorporated_organisation
  chartered_company
  association
  non_profit_organisation
  im_not_sure
}

enum users_onboarding_step {
  initial
  details
  compliance_process
  security
  signing
  complete
  riseid
  signer
  v1_migration_in_progress
  v1_migration_need_rsk
}

enum users_onboarding_role {
  payer
  payee
  viewer
  admin
}

enum invites_status {
  draft
  pending
  accepted
  canceled
  rejected
}

enum invites_role {
  org_admin
  org_finance_admin
  org_viewer
  team_admin
  team_viewer
  team_finance_admin
  contractor
  team_employee
}

enum blockchain_networks {
  ethereum
  arbitrum
  base
  polygon
  optimism
  avalanche
  bitcoin
  tron
}

enum documents_status {
  void
  draft
  sent
  complete
}

enum template_status {
  void
  draft
  sent
  complete
}

enum template_type {
  msa_c
  msa_sp
  psa
  sow
  ea_us
  ea_rise
}

enum signatures_status {
  pending
  rejected
  completed
  void
}

enum document_user_role {
  owner
  viewer
  editor
  signer
}

enum document_activity_event {
  draft_created
  draft_updated
  voided
  sent
  viewed
  signed
}

model event_data {
  id   String
  data Json

  created_at DateTime @default(now())
  updated_at DateTime @default(now())

  @@id([id])
}

enum chainflip_input_chain {
  Bitcoin
}

enum chainflip_input_asset {
  BTC
}

enum chainflip_output_chain {
  Arbitrum
}

enum chainflip_output_asset {
  USDC
}

enum chainflip_swap_status {
  WAITING
  RECEIVING
  SWAPPING
  SENDING
  SENT
  COMPLETED
  FAILED
}

enum chainflip_quote_type {
  regular
  boosted
}

enum chainflip_error {
  DEPOSIT_IGNORED
  DEPOSIT_REJECTED
  SWAP_OUTPUT_TOO_SMALL
  SENDING_FAILED
  REFUND_OUTPUT_TOO_SMALL
}

model chainflip_swaps {
  entity_nanoid String /// @kysely(CompanyNanoid | TeamNanoid) @zod(companyNanoid.or(teamNanoid))
  created_at    DateTime @default(now())
  updated_at    DateTime @default(now())

  // Get when preparing swap
  input_asset       chainflip_input_asset
  input_chain       chainflip_input_chain
  input_addresses   Json /// @kysely(JSONColumnType<string[]>) @zod(z.string().array()) -- Which addresses were used to fund this swap.
  input_value       Int /// @kysely(number) @zod(z.number())
  input_network_fee Int /// @kysely(number) @zod(z.number())
  input_txid        String
  input_sent_at     DateTime
  previous_swap_ids Json /// @kysely(JSONColumnType<string[]>) @zod(z.string().array()) -- If this swap was funded using refunds from previous swaps, include the channel_ids(s) here.
  output_asset      chainflip_output_asset
  output_chain      chainflip_output_chain
  output_address    String // Should be a payment handler for deposits.
  refund_address    String // If the swap fails, where funds should be sent back to.

  // Get when processing swap-related outputs
  input_confirmed_at DateTime?
  refund_value       Int? /// @kysely(number | null) @zod(z.number().nullish())
  refund_txid        String?
  refund_received_at DateTime?

  // Get from Chainflip responses
  quote_type         chainflip_quote_type
  channel_id         String                 @id
  channel_address    String // Where funds must be sent to initiate the swap.
  channel_expires_at DateTime
  status             chainflip_swap_status?
  output_value       Int? /// @kysely(number | null) @zod(z.number().nullish())
  output_txid        String? // Can be used for reverse-lookup of USDC deposits
  output_sent_at     DateTime?
  fee_usdc           Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_eth            Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_flip           Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_dot            Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_btc            Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_usdt           Int? /// @kysely(number | null) @zod(z.number().nullish())
  fee_sol            Int? /// @kysely(number | null) @zod(z.number().nullish())
  completed_at       DateTime?
  error              chainflip_error?
  failed_at          DateTime?
}

enum currency_type {
  FIAT
  CRYPTO
}

model supported_currencies {
  id                             Int           @id @default(autoincrement())
  name                           String        @db.VarChar(128)
  symbol                         String        @db.VarChar(16)
  type                           currency_type
  rise_erc20_token               String        @unique @db.VarChar(255)
  active                         Boolean       @default(false)
  supports_deposits_or_withdraws Boolean       @default(true)
  supports_invoices              Boolean       @default(true)
  supports_balance               Boolean       @default(true)
}

model entities_multi_currency_configurations {
  nanoid           String /// @kysely(AllNanoids) @zod(allNanoids)
  currency_id      Int
  deposits         Boolean  @default(false)
  invoices         Boolean  @default(false)
  rise_balance     Boolean  @default(false)
  is_main_currency Boolean  @default(false)
  created_by       String /// @kysely(UserNanoid) @zod(userNanoid)
  last_modified_by String? /// @kysely(UserNanoid | null) @zod(userNanoid.nullish())
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())

  @@unique([nanoid, currency_id])
}

model users_rsk {
  nanoid           String                  @id() /// @kysely(UserNanoid)  @zod(userNanoid)
  has_passkey      Boolean                 @default(false)
  has_iframe_rsk   Boolean                 @default(false)
  reset_status     users_rsk_reset_status  @default(completed)
  status           users_rsk_reset_status?
  verified         Boolean                 @default(false)
  verified_at      DateTime?
  created_at       DateTime                @default(now())
  updated_at       DateTime                @default(now())
  last_modified_by String? /// @kysely(UserNanoid | null)
  ext_inquiry_id   String?
  is_test          Boolean                 @default(false)
}

enum users_rsk_reset_status {
  started
  pending
  completed
  failed
}

model payroll_healthcheck_logs {
  id               Int                                @id @default(autoincrement())
  run_id           String
  created_at       DateTime                           @default(now())
  team_nanoid      String /// @ts(TeamNanoid) @zod(teamNanoid)
  payroll_program  payroll_programs
  user_nanoid      String? /// @ts(UserNanoid | null) @zod(userNanoid.nullish())
  target_account   payroll_healthcheck_target_account
  rise_account     String
  pay_cycle_year   Int
  pay_cycle_month  Int
  pay_cycle_period Int
  success          Boolean
  errors           Json
}

model payroll_program_config {
  id                    Int                   @id @default(autoincrement())
  country_code          String                @db.Char(2)
  payroll_program       payroll_programs
  pay_schedule          payroll_pay_schedules
  periods_per_year      Int
  pay_date_offset       Int
  due_date_offset       Int
  supported_currency_id Int
  is_active             Boolean               @default(true)
  created_at            DateTime              @default(now())
  updated_at            DateTime              @default(now())

  @@index([country_code, payroll_program])
}

enum payroll_healthcheck_target_account {
  employee_payroll
  team_payroll
}

enum webhook_delivery_status {
  queued
  success
  failed
  retrying
}

model webhook_endpoints {
  nanoid           String   @id /// @kysely(WebhookEndpointNanoid) @zod(webhookEndpointNanoid)
  company_nanoid   String /// @kysely(CompanyNanoid) @zod(companyNanoid)
  is_removed       Boolean  @default(false)
  team_nanoid      String? /// @kysely(TeamNanoid | null) @zod(teamNanoid.nullish())
  created_by       String /// @kysely(UserNanoid) @zod(userNanoid)
  last_modified_by String? /// @kysely(UserNanoid | null) @zod(userNanoid.nullish())
  url              String   @db.Text
  secret_encrypted String   @db.Text
  events           Json /// @kysely(JSONColumnType<string[]>) @zod(z.string().array())
  is_active        Boolean  @default(true)
  created_at       DateTime @default(now())
  updated_at       DateTime @default(now())

  webhook_deliveries webhook_deliveries[] @relation("webhook_deliveries")
}

model webhook_events {
  nanoid     String      @id /// @kysely(WebhookEventNanoid) @zod(webhookEventNanoid)
  event_type event_types ///@kysely(WebhookEventTypes)
  payload    Json /// @kysely(JSONColumnType<Record<string, any>>) @zod(z.record(z.any()))
  created_at DateTime    @default(now())
  version    String      @db.VarChar(10)

  webhook_deliveries webhook_deliveries[] @relation("webhook_event_deliveries")
}

model webhook_deliveries {
  nanoid         String                  @id /// @kysely(WebhookDeliveryNanoid) @zod(webhookDeliveryNanoid)
  webhook_nanoid String /// @kysely(WebhookEndpointNanoid) @zod(webhookEndpointNanoid)
  event_nanoid   String /// @kysely(WebhookEventNanoid) @zod(webhookEventNanoid)
  status         webhook_delivery_status @default(queued)
  response_code  Int?
  error_message  String?                 @db.Text
  response_body  Json? /// @kysely(JSONColumnType<Record<string, any>>) @zod(z.record(z.any()).nullish())
  created_at     DateTime                @default(now())
  updated_at     DateTime                @default(now())

  webhook_endpoint webhook_endpoints @relation("webhook_deliveries", fields: [webhook_nanoid], references: [nanoid])
  webhook_event    webhook_events    @relation("webhook_event_deliveries", fields: [event_nanoid], references: [nanoid])
}

enum event_types {
  account_duplicated_detected @map("withdraw_account.duplicated_detected")
  invite_accepted             @map("invite.accepted")
  deposit_received            @map("deposit.received")
  payment_sent                @map("payment.sent")
  payment_group_created       @map("payment.group.created")
}
