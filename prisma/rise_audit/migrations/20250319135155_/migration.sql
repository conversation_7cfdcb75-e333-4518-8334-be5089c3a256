/*
  Warnings:

  - The primary key for the `request_metrics` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - Added the required column `request_attributes` to the `request_metrics` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `request_metrics` DROP PRIMARY KEY,
    ADD COLUMN `request_attributes` JSON NOT NULL,
    MODIFY `created_at` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`nanoid`, `url`, `method`, `status`, `created_at`);
