-- CreateTable
CREATE TABLE `file_header` (
    `customer_id` VARCHAR(191) NOT NULL DEFAULT '12214',
    `payroll_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '0',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '00',
    `process_date` DATETIME(3) NOT NULL,
    `process_time` VARCHAR(191) NOT NULL,
    `file_type` VARCHAR(191) NOT NULL DEFAULT 'MTAXPTS',
    `version` VARCHAR(191) NOT NULL DEFAULT '29',

    UNIQUE INDEX `file_header_payroll_code_key`(`payroll_code`),
    UNIQUE INDEX `file_header_check_date_key`(`check_date`),
    PRIMARY KEY (`payroll_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payroll_header` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '1',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '00',
    `tax_liabilities` ENUM('yes', 'no') NOT NULL,
    `company_setup` ENUM('yes', 'no') NOT NULL,
    `variance_payroll_code` ENUM('yes', 'no') NULL,
    `bank_setup` ENUM('yes', 'no') NULL,
    `payroll_description` VARCHAR(191) NULL,
    `company_start_date` DATETIME(3) NOT NULL,
    `company_status` ENUM('active', 'inactive') NOT NULL,
    `company_name` VARCHAR(191) NOT NULL,
    `service_level` ENUM('full_service', 'return_only', 'balance_only') NOT NULL,
    `fein_type` ENUM('applied_for', 'registered', 'common_pay_parent', 'common_pay_child') NULL,
    `fein` VARCHAR(191) NULL,
    `bank_account_name` VARCHAR(191) NULL,
    `transit_routing_number` VARCHAR(191) NULL,
    `bank_account_number` VARCHAR(191) NULL,
    `bank_account_type` ENUM('checking', 'savings') NULL,
    `draft_days` INTEGER NULL,
    `next_check_date` DATETIME(3) NULL,
    `name_control` VARCHAR(191) NULL,
    `disbursement_ach_bank_destination` VARCHAR(191) NULL,
    `disbursement_bank_account_name` VARCHAR(191) NULL,
    `disbursement_bank_routing_number` VARCHAR(191) NULL,
    `disbursement_bank_account_number` VARCHAR(191) NULL,
    `cafeteria_plan_benefits` ENUM('applicable', 'not_applicable') NULL,
    `group_term_life_insurance` ENUM('applicable', 'not_applicable') NULL,
    `dependent_care_assistance` ENUM('applicable', 'not_applicable') NULL,
    `business_expense_reimbursement` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_401k` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_sep_ira` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_simple` ENUM('applicable', 'not_applicable') NULL,
    `accident_health_insurance_premiums` ENUM('applicable', 'not_applicable') NULL,
    `sick_pay` ENUM('applicable', 'not_applicable') NULL,
    `workers_compensation` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_family_employees` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_hospital_interns` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_hospital_patients` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_general_partnership` ENUM('applicable', 'not_applicable') NULL,
    `state_govt_employee_salaries` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_election_workers` ENUM('applicable', 'not_applicable') NULL,
    `supplemental_unemployment_benefits` ENUM('applicable', 'not_applicable') NULL,
    `nonqualified_deferred_comp` ENUM('applicable', 'not_applicable') NULL,
    `meals_furnished_in_kind` ENUM('applicable', 'not_applicable') NULL,
    `qualified_moving_expense` ENUM('applicable', 'not_applicable') NULL,
    `hsa` ENUM('applicable', 'not_applicable') NULL,
    `exempt_501c3_organization` ENUM('applicable', 'not_applicable') NULL,
    `employee_stock_purchase_plan` ENUM('applicable', 'not_applicable') NULL,
    `non_taxable_fringe_payments` ENUM('applicable', 'not_applicable') NULL,
    `public_transportation_non_tax` ENUM('applicable', 'not_applicable') NULL,
    `wc_housing_employment_condition` ENUM('applicable', 'not_applicable') NULL,
    `chaplain_housing` ENUM('applicable', 'not_applicable') NULL,
    `clergy_housing_poverty_vow` ENUM('applicable', 'not_applicable') NULL,
    `foreign_source_income` ENUM('applicable', 'not_applicable') NULL,
    `student_exempt` ENUM('applicable', 'not_applicable') NULL,
    `company_group_name` VARCHAR(191) NULL,
    `agent_client_type` ENUM('agent_3504', 'cpeo_3511a', 'cpeo_3511c', 'cpeo_3504', 'cpeo_mixed', 'cpeo_client_3511a', 'cpeo_client_3511c', 'cpeo_client_31_3504', 'other_third_party', 'none') NULL,
    `filer_944` ENUM('yes', 'no') NULL,
    `quarterly_wage_reporting` ENUM('yes', 'no') NULL,
    `year_end_employee_filing` ENUM('yes', 'no') NULL,
    `cash_service_level` ENUM('full', 'variances_only') NULL,
    `payroll_run_id` VARCHAR(191) NULL,
    `worksite_reporting` ENUM('yes', 'no') NULL,
    `wage_attachment_flag` ENUM('yes', 'no') NULL,
    `short_reporting_payroll_code` VARCHAR(191) NULL,
    `company_effective_date` DATETIME(3) NULL,
    `kind_of_employer` ENUM('federal_government', 'state_local_government', 'tax_exempt', 'state_local_tax_exempt', 'none') NULL,
    `naics_code` VARCHAR(191) NULL,
    `reporting_payroll_code` VARCHAR(191) NULL,

    UNIQUE INDEX `payroll_header_payroll_code_key`(`payroll_code`),
    PRIMARY KEY (`payroll_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_disbursement_banks` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '1',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '01',
    `payroll_tax_disbursement_ach_point` VARCHAR(191) NULL,
    `payroll_tax_disbursement_bank_name` VARCHAR(191) NULL,
    `payroll_tax_disbursement_routing_number` VARCHAR(191) NULL,
    `payroll_tax_disbursement_account_number` VARCHAR(191) NULL,
    `wage_attachment_disbursement_ach_point` VARCHAR(191) NULL,
    `wage_attachment_disbursement_bank_name` VARCHAR(191) NULL,
    `wage_attachment_disbursement_routing_number` VARCHAR(191) NULL,
    `wage_attachment_disbursement_account_number` VARCHAR(191) NULL,

    UNIQUE INDEX `company_disbursement_banks_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_cash_care_banks` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '1',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '02',
    `payroll_tax_bank_name` VARCHAR(191) NULL,
    `payroll_tax_routing_number` VARCHAR(191) NULL,
    `payroll_tax_account_number` VARCHAR(191) NULL,
    `payroll_tax_account_type` ENUM('checking', 'savings') NULL,
    `payroll_tax_draft_days` INTEGER NULL,
    `wage_attachment_bank_name` VARCHAR(191) NULL,
    `wage_attachment_routing_number` VARCHAR(191) NULL,
    `wage_attachment_account_number` VARCHAR(191) NULL,
    `wage_attachment_account_type` ENUM('checking', 'savings') NULL,
    `wage_attachment_draft_days` INTEGER NULL,
    `cash_care_ach_bank_destination` VARCHAR(191) NULL,

    UNIQUE INDEX `company_cash_care_banks_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_general_ledger` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '1',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '05',
    `chart_of_accounts_code` VARCHAR(191) NULL,
    `chart_of_accounts_description` VARCHAR(191) NULL,
    `company_code` VARCHAR(191) NULL,
    `variance_payroll_code` VARCHAR(191) NULL,
    `gl_payroll_code` VARCHAR(191) NULL,

    UNIQUE INDEX `company_general_ledger_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `secondary_company_groups` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '1',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '10',
    `company_group_type_1` VARCHAR(191) NULL,
    `company_group_name_1` VARCHAR(191) NULL,
    `company_group_type_2` VARCHAR(191) NULL,
    `company_group_name_2` VARCHAR(191) NULL,
    `company_group_type_3` VARCHAR(191) NULL,
    `company_group_name_3` VARCHAR(191) NULL,
    `company_group_type_4` VARCHAR(191) NULL,
    `company_group_name_4` VARCHAR(191) NULL,

    UNIQUE INDEX `secondary_company_groups_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_address` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '2',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '01',
    `company_dba` VARCHAR(191) NULL,
    `address_line_1` VARCHAR(191) NULL,
    `address_line_2` VARCHAR(191) NULL,
    `city` VARCHAR(191) NULL,
    `state_code` VARCHAR(191) NULL,
    `zip_code` VARCHAR(191) NULL,
    `country_code` ENUM('US', 'CA', 'MX') NULL,
    `psd_code` VARCHAR(191) NULL,
    `first_name` VARCHAR(191) NULL,
    `middle_initial` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `area_code` VARCHAR(191) NULL,
    `telephone_number` VARCHAR(191) NULL,
    `extension` VARCHAR(191) NULL,
    `fax_area_code` VARCHAR(191) NULL,
    `fax_number` VARCHAR(191) NULL,
    `email_address` VARCHAR(191) NULL,
    `in_care_of` VARCHAR(191) NULL,

    UNIQUE INDEX `company_address_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_mailing_address` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '2',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '02',
    `company_dba` VARCHAR(191) NULL,
    `address_line_1` VARCHAR(191) NULL,
    `address_line_2` VARCHAR(191) NULL,
    `city` VARCHAR(191) NULL,
    `state_code` VARCHAR(191) NULL,
    `zip_code` VARCHAR(191) NULL,
    `country_code` ENUM('US', 'CA', 'MX') NULL,
    `route_code` VARCHAR(191) NULL,
    `first_name` VARCHAR(191) NULL,
    `middle_initial` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `area_code` VARCHAR(191) NULL,
    `telephone_number` VARCHAR(191) NULL,
    `extension` VARCHAR(191) NULL,
    `fax_area_code` VARCHAR(191) NULL,
    `fax_number` VARCHAR(191) NULL,
    `email_address` VARCHAR(191) NULL,

    UNIQUE INDEX `company_mailing_address_payroll_code_key`(`payroll_code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_tax` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `tax_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '2',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '10',
    `work_out_of_state_flag` ENUM('yes', 'no') NULL,
    `effective_date` DATETIME(3) NULL,
    `company_tax_status` ENUM('active', 'inactive') NULL,
    `ein_type` ENUM('applied_for', 'registered', 'reimbursable', 'same_as_fein', 'exempt', 'common_pay_parent', 'common_pay_child', 'michigan_501') NULL,
    `ein` VARCHAR(191) NULL,
    `tax_rate` VARCHAR(191) NULL,
    `tax_rate_2` VARCHAR(191) NULL,
    `expanded_tax_rate` VARCHAR(191) NULL,
    `payment_frequency` VARCHAR(191) NULL,
    `payment_method` ENUM('check', 'eft_credit', 'eft_debit', 'eft_debit_touch_tone', 'eft_debit_online', 'eft_debit_file', 'eft_debit_return') NULL,
    `eft_password` VARCHAR(191) NULL,
    `reference_ein` VARCHAR(191) NULL,
    `county_code` VARCHAR(191) NULL,
    `company_tax_service_level` ENUM('return_only', 'balance_only') NULL,
    `mark_all_returns_final` ENUM('yes', 'no') NULL,
    `final_return_effective_date` DATETIME(3) NULL,

    PRIMARY KEY (`payroll_code`, `tax_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company_workers_comp_codes` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `tax_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '2',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '11',
    `effective_date` DATETIME(3) NULL,
    `class_code` VARCHAR(191) NULL,
    `rate` VARCHAR(191) NULL,

    PRIMARY KEY (`payroll_code`, `tax_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payroll_tax_detail` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `tax_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '5',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '00',
    `work_out_of_state_flag` ENUM('yes', 'no') NULL,
    `ein` VARCHAR(191) NULL,
    `tax_rate` VARCHAR(191) NULL,
    `tax` DECIMAL(65, 30) NULL,
    `taxable_wages` DECIMAL(65, 30) NULL,
    `gross_wages` DECIMAL(65, 30) NULL,
    `employee_count` INTEGER NULL,
    `employee_count_sign` ENUM('positive', 'negative') NULL,
    `exempt_overtime_wages_employee_count` INTEGER NULL,
    `liability_trace_id` VARCHAR(191) NULL,
    `exempt_wages` DECIMAL(65, 30) NULL,
    `wc_class_code` VARCHAR(191) NULL,
    `payroll_frequency` ENUM('weekly', 'bi_weekly', 'semi_monthly', 'monthly') NULL,
    `expanded_tax_rate` VARCHAR(191) NULL,

    PRIMARY KEY (`payroll_code`, `tax_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payroll_tax_user_defined_fields` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `tax_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '5',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '10',
    `work_out_of_state_flag` ENUM('yes', 'no') NULL,
    `user_field_1` VARCHAR(191) NULL,
    `user_field_2` VARCHAR(191) NULL,
    `user_field_3` VARCHAR(191) NULL,
    `user_field_4` VARCHAR(191) NULL,
    `user_field_5` VARCHAR(191) NULL,
    `user_field_6` VARCHAR(191) NULL,
    `user_field_7` VARCHAR(191) NULL,
    `user_field_8` VARCHAR(191) NULL,

    PRIMARY KEY (`payroll_code`, `tax_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payroll_trailer` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '8',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '99',
    `record_count` INTEGER NULL,
    `tax_total` DECIMAL(65, 30) NULL,

    UNIQUE INDEX `payroll_trailer_payroll_code_key`(`payroll_code`),
    PRIMARY KEY (`payroll_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `file_trailer` (
    `payroll_code` VARCHAR(191) NOT NULL,
    `check_date` DATETIME(3) NOT NULL,
    `record_type` VARCHAR(191) NOT NULL DEFAULT '9',
    `sub_type` VARCHAR(191) NOT NULL DEFAULT '99',
    `record_count` INTEGER NULL,
    `tax_total` DECIMAL(65, 30) NULL,

    UNIQUE INDEX `file_trailer_payroll_code_key`(`payroll_code`),
    UNIQUE INDEX `file_trailer_check_date_key`(`check_date`),
    PRIMARY KEY (`payroll_code`, `check_date`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `payroll_header` ADD CONSTRAINT `payroll_header_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `file_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_disbursement_banks` ADD CONSTRAINT `company_disbursement_banks_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_cash_care_banks` ADD CONSTRAINT `company_cash_care_banks_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_general_ledger` ADD CONSTRAINT `company_general_ledger_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `secondary_company_groups` ADD CONSTRAINT `secondary_company_groups_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_address` ADD CONSTRAINT `company_address_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_mailing_address` ADD CONSTRAINT `company_mailing_address_payroll_code_fkey` FOREIGN KEY (`payroll_code`) REFERENCES `payroll_header`(`payroll_code`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_tax` ADD CONSTRAINT `company_tax_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_workers_comp_codes` ADD CONSTRAINT `company_workers_comp_codes_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payroll_tax_detail` ADD CONSTRAINT `payroll_tax_detail_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payroll_tax_user_defined_fields` ADD CONSTRAINT `payroll_tax_user_defined_fields_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payroll_trailer` ADD CONSTRAINT `payroll_trailer_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `file_trailer` ADD CONSTRAINT `file_trailer_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `file_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;
