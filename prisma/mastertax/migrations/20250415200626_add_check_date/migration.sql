/*
  Warnings:

  - Added the required column `check_date` to the `company_address` table without a default value. This is not possible if the table is not empty.
  - Added the required column `check_date` to the `company_cash_care_banks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `check_date` to the `company_disbursement_banks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `check_date` to the `company_general_ledger` table without a default value. This is not possible if the table is not empty.
  - Added the required column `check_date` to the `company_mailing_address` table without a default value. This is not possible if the table is not empty.
  - Added the required column `check_date` to the `secondary_company_groups` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `company_address` DROP FOREIGN KEY `company_address_payroll_code_fkey`;

-- DropForeignKey
ALTER TABLE `company_cash_care_banks` DROP FOREIGN KEY `company_cash_care_banks_payroll_code_fkey`;

-- DropForeignKey
ALTER TABLE `company_disbursement_banks` DROP FOREIGN KEY `company_disbursement_banks_payroll_code_fkey`;

-- DropForeignKey
ALTER TABLE `company_general_ledger` DROP FOREIGN KEY `company_general_ledger_payroll_code_fkey`;

-- DropForeignKey
ALTER TABLE `company_mailing_address` DROP FOREIGN KEY `company_mailing_address_payroll_code_fkey`;

-- DropForeignKey
ALTER TABLE `secondary_company_groups` DROP FOREIGN KEY `secondary_company_groups_payroll_code_fkey`;

-- DropIndex
DROP INDEX `company_address_payroll_code_key` ON `company_address`;

-- DropIndex
DROP INDEX `company_cash_care_banks_payroll_code_key` ON `company_cash_care_banks`;

-- DropIndex
DROP INDEX `company_disbursement_banks_payroll_code_key` ON `company_disbursement_banks`;

-- DropIndex
DROP INDEX `company_general_ledger_payroll_code_key` ON `company_general_ledger`;

-- DropIndex
DROP INDEX `company_mailing_address_payroll_code_key` ON `company_mailing_address`;

-- DropIndex
DROP INDEX `file_header_check_date_key` ON `file_header`;

-- DropIndex
DROP INDEX `file_header_payroll_code_key` ON `file_header`;

-- DropIndex
DROP INDEX `file_trailer_check_date_key` ON `file_trailer`;

-- DropIndex
DROP INDEX `file_trailer_payroll_code_key` ON `file_trailer`;

-- DropIndex
DROP INDEX `payroll_header_payroll_code_key` ON `payroll_header`;

-- DropIndex
DROP INDEX `payroll_trailer_payroll_code_key` ON `payroll_trailer`;

-- DropIndex
DROP INDEX `secondary_company_groups_payroll_code_key` ON `secondary_company_groups`;

-- AlterTable
ALTER TABLE `company_address` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AlterTable
ALTER TABLE `company_cash_care_banks` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AlterTable
ALTER TABLE `company_disbursement_banks` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AlterTable
ALTER TABLE `company_general_ledger` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AlterTable
ALTER TABLE `company_mailing_address` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AlterTable
ALTER TABLE `secondary_company_groups` ADD COLUMN `check_date` DATETIME(3) NOT NULL,
    ADD PRIMARY KEY (`payroll_code`, `check_date`);

-- AddForeignKey
ALTER TABLE `company_disbursement_banks` ADD CONSTRAINT `company_disbursement_banks_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_cash_care_banks` ADD CONSTRAINT `company_cash_care_banks_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_general_ledger` ADD CONSTRAINT `company_general_ledger_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `secondary_company_groups` ADD CONSTRAINT `secondary_company_groups_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_address` ADD CONSTRAINT `company_address_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company_mailing_address` ADD CONSTRAINT `company_mailing_address_payroll_code_check_date_fkey` FOREIGN KEY (`payroll_code`, `check_date`) REFERENCES `payroll_header`(`payroll_code`, `check_date`) ON DELETE CASCADE ON UPDATE CASCADE;
