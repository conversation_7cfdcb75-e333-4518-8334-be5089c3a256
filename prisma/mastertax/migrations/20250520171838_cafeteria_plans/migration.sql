-- AlterTable
ALTER TABLE `payroll_header` MODIFY `company_start_date` DATETIME(3) NOT NULL DEFAULT '2025-03-01T00:00:00+00:00';

-- CreateTable
CREATE TABLE `cafeteria_plan_benefits` (
    `nanoid` VARCHAR(191) NOT NULL,
    `cafeteria_plan_benefits` ENUM('applicable', 'not_applicable') NULL,
    `group_term_life_insurance` ENUM('applicable', 'not_applicable') NULL,
    `dependent_care_assistance` ENUM('applicable', 'not_applicable') NULL,
    `business_expense_reimbursement` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_401k` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_sep_ira` ENUM('applicable', 'not_applicable') NULL,
    `employer_contribution_simple` ENUM('applicable', 'not_applicable') NULL,
    `accident_health_insurance_premiums` ENUM('applicable', 'not_applicable') NULL,
    `sick_pay` ENUM('applicable', 'not_applicable') NULL,
    `workers_compensation` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_family_employees` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_hospital_interns` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_hospital_patients` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_general_partnership` ENUM('applicable', 'not_applicable') NULL,
    `state_govt_employee_salaries` ENUM('applicable', 'not_applicable') NULL,
    `payments_to_election_workers` ENUM('applicable', 'not_applicable') NULL,
    `supplemental_unemployment_benefits` ENUM('applicable', 'not_applicable') NULL,
    `nonqualified_deferred_comp` ENUM('applicable', 'not_applicable') NULL,
    `meals_furnished_in_kind` ENUM('applicable', 'not_applicable') NULL,
    `qualified_moving_expense` ENUM('applicable', 'not_applicable') NULL,
    `hsa` ENUM('applicable', 'not_applicable') NULL,
    `exempt_501c3_organization` ENUM('applicable', 'not_applicable') NULL,
    `employee_stock_purchase_plan` ENUM('applicable', 'not_applicable') NULL,
    `non_taxable_fringe_payments` ENUM('applicable', 'not_applicable') NULL,
    `public_transportation_non_tax` ENUM('applicable', 'not_applicable') NULL,
    `wc_housing_employment_condition` ENUM('applicable', 'not_applicable') NULL,
    `chaplain_housing` ENUM('applicable', 'not_applicable') NULL,
    `clergy_housing_poverty_vow` ENUM('applicable', 'not_applicable') NULL,
    `foreign_source_income` ENUM('applicable', 'not_applicable') NULL,
    `student_exempt` ENUM('applicable', 'not_applicable') NULL,
    `company_group_name` VARCHAR(191) NULL,
    `agent_client_type` ENUM('agent_3504', 'cpeo_3511a', 'cpeo_3511c', 'cpeo_3504', 'cpeo_mixed', 'cpeo_client_3511a', 'cpeo_client_3511c', 'cpeo_client_31_3504', 'other_third_party', 'none') NULL,
    `filer_944` ENUM('yes', 'no') NULL,
    `quarterly_wage_reporting` ENUM('yes', 'no') NULL,
    `year_end_employee_filing` ENUM('yes', 'no') NULL,
    `cash_service_level` ENUM('full', 'variances_only') NULL,
    `payroll_run_id` VARCHAR(191) NULL,
    `worksite_reporting` ENUM('yes', 'no') NULL,
    `wage_attachment_flag` ENUM('yes', 'no') NULL,
    `short_reporting_payroll_code` VARCHAR(191) NULL,
    `company_effective_date` DATETIME(3) NULL,
    `kind_of_employer` ENUM('federal_government', 'state_local_government', 'tax_exempt', 'state_local_tax_exempt', 'none') NULL,
    `naics_code` VARCHAR(191) NULL DEFAULT '541214',
    `reporting_payroll_code` VARCHAR(191) NULL,

    PRIMARY KEY (`nanoid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
