/*
  Warnings:

  - You are about to drop the column `reporting_payroll_code` on the `cafeteria_plan_benefits` table. All the data in the column will be lost.
  - You are about to drop the column `short_reporting_payroll_code` on the `cafeteria_plan_benefits` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `cafeteria_plan_benefits` DROP COLUMN `reporting_payroll_code`,
    DROP COLUMN `short_reporting_payroll_code`,
    MODIFY `cafeteria_plan_benefits` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `group_term_life_insurance` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `dependent_care_assistance` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `business_expense_reimbursement` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `employer_contribution_401k` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `employer_contribution_sep_ira` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `employer_contribution_simple` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `accident_health_insurance_premiums` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `sick_pay` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `workers_compensation` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `payments_to_family_employees` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `payments_to_hospital_interns` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `payments_to_hospital_patients` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `payments_to_general_partnership` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `state_govt_employee_salaries` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `payments_to_election_workers` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `supplemental_unemployment_benefits` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `nonqualified_deferred_comp` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `meals_furnished_in_kind` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `qualified_moving_expense` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `hsa` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `exempt_501c3_organization` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `employee_stock_purchase_plan` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `non_taxable_fringe_payments` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `public_transportation_non_tax` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `wc_housing_employment_condition` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `chaplain_housing` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `clergy_housing_poverty_vow` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `foreign_source_income` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `student_exempt` ENUM('applicable', 'not_applicable') NULL DEFAULT 'not_applicable',
    MODIFY `agent_client_type` ENUM('agent_3504', 'cpeo_3511a', 'cpeo_3511c', 'cpeo_3504', 'cpeo_mixed', 'cpeo_client_3511a', 'cpeo_client_3511c', 'cpeo_client_31_3504', 'other_third_party', 'none') NULL DEFAULT 'none',
    MODIFY `filer_944` ENUM('yes', 'no') NULL DEFAULT 'no',
    MODIFY `quarterly_wage_reporting` ENUM('yes', 'no') NULL DEFAULT 'no',
    MODIFY `year_end_employee_filing` ENUM('yes', 'no') NULL DEFAULT 'no',
    MODIFY `cash_service_level` ENUM('full', 'variances_only') NULL DEFAULT 'full',
    MODIFY `worksite_reporting` ENUM('yes', 'no') NULL DEFAULT 'no',
    MODIFY `wage_attachment_flag` ENUM('yes', 'no') NULL DEFAULT 'no',
    MODIFY `kind_of_employer` ENUM('federal_government', 'state_local_government', 'tax_exempt', 'state_local_tax_exempt', 'none') NULL DEFAULT 'none';
