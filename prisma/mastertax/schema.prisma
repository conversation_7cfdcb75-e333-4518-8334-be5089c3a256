datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Master Tax Models based on PTS file specification
// https://my.mastertax.com/wp-content/uploads/2025/03/pts_spec_v29.pdf
model file_header {
  customer_id  String   @default("12214")
  payroll_code String
  check_date   DateTime // YYYYMMDD format
  record_type  String   @default("0")
  sub_type     String   @default("00")
  process_date DateTime // YYYYMMDD format
  process_time String // HHMMSS format (kept as String since it's a time)
  file_type    String   @default("MTAXPTS")
  version      String   @default("29")

  // Define the relationships
  payroll_header payroll_header? @relation()
  file_trailer   file_trailer?   @relation()

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("file_header")
}

model payroll_header {
  payroll_code String
  check_date   DateTime // YYYYMMDD format

  record_type     String @default("1")
  sub_type        String @default("00")
  tax_liabilities yes_no
  company_setup   yes_no

  variance_payroll_code yes_no?
  bank_setup            yes_no?
  payroll_description   String?

  company_start_date DateTime @default("2025-03-01T00:00:00Z") // YYYYMMDD format
  company_status     company_status
  company_name       String
  service_level      service_level

  fein_type              fein_type?
  fein                   String?
  bank_account_name      String?
  transit_routing_number String?
  bank_account_number    String?
  bank_account_type      bank_account_type?
  draft_days             Int?
  next_check_date        DateTime? // YYYYMMDD format
  name_control           String?

  // Disbursement Bank Fields
  disbursement_ach_bank_destination String?
  disbursement_bank_account_name    String?
  disbursement_bank_routing_number  String?
  disbursement_bank_account_number  String?

  // FUTA Exempt Categories
  cafeteria_plan_benefits            futa_exemption?
  group_term_life_insurance          futa_exemption?
  dependent_care_assistance          futa_exemption?
  business_expense_reimbursement     futa_exemption?
  employer_contribution_401k         futa_exemption?
  employer_contribution_sep_ira      futa_exemption?
  employer_contribution_simple       futa_exemption?
  accident_health_insurance_premiums futa_exemption?
  sick_pay                           futa_exemption?
  workers_compensation               futa_exemption?
  payments_to_family_employees       futa_exemption?
  payments_to_hospital_interns       futa_exemption?
  payments_to_hospital_patients      futa_exemption?
  payments_to_general_partnership    futa_exemption?
  state_govt_employee_salaries       futa_exemption?
  payments_to_election_workers       futa_exemption?
  supplemental_unemployment_benefits futa_exemption?
  nonqualified_deferred_comp         futa_exemption?
  meals_furnished_in_kind            futa_exemption?
  qualified_moving_expense           futa_exemption?
  hsa                                futa_exemption?
  exempt_501c3_organization          futa_exemption?
  employee_stock_purchase_plan       futa_exemption?
  non_taxable_fringe_payments        futa_exemption?
  public_transportation_non_tax      futa_exemption?
  wc_housing_employment_condition    futa_exemption?
  chaplain_housing                   futa_exemption?
  clergy_housing_poverty_vow         futa_exemption?
  foreign_source_income              futa_exemption?
  student_exempt                     futa_exemption?

  // Other Fields
  company_group_name           String?
  agent_client_type            agent_client_type?
  filer_944                    yes_no?
  quarterly_wage_reporting     yes_no?
  year_end_employee_filing     yes_no?
  cash_service_level           cash_service_level?
  payroll_run_id               String?
  worksite_reporting           yes_no?
  wage_attachment_flag         yes_no?
  short_reporting_payroll_code String?
  company_effective_date       DateTime? // YYYYMMDD format
  kind_of_employer             kind_of_employer?
  naics_code                   String?
  reporting_payroll_code       String?

  // Define relations (only in this model)
  file_header                     file_header                       @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)
  company_disbursement_banks      company_disbursement_banks?       @relation()
  company_cash_care_banks         company_cash_care_banks?          @relation()
  company_general_ledger          company_general_ledger?           @relation()
  secondary_company_groups        secondary_company_groups?         @relation()
  company_address                 company_address?                  @relation()
  company_mailing_address         company_mailing_address?          @relation()
  company_tax                     company_tax[]
  company_workers_comp_codes      company_workers_comp_codes[]
  payroll_tax_detail              payroll_tax_detail[]
  payroll_tax_user_defined_fields payroll_tax_user_defined_fields[]
  payroll_trailer                 payroll_trailer?                  @relation()

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("payroll_header")
}

model company_disbursement_banks {
  payroll_code String
  check_date   DateTime

  record_type String @default("1")
  sub_type    String @default("01")

  // Payroll Tax Disbursement Bank Fields
  payroll_tax_disbursement_ach_point      String?
  payroll_tax_disbursement_bank_name      String?
  payroll_tax_disbursement_routing_number String?
  payroll_tax_disbursement_account_number String?

  // Wage Attachment Disbursement Bank Fields
  wage_attachment_disbursement_ach_point      String?
  wage_attachment_disbursement_bank_name      String?
  wage_attachment_disbursement_routing_number String?
  wage_attachment_disbursement_account_number String?

  // Foreign Key Relation
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date])

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("company_disbursement_banks")
}

model company_cash_care_banks {
  payroll_code String
  check_date   DateTime

  record_type String @default("1")
  sub_type    String @default("02")

  // Payroll Tax Cash Care Bank Fields
  payroll_tax_bank_name      String?
  payroll_tax_routing_number String? // Stored as String to preserve leading zeros
  payroll_tax_account_number String?
  payroll_tax_account_type   bank_account_type?
  payroll_tax_draft_days     Int?

  // Wage Attachment Cash Care Bank Fields
  wage_attachment_bank_name      String?
  wage_attachment_routing_number String? // Stored as String to preserve leading zeros
  wage_attachment_account_number String?
  wage_attachment_account_type   bank_account_type?
  wage_attachment_draft_days     Int? // Number of days before cash is impounded

  // Cash Care ACH Bank Fields
  cash_care_ach_bank_destination String?

  // Foreign Key Relation
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("company_cash_care_banks")
}

model company_general_ledger {
  payroll_code String
  check_date   DateTime

  record_type String @default("1")
  sub_type    String @default("05")

  chart_of_accounts_code        String?
  chart_of_accounts_description String?
  company_code                  String?
  variance_payroll_code         String?
  gl_payroll_code               String?

  // Foreign Key Relation (One-to-One)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("company_general_ledger")
}

model secondary_company_groups {
  payroll_code String
  check_date   DateTime

  record_type String @default("1")
  sub_type    String @default("10")

  // Secondary Company Group – Type 1
  company_group_type_1 String?
  company_group_name_1 String?

  // Secondary Company Group – Type 2
  company_group_type_2 String?
  company_group_name_2 String?

  // Secondary Company Group – Type 3
  company_group_type_3 String?
  company_group_name_3 String?

  // Secondary Company Group – Type 4
  company_group_type_4 String?
  company_group_name_4 String?

  // Foreign Key Relation (One-to-One)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("secondary_company_groups")
}

model company_address {
  payroll_code String
  check_date   DateTime

  record_type String @default("2")
  sub_type    String @default("01")

  company_dba    String?
  address_line_1 String?
  address_line_2 String?
  city           String?
  state_code     String?
  zip_code       String?
  country_code   country_code?
  psd_code       String? // Pennsylvania Political Division Code  

  // Contact Information
  first_name       String?
  middle_initial   String?
  last_name        String?
  area_code        String?
  telephone_number String?
  extension        String?
  fax_area_code    String?
  fax_number       String?
  email_address    String?
  in_care_of       String?

  // Foreign Key Relation (One-to-One)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("company_address")
}

model company_mailing_address {
  payroll_code String
  check_date   DateTime

  record_type String @default("2")
  sub_type    String @default("02")

  company_dba    String?
  address_line_1 String?
  address_line_2 String?
  city           String?
  state_code     String?
  zip_code       String?
  country_code   country_code?
  route_code     String? // Delivery route code for barcoding mailing  

  // Contact Information
  first_name       String?
  middle_initial   String?
  last_name        String?
  area_code        String?
  telephone_number String?
  extension        String?
  fax_area_code    String?
  fax_number       String?
  email_address    String?

  // Foreign Key Relation (One-to-One)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("company_mailing_address")
}

model company_tax {
  payroll_code String
  tax_code     String
  check_date   DateTime // YYYYMMDD format

  record_type String @default("2")
  sub_type    String @default("10")

  work_out_of_state_flag yes_no?
  effective_date         DateTime? // YYYYMMDD format  
  company_tax_status     company_tax_status?
  ein_type               ein_type?
  ein                    String?

  // Tax Rates
  tax_rate          String? // Employer tax rate (stored as a string to preserve decimal places)
  tax_rate_2        String? // Second employer tax rate
  expanded_tax_rate String? // Additional decimal precision tax rate

  // Payment Details
  payment_frequency String?
  payment_method    payment_method?
  eft_password      String? // PIN/password assigned by authority  
  reference_ein     String? // Company identifier required for certain tax setups  
  county_code       String? // County-specific code for unemployment tax in some states  

  // Service Level & Returns
  company_tax_service_level   company_tax_service_level?
  mark_all_returns_final      yes_no?
  final_return_effective_date DateTime? // YYYYMMDD format  

  // Foreign Key Relation (One-to-Many)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, tax_code, check_date]) // Composite Primary Key
  @@map("company_tax")
}

model company_workers_comp_codes {
  payroll_code String
  tax_code     String
  check_date   DateTime // YYYYMMDD format

  record_type String @default("2")
  sub_type    String @default("11")

  effective_date DateTime? // YYYYMMDD format  
  class_code     String? // Numeric but stored as String to preserve leading zeros  
  rate           String? // Workers' Comp rate (stored as String to preserve decimal places)  

  // Foreign Key Relation (One-to-Many)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, tax_code, check_date]) // Composite Primary Key
  @@map("company_workers_comp_codes")
}

model payroll_tax_detail {
  payroll_code String
  tax_code     String
  check_date   DateTime // YYYYMMDD format

  record_type String @default("5")
  sub_type    String @default("00")

  work_out_of_state_flag yes_no?
  ein                    String? // Agency-assigned EIN for the tax  

  // Tax Information
  tax_rate      String? // Tax rate (stored as a string to preserve decimal places)
  tax           Decimal? // Tax liability  
  taxable_wages Decimal? // Taxable wages  
  gross_wages   Decimal? // Gross wages  

  // Employee Information
  employee_count                       Int?
  employee_count_sign                  sign?
  exempt_overtime_wages_employee_count Int?
  liability_trace_id                   String? // Used to trace payroll tax liability in GL  

  // Additional Fields
  exempt_wages      Decimal?
  wc_class_code     String? // Workers' Compensation class code (Canada)  
  payroll_frequency payroll_frequency?
  expanded_tax_rate String? // Additional decimal precision tax rate  

  // Foreign Key Relation (One-to-Many)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, tax_code, check_date]) // Composite Primary Key
  @@map("payroll_tax_detail")
}

model payroll_tax_user_defined_fields {
  payroll_code String
  tax_code     String
  check_date   DateTime // YYYYMMDD format

  record_type String @default("5")
  sub_type    String @default("10")

  work_out_of_state_flag yes_no?

  // User Defined Fields
  user_field_1 String?
  user_field_2 String?
  user_field_3 String?
  user_field_4 String?
  user_field_5 String?
  user_field_6 String?
  user_field_7 String?
  user_field_8 String?

  // Foreign Key Relation (One-to-Many)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, tax_code, check_date]) // Composite Primary Key
  @@map("payroll_tax_user_defined_fields")
}

model payroll_trailer {
  payroll_code String // Ensure one-to-one relationship
  check_date   DateTime // YYYYMMDD format

  record_type String @default("8")
  sub_type    String @default("99")

  record_count Int? // Number of records in the payroll transaction set, including the header and trailer  
  tax_total    Decimal? // Total amount of taxes in the payroll transaction set  

  // Foreign Key Relation (One-to-one)
  payroll_header payroll_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("payroll_trailer")
}

model file_trailer {
  payroll_code String // Ensure one-to-one relationship
  check_date   DateTime // YYYYMMDD format

  record_type String @default("9")
  sub_type    String @default("99")
  // the file should end with 10 9s

  record_count Int? // Number of records in the file, including the header and trailer  
  tax_total    Decimal? // Total amount of taxes in the file  

  // Foreign Key Relation (One-to-One)
  file_header file_header @relation(fields: [payroll_code, check_date], references: [payroll_code, check_date], onDelete: Cascade)

  @@id([payroll_code, check_date]) // Composite Primary Key
  @@map("file_trailer")
}

model cafeteria_plan_benefits {
  nanoid                             String             @id /// @kysely(CompanyNanoid)
  cafeteria_plan_benefits            futa_exemption?    @default(not_applicable)
  group_term_life_insurance          futa_exemption?    @default(not_applicable)
  dependent_care_assistance          futa_exemption?    @default(not_applicable)
  business_expense_reimbursement     futa_exemption?    @default(not_applicable)
  employer_contribution_401k         futa_exemption?    @default(not_applicable)
  employer_contribution_sep_ira      futa_exemption?    @default(not_applicable)
  employer_contribution_simple       futa_exemption?    @default(not_applicable)
  accident_health_insurance_premiums futa_exemption?    @default(not_applicable)
  sick_pay                           futa_exemption?    @default(not_applicable)
  workers_compensation               futa_exemption?    @default(not_applicable)
  payments_to_family_employees       futa_exemption?    @default(not_applicable)
  payments_to_hospital_interns       futa_exemption?    @default(not_applicable)
  payments_to_hospital_patients      futa_exemption?    @default(not_applicable)
  payments_to_general_partnership    futa_exemption?    @default(not_applicable)
  state_govt_employee_salaries       futa_exemption?    @default(not_applicable)
  payments_to_election_workers       futa_exemption?    @default(not_applicable)
  supplemental_unemployment_benefits futa_exemption?    @default(not_applicable)
  nonqualified_deferred_comp         futa_exemption?    @default(not_applicable)
  meals_furnished_in_kind            futa_exemption?    @default(not_applicable)
  qualified_moving_expense           futa_exemption?    @default(not_applicable)
  hsa                                futa_exemption?    @default(not_applicable)
  exempt_501c3_organization          futa_exemption?    @default(not_applicable)
  employee_stock_purchase_plan       futa_exemption?    @default(not_applicable)
  non_taxable_fringe_payments        futa_exemption?    @default(not_applicable)
  public_transportation_non_tax      futa_exemption?    @default(not_applicable)
  wc_housing_employment_condition    futa_exemption?    @default(not_applicable)
  chaplain_housing                   futa_exemption?    @default(not_applicable)
  clergy_housing_poverty_vow         futa_exemption?    @default(not_applicable)
  foreign_source_income              futa_exemption?    @default(not_applicable)
  student_exempt                     futa_exemption?    @default(not_applicable)

  // Other Fields
  company_group_name           String?                  
  agent_client_type            agent_client_type?       @default(none)
  filer_944                    yes_no?                  @default(no)  
  quarterly_wage_reporting     yes_no?                  @default(no)  
  year_end_employee_filing     yes_no?                  @default(no)  
  cash_service_level           cash_service_level?      @default(full)
  payroll_run_id               String?
  worksite_reporting           yes_no?                  @default(no)  
  wage_attachment_flag         yes_no?                  @default(no)
  company_effective_date       DateTime? 
  kind_of_employer             kind_of_employer?        @default(none)
  naics_code                   String?                  @default("541214")
}

// Enum for Payroll Frequency
enum payroll_frequency {
  weekly // W - Weekly
  bi_weekly // B - Bi-Weekly
  semi_monthly // S - Semi-Monthly
  monthly // M - Monthly
}

// Enum for Employee Count Sign
enum sign {
  positive // +
  negative // -
}

// Enum for Country Codes
enum country_code {
  US // United States
  CA // Canada
  MX // Mexico
}

// Enum for Company Status
enum company_status {
  active // A
  inactive // I
}

// Enum for Service Level
enum service_level {
  full_service // F
  return_only // R
  balance_only // B
}

// Enum for FEIN Type
enum fein_type {
  applied_for // Y
  registered // N
  common_pay_parent // P
  common_pay_child // C
}

// Enum for Bank Account Type
enum bank_account_type {
  checking // C
  savings // S
}

// Enum for FUTA Exempt Categories (Y/N fields)
enum futa_exemption {
  applicable // Y
  not_applicable // N
}

// Enum for Agent/Client Type
enum agent_client_type {
  agent_3504 // 1
  cpeo_3511a // 2
  cpeo_3511c // 3
  cpeo_3504 // 4
  cpeo_mixed // 0
  cpeo_client_3511a // A
  cpeo_client_3511c // B
  cpeo_client_31_3504 // C
  other_third_party // T
  none // N
}

// Enum for Yes/No Fields
enum yes_no {
  yes // Y
  no // N
}

// Enum for Cash Service Level
enum cash_service_level {
  full // F
  variances_only // V
}

// Enum for Kind of Employer
enum kind_of_employer {
  federal_government // F
  state_local_government // S
  tax_exempt // T
  state_local_tax_exempt // Y
  none // N
}

// Enum for Company Tax Status
enum company_tax_status {
  active // A
  inactive // I
}

// Enum for EIN Type
enum ein_type {
  applied_for // Y
  registered // N
  reimbursable // I
  same_as_fein // F
  exempt // E
  common_pay_parent // P
  common_pay_child // C
  michigan_501 // 5
}

// Enum for Payment Method
enum payment_method {
  check // 01 - Check
  eft_credit // 11 - EFT Credit
  eft_debit // 21 - EFT Debit
  eft_debit_touch_tone // 22 - EFT Debit TouchTone
  eft_debit_online // 23 - EFT Debit Online Hand Key
  eft_debit_file // 24 - EFT Debit in a Standalone Payment File
  eft_debit_return // 25 - EFT Debit within the Return e-File
}

// Enum for Company Tax Service Level
enum company_tax_service_level {
  return_only // R - Return only; only tax returns will be produced
  balance_only // B - Balance only; no payments or returns will be produced
}

// end Master Tax Models
