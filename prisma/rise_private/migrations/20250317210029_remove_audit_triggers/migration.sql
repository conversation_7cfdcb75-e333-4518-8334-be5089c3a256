DROP TRIGGER IF EXISTS rise_audit_rise_private_addresses_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_addresses_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_company_contacts_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_company_contacts_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_company_owners_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_company_owners_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_document_signers_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_document_signers_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_documents_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_documents_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_entity_risk_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_entity_risk_update;
DROP TRIGGER IF EXISTS rise_audit_rise_private_entity_risk_delete;
DROP TRIGGER IF EXISTS rise_audit_rise_private_private_data_insert;
DROP TRIGGER IF EXISTS rise_audit_rise_private_private_data_update;