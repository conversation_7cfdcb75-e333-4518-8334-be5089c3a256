-- AlterTable
ALTER TABLE `users_withdraw_account` MODIFY `ramp` ENUM('domestic_usd', 'international_exchange', 'international_usd', 'international_usd_default', 'europe', 'gbp', 'ngn', 'arbitrum_wallet', 'avalanche_wallet', 'base_wallet', 'ethereum_wallet', 'optimism_wallet', 'polygon_wallet', 'coinbase', 'coinbase_wallet', 'token_swap') NOT NULL,
    MODIFY `network` ENUM('mainnet', 'ethereum', 'arbitrum', 'optimism', 'polygon', 'base', 'avalanche') NULL,
    MODIFY `payment_handler_network` ENUM('mainnet', 'ethereum', 'arbitrum', 'optimism', 'polygon', 'base', 'avalanche') NULL;
