{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "scripts: document-builder", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "documentBuilder"], "cwd": "${workspaceFolder}/packages/scripts", "program": "${workspaceFolder}/packages/scripts/src/documentBuilder.ts", "autoAttachChildProcesses": true, "restart": true, "sourceMaps": true, "stopOnEntry": false, "console": "integratedTerminal"}, {"name": "debug", "type": "node", "request": "launch", "program": "${workspaceFolder}/apps/dev/index.ts", "runtimeExecutable": "tsx", "runtimeArgs": ["--env-file-if-exists=${workspaceFolder}/.base.env", "--env-file-if-exists=${workspaceFolder}/.local.env", "--import", "${workspaceFolder}/packages/backend/src/opentelemetry/index.mjs"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"]}, {"name": "debug script: <PERSON><PERSON>", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "script", "payslip"]}]}