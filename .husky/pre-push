#!/bin/bash

# Pre-push hook to check and publish @riseworks/contracts package if needed
set -e

echo "🔍 Checking for changes in contracts package..."

# Store original directory
ORIGINAL_DIR=$(pwd)

# Check if there are any changes in the contracts package since the last commit
CONTRACTS_CHANGED=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep "^packages/contracts/src/" || true)

# If no changes in recent commit, check staged changes
if [ -z "$CONTRACTS_CHANGED" ]; then
    CONTRACTS_CHANGED=$(git diff --cached --name-only | grep "^packages/contracts/src/" || true)
fi

if [ -z "$CONTRACTS_CHANGED" ]; then
    echo "✅ No changes detected in contracts package, skipping version bump and publish"
else
    echo "📦 Changes detected in contracts package:"
    echo "$CONTRACTS_CHANGED"

    # Navigate to contracts directory
    cd packages/contracts || {
        echo "❌ Error: Could not navigate to packages/contracts directory"
        exit 1
    }

    # Get current local version
    LOCAL_VERSION=$(node -p "require('./package.json').version" 2>/dev/null) || {
        echo "❌ Error: Could not read version from package.json"
        exit 1
    }
    echo "📍 Current local version: $LOCAL_VERSION"

    # Try to get published version with timeout, but don't fail if it doesn't work
    echo "🔍 Checking if version needs to be bumped..."
    SHOULD_BUMP=true

    # Try to check if this version already exists by attempting to view it
    if timeout 15 npm view @riseworks/contracts@$LOCAL_VERSION version >/dev/null 2>&1; then
        echo "📍 Version $LOCAL_VERSION already exists in registry"
        SHOULD_BUMP=false
    else
        echo "📍 Version $LOCAL_VERSION not found in registry, will bump and publish"
    fi

    if [ "$SHOULD_BUMP" = true ]; then
        # Bump patch version
        echo "🔄 Bumping patch version..."
        npm version patch --no-git-tag-version || {
            echo "❌ Error: Failed to bump version"
            cd "$ORIGINAL_DIR"
            exit 1
        }

        NEW_VERSION=$(node -p "require('./package.json').version")
        echo "✨ Bumped version to: $NEW_VERSION"

        # Publish to GitHub Package Registry
        echo "📤 Publishing @riseworks/contracts@$NEW_VERSION..."
        if npm publish 2>&1 | tee /tmp/npm_publish.log; then
            echo "✅ Successfully published @riseworks/contracts@$NEW_VERSION"

            # Go back to root and commit the version change
            cd "$ORIGINAL_DIR"
            git add packages/contracts/package.json
            git commit -m "chore: bump @riseworks/contracts to $NEW_VERSION" || {
                echo "⚠️  Warning: Could not commit version bump (may already be committed)"
            }
        else
            if grep -q "Cannot publish over existing version" /tmp/npm_publish.log; then
                echo "⚠️  Version $NEW_VERSION already exists in registry, skipping publish"
            else
                echo "❌ Error: Failed to publish package"
                cat /tmp/npm_publish.log
                cd "$ORIGINAL_DIR"
                exit 1
            fi
        fi
    else
        echo "✅ No version bump needed, package already published"
    fi

    # Return to root directory
    cd "$ORIGINAL_DIR"
fi

# Run existing typecheck
echo "🔍 Running type checks..."
pnpm typecheck-all || {
    echo "❌ Error: Type check failed"
    exit 1
}

echo "✅ Pre-push checks completed successfully!"