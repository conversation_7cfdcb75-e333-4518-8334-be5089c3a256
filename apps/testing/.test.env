NODE_ENV=localhost
MIGRATION_ENV=test

# TEST DATABASE - Completely separate from development
DB_HOST=localhost
DB_PORT=3308
DB_USER=root
DB_PW=testpass
DB_SCHEMA=rise
DB_CONNECTION_LIMIT=10
DB_CONNECTION_TIMEOUT=60000

# DATABASE_URL for Prisma (using test database)
DATABASE_URL="mysql://root:testpass@localhost:3308/rise"

# Database secrets for getSecrets function
B2B_API_DATABASE_USER=root
B2B_API_DATABASE_PW=testpass
GCP_TF_SERVER_CA=test
GCP_TF_CLIENT_KEY=test
GCP_TF_CLIENT_CERT=test

# APP
HTTP_PORT=8080

# REDIS
REDIS_URI=redis://localhost:6379

# OPENTELEMETRY
JAEGER_URL=http://localhost:4318/v1/traces
OTEL_SERVICE_NAME=rise-test

# BLOCKCHAIN
ETH_RPC_URL=http://localhost:8547
METAMASK_API_ENDPOINT=http://localhost:8547

# GOOGLE (mocked in tests)
GOOGLE_PROJECT_ID=test
GOOGLE_LOCATION_ID=test
GOOGLE_PROJECT_NUMBER=test
GOOGLE_OIDC_EMAIL=<EMAIL>
GOOGLE_APPLICATION_CREDENTIALS=test
K_SERVICE=test
GOOGLE_RECAPTCHA=test
GOOGLE_DRIVE_COMPANY_DOCUMENTS_FOLDER_ID=test
APP_DOMAIN=http://localhost:8080
DOMAIN=localhost
SEGMENT_SOURCE=test
AVATARS_BUCKET=test
INVOICE_ATTACHMENTS_BUCKET=test
AVATARS_BUCKET_URL=test
INVOICE_ATTACHMENTS_BUCKET_URL=test

# Test secrets (all mocked)
route-fusion-entity=test:test
unit21-api-key=test
route-fusion-entity-usd=test:test
rise-secret-key=test-secret-key
chainalysis-api-key=test
inngest-event-key=test
integration-workspace-key=test
integration-workspace-secret=test
BITCOIN_KEYPAIR=test:test 