# Testing Package

Comprehensive test suites and utilities for the Rise API monorepo using Vitest, with structured patterns for database entities and reusable mock generators.

## Directory Structure

```
src/
├── prisma-entities/          # Database entity integration tests  
├── mocks/                    # Type-safe test data generators
├── utils/                    # Testing utilities and helpers
├── setup.ts                 # Global test setup
└── googleEnv.ts             # Environment configuration
```

## Quick Start

```bash
# Run all tests
npm test

# Run specific test files
npm test -- webhook-endpoints.entity.test.ts

# Run with coverage
npm test -- --coverage
```

## Core Principles

1. **Type Safety**: All mocks use proper TypeScript types from database schema
2. **AAA Pattern**: All tests follow Arrange, Act, Assert structure
3. **DRY Principle**: Reusable generators prevent code duplication
4. **Database Integration**: Tests run against real database connections

## Writing Tests

### Basic Test Structure

```typescript
import { describe, expect, test, afterEach } from 'vitest'
import { db } from 'db'
import { mockWebhookEndpoint } from '../mocks/index.js'

describe('webhook_endpoints', () => {
  afterEach(async () => {
    await db.deleteFrom('rise.webhook_endpoints').execute()
  })

  test('should create webhook endpoint successfully', async () => {
    // Arrange
    const webhookData = mockWebhookEndpoint({
      url: 'https://test.example.com/webhook'
    })
    
    // Act
    const result = await db
      .insertInto('rise.webhook_endpoints')
      .values(webhookData)
      .execute()
    
    // Assert
    expect(result).toBeDefined()
  })
})
```

### Parameterized Tests

Use `test.each` with table format for similar test cases:

```typescript
test.each`
  fieldName             | invalidData
  ${'nanoid'}           | ${() => ({ /* missing nanoid */ })}
  ${'company_nanoid'}   | ${() => ({ /* missing company_nanoid */ })}
`('should fail when missing required $fieldName field', async ({ invalidData }) => {
  const webhook = invalidData()
  await expect(
    db.insertInto('rise.webhook_endpoints').values(webhook).execute()
  ).rejects.toThrow()
})
```

## Best Practices

- **Use centralized mock generators** from `/mocks` directory
- **Clean up test data** in `afterEach()` hooks in dependency order
- **Override only necessary fields** for specific test scenarios
- **Test both success and failure scenarios**
- **Use descriptive test names** that explain the scenario

## Adding New Entity Tests

1. Create mock generators in `/mocks` directory
2. Add entity tests in `prisma-entities/` directory
3. Follow existing naming conventions (`entity-name.entity.test.ts`)
4. Export from `/mocks/index.ts`

## Resources

- [Entity Testing Guide](./src/prisma-entities/README.md) - Detailed patterns and examples
- [Mocks Documentation](./src/mocks/README.md) - Mock generation guide
- [Vitest Documentation](https://vitest.dev/) - Test runner documentation 