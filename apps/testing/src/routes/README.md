# API Route Testing Guide

Integration tests for API routes, verifying request handling against Zod contracts. This includes input validation (400 errors), "Not Implemented" stubs (501 errors), and successful operations (200 OK) with database interactions.

## Core Principles

- **Contract-Driven**: Tests leverage Zod schemas from the `contracts` package.
- **AAA Pattern**: All tests clearly follow the Arrange, Act, Assert structure.
- **Focused Scenarios**: Individual tests target specific responses (501, 400, 200).
- **Helper Functions**: Route-specific `exec` functions simplify API client calls.
- **State Management**: Use `beforeEach` for payload setup and `afterEach` for database cleanup in state-changing tests.
- **Verify Database State**: For state-changing operations, assert the expected state in the database.
- **Ensure Test Idempotency**: Tests should produce the same results when run multiple times, achieved through proper setup and teardown.

## Database State Verification

For tests that alter the database state, it's essential to verify that the expected changes have occurred. This can be done by querying the database after the operation and asserting the expected results.

### Example: Verifying Database State After Creation

In this example, we will create a new resource and verify that it has been correctly inserted into the database.

```typescript
import { beforeAll, afterEach, describe, expect, test } from 'vitest';
import { createFakeClient } from 'backend/src/testing/client.js'; // Adjust to your client path
import { app } from 'dev/app.js'; // Adjust to your app path
import { db } from 'db'; // Import your database client

describe('POST /your-resource', () => {
  let client: ReturnType<typeof createFakeClient>;
  let createdResourceId: string | null = null;

  beforeAll(async () => {
    client = createFakeClient(app);
  });

  afterEach(async () => {
    // Clean up any records created by the test
    if (createdResourceId) {
      await db.deleteFrom('your_table').where('id', '=', createdResourceId).execute();
      createdResourceId = null;
    }
  });

  test('should create the resource and verify it in the database', async () => {
    // Arrange
    const newResourcePayload = {
      company_id: '22222222-2222-2222-2222-222222222222',
      url: 'https://new.valid.url/endpoint',
      events: ['another.event'],
      secret: 'another-valid-secret-key-456',
      is_active: true,
    };

    // Act
    const response = await client['/your-resource'].post({ body: newResourcePayload });
    
    // Assert
    expect(response.status).toBe(200);
    expect(response.data.success).toBe(true);
    
    // Store ID for verification
    createdResourceId = response.data.data.id;

    // Verify the resource exists in the database
    const dbRecord = await db.selectFrom('your_table').where('id', '=', createdResourceId).executeTakeFirst();
    expect(dbRecord).toBeDefined();
    expect(dbRecord).toMatchObject(newResourcePayload);
  });
});
```

### Key Points:
- **Database State Verification**: After performing operations that change the database, always verify the expected state.
- **Cleanup**: Use `afterEach` hooks to ensure that any created records are removed after each test, maintaining test isolation.

## Test Structure & Key Patterns

API route tests typically group all tests for a specific HTTP endpoint within a `describe` block. Below is a condensed example illustrating common patterns:

```typescript
import { beforeAll, describe, expect, test } from 'vitest';
import { createFakeClient } from 'backend/src/testing/client.js'; // Adjust to your client path
import { app } from 'dev/app.js'; // Adjust to your app path

describe('POST /your-resource', () => {
  let client: ReturnType<typeof createFakeClient>;

  beforeAll(async () => {
    client = createFakeClient(app);
  });

  // exec helper simplifies making requests to this specific endpoint
  const exec = async (payload: object) => {
    try {
      return await client['/your-resource'].post({ body: payload });
    } catch (error: any) {
      return error.response || { status: error.status, data: error.data };
    }
  };

  test('should return 501 Not Implemented', async () => {
    const validPayload = { /* provide a valid payload */ };
    const response = await exec(validPayload);
    expect(response.status).toBe(501);
    expect(response.data).toMatchObject({ data: 'Not Implemented', success: false });
  });

  // Additional tests for input validation and successful operations can follow the same pattern
});
```

### Writing New Route Tests

1. Create `your-resource.routes.test.ts` in the `src/routes/` directory.
2. Import testing utilities (Vitest), your fake client, app instance, and relevant Zod contract types.
3. For each HTTP endpoint of the resource:
   * Create a top-level `describe` block (e.g., `describe('POST /your-resource', () => { ... });`).
   * Implement an `exec` helper function specific to that endpoint.
   * Add `test` cases for:
     * 501 Not Implemented (if applicable).
     * Input validation (400 errors) in a nested `describe` block, using `beforeEach` to reset a `testPayload`.
     * Successful operations (200 OK), including database assertions and `afterEach` for cleanup if state changes.
4. Strictly follow the AAA (Arrange, Act, Assert) pattern with clear comments for each part of your test.

This revised guide aims to be more direct and example-driven while covering the essential practices for your API route testing. 