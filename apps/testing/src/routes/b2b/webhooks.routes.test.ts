import {
  type CompanyNanoid,
  type TeamNanoid,
  type UserNanoid,
  type WebhookEndpointNanoid,
  webhookEventTypes,
} from '@riseworks/contracts/src/brands.js'
import type {
  RegisterWebhookInput,
  RegisterWebhookResponse,
  TestWebhookInput,
  UpdateWebhookInput,
} from '@riseworks/contracts/src/routes/b2b/webhook.js'
import {
  createFakeClient,
  generateAuthorizationHeader,
} from 'backend/src/testing/client.js'
import { db } from 'db'
import { app } from 'dev/app.js'
import {
  generateCompany,
  generateCompanyNanoid,
  generateTeam,
  generateTeamNanoid,
  generateTeamRiseId,
  generateUser,
  generateUserNanoid,
  generateWebhookDelivery,
  generateWebhookDeliveryNanoid,
  generateWebhookEndpoint,
  generateWebhookEndpointNanoid,
  generateWebhookEvent,
  generateWebhookEventNanoid,
} from 'src/mocks/index.js'
import { decrypt } from 'utils/src/common/crypter.js'
import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  test,
} from 'vitest'
import { mockGetSecrets, parseResponseBody } from '../../utils/index.js'

// Mock getSecrets to avoid real secret fetching in tests
mockGetSecrets()

// Helper function to create a test user in the database
const createTestUser = async (userNanoid: UserNanoid) => {
  const { userEntity, userData } = generateUser({ nanoid: userNanoid })

  // Insert into rise_entities first
  await db.insertInto('rise.rise_entities').values(userEntity).execute()

  // Insert into users_data
  await db
    .insertInto('rise.users_data')
    .values(userData as any)
    .execute()
}

// Helper function to create a test company in the database
const createTestCompany = async (
  companyNanoid: CompanyNanoid,
  createdBy: UserNanoid,
) => {
  // Get the user entity to get the user's riseid
  const userEntity = await db
    .selectFrom('rise.rise_entities')
    .selectAll()
    .where('nanoid', '=', createdBy)
    .where('type', '=', 'user')
    .executeTakeFirst()

  if (!userEntity) {
    throw new Error(`User entity not found for ${createdBy}`)
  }

  const { companyEntity, companyData } = generateCompany({
    nanoid: companyNanoid,
    created_by: createdBy,
  })

  // Fix the company entity to be owned by the user, not self-referential
  const correctedCompanyEntity = {
    ...companyEntity,
    parent_riseid: userEntity.riseid, // Company should be owned by user
  }

  // Insert into rise_entities first
  await db
    .insertInto('rise.rise_entities')
    .values(correctedCompanyEntity)
    .execute()

  // Insert into companies_data
  await db
    .insertInto('rise.companies_data')
    .values(companyData as any)
    .execute()

  // Create an 'org_admin' relationship that gives the user access to the company
  await db
    .insertInto('rise.rise_entities')
    .values({
      riseid: generateTeamRiseId(),
      parent_riseid: userEntity.riseid,
      type: 'org_admin',
      nanoid: generateUserNanoid(),
      avatar: '',
    })
    .execute()
}

// Helper function to create a test webhook directly in the database
const createTestWebhook = async (
  webhookNanoid: WebhookEndpointNanoid,
  companyNanoid: CompanyNanoid,
  createdBy: UserNanoid,
  overrides: {
    url?: string
    events?: string[]
    secret?: string
    is_active?: boolean
    is_removed?: boolean
    team_nanoid?: TeamNanoid | null
  } = {},
) => {
  const webhookData = generateWebhookEndpoint({
    nanoid: webhookNanoid,
    company_nanoid: companyNanoid,
    created_by: createdBy,
    last_modified_by: createdBy,
    url: overrides.url,
    secret_encrypted: overrides.secret,
    events: overrides.events,
    is_active: overrides.is_active,
    team_nanoid:
      overrides.team_nanoid !== undefined ? overrides.team_nanoid : null,
    is_removed: overrides.is_removed ?? false,
  })

  await db.insertInto('rise.webhook_endpoints').values(webhookData).execute()
}

// Helper function to create a test team in the database
const createTestTeam = async (
  teamNanoid: TeamNanoid,
  companyNanoid: CompanyNanoid,
  createdBy: UserNanoid,
) => {
  const { teamEntity, teamData } = generateTeam({
    nanoid: teamNanoid,
    company_nanoid: companyNanoid,
    created_by: createdBy,
  })

  // Insert into rise_entities first
  await db.insertInto('rise.rise_entities').values(teamEntity).execute()

  // Insert into teams_data
  await db
    .insertInto('rise.teams_data')
    .values(teamData as any)
    .execute()

  // Get the user entity to get the user's riseid
  const userEntity = await db
    .selectFrom('rise.rise_entities')
    .selectAll()
    .where('nanoid', '=', createdBy)
    .where('type', '=', 'user')
    .executeTakeFirst()

  if (!userEntity) {
    throw new Error(`User entity not found for ${createdBy}`)
  }

  // For getTeamNanoidsForUser to work, we need to create a team role relationship
  await db
    .insertInto('rise.rise_entities')
    .values({
      riseid: teamEntity.riseid,
      parent_riseid: userEntity.riseid,
      type: 'team_admin',
      nanoid: `tr-${teamNanoid.slice(3)}` as any,
      avatar: '',
    })
    .execute()
}

// Helper function to create a test event in the database
const createTestEvent = async (overrides = {}) => {
  const event = generateWebhookEvent(overrides)
  await db.insertInto('rise.webhook_events').values(event).execute()
  return event
}

// Helper function to create a test delivery in the database
const createTestDelivery = async (
  webhookNanoid: WebhookEndpointNanoid,
  eventNanoid: any,
  overrides = {},
) => {
  const delivery = generateWebhookDelivery(
    webhookNanoid,
    eventNanoid,
    overrides,
  )
  await db.insertInto('rise.webhook_deliveries').values(delivery).execute()
  return delivery
}

describe('B2B Webhook Routes', () => {
  let client: ReturnType<typeof createFakeClient>

  beforeAll(async () => {
    client = createFakeClient(app)
  })

  afterEach(async () => {
    // Clean up in correct order to avoid foreign key constraint violations
    await db.deleteFrom('rise.webhook_deliveries').execute()
    await db.deleteFrom('rise.webhook_endpoints').execute()
    await db.deleteFrom('rise.companies_data').execute()
    await db.deleteFrom('rise.users_data').execute()
    await db.deleteFrom('rise.rise_entities').execute()
  })

  describe('POST /webhooks/register', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)
    })

    const exec = async (
      body: object,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/register'].post({
          body,
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully register a webhook with all fields', async () => {
      // Arrange
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://webhook.example.com/receive',
        events: [
          webhookEventTypes.PAYMENT_SENT,
          webhookEventTypes.DEPOSIT_RECEIVED,
        ],
        secret: 'my-super-secure-webhook-secret-key',
        is_active: true,
      }

      const webhookCountBefore = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .execute()

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(201)

      const responseBody = (await parseResponseBody(
        response,
      )) as RegisterWebhookResponse

      expect(responseBody.data).toMatchObject({
        webhook_nanoid: expect.any(String),
        company_nanoid: validBody.company_nanoid,
        url: validBody.url,
        events: validBody.events,
        is_active: validBody.is_active,
      })

      const webhooksAfter = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .execute()

      expect(webhooksAfter).toHaveLength(webhookCountBefore.length + 1)

      const createdWebhook = webhooksAfter.find(
        (webhook) => webhook.nanoid === responseBody.data.webhook_nanoid,
      )
      expect(createdWebhook).toMatchObject({
        nanoid: responseBody.data.webhook_nanoid,
        company_nanoid: validBody.company_nanoid,
        created_by: testUserNanoid,
        url: validBody.url,
        events: validBody.events,
        is_active: validBody.is_active,
      })

      expect(createdWebhook?.secret_encrypted).not.toBe(validBody.secret)
      expect(createdWebhook?.secret_encrypted).toMatch(/^[a-f0-9]+$/)
      expect(createdWebhook?.secret_encrypted.length).toBeGreaterThan(
        validBody.secret.length,
      )

      const decryptedSecret = decrypt({
        value: createdWebhook!.secret_encrypted,
      })
      expect(decryptedSecret).toBe(validBody.secret)
    })

    test('should register multiple webhooks for same company', async () => {
      // Arrange
      const webhook1: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://api1.example.com/webhook',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: 'webhook-1-secret-key-123',
      }

      const webhook2: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://api2.example.com/webhook',
        events: [webhookEventTypes.INVITE_ACCEPTED],
        secret: 'webhook-2-secret-key-456',
      }

      // Act
      const response1 = await exec(webhook1)
      const response2 = await exec(webhook2)

      // Assert
      expect(response1.status).toBe(201)
      expect(response2.status).toBe(201)

      const responseBody1 = (await parseResponseBody(
        response1,
      )) as RegisterWebhookResponse
      const responseBody2 = (await parseResponseBody(
        response2,
      )) as RegisterWebhookResponse
      expect(responseBody1.data.webhook_nanoid).not.toBe(
        responseBody2.data.webhook_nanoid,
      )

      const createdWebhooks = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('company_nanoid', '=', testCompanyNanoid)
        .execute()

      expect(createdWebhooks).toHaveLength(2)
    })

    test('should handle many events in array', async () => {
      // Arrange
      const manyEvents = Array.from({ length: 50 }, (_, i) => `event.${i}`) as [
        string,
        ...string[],
      ]
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://example.com/webhook',
        events: manyEvents,
        secret: 'valid-secret-key-16-chars',
      }

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = (await parseResponseBody(
        response,
      )) as RegisterWebhookResponse
      expect(responseBody.data.events).toEqual(manyEvents)
    })

    test('should successfully register a webhook with valid team', async () => {
      // Arrange
      const testTeamNanoid = generateTeamNanoid()
      await createTestTeam(testTeamNanoid, testCompanyNanoid, testUserNanoid)

      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        team_nanoid: testTeamNanoid,
        url: 'https://webhook.example.com/team',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: 'my-super-secure-webhook-secret-key',
        is_active: true,
      }

      const webhookCountBefore = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .execute()

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(201)

      const responseBody = (await parseResponseBody(
        response,
      )) as RegisterWebhookResponse

      expect(responseBody.data).toMatchObject({
        webhook_nanoid: expect.any(String),
        company_nanoid: validBody.company_nanoid,
        url: validBody.url,
        events: validBody.events,
        is_active: validBody.is_active,
      })

      const webhooksAfter = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .execute()

      expect(webhooksAfter).toHaveLength(webhookCountBefore.length + 1)

      const createdWebhook = webhooksAfter.find(
        (webhook) => webhook.nanoid === responseBody.data.webhook_nanoid,
      )
      expect(createdWebhook).toMatchObject({
        nanoid: responseBody.data.webhook_nanoid,
        company_nanoid: validBody.company_nanoid,
        team_nanoid: testTeamNanoid,
        created_by: testUserNanoid,
        url: validBody.url,
        events: validBody.events,
        is_active: validBody.is_active,
      })

      expect(createdWebhook?.secret_encrypted).not.toBe(validBody.secret)
      expect(createdWebhook?.secret_encrypted).toMatch(/^[a-f0-9]+$/)
      expect(createdWebhook?.secret_encrypted.length).toBeGreaterThan(
        validBody.secret.length,
      )

      const decryptedSecret = decrypt({
        value: createdWebhook!.secret_encrypted,
      })
      expect(decryptedSecret).toBe(validBody.secret)
    })

    test('should require authentication', async () => {
      // Arrange
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://example.com/webhook',
        events: ['event.created'],
        secret: 'a-secure-secret-phrase-min-16-chars',
        is_active: true,
      }

      // Act
      const response = await exec(validBody, false)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toEqual('User not logged in')
    })

    test('should return 403 when registering webhook for company user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)

      const webhookBody: RegisterWebhookInput = {
        company_nanoid: unauthorizedCompanyNanoid,
        url: 'https://webhook.example.com/unauthorized',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: 'my-super-secure-webhook-secret-key',
        is_active: true,
      }

      // Act
      const response = await exec(webhookBody)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when registering webhook for team user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedTeamNanoid = generateTeamNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestTeam(
        unauthorizedTeamNanoid,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
      )

      const webhookBody: RegisterWebhookInput = {
        company_nanoid: unauthorizedCompanyNanoid,
        team_nanoid: unauthorizedTeamNanoid,
        url: 'https://webhook.example.com/unauthorized-team',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: 'my-super-secure-webhook-secret-key',
        is_active: true,
      }

      // Act
      const response = await exec(webhookBody)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 404 when team does not exist', async () => {
      // Arrange
      const validBody: RegisterWebhookInput = {
        company_nanoid: generateCompanyNanoid(),
        url: 'https://example.com/webhook',
        events: ['event.created'],
        secret: 'a-secure-secret-phrase-min-16-chars',
      }

      const invalidUserNanoid = generateUserNanoid()

      // Act
      const response = await exec(validBody, true, invalidUserNanoid)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
    })

    test('should return 404 when company does not exist', async () => {
      // Arrange
      const nonExistentCompany = generateCompanyNanoid()
      const validBody: RegisterWebhookInput = {
        company_nanoid: nonExistentCompany,
        url: 'https://example.com/webhook',
        events: ['event.created'],
        secret: 'valid-secret-key-16-chars',
      }

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Company not found')
    })

    test('should return 404 for non-existent team', async () => {
      // Arrange
      const nonExistentTeam = generateTeamNanoid()
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        team_nanoid: nonExistentTeam,
        url: 'https://webhook.example.com/team',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: 'my-super-secure-webhook-secret-key',
        is_active: true,
      }

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Team not found')
    })

    describe('Input Validation (400 Bad Request)', () => {
      let testPayload: RegisterWebhookInput

      beforeEach(() => {
        testPayload = {
          company_nanoid: testCompanyNanoid,
          url: 'https://valid.url/endpoint',
          events: ['event.updated'],
          secret: 'this-is-a-valid-secret-key-123',
        }
      })

      test('should return 400 if company_nanoid is null', async () => {
        // Arrange
        testPayload.company_nanoid = null as any

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Expected string, received null at "company_nanoid"',
        )
      })

      test('should return 400 if company_nanoid is not a valid nanoid', async () => {
        // Arrange
        testPayload.company_nanoid = 'not-a-nanoid' as CompanyNanoid

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Invalid input: must start with "co-" at "company_nanoid"',
        )
      })

      test('should return 400 if url is null', async () => {
        // Arrange
        testPayload.url = null as any

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Expected string, received null at "url"',
        )
      })

      test('should return 400 if url is not a valid URL', async () => {
        // Arrange
        testPayload.url = 'not-a-valid-url'

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('Invalid url at "url"')
      })

      test('should return 400 if url is empty string', async () => {
        // Arrange
        testPayload.url = ''

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('Invalid url at "url"')
      })

      test('should return 400 if events is null', async () => {
        // Arrange
        testPayload.events = null as any

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Expected array, received null at "events"',
        )
      })

      test('should return 400 if events is empty array', async () => {
        // Arrange
        testPayload.events = [] as any

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Array must contain at least 1 element(s)',
        )
      })

      test('should return 400 if secret is null', async () => {
        // Arrange
        testPayload.secret = null as any

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Expected string, received null at "secret"',
        )
      })

      test('should return 400 if secret is too short', async () => {
        // Arrange
        testPayload.secret = 'short'

        // Act
        const response = await exec(testPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'String must contain at least 16 character(s) at "secret"',
        )
      })

      test('should return 400 if is_active is not boolean', async () => {
        testPayload.is_active = 'not-boolean' as any

        const response = await exec(testPayload)

        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Expected boolean, received string at "is_active"',
        )
      })
    })

    test('should encrypt webhook secret when storing in database', async () => {
      // Arrange
      const originalSecret = 'my-original-secret-key-123'
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://webhook.example.com/receive',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: originalSecret,
        is_active: true,
      }

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = (await parseResponseBody(
        response,
      )) as RegisterWebhookResponse

      // Check that the webhook was created
      const createdWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', responseBody.data.webhook_nanoid)
        .executeTakeFirst()

      expect(createdWebhook).toBeDefined()
      // The stored secret should be encrypted (different from original)
      expect(createdWebhook?.secret_encrypted).not.toBe(originalSecret)
      // The encrypted secret should be a hex string (longer than original)
      expect(createdWebhook?.secret_encrypted).toMatch(/^[a-f0-9]+$/)
      expect(createdWebhook?.secret_encrypted.length).toBeGreaterThan(
        originalSecret.length,
      )
    })

    test('should be able to decrypt stored webhook secret', async () => {
      // Arrange
      const originalSecret = 'my-decryptable-secret-key-123'
      const validBody: RegisterWebhookInput = {
        company_nanoid: testCompanyNanoid,
        url: 'https://webhook.example.com/receive',
        events: [webhookEventTypes.PAYMENT_SENT],
        secret: originalSecret,
        is_active: true,
      }

      // Act
      const response = await exec(validBody)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = (await parseResponseBody(
        response,
      )) as RegisterWebhookResponse

      // Get the stored encrypted webhook
      const createdWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', responseBody.data.webhook_nanoid)
        .executeTakeFirst()

      expect(createdWebhook).toBeDefined()

      // Decrypt the stored secret and verify it matches the original
      const decryptedSecret = decrypt({
        value: createdWebhook!.secret_encrypted,
      })
      expect(decryptedSecret).toBe(originalSecret)
    })
  })

  describe('PUT /webhooks/:webhook_nanoid', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://original.example.com/webhook',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'original-secret-key-123',
          is_active: true,
        },
      )
    })

    const exec = async (
      webhook_nanoid: string,
      body: object,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/{webhook_nanoid}'].put({
          params: { webhook_nanoid },
          body,
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should return 403 when updating team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      const updateBody = {
        url: 'https://updated.example.com/webhook',
        events: [webhookEventTypes.DEPOSIT_RECEIVED],
        secret: 'updated-secret-key',
        is_active: false,
      }

      // Act
      const response = await exec(
        teamWebhookId,
        updateBody,
        true,
        otherUserNanoid,
      )

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should successfully update all webhook fields', async () => {
      // Arrange
      const updateBody: UpdateWebhookInput = {
        url: 'https://updated.example.com/webhook',
        events: [
          webhookEventTypes.PAYMENT_SENT,
          webhookEventTypes.DEPOSIT_RECEIVED,
          webhookEventTypes.INVITE_ACCEPTED,
        ],
        secret: 'updated-secret-key-456',
        is_active: false,
      }

      // Act
      const response = await exec(testWebhookId, updateBody)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data).toMatchObject({
        webhook_nanoid: testWebhookId,
        url: updateBody.url,
        events: updateBody.events,
        is_active: updateBody.is_active,
      })

      const updatedWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .executeTakeFirst()

      expect(updatedWebhook).toMatchObject({
        nanoid: testWebhookId,
        url: updateBody.url,
        events: updateBody.events,
        is_active: updateBody.is_active,
        last_modified_by: testUserNanoid,
      })
    })

    test('should update only specific fields', async () => {
      // Arrange
      const partialUpdate: Partial<UpdateWebhookInput> = {
        url: 'https://partially-updated.example.com/webhook',
      }

      // Act
      const response = await exec(testWebhookId, partialUpdate)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.url).toBe(partialUpdate.url)

      const updatedWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .executeTakeFirst()

      expect(updatedWebhook?.url).toBe(partialUpdate.url)
      expect(updatedWebhook?.events).toEqual([webhookEventTypes.PAYMENT_SENT])
      expect(updatedWebhook?.secret_encrypted).toBe('original-secret-key-123')
    })

    test('should update webhook status from active to inactive', async () => {
      // Arrange
      const statusUpdate: UpdateWebhookInput = {
        is_active: false,
      }

      // Act
      const response = await exec(testWebhookId, statusUpdate)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.is_active).toBe(false)

      const updatedWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .executeTakeFirst()

      expect(updatedWebhook?.is_active).toBe(false)
    })

    test('should require authentication', async () => {
      // Arrange
      const updateBody = { url: 'https://new.example.com/webhook' }

      // Act
      const response = await exec(testWebhookId, updateBody, false)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toEqual('User not logged in')
    })

    test('should return 403 when updating webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
      )

      const updateBody: UpdateWebhookInput = {
        url: 'https://updated.example.com/webhook',
        events: [webhookEventTypes.DEPOSIT_RECEIVED],
        secret: 'updated-secret-key-456',
        is_active: false,
      }

      // Act
      const response = await exec(unauthorizedWebhookId, updateBody)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 404 for non-existent webhook', async () => {
      // Arrange
      const nonExistentId = generateWebhookEndpointNanoid()
      const updateBody = { url: 'https://new.example.com/webhook' }

      // Act
      const response = await exec(nonExistentId, updateBody)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    test('should return 404 for removed webhook', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookId)
        .execute()

      const updateBody = { url: 'https://new.example.com/webhook' }

      // Act
      const response = await exec(testWebhookId, updateBody)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    describe('Input Validation (400 Bad Request)', () => {
      test('should return 400 if url is invalid', async () => {
        // Arrange
        const invalidUpdate = { url: 'not-a-valid-url' }

        // Act
        const response = await exec(testWebhookId, invalidUpdate)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('Invalid url')
      })

      test('should return 400 if events is empty array', async () => {
        // Arrange
        const invalidUpdate = { events: [] }

        // Act
        const response = await exec(testWebhookId, invalidUpdate)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Array must contain at least 1 element(s)',
        )
      })

      test('should return 400 if secret is too short', async () => {
        // Arrange
        const invalidUpdate = { secret: 'short' }

        // Act
        const response = await exec(testWebhookId, invalidUpdate)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'String must contain at least 16 character(s)',
        )
      })
    })

    test('should return 403 when updating webhook for team user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)
      await createTestCompany(generateCompanyNanoid(), otherUserNanoid)

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      const updateBody = {
        url: 'https://updated.example.com/webhook',
        events: [webhookEventTypes.DEPOSIT_RECEIVED],
        secret: 'updated-secret-key',
        is_active: false,
      }

      // Act
      const response = await exec(
        teamWebhookId,
        updateBody,
        true,
        otherUserNanoid,
      )

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should encrypt webhook secret when updating in database', async () => {
      // Arrange
      const newSecret = 'updated-secret-key-456789'
      const updateBody: UpdateWebhookInput = {
        url: 'https://updated.example.com/webhook',
        events: [webhookEventTypes.DEPOSIT_RECEIVED],
        secret: newSecret,
        is_active: false,
      }

      // Act
      const response = await exec(testWebhookId, updateBody)

      // Assert
      expect(response.status).toBe(200)

      const updatedWebhook = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .executeTakeFirst()

      expect(updatedWebhook).toBeDefined()
      // The stored secret should be encrypted (different from original)
      expect(updatedWebhook?.secret_encrypted).not.toBe(newSecret)
      // The encrypted secret should be a hex string (longer than original)
      expect(updatedWebhook?.secret_encrypted).toMatch(/^[a-f0-9]+$/)
      expect(updatedWebhook?.secret_encrypted.length).toBeGreaterThan(
        newSecret.length,
      )
    })
  })

  describe('DELETE /webhooks/:webhook_nanoid', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(testWebhookId, testCompanyNanoid, testUserNanoid)
    })

    const exec = async (
      webhook_nanoid: string,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/{webhook_nanoid}'].delete({
          params: { webhook_nanoid },
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully delete a webhook', async () => {
      // Arrange
      const webhooksBefore = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .execute()
      expect(webhooksBefore).toHaveLength(1)
      expect(webhooksBefore[0]?.is_removed).toBe(false)

      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(true)

      const webhooksAfter = await db
        .selectFrom('rise.webhook_endpoints')
        .selectAll()
        .where('nanoid', '=', testWebhookId)
        .execute()
      expect(webhooksAfter).toHaveLength(1)
      expect(webhooksAfter[0]?.is_removed).toBe(true)
      expect(webhooksAfter[0]?.last_modified_by).toBe(testUserNanoid)
    })

    test('should return 403 when deleting webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
      )

      // Act
      const response = await exec(unauthorizedWebhookId)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when deleting team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      // Act
      const response = await exec(teamWebhookId, true, otherUserNanoid)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 404 for non-existent webhook', async () => {
      // Arrange
      const nonExistentId = generateWebhookEndpointNanoid()

      // Act
      const response = await exec(nonExistentId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    test('should return 404 for already removed webhook', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookId)
        .execute()

      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })
  })

  describe('GET /webhooks/:webhook_nanoid', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://api.example.com/webhooks/webhook1',
          events: [
            webhookEventTypes.PAYMENT_SENT,
            webhookEventTypes.DEPOSIT_RECEIVED,
          ],
          secret: 'test-webhook-secret-key-1',
          is_active: true,
        },
      )
    })

    const exec = async (
      webhook_nanoid: string,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/{webhook_nanoid}'].get({
          params: { webhook_nanoid },
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully get a webhook', async () => {
      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data).toMatchObject({
        webhook_nanoid: testWebhookId,
        company_nanoid: testCompanyNanoid,
        url: 'https://api.example.com/webhooks/webhook1',
        events: [
          webhookEventTypes.PAYMENT_SENT,
          webhookEventTypes.DEPOSIT_RECEIVED,
        ],
        is_active: true,
      })
      expect(responseBody.data.secret).toBeUndefined()
    })

    test('should return 403 when getting webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
        {
          url: 'https://unauthorized.example.com/webhook',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'unauthorized-webhook-secret-key',
          is_active: true,
        },
      )

      // Act
      const response = await exec(unauthorizedWebhookId)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when getting team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      // Act
      const response = await exec(teamWebhookId, true, otherUserNanoid)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 404 for non-existent webhook', async () => {
      // Arrange
      const nonExistentId = generateWebhookEndpointNanoid()

      // Act
      const response = await exec(nonExistentId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    test('should return 404 for removed webhook', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookId)
        .execute()

      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })
  })

  describe('GET /webhooks', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId1: WebhookEndpointNanoid
    let testWebhookId2: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId1 = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookId1,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://api.example.com/webhooks/webhook1',
          events: [
            webhookEventTypes.PAYMENT_SENT,
            webhookEventTypes.DEPOSIT_RECEIVED,
          ],
          secret: 'test-webhook-secret-key-1',
          is_active: true,
        },
      )

      testWebhookId2 = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookId2,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://api.example.com/webhooks/webhook2',
          events: [webhookEventTypes.INVITE_ACCEPTED],
          secret: 'test-webhook-secret-key-2',
          is_active: false,
        },
      )
    })

    const exec = async (
      queryParams?: Record<string, any>,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks'].get({
          query: queryParams,
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully list all webhooks for company', async () => {
      // Act
      const response = await exec({ company_nanoid: testCompanyNanoid })

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.webhooks).toHaveLength(2)
      expect(responseBody.data.webhooks).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            webhook_nanoid: testWebhookId1,
            url: 'https://api.example.com/webhooks/webhook1',
            events: [
              webhookEventTypes.PAYMENT_SENT,
              webhookEventTypes.DEPOSIT_RECEIVED,
            ],
            is_active: true,
          }),
          expect.objectContaining({
            webhook_nanoid: testWebhookId2,
            url: 'https://api.example.com/webhooks/webhook2',
            events: [webhookEventTypes.INVITE_ACCEPTED],
            is_active: false,
          }),
        ]),
      )

      responseBody.data.webhooks.forEach((webhook: any) => {
        expect(webhook.secret).toBeUndefined()
      })
    })

    test('should filter webhooks from other teams', async () => {
      //Arrange
      const userTeamNanoid = generateTeamNanoid()
      const otherTeamNanoid = generateTeamNanoid()

      await createTestTeam(userTeamNanoid, testCompanyNanoid, testUserNanoid)
      await createTestTeam(otherTeamNanoid, testCompanyNanoid, testUserNanoid)

      await db
        .deleteFrom('rise.rise_entities')
        .where('type', '=', 'team_admin')
        .where('nanoid', '=', `tr-${otherTeamNanoid.slice(3)}` as any)
        .execute()

      const companyWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        companyWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://company.example.com/webhook',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'company-webhook-secret',
          is_active: true,
          team_nanoid: undefined,
        },
      )

      const userTeamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        userTeamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://user-team.example.com/webhook',
          secret: 'user-team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: userTeamNanoid,
        },
      )

      const otherTeamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        otherTeamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://other-team.example.com/webhook',
          secret: 'other-team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: otherTeamNanoid,
        },
      )

      // Act
      const response = await exec(
        { company_nanoid: testCompanyNanoid },
        true,
        testUserNanoid,
      )

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)

      expect(responseBody.data.webhooks).toHaveLength(4)
      const webhookIds = responseBody.data.webhooks.map(
        (w: any) => w.webhook_nanoid,
      )

      expect(webhookIds).toContain(testWebhookId1)
      expect(webhookIds).toContain(testWebhookId2)
      expect(webhookIds).toContain(companyWebhookId)
      expect(webhookIds).toContain(userTeamWebhookId)
      expect(webhookIds).not.toContain(otherTeamWebhookId)
    })

    test('should return empty list for company with no webhooks', async () => {
      // Arrange
      const anotherCompanyNanoid = generateCompanyNanoid()
      await createTestCompany(anotherCompanyNanoid, testUserNanoid)

      // Act
      const response = await exec({ company_nanoid: anotherCompanyNanoid })

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.webhooks).toHaveLength(0)
    })

    test('should respect limit parameter', async () => {
      // Act
      const response = await exec({
        company_nanoid: testCompanyNanoid,
        limit: 1,
      })

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.webhooks).toHaveLength(1)
    })

    test('should support cursor-based pagination', async () => {
      // Act
      const firstPage = await exec({
        company_nanoid: testCompanyNanoid,
        limit: 1,
      })

      // Assert
      expect(firstPage.status).toBe(200)
      const firstPageBody = await parseResponseBody(firstPage)
      expect(firstPageBody.data.webhooks).toHaveLength(1)
      expect(firstPageBody.data.next_cursor).toBeTruthy()

      // Act
      const secondPage = await exec({
        company_nanoid: testCompanyNanoid,
        limit: 1,
        cursor: firstPageBody.data.next_cursor,
      })

      // Assert
      expect(secondPage.status).toBe(200)
      const secondPageBody = await parseResponseBody(secondPage)
      expect(secondPageBody.data.webhooks).toHaveLength(1)

      expect(firstPageBody.data.webhooks[0].webhook_nanoid).not.toBe(
        secondPageBody.data.webhooks[0].webhook_nanoid,
      )
    })

    test('should not include removed webhooks in list', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookId1)
        .execute()

      // Act
      const response = await exec({ company_nanoid: testCompanyNanoid })

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.webhooks).toHaveLength(1)
      expect(responseBody.data.webhooks[0].webhook_nanoid).toBe(testWebhookId2)
      expect(responseBody.data.webhooks[0].is_active).toBe(false)
    })

    test('should return 403 when listing webhooks for company user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)

      // Act
      const response = await exec({ company_nanoid: unauthorizedCompanyNanoid })

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 404 if company does not exist', async () => {
      // Arrange
      const nonExistentCompany = generateCompanyNanoid()

      // Act
      const response = await exec({ company_nanoid: nonExistentCompany })

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Company not found')
    })

    describe('Input Validation (400 Bad Request)', () => {
      test('should return 400 if company_nanoid is missing', async () => {
        // Act
        const response = await exec({})

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('company_nanoid')
      })

      test('should return 400 if company_nanoid is not a valid nanoid', async () => {
        // Act
        const response = await exec({ company_nanoid: 'not-a-nanoid' })

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Invalid input: must start with "co-"',
        )
      })

      test('should return 400 if limit is not a number', async () => {
        // Act
        const response = await exec({
          company_nanoid: testCompanyNanoid,
          limit: 'not-a-number',
        })

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('Expected number')
      })

      test('should return 400 if limit is less than 1', async () => {
        // Act
        const response = await exec({
          company_nanoid: testCompanyNanoid,
          limit: 0,
        })

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Number must be greater than or equal to 1',
        )
      })

      test('should return 400 if limit is greater than maximum allowed', async () => {
        // Act
        const response = await exec({
          company_nanoid: testCompanyNanoid,
          limit: 101,
        })

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain(
          'Number must be less than or equal to',
        )
      })
    })
  })

  describe('POST /webhooks/test/:webhook_nanoid', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookNanoid: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookNanoid = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookNanoid,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://api.example.com/webhooks/test',
          events: [
            webhookEventTypes.PAYMENT_SENT,
            webhookEventTypes.INVITE_ACCEPTED,
          ],
          secret: 'test-webhook-secret-key',
          is_active: true,
        },
      )
    })

    const exec = async (
      webhook_nanoid: string,
      body: object,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/test/{webhook_nanoid}'].post({
          params: { webhook_nanoid },
          body,
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully test a webhook and create delivery', async () => {
      // Arrange
      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(testWebhookNanoid, testPayload)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data).toMatchObject({
        delivery_nanoid: expect.any(String),
        message: 'Test event created successfully',
      })

      const events = await db
        .selectFrom('rise.webhook_events')
        .selectAll()
        .where('event_type', '=', webhookEventTypes.PAYMENT_SENT)
        .execute()
      expect(events.length).toBeGreaterThan(0)

      const deliveries = await db
        .selectFrom('rise.webhook_deliveries')
        .selectAll()
        .where('webhook_nanoid', '=', testWebhookNanoid)
        .execute()
      expect(deliveries.length).toBeGreaterThan(0)
      expect(deliveries[0]!.status).toBe('queued')
    })

    test('should handle different event types', async () => {
      // Arrange
      const userEventPayload: TestWebhookInput = {
        event_type: webhookEventTypes.INVITE_ACCEPTED,
      }

      // Act
      const response = await exec(testWebhookNanoid, userEventPayload)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.delivery_nanoid).toBeTruthy()
    })

    test('should throw 400 case the webhook does not contain the event type', async () => {
      // Arrange
      const userEventPayload: TestWebhookInput = {
        event_type: webhookEventTypes.RISEID_ADDRESS_UPDATED,
      }

      // Act
      const response = await exec(testWebhookNanoid, userEventPayload)

      // Assert
      expect(response.status).toBe(400)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Event type not supported')
    })

    test('should throw 400 case the webhook is not active', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_active: false })
        .where('nanoid', '=', testWebhookNanoid)
        .execute()

      const userEventPayload: TestWebhookInput = {
        event_type: webhookEventTypes.INVITE_ACCEPTED,
      }

      // Act
      const response = await exec(testWebhookNanoid, userEventPayload)

      // Assert
      expect(response.status).toBe(400)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook is not active')
    })

    test('should return 403 when testing webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
        {
          url: 'https://unauthorized.example.com/webhook',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'unauthorized-webhook-secret-key',
          is_active: true,
        },
      )

      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(unauthorizedWebhookId, testPayload)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when testing team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      // Create a team-specific webhook (otherUser has no access to this team)
      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(
        teamWebhookId,
        testPayload,
        true,
        otherUserNanoid,
      )

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should require authentication', async () => {
      // Arrange
      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(testWebhookNanoid, testPayload, false)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toEqual('User not logged in')
    })

    test('should return 404 for non-existent webhook', async () => {
      // Arrange
      const nonExistentId = generateWebhookEndpointNanoid()
      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(nonExistentId, testPayload)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    test('should return 404 for removed webhook', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookNanoid)
        .execute()

      const testPayload: TestWebhookInput = {
        event_type: webhookEventTypes.PAYMENT_SENT,
      }

      // Act
      const response = await exec(testWebhookNanoid, testPayload)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    describe('Input Validation (400 Bad Request)', () => {
      test('should return 400 if event_type is missing', async () => {
        // Arrange
        const invalidPayload = {}

        // Act
        const response = await exec(testWebhookNanoid, invalidPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
        expect(responseBody.data).toContain('event_type')
      })

      test('should return 400 if payload is empty', async () => {
        // Arrange
        const emptyPayload = {}

        // Act
        const response = await exec(testWebhookNanoid, emptyPayload)

        // Assert
        expect(response.status).toBe(400)
        const responseBody = await parseResponseBody(response)
        expect(responseBody.success).toBe(false)
      })
    })
  })

  describe('POST /webhooks/retry/:delivery_nanoid', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId: WebhookEndpointNanoid
    let testDeliveryId: string

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(testWebhookId, testCompanyNanoid, testUserNanoid)

      // Create test event and delivery directly in the database
      const testEvent = await createTestEvent({
        event_type: webhookEventTypes.PAYMENT_SENT,
        payload: { payment_id: 'pay_retry_test' },
      })

      const testDelivery = await createTestDelivery(
        testWebhookId,
        testEvent.nanoid,
        { status: 'failed' },
      )

      testDeliveryId = testDelivery.nanoid
    })

    const exec = async (
      delivery_nanoid: string,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/retry/{delivery_nanoid}'].post({
          params: { delivery_nanoid },
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully retry a webhook delivery', async () => {
      // Act
      const response = await exec(testDeliveryId)

      // Assert
      expect(response.status).toBe(201)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data).toMatchObject({
        delivery_nanoid: testDeliveryId,
        status: 'queued',
      })
    })

    test('should return 403 when retrying delivery for webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
      )

      const testEvent = await createTestEvent({
        nanoid: generateWebhookEventNanoid(),
        event_type: webhookEventTypes.PAYMENT_SENT,
        payload: { payment_id: 'pay_unauthorized_retry_test' },
      })

      const testDelivery = await createTestDelivery(
        unauthorizedWebhookId,
        testEvent.nanoid,
        { status: 'failed' },
      )

      // Act
      const response = await exec(testDelivery.nanoid)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when retrying delivery for team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      // Create test event and delivery for team webhook
      const testEvent = await createTestEvent({
        nanoid: generateWebhookEventNanoid(),
        event_type: webhookEventTypes.PAYMENT_SENT,
        payload: { payment_id: 'pay_team_retry_test' },
      })

      const testDelivery = await createTestDelivery(
        teamWebhookId,
        testEvent.nanoid,
        { status: 'failed' },
      )

      // Act
      const response = await exec(testDelivery.nanoid, true, otherUserNanoid)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should require authentication', async () => {
      // Act
      const response = await exec(testDeliveryId, false)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toEqual('User not logged in')
    })

    test('should return 404 for non-existent delivery', async () => {
      // Arrange
      const nonExistentId = generateWebhookDeliveryNanoid()

      // Act
      const response = await exec(nonExistentId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Delivery not found')
    })
  })

  describe('GET /webhooks/:webhook_nanoid/deliveries', () => {
    let testUserNanoid: UserNanoid
    let testCompanyNanoid: CompanyNanoid
    let testWebhookId: WebhookEndpointNanoid

    beforeEach(async () => {
      testUserNanoid = generateUserNanoid()
      testCompanyNanoid = generateCompanyNanoid()

      await createTestUser(testUserNanoid)
      await createTestCompany(testCompanyNanoid, testUserNanoid)

      testWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        testWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://api.example.com/webhooks/deliveries',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'test-webhook-deliveries-key',
          is_active: true,
        },
      )
    })

    const exec = async (
      webhook_nanoid: string,
      queryParams?: Record<string, any>,
      withAuth = true,
      userNanoid = testUserNanoid,
    ) => {
      const headers = withAuth
        ? {
            Authorization: generateAuthorizationHeader(userNanoid),
          }
        : {}

      try {
        return await client['/webhooks/{webhook_nanoid}/deliveries'].get({
          params: { webhook_nanoid },
          query: queryParams,
          headers,
        })
      } catch (error: any) {
        return error.response || { status: error.status, data: error.data }
      }
    }

    test('should successfully get webhook deliveries', async () => {
      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.deliveries).toBeDefined()
      expect(Array.isArray(responseBody.data.deliveries)).toBe(true)
      expect(responseBody.data.deliveries).toHaveLength(0)
      expect(responseBody.data.next_cursor).toBeNull()
    })

    test('should get deliveries with pagination', async () => {
      // Arrange
      const testEvent = generateWebhookEvent({
        event_type: webhookEventTypes.PAYMENT_SENT,
        payload: { payment_id: 'pay_delivery_test' },
      })

      await db
        .insertInto('rise.webhook_events')
        .values(testEvent as any)
        .execute()

      const testDelivery = generateWebhookDelivery(
        testWebhookId,
        testEvent.nanoid,
        { status: 'queued' },
      )

      await db
        .insertInto('rise.webhook_deliveries')
        .values(testDelivery)
        .execute()

      // Act
      const response = await exec(testWebhookId, { limit: 10 })

      // Assert
      expect(response.status).toBe(200)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.data.deliveries).toHaveLength(1)
      expect(responseBody.data.deliveries[0]).toMatchObject({
        delivery_nanoid: expect.any(String),
        event_nanoid: expect.any(String),
        status: 'queued',
        created_at: expect.any(String),
      })
    })

    test('should return 403 when getting deliveries for webhook user has no access to', async () => {
      // Arrange
      const unauthorizedUserNanoid = generateUserNanoid()
      const unauthorizedCompanyNanoid = generateCompanyNanoid()
      const unauthorizedWebhookId = generateWebhookEndpointNanoid()

      await createTestUser(unauthorizedUserNanoid)
      await createTestCompany(unauthorizedCompanyNanoid, unauthorizedUserNanoid)
      await createTestWebhook(
        unauthorizedWebhookId,
        unauthorizedCompanyNanoid,
        unauthorizedUserNanoid,
        {
          url: 'https://unauthorized.example.com/webhook',
          events: [webhookEventTypes.PAYMENT_SENT],
          secret: 'unauthorized-webhook-secret-key',
          is_active: true,
        },
      )

      // Act
      const response = await exec(unauthorizedWebhookId)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should return 403 when getting deliveries for team webhook user has no access to', async () => {
      // Arrange
      const teamNanoid = generateTeamNanoid()
      await createTestTeam(teamNanoid, testCompanyNanoid, testUserNanoid)

      const otherUserNanoid = generateUserNanoid()
      await createTestUser(otherUserNanoid)

      const otherUserEntity = await db
        .selectFrom('rise.rise_entities')
        .selectAll()
        .where('nanoid', '=', otherUserNanoid)
        .where('type', '=', 'user')
        .executeTakeFirst()

      if (otherUserEntity) {
        await db
          .insertInto('rise.rise_entities')
          .values({
            riseid: generateTeamRiseId(),
            parent_riseid: otherUserEntity.riseid,
            type: 'org_admin',
            nanoid: `or-${otherUserNanoid.slice(3)}` as any,
            avatar: '',
          })
          .execute()
      }

      const teamWebhookId = generateWebhookEndpointNanoid()
      await createTestWebhook(
        teamWebhookId,
        testCompanyNanoid,
        testUserNanoid,
        {
          url: 'https://team.example.com/webhook',
          secret: 'team-webhook-secret',
          events: [webhookEventTypes.PAYMENT_SENT],
          is_active: true,
          is_removed: false,
          team_nanoid: teamNanoid,
        },
      )

      // Act
      const response = await exec(teamWebhookId, {}, true, otherUserNanoid)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('access')
    })

    test('should require authentication', async () => {
      // Act
      const response = await exec(testWebhookId, {}, false)

      // Assert
      expect(response.status).toBe(403)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toEqual('User not logged in')
    })

    test('should return 404 for non-existent webhook', async () => {
      // Arrange
      const nonExistentId = generateWebhookEndpointNanoid()

      // Act
      const response = await exec(nonExistentId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })

    test('should return 404 for removed webhook', async () => {
      // Arrange
      await db
        .updateTable('rise.webhook_endpoints')
        .set({ is_removed: true, last_modified_by: testUserNanoid })
        .where('nanoid', '=', testWebhookId)
        .execute()

      // Act
      const response = await exec(testWebhookId)

      // Assert
      expect(response.status).toBe(404)
      const responseBody = await parseResponseBody(response)
      expect(responseBody.success).toBe(false)
      expect(responseBody.data).toContain('Webhook endpoint not found')
    })
  })
})
