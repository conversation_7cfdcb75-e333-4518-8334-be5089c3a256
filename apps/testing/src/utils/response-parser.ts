/**
 * Utility functions for parsing HTTP response bodies in tests
 * Handles various response formats including ReadableStream
 */

// Helper function to convert ReadableStream response body to JSON
export const parseResponseBody = async (response: any) => {
  if (response.data) {
    // If response.data exists, use it directly
    return response.data
  }

  if (response.body && typeof response.body.getReader === 'function') {
    // If response.body is a ReadableStream
    const reader = response.body.getReader()
    const chunks: Uint8Array[] = []

    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      chunks.push(value)
    }

    // Combine chunks and convert to string
    const combined = new Uint8Array(
      chunks.reduce((acc, chunk) => acc + chunk.length, 0),
    )
    let offset = 0
    for (const chunk of chunks) {
      combined.set(chunk, offset)
      offset += chunk.length
    }

    const text = new TextDecoder().decode(combined)
    return JSON.parse(text)
  }

  if (response.text && typeof response.text === 'function') {
    // If response has a text() method
    const text = await response.text()
    return JSON.parse(text)
  }

  if (response.json && typeof response.json === 'function') {
    // If response has a json() method
    return await response.json()
  }

  // Fallback - return response as is
  return response
}
