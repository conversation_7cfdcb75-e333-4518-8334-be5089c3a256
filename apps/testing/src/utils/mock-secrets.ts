import { vi } from 'vitest'

// Utility to mock getSecrets for database connection in tests
export function mockGetSecrets() {
  vi.mock('utils/src/google/getSecrets.js', () => ({
    getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
      // Define specific mock values for known secrets
      const mockValues: Record<string, string> = {
        // Route Fusion secrets - need specific format entityId:userId
        'route-fusion-entity': 'test-entity:test-user',
        'route-fusion-entity-usd': 'test-entity-usd:test-user-usd',
        'route-fusion-api-key': 'test-api-key-12345',
        'route-fusion-api-key-usd': 'test-api-key-usd-12345',
        'route-fusion-webhook-secret': 'test-webhook-secret',

        // Database secrets
        B2B_API_DATABASE_USER: 'root',
        B2B_API_DATABASE_PW: 'testpass',
        GCP_TF_SERVER_CA: 'test-ca-cert',
        GCP_TF_CLIENT_KEY: 'test-client-key',
        GCP_TF_CLIENT_CERT: 'test-client-cert',

        // Other common secrets
        'unit21-api-key': 'test-unit21-key',
        'rise-secret-key': 'test-secret-key',
        'webhook-encryption-key': 'test-webhook-encryption-key-32-chars-long',
        'chainalysis-api-key': 'test-chainalysis-key',
        'inngest-event-key': 'test-inngest-key',
        'integration-workspace-key': 'test-workspace-key',
        'integration-workspace-secret': 'test-workspace-secret',
        BITCOIN_KEYPAIR: 'test-public-key:test-private-key',
      }

      return Object.fromEntries(
        secrets.map((secretName) => [
          secretName,
          mockValues[secretName] || `mocked-${secretName}`,
        ]),
      )
    }),
  }))
}
