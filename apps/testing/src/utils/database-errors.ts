/**
 * Database error codes and utilities for testing
 * MySQL error table: https://fromdual.com/mysql-error-codes-and-messages
 */
export enum DatabaseErrorCodes {
  // Constraint violations
  NOT_NULL_VIOLATION = 1048, // ER_BAD_NULL_ERROR
  ER_NO_DEFAULT_FOR_FIELD = 1364, // ER_NO_DEFAULT_FOR_FIELD
  UNIQUE_CONSTRAINT_VIOLATION = 1062, // ER_DUP_ENTRY
  FOREIGN_KEY_CONSTRAINT_VIOLATION = 1452, // ER_NO_REFERENCED_ROW_2
  CHECK_CONSTRAINT_VIOLATION = 4025, // ER_CHECK_CONSTRAINT_VIOLATED
  ROW_IS_REFERENCED = 1451, // ER_ROW_IS_REFERENCED_2

  // Data type errors
  DATA_TOO_LONG = 1406, // ER_DATA_TOO_LONG
  TRUNCATED_WRONG_VALUE = 1265, // ER_TRUNCATED_WRONG_VALUE

  // Table/Column errors
  NO_SUCH_TABLE = 1146, // ER_NO_SUCH_TABLE
  BAD_FIELD_ERROR = 1054, // ER_BAD_FIELD_ERROR
  NoDefaultForField = 1364, // ER_NO_DEFAULT_FOR_FIELD
}
