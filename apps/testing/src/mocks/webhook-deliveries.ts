import type {
  WebhookDeliveryNanoid,
  WebhookEndpointNanoid,
  WebhookEventNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableWebhookDeliveries } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { generateWebhookDeliveryNanoid } from './nanoids.js'

/**
 * Webhook deliveries mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface WebhookDeliveryOverrides {
  nanoid?: WebhookDeliveryNanoid
  status?: 'queued' | 'success' | 'failed' | 'retrying'
  response_code?: number | null
  error_message?: string | null
  response_body?: Record<string, any> | null // Pass as object, will be JSON stringified
  created_at?: Date
  updated_at?: Date
}

export const generateWebhookDelivery = (
  webhookNanoid: WebhookEndpointNanoid,
  eventNanoid: WebhookEventNanoid,
  overrides: WebhookDeliveryOverrides = {},
): InsertableWebhookDeliveries => {
  // Handle response_body serialization
  const responseBody =
    overrides.response_body !== undefined
      ? overrides.response_body === null
        ? null
        : JSON.stringify(overrides.response_body)
      : null

  return {
    nanoid: overrides.nanoid ?? generateWebhookDeliveryNanoid(),
    webhook_nanoid: webhookNanoid,
    event_nanoid: eventNanoid,
    status: overrides.status ?? 'queued',
    response_code:
      overrides.response_code !== undefined ? overrides.response_code : null,
    error_message:
      overrides.error_message !== undefined ? overrides.error_message : null,
    response_body: responseBody, // Serialize to JSON string for database
    created_at: overrides.created_at ?? new Date(),
    updated_at: overrides.updated_at ?? new Date(),
  }
}
