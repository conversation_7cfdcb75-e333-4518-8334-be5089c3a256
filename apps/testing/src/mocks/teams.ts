import type {
  CompanyNanoid,
  TeamNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableRiseEntitiesType } from '@riseworks/contracts/src/codegen/zod/rise/rise_entities.js'
import type { InsertableTeamsDataType } from '@riseworks/contracts/src/codegen/zod/rise/teams_data.js'
import {
  generateCompanyNanoid,
  generateTeamNanoid,
  generateUserNanoid,
} from './nanoids.js'

/**
 * Team mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface TeamOverrides {
  nanoid?: TeamNanoid
  company_nanoid?: CompanyNanoid
  created_by?: UserNanoid
  name?: string
  rise_account?: string
  created_at?: Date
  updated_at?: Date
}

export const generateTeam = (
  overrides: TeamOverrides = {},
): {
  teamEntity: InsertableRiseEntitiesType
  teamData: InsertableTeamsDataType
} => {
  const teamNanoid = overrides.nanoid ?? generateTeamNanoid()
  const companyNanoid = overrides.company_nanoid ?? generateCompanyNanoid()
  const createdBy = overrides.created_by ?? generateUserNanoid()
  const mockRiseid = `0x${teamNanoid.replace('te-', '')}`
  const parentRiseid = `0x${companyNanoid.replace('co-', '')}`

  const teamEntity: InsertableRiseEntitiesType = {
    riseid: mockRiseid as any,
    type: 'team',
    parent_riseid: parentRiseid as any,
    nanoid: teamNanoid as any,
    avatar: '',
    last_modified_by: createdBy as any,
  }

  const teamData: InsertableTeamsDataType = {
    nanoid: teamNanoid as any,
    name: overrides.name ?? 'Test Team',
    rise_account: overrides.rise_account ?? ('' as any), // Empty rise account for test
    last_modified_by: createdBy as any,
  }

  return { teamEntity, teamData }
}
