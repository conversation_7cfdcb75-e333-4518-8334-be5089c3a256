import type { WebhookEventNanoid } from '@riseworks/contracts/src/brands.js'
import type { InsertableWebhookEventsType } from '@riseworks/contracts/src/codegen/zod/rise/webhook_events.js'
import { generateUserNanoid, generateWebhookEventNanoid } from './nanoids.js'

/**
 * Webhook events mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface WebhookEventOverrides {
  nanoid?: WebhookEventNanoid
  event_type?: PrismaCompatibleInsertableWebhookEvent['event_type']
  payload?: Record<string, any> // Pass as object, will be JSON stringified
  created_at?: Date
  version?: string
}

// Define the new type for Prisma compatibility
type PrismaCompatibleInsertableWebhookEvent = Omit<
  InsertableWebhookEventsType,
  'payload'
> & {
  payload: string
}

export const generateWebhookEvent = (
  overrides: WebhookEventOverrides = {},
): PrismaCompatibleInsertableWebhookEvent => {
  const defaultPayload = {
    id: generateUserNanoid(),
    amount: 1000,
    currency: 'USD',
  }

  const payload = overrides.payload ?? defaultPayload

  return {
    nanoid: overrides.nanoid ?? generateWebhookEventNanoid(),
    event_type: overrides.event_type ?? 'payment.payment_sent',
    payload: JSON.stringify(payload), // Serialize to JSON string for database
    version: overrides.version ?? '1.0.0',
  }
}
