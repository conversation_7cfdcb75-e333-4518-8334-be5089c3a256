# Test Data Generators

Type-safe data generators for database entities that ensure consistency across tests, with proper TypeScript types and automatic JSON serialization.

## Core Concepts

### Type Safety
All generators use `Insertable<T>` types from database schema:
```typescript
export const generateEntity = (overrides = {}): InsertableEntity => {
  return {
    nanoid: overrides.nanoid ?? generateEntityNanoid(),
    // ... properly typed fields
  }
}
```

### JSON Serialization
Data generators automatically handle JSON fields for database compatibility:
```typescript
// Input: JavaScript array/object
const webhook = generateWebhookEndpoint({
  events: ['payment.created', 'invoice.paid']  // Array input
})

// Output: Database-ready JSON string
// webhook.events = '["payment.created", "invoice.paid"]'
```

### Override Pattern
All data generators follow the same override pattern:
```typescript
const entity = generateEntity({
  specific_field: 'test-value',  // Override only what you need
  // All other fields get sensible defaults
})
```

## Usage Patterns

### Basic Test
```typescript
import { generateWebhookEndpoint } from './index.js' // Assuming index is in the same directory

test('should create entity', async () => {
  const entity = generateEntity({ field: 'test-value' })
  await db.insertInto('table').values(entity).execute()
})
```

### Parameterized Testing
```typescript
test.each`
  status        | responseCode
  ${'success'}  | ${200}
  ${'failed'}   | ${500}
`('should handle $status', async ({ status, responseCode }) => {
  const entity = generateEntity({ status, response_code: responseCode })
  // Test logic
})
```

### Complex Scenarios
```typescript
test('should handle complete flow', async () => {
  const parent = generateParentEntity()
  const child = generateChildEntity(parent.nanoid, { status: 'active' })
  
  await db.insertInto('parent_table').values(parent).execute()
  await db.insertInto('child_table').values(child).execute()
})
```

## Best Practices

### Use Centralized Generators
```typescript
// ✅ Good - Type-safe and consistent
const entity = generateEntity({ field: 'test-value' })

// ❌ Bad - Manual object creation
const entity = { nanoid: 'manual-id', field: 'test-value' }
```

### Override Only What You Need
```typescript
// ✅ Good - Override only test-specific fields
const entity = generateEntity({ is_active: false })

// ❌ Bad - Unnecessary overrides
const entity = generateEntity({ nanoid: 'id', company_nanoid: 'comp', is_active: false })
```

### Use Helper Functions
Many generators include helper functions for common scenarios:
```typescript
// ✅ Good - Semantic helper functions
const { active, inactive } = EntityHelpers.generateActiveInactivePair()

// ❌ Bad - Manual creation
const active = generateEntity({ is_active: true })
const inactive = generateEntity({ is_active: false })
```

## Adding New Generators

1. **Create generator file** using `Insertable<T>` types:
```typescript
// src/mocks/new-entity.ts (or src/generators/new-entity.ts if directory is renamed)
import type { InsertableNewEntity } from '@riseworks/contracts/src/codegen/db/models_rise.js'

export const generateNewEntity = (overrides = {}): InsertableNewEntity => {
  return {
    nanoid: overrides.nanoid ?? generateNewEntityNanoid(),
    required_field: overrides.required_field ?? 'default-value',
    json_field: overrides.json_field ? JSON.stringify(overrides.json_field) : null,
    // ... other fields
  }
}

// Helper functions for common scenarios
export const NewEntityHelpers = {
  generateSpecialScenario: () => generateNewEntity({ /* special config */ })
}
```

2. **Add nanoid generator** if needed:
```typescript
// src/mocks/nanoids.ts (or src/generators/nanoids.ts)
export const generateNewEntityNanoid = () => `ne_${Math.random().toString(36).substring(2, 14)}`
```

3. **Export from index**:
```typescript
// src/mocks/index.ts (or src/generators/index.ts)
export * from './new-entity.js'
```

## JSON Field Handling

For entities with JSON fields, always serialize objects in their respective generator functions:
```typescript
export const generateEntityWithJson = (overrides = {}) => {
  const jsonField = overrides.json_field ?? { default: 'data' }
  
  return {
    // ... other fields
    json_field: JSON.stringify(jsonField), // Always serialize for database
  }
}
```

This pattern-focused approach ensures consistent, type-safe test data generation that scales with your database schema changes.

## Special Case: Aligning Generator Output with Database Input Types

Sometimes, an auto-generated `Insertable<EntityName>Type` might define a field (e.g., `events: string[]`) differently from how the database operation expects it (e.g., `events: string` for a JSON string).

To handle this, the specific data generator function:
1.  Defines a new return type (e.g., `PrismaCompatibleType`) that matches the database's expectation for that field.
    ```typescript
    // Example:
    // Original: InsertableEntity { problematicField: string[] }
    // New Type: PrismaCompatibleEntity { problematicField: string }
    type PrismaCompatibleEntity = Omit<InsertableEntity, 'problemField'> & { problematicField: string };
    ```
2.  The generator function is typed to return this `PrismaCompatibleType`.
3.  Internally, the generator transforms the input (e.g., stringifies an array) to match this new return type.

This keeps the transformation logic within the generator, so its output is directly usable by database functions. See `generateWebhookEndpoint` in `webhook-endpoints.ts` for an example with its `PrismaCompatibleInsertableWebhookEndpoint` type.
