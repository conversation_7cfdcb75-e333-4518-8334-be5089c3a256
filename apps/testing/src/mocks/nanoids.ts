import { nanoid } from 'utils/src/common/nanoid.js'

/**
 * Nanoid generators for testing purposes
 * These generate valid nanoids for different entity types
 */

// Core entity nanoid generators (using existing branded types)
export const generateCompanyNanoid = () => nanoid('company')
export const generateTeamNanoid = () => nanoid('team')
export const generateUserNanoid = () => nanoid('user')

// Webhook entity nanoid generators (fallback pattern until added to nanoid util)
export const generateWebhookEndpointNanoid = () => nanoid('webhook_endpoint')
export const generateWebhookEventNanoid = () => nanoid('webhook_event')
export const generateWebhookDeliveryNanoid = () => nanoid('webhook_delivery')
