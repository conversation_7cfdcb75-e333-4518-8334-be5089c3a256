import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { InsertableRiseEntitiesType } from '@riseworks/contracts/src/codegen/zod/rise/rise_entities.js'
import type { InsertableUsersDataType } from '@riseworks/contracts/src/codegen/zod/rise/users_data.js'
import { generateUserNanoid } from './nanoids.js'

/**
 * User mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface UserOverrides {
  nanoid?: UserNanoid
  email?: string
  first_name?: string
  middle_name?: string
  last_name?: string
  country?: string
  state?: string
  dob?: Date
  phone?: string
  alias?: string
  linkedin?: string
  discord?: string
  x?: string
  website?: string
  account_status?: 'created' | 'activated' | 'blocked' | 'expired'
  advanced_security?: boolean
  v1_id?: string
}

export const generateUser = (
  overrides: UserOverrides = {},
): {
  userEntity: InsertableRiseEntitiesType
  userData: InsertableUsersDataType
} => {
  const userNanoid = overrides.nanoid ?? generateUserNanoid()
  const mockRiseid = `0x${userNanoid.replace('us-', '')}`

  const userEntity: InsertableRiseEntitiesType = {
    riseid: mockRiseid as any,
    type: 'user',
    parent_riseid: mockRiseid as any,
    nanoid: userNanoid as any,
    avatar: '',
  }

  const userData: InsertableUsersDataType = {
    nanoid: userNanoid as any,
    rise_account: '' as any, // Empty rise account for test
    email: overrides.email ?? `test-${userNanoid}@example.com`,
    first_name: overrides.first_name ?? 'Test',
    middle_name: overrides.middle_name ?? '',
    last_name: overrides.last_name ?? 'User',
    country: overrides.country ?? 'US',
    state: overrides.state ?? 'CA',
    recovery_email: '',
    occupation: '',
    phone: overrides.phone ?? '',
    dob: overrides.dob ?? new Date('1990-01-01'),
    alias: overrides.alias ?? '',
    linkedin: overrides.linkedin ?? '',
    discord: overrides.discord ?? '',
    x: overrides.x ?? '',
    website: overrides.website ?? '',
    account_status: overrides.account_status ?? 'created',
    advanced_security: overrides.advanced_security ?? false,
    v1_id: overrides.v1_id ?? '',
  }

  return { userEntity, userData }
}
