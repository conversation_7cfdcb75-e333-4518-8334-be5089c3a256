import type {
  CompanyNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableCompaniesDataType } from '@riseworks/contracts/src/codegen/zod/rise/companies_data.js'
import type { InsertableRiseEntitiesType } from '@riseworks/contracts/src/codegen/zod/rise/rise_entities.js'
import { generateCompanyNanoid } from './nanoids.js'

/**
 * Company mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface CompanyOverrides {
  nanoid?: CompanyNanoid
  created_by?: UserNanoid
  name?: string
  country?: string
  state?: string
  incorporation_country?: string
  incorporation_type?:
    | 'sole_proprietorship'
    | 'private_limited_company'
    | 'limited_liability_company'
    | 's_corporation'
    | 'c_corporation'
    | 'public_limited_company'
    | 'limited_partnership'
    | 'holding_company'
    | 'non_government_organisation'
    | 'statutory_company'
    | 'subsidiary_company'
    | 'unlimited_partnership'
    | 'charitable_incorporated_organisation'
    | 'chartered_company'
    | 'association'
    | 'non_profit_organisation'
    | 'im_not_sure'
  is_dao?: boolean
  website?: string
  size?: 'micro_sized' | 'small_sized' | 'medium' | 'large_sized'
  phone?: string
  doing_business_as?: string
  payroll_enabled?: boolean
  v1_id?: string
}

export const generateCompany = (
  overrides: CompanyOverrides = {},
): {
  companyEntity: InsertableRiseEntitiesType
  companyData: InsertableCompaniesDataType
} => {
  const companyNanoid = overrides.nanoid ?? generateCompanyNanoid()
  const mockRiseid = `0x${companyNanoid.replace('co-', '')}`

  const companyEntity: InsertableRiseEntitiesType = {
    riseid: mockRiseid as any,
    type: 'company',
    parent_riseid: mockRiseid as any,
    nanoid: companyNanoid as any,
    avatar: '',
    last_modified_by: overrides.created_by as any,
  }

  const companyData: InsertableCompaniesDataType = {
    nanoid: companyNanoid as any,
    rise_account: '' as any, // Empty rise account for test
    name: overrides.name ?? 'Test Company LLC',
    country: overrides.country ?? 'US',
    state: overrides.state ?? 'CA',
    incorporation_country: overrides.incorporation_country ?? 'US',
    incorporation_type:
      overrides.incorporation_type ?? ('limited_liability_company' as any),
    is_dao: overrides.is_dao ?? false,
    website: overrides.website ?? 'https://testcompany.com',
    size: overrides.size ?? 'medium',
    phone: overrides.phone ?? '+**********',
    doing_business_as: overrides.doing_business_as ?? '',
    payroll_enabled: overrides.payroll_enabled ?? false,
    last_modified_by: overrides.created_by as any,
    v1_id: overrides.v1_id ?? '',
  }

  return { companyEntity, companyData }
}
