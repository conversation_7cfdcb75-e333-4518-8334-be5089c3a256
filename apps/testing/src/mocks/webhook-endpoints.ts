import type {
  CompanyNanoid,
  TeamNanoid,
  UserNanoid,
  WebhookEndpointNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableWebhookEndpointsType } from '@riseworks/contracts/src/codegen/zod/rise/webhook_endpoints.js'
import {
  generateCompanyNanoid,
  generateTeamNanoid,
  generateUserNanoid,
  generateWebhookEndpointNanoid,
} from './nanoids.js'

/**
 * Webhook endpoints mocks for testing purposes
 * These generate realistic test data that matches the database schema exactly
 */

export interface WebhookEndpointOverrides {
  nanoid?: WebhookEndpointNanoid
  company_nanoid?: CompanyNanoid
  team_nanoid?: TeamNanoid | null
  created_by?: UserNanoid
  last_modified_by?: UserNanoid | null
  url?: string
  secret_encrypted?: string
  events?: string[] // Pass as array, will be JSON stringified
  is_active?: boolean
  is_removed?: boolean
  created_at?: Date
  updated_at?: Date
}

// Define the new type for Prisma compatibility
type PrismaCompatibleInsertableWebhookEndpoint = Omit<
  InsertableWebhookEndpointsType,
  'events' | 'nanoid'
> & {
  events: string
  nanoid: WebhookEndpointNanoid
}

export const generateWebhookEndpoint = (
  overrides: WebhookEndpointOverrides = {},
): PrismaCompatibleInsertableWebhookEndpoint => {
  const eventsArray = overrides.events ?? [
    'payment.created',
    'deposit.received',
  ]

  return {
    nanoid: overrides.nanoid ?? generateWebhookEndpointNanoid(),
    company_nanoid: overrides.company_nanoid ?? generateCompanyNanoid(),
    team_nanoid:
      overrides.team_nanoid !== undefined
        ? overrides.team_nanoid
        : generateTeamNanoid(),
    created_by: overrides.created_by ?? generateUserNanoid(),
    last_modified_by:
      overrides.last_modified_by !== undefined
        ? overrides.last_modified_by
        : generateUserNanoid(),
    url: overrides.url ?? 'https://example.com/webhook',
    secret_encrypted:
      overrides.secret_encrypted ??
      `encrypted_secret_${overrides.nanoid ?? generateWebhookEndpointNanoid()}`,
    events: JSON.stringify(eventsArray), // Actual value is string, matching the new return type
    is_active: overrides.is_active ?? true,
    is_removed: overrides.is_removed ?? false,
  }
}
