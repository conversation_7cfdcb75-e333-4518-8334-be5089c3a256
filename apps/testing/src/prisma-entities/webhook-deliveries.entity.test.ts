import { describe, expect, test, afterEach } from 'vitest'
import { db } from 'db'
import {
  generateWebhookEndpointNanoid,
  generateWebhookEventNanoid,
  generateWebhookDeliveryNanoid,
  generateWebhookEndpoint,
  generateWebhookEvent,
  generateWebhookDelivery,
} from '../mocks/index.js'
import { DatabaseErrorCodes, mockGetSecrets } from '../utils/index.js'

// Mock getSecrets to avoid real secret fetching in tests
mockGetSecrets()

describe('webhook_deliveries', () => {
  afterEach(async () => {
    // Clean up in reverse dependency order
    await db.deleteFrom('rise.webhook_deliveries').execute()
    await db.deleteFrom('rise.webhook_events').execute()
    await db.deleteFrom('rise.webhook_endpoints').execute()
  })

  test('should create webhook delivery successfully', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()

    const delivery = generateWebhookDelivery(webhook.nanoid, event.nanoid)

    // Act
    const result = await db
      .insertInto('rise.webhook_deliveries')
      .values(delivery)
      .execute()

    const created = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('nanoid', '=', delivery.nanoid)
      .executeTakeFirst()

    // Assert
    expect(result).toBeDefined()
    expect(created).toBeDefined()
    expect(created?.webhook_nanoid).toBe(webhook.nanoid)
    expect(created?.event_nanoid).toBe(event.nanoid)
    expect(created?.status).toBe('queued')
  })

  test('should update delivery status', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()
    const delivery = generateWebhookDelivery(webhook.nanoid, event.nanoid)

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()
    await db.insertInto('rise.webhook_deliveries').values(delivery).execute()

    // Act
    await db
      .updateTable('rise.webhook_deliveries')
      .set({
        status: 'success',
        response_code: 200,
        response_body: JSON.stringify({ success: true }),
        updated_at: new Date(),
      })
      .where('nanoid', '=', delivery.nanoid)
      .execute()

    const updated = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('nanoid', '=', delivery.nanoid)
      .executeTakeFirst()

    // Assert
    expect(updated?.status).toBe('success')
    expect(updated?.response_code).toBe(200)
    expect(updated?.response_body).toEqual({ success: true })
  })

  test('should handle failed deliveries with retry', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()
    const delivery = generateWebhookDelivery(webhook.nanoid, event.nanoid, {
      status: 'failed',
      response_code: 500,
      error_message: 'Internal server error',
    })

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()

    // Act
    await db.insertInto('rise.webhook_deliveries').values(delivery).execute()

    const created = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('nanoid', '=', delivery.nanoid)
      .executeTakeFirst()

    // Assert
    expect(created?.status).toBe('failed')
    expect(created?.response_code).toBe(500)
    expect(created?.error_message).toBe('Internal server error')
  })

  test('should query deliveries by status', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event1 = generateWebhookEvent()
    const event2 = generateWebhookEvent()
    const event3 = generateWebhookEvent()

    const successDelivery = generateWebhookDelivery(
      webhook.nanoid,
      event1.nanoid,
      { status: 'success' },
    )
    const queuedDelivery = generateWebhookDelivery(
      webhook.nanoid,
      event2.nanoid,
      { status: 'queued' },
    )
    const failedDelivery = generateWebhookDelivery(
      webhook.nanoid,
      event3.nanoid,
      { status: 'failed' },
    )

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db
      .insertInto('rise.webhook_events')
      .values([event1, event2, event3])
      .execute()
    await db
      .insertInto('rise.webhook_deliveries')
      .values([successDelivery, queuedDelivery, failedDelivery])
      .execute()

    // Act
    const successDeliveries = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('status', '=', 'success')
      .execute()

    const queuedDeliveries = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('status', '=', 'queued')
      .execute()

    // Assert
    expect(successDeliveries).toHaveLength(1)
    expect(queuedDeliveries).toHaveLength(1)
    expect(successDeliveries[0]?.nanoid).toBe(successDelivery.nanoid)
  })

  test('should query deliveries for specific webhook', async () => {
    // Arrange
    const webhook1 = generateWebhookEndpoint()
    const webhook2 = generateWebhookEndpoint()
    const event = generateWebhookEvent()

    const delivery1 = generateWebhookDelivery(webhook1.nanoid, event.nanoid)
    const delivery2 = generateWebhookDelivery(webhook1.nanoid, event.nanoid)
    const delivery3 = generateWebhookDelivery(webhook2.nanoid, event.nanoid)

    await db
      .insertInto('rise.webhook_endpoints')
      .values([webhook1, webhook2])
      .execute()
    await db.insertInto('rise.webhook_events').values(event).execute()
    await db
      .insertInto('rise.webhook_deliveries')
      .values([delivery1, delivery2, delivery3])
      .execute()

    // Act
    const webhook1Deliveries = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('webhook_nanoid', '=', webhook1.nanoid)
      .execute()

    // Assert
    expect(webhook1Deliveries).toHaveLength(2)
    expect(
      webhook1Deliveries.every((d) => d.webhook_nanoid === webhook1.nanoid),
    ).toBe(true)
  })

  // NULL/NOT NULL validation tests for webhook_deliveries
  test.each`
    fieldName | invalidDelivery
    ${'nanoid'} | ${(webhook: any, event: any) => {
  const base = generateWebhookDelivery(webhook.nanoid, event.nanoid)
  const { nanoid, ...delivery } = base
  return delivery
}}
    ${'webhook_nanoid'} | ${(webhook: any, event: any) => {
  const base = generateWebhookDelivery(webhook.nanoid, event.nanoid)
  const { webhook_nanoid, ...delivery } = base
  return delivery
}}
    ${'event_nanoid'} | ${(webhook: any, event: any) => {
  const base = generateWebhookDelivery(webhook.nanoid, event.nanoid)
  const { event_nanoid, ...delivery } = base
  return delivery
}}
  `(
    'should fail when missing required $fieldName field',
    async ({ fieldName, invalidDelivery }) => {
      // Arrange
      const webhook = generateWebhookEndpoint()
      const event = generateWebhookEvent()

      await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
      await db.insertInto('rise.webhook_events').values(event).execute()

      const delivery = invalidDelivery(webhook, event)

      // Act & Assert
      try {
        await db
          .insertInto('rise.webhook_deliveries')
          .values(delivery)
          .execute()
      } catch (error: any) {
        expect(error).toBeDefined()
        expect(error.errno).toBe(DatabaseErrorCodes.ER_NO_DEFAULT_FOR_FIELD)
      }
    },
  )

  test('should allow nullable optional fields', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()

    const deliveryWithNulls = {
      nanoid: generateWebhookDeliveryNanoid(),
      webhook_nanoid: webhook.nanoid,
      event_nanoid: event.nanoid,
      status: 'queued' as const,
      response_code: null,
      error_message: null,
      response_body: null,
      created_at: new Date(),
      updated_at: new Date(),
    }

    // Act
    const result = await db
      .insertInto('rise.webhook_deliveries')
      .values(deliveryWithNulls)
      .execute()

    const created = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('nanoid', '=', deliveryWithNulls.nanoid)
      .executeTakeFirst()

    // Assert
    expect(result).toBeDefined()
    expect(created?.response_code).toBeNull()
    expect(created?.error_message).toBeNull()
    expect(created?.response_body).toBeNull()
  })

  test('should fail with invalid foreign key reference', async () => {
    // Arrange
    const nonExistentWebhookId = generateWebhookEndpointNanoid()
    const nonExistentEventId = generateWebhookEventNanoid()

    const invalidDelivery = {
      nanoid: generateWebhookDeliveryNanoid(),
      webhook_nanoid: nonExistentWebhookId, // References non-existent webhook
      event_nanoid: nonExistentEventId, // References non-existent event
      status: 'queued' as const,
      created_at: new Date(),
      updated_at: new Date(),
    }

    // Act & Assert
    try {
      await db
        .insertInto('rise.webhook_deliveries')
        .values(invalidDelivery)
        .execute()
    } catch (error: any) {
      expect(error).toBeDefined()
      expect(error.errno).toBe(
        DatabaseErrorCodes.FOREIGN_KEY_CONSTRAINT_VIOLATION,
      )
    }
  })

  // Relational Queries
  test('should join webhook deliveries with endpoints and events', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()
    const delivery = generateWebhookDelivery(webhook.nanoid, event.nanoid)

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()
    await db.insertInto('rise.webhook_deliveries').values(delivery).execute()

    // Act
    const joined = await db
      .selectFrom('rise.webhook_deliveries')
      .leftJoin(
        'rise.webhook_endpoints',
        'rise.webhook_deliveries.webhook_nanoid',
        'rise.webhook_endpoints.nanoid',
      )
      .leftJoin(
        'rise.webhook_events',
        'rise.webhook_deliveries.event_nanoid',
        'rise.webhook_events.nanoid',
      )
      .select([
        'rise.webhook_deliveries.nanoid as delivery_nanoid',
        'rise.webhook_deliveries.status',
        'rise.webhook_endpoints.url',
        'rise.webhook_events.event_type',
      ])
      .where('rise.webhook_deliveries.nanoid', '=', delivery.nanoid)
      .executeTakeFirst()

    // Assert
    expect(joined).toBeDefined()
    expect(joined?.delivery_nanoid).toBe(delivery.nanoid)
    expect(joined?.url).toBe(webhook.url)
    expect(joined?.event_type).toBe(event.event_type)
  })

  test('should handle webhook delivery history', async () => {
    // Arrange
    const webhook = generateWebhookEndpoint()
    const event = generateWebhookEvent()

    const attempt1 = generateWebhookDelivery(webhook.nanoid, event.nanoid, {
      status: 'failed',
      response_code: 500,
      created_at: new Date('2023-01-01'),
    })
    const attempt2 = generateWebhookDelivery(webhook.nanoid, event.nanoid, {
      status: 'failed',
      response_code: 502,
      created_at: new Date('2023-01-02'),
    })
    const attempt3 = generateWebhookDelivery(webhook.nanoid, event.nanoid, {
      status: 'success',
      response_code: 200,
      created_at: new Date('2023-01-03'),
    })

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
    await db.insertInto('rise.webhook_events').values(event).execute()

    // Act
    await db
      .insertInto('rise.webhook_deliveries')
      .values([attempt1, attempt2, attempt3])
      .execute()

    const history = await db
      .selectFrom('rise.webhook_deliveries')
      .selectAll()
      .where('webhook_nanoid', '=', webhook.nanoid)
      .where('event_nanoid', '=', event.nanoid)
      .orderBy('created_at', 'asc')
      .execute()

    // Assert
    expect(history).toHaveLength(3)
    expect(history[0]?.status).toBe('failed')
    expect(history[1]?.status).toBe('failed')
    expect(history[2]?.status).toBe('success')
  })
})
