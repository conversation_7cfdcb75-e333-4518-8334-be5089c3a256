import { describe, expect, test, afterEach } from 'vitest'
import { db } from 'db'
import {
  generateCompanyNanoid,
  generateTeamNanoid,
  generateUserNanoid,
  generateWebhookEndpoint,
} from '../mocks/index.js'
import { DatabaseErrorCodes, mockGetSecrets } from '../utils/index.js'

// Mock getSecrets for database connection
mockGetSecrets()

describe('webhook_endpoints', () => {
  afterEach(async () => {
    // Clean up in correct order to avoid foreign key constraint violations
    await db.deleteFrom('rise.webhook_deliveries').execute()
    await db.deleteFrom('rise.webhook_events').execute()
    await db.deleteFrom('rise.webhook_endpoints').execute()
  })

  test('should create webhook endpoint successfully', async () => {
    const webhook = generateWebhookEndpoint()

    const result = await db
      .insertInto('rise.webhook_endpoints')
      .values(webhook)
      .execute()

    expect(result).toBeDefined()

    const created = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('nanoid', '=', webhook.nanoid)
      .executeTakeFirst()

    expect(created).toBeDefined()
    expect(created?.company_nanoid).toBe(webhook.company_nanoid)
    expect(created?.url).toBe(webhook.url)
    expect(created?.is_active).toBe(true)
  })

  test('should retrieve webhook endpoint by company', async () => {
    const webhook1 = generateWebhookEndpoint()
    const webhook2 = generateWebhookEndpoint({
      company_nanoid: webhook1.company_nanoid,
      team_nanoid: null,
    })
    const webhook3 = generateWebhookEndpoint()

    await db
      .insertInto('rise.webhook_endpoints')
      .values([webhook1, webhook2, webhook3])
      .execute()

    const companyWebhooks = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('company_nanoid', '=', webhook1.company_nanoid)
      .execute()

    expect(companyWebhooks).toHaveLength(2)
    expect(
      companyWebhooks.every(
        (w) => w.company_nanoid === webhook1.company_nanoid,
      ),
    ).toBe(true)
  })

  test('should update webhook endpoint', async () => {
    const webhook = generateWebhookEndpoint()

    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()

    const newUrl = 'https://updated.example.com/webhook'
    const newModifier = generateUserNanoid()

    await db
      .updateTable('rise.webhook_endpoints')
      .set({
        url: newUrl,
        last_modified_by: newModifier,
        updated_at: new Date(),
      })
      .where('nanoid', '=', webhook.nanoid)
      .execute()

    const updated = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('nanoid', '=', webhook.nanoid)
      .executeTakeFirst()

    expect(updated?.url).toBe(newUrl)
    expect(updated?.last_modified_by).toBe(newModifier)
  })

  test('should filter active/inactive webhooks', async () => {
    const companyNanoid = generateCompanyNanoid()
    const activeWebhook = generateWebhookEndpoint({
      company_nanoid: companyNanoid,
      is_active: true,
    })
    const inactiveWebhook = generateWebhookEndpoint({
      company_nanoid: companyNanoid,
      is_active: false,
    })

    await db
      .insertInto('rise.webhook_endpoints')
      .values([activeWebhook, inactiveWebhook])
      .execute()

    const activeWebhooks = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('company_nanoid', '=', companyNanoid)
      .where('is_active', '=', true)
      .execute()

    const inactiveWebhooks = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('company_nanoid', '=', companyNanoid)
      .where('is_active', '=', false)
      .execute()

    expect(activeWebhooks).toHaveLength(1)
    expect(inactiveWebhooks).toHaveLength(1)
    expect(activeWebhooks[0]?.nanoid).toBe(activeWebhook.nanoid)
    expect(inactiveWebhooks[0]?.nanoid).toBe(inactiveWebhook.nanoid)
  })

  test('should handle team-level webhooks', async () => {
    const companyNanoid = generateCompanyNanoid()
    const teamNanoid = generateTeamNanoid()

    const companyLevelWebhook = generateWebhookEndpoint({
      company_nanoid: companyNanoid,
      team_nanoid: null,
    })
    const teamLevelWebhook = generateWebhookEndpoint({
      company_nanoid: companyNanoid,
      team_nanoid: teamNanoid,
    })

    await db
      .insertInto('rise.webhook_endpoints')
      .values([companyLevelWebhook, teamLevelWebhook])
      .execute()

    const companyLevel = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('company_nanoid', '=', companyNanoid)
      .where('team_nanoid', 'is', null)
      .execute()

    const teamLevel = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('team_nanoid', '=', teamNanoid)
      .execute()

    expect(companyLevel).toHaveLength(1)
    expect(teamLevel).toHaveLength(1)
    expect(companyLevel[0]?.nanoid).toBe(companyLevelWebhook.nanoid)
    expect(teamLevel[0]?.nanoid).toBe(teamLevelWebhook.nanoid)
  })

  // NULL/NOT NULL validation tests for webhook_endpoints
  test.each`
    fieldName | invalidWebhook
    ${'nanoid'} | ${() => {
  const base = generateWebhookEndpoint()
  const { nanoid, ...webhook } = base
  return webhook
}}
    ${'company_nanoid'} | ${() => {
  const base = generateWebhookEndpoint()
  const { company_nanoid, ...webhook } = base
  return webhook
}}
    ${'created_by'} | ${() => {
  const base = generateWebhookEndpoint()
  const { created_by, ...webhook } = base
  return webhook
}}
    ${'url'} | ${() => {
  const base = generateWebhookEndpoint()
  const { url, ...webhook } = base
  return webhook
}}
    ${'secret_encrypted'} | ${() => {
  const base = generateWebhookEndpoint()
  const { secret_encrypted, ...webhook } = base
  return webhook
}}
    ${'events'} | ${() => {
  const base = generateWebhookEndpoint()
  const { events, ...webhook } = base
  return webhook
}}
  `(
    'should fail when missing required $fieldName field',
    async ({ fieldName, invalidWebhook }) => {
      // Arrange
      const webhook = invalidWebhook()

      // Act & Assert
      try {
        await db.insertInto('rise.webhook_endpoints').values(webhook).execute()
      } catch (error: any) {
        expect(error).toBeDefined()
        expect(error.errno).toBe(DatabaseErrorCodes.ER_NO_DEFAULT_FOR_FIELD)
      }
    },
  )

  test('should allow nullable team_nanoid and last_modified_by fields', async () => {
    // Arrange
    const webhookWithNulls = generateWebhookEndpoint({
      team_nanoid: null,
      last_modified_by: null,
    })

    // Act
    const result = await db
      .insertInto('rise.webhook_endpoints')
      .values(webhookWithNulls)
      .execute()

    const created = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('nanoid', '=', webhookWithNulls.nanoid)
      .executeTakeFirst()

    // Assert
    expect(result).toBeDefined()
    expect(created?.team_nanoid).toBeNull()
    expect(created?.last_modified_by).toBeNull()
  })

  test('should handle webhook endpoint without team (company-level)', async () => {
    // Arrange
    const companyWebhook = generateWebhookEndpoint({
      team_nanoid: null,
    })

    // Act
    const result = await db
      .insertInto('rise.webhook_endpoints')
      .values(companyWebhook)
      .execute()

    const created = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('nanoid', '=', companyWebhook.nanoid)
      .executeTakeFirst()

    // Assert
    expect(result).toBeDefined()
    expect(created?.team_nanoid).toBeNull()
    expect(created?.company_nanoid).toBe(companyWebhook.company_nanoid)
  })

  test('should store JSON events array correctly', async () => {
    // Arrange
    const eventTypes = ['payment.created', 'invoice.paid', 'user.created']
    const webhook = generateWebhookEndpoint({
      events: eventTypes,
    })

    // Act
    await db.insertInto('rise.webhook_endpoints').values(webhook).execute()

    const created = await db
      .selectFrom('rise.webhook_endpoints')
      .selectAll()
      .where('nanoid', '=', webhook.nanoid)
      .executeTakeFirst()

    // Assert
    expect(created?.events).toEqual(eventTypes)
  })
})
