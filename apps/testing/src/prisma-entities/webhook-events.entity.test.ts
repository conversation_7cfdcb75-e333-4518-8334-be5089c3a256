import { describe, expect, test, afterEach } from 'vitest'
import { db } from 'db'
import { generateUserNanoid, generateWebhookEvent } from '../mocks/index.js'
import { DatabaseErrorCodes, mockGetSecrets } from '../utils/index.js'

// Mock getSecrets for database connection
mockGetSecrets()

describe('webhook_events', () => {
  afterEach(async () => {
    // Clean up in correct order to avoid foreign key constraint violations
    await db.deleteFrom('rise.webhook_deliveries').execute()
    await db.deleteFrom('rise.webhook_events').execute()
  })

  test('should create webhook event successfully', async () => {
    // Arrange
    const event = generateWebhookEvent()

    // Act
    const result = await db
      .insertInto('rise.webhook_events')
      .values(event)
      .execute()

    const created = await db
      .selectFrom('rise.webhook_events')
      .selectAll()
      .where('nanoid', '=', event.nanoid)
      .executeTakeFirst()

    // Assert
    expect(result).toBeDefined()
    expect(created).toBeDefined()
    expect(created?.nanoid).toBe(event.nanoid)
    expect(created?.event_type).toBe(event.event_type)
    expect(created!.payload).toEqual(JSON.parse(event.payload as any))
  })

  test('should query events by type', async () => {
    // Arrange
    const paymentEvent = generateWebhookEvent({
      event_type: 'payment.payment_sent',
    })
    const userEvent = generateWebhookEvent({
      event_type: 'deposit.deposit_received',
    })

    // Act
    await db
      .insertInto('rise.webhook_events')
      .values([paymentEvent, userEvent])
      .execute()

    const paymentEvents = await db
      .selectFrom('rise.webhook_events')
      .selectAll()
      .where('event_type', '=', 'payment.payment_sent')
      .execute()

    // Assert
    expect(paymentEvents).toHaveLength(1)
    expect(paymentEvents[0]?.nanoid).toBe(paymentEvent.nanoid)
  })

  test('should handle different payload types', async () => {
    // Arrange
    const complexPayload = {
      user_id: generateUserNanoid(),
      amount: 50000,
      currency: 'EUR',
      metadata: {
        source: 'api',
        version: '2.0',
      },
    }

    const event = generateWebhookEvent({
      event_type: 'payment.payment_sent',
      payload: complexPayload,
    })

    // Act
    await db.insertInto('rise.webhook_events').values(event).execute()

    const created = await db
      .selectFrom('rise.webhook_events')
      .selectAll()
      .where('nanoid', '=', event.nanoid)
      .executeTakeFirst()

    // Assert
    expect(created?.payload).toEqual(complexPayload)
  })

  test('should handle different event types', async () => {
    // Arrange
    const eventTypes = [
      'payment.payment_sent',
      'deposit.deposit_received',
      'account_duplicated.detected',
      'invites.invite_accepted',
    ] as const

    const events = eventTypes.map((type) =>
      generateWebhookEvent({
        event_type: type,
        payload: { type, timestamp: new Date().toISOString() },
      }),
    )

    // Act
    await db.insertInto('rise.webhook_events').values(events).execute()

    // Assert
    for (const eventType of eventTypes) {
      const typeEvents = await db
        .selectFrom('rise.webhook_events')
        .selectAll()
        .where('event_type', '=', eventType)
        .execute()

      expect(typeEvents).toHaveLength(1)
      expect(typeEvents[0]?.payload).toEqual({
        type: eventType,
        timestamp: expect.any(String),
      })
    }
  })

  test('should order events by creation time', async () => {
    // Arrange
    const baseTime = new Date('2023-01-01')
    const firstEvent = generateWebhookEvent({
      event_type: 'deposit.deposit_received',
      created_at: new Date(baseTime.getTime()),
    })
    const secondEvent = generateWebhookEvent({
      event_type: 'account_duplicated.detected',
      created_at: new Date(baseTime.getTime() + 1000),
    })

    // Act
    await db
      .insertInto('rise.webhook_events')
      .values([secondEvent, firstEvent])
      .execute()

    const orderedEvents = await db
      .selectFrom('rise.webhook_events')
      .selectAll()
      .orderBy('created_at', 'asc')
      .execute()

    // Assert
    expect(orderedEvents).toHaveLength(2)
  })

  // NULL/NOT NULL validation tests for webhook_events
  test.each`
    fieldName | invalidEvent
    ${'nanoid'} | ${() => {
  const base = generateWebhookEvent()
  const { nanoid, ...event } = base
  return event
}}
    ${'event_type'} | ${() => {
  const base = generateWebhookEvent()
  const { event_type, ...event } = base
  return event
}}
    ${'payload'} | ${() => {
  const base = generateWebhookEvent()
  const { payload, ...event } = base
  return event
}}
    ${'version'} | ${() => {
  const base = generateWebhookEvent()
  const { version, ...event } = base
  return event
}}
  `(
    'should fail when missing required $fieldName field',
    async ({ fieldName, invalidEvent }) => {
      // Arrange
      const event = invalidEvent()

      // Act & Assert
      try {
        await db.insertInto('rise.webhook_events').values(event).execute()
      } catch (error: any) {
        expect(error).toBeDefined()
        expect(error.errno).toBe(DatabaseErrorCodes.ER_NO_DEFAULT_FOR_FIELD)
      }
    },
  )

  test('should store version information correctly', async () => {
    // Arrange
    const customVersions = ['1.0.0', '2.1.0', '3.0.0-beta']

    const events = customVersions.map((version) =>
      generateWebhookEvent({
        version,
        event_type: 'payment.payment_sent',
      }),
    )

    // Act
    await db.insertInto('rise.webhook_events').values(events).execute()

    // Assert
    for (const version of customVersions) {
      const versionEvents = await db
        .selectFrom('rise.webhook_events')
        .selectAll()
        .where('version', '=', version)
        .execute()

      expect(versionEvents).toHaveLength(1)
      expect(versionEvents[0]?.version).toBe(version)
    }
  })
})
