# Entity Testing Guide

Database entity integration tests that verify CRUD operations, constraints, relationships, and business logic using type-safe mocks and consistent patterns.

## Test Patterns

### AAA Pattern (Arrange, Act, Assert)

All tests must follow the AAA pattern with clear comment sections:

```typescript
test('should create webhook endpoint successfully', async () => {
  // Arrange
  const webhookData = mockWebhookEndpoint({
    url: 'https://test.example.com/webhook',
    events: ['payment.created']
  })

  // Act
  const result = await db
    .insertInto('rise.webhook_endpoints')
    .values(webhookData)
    .execute()

  const created = await db
    .selectFrom('rise.webhook_endpoints')
    .selectAll()
    .where('nanoid', '=', webhookData.nanoid)
    .executeTakeFirst()

  // Assert
  expect(result).toBeDefined()
  expect(created?.url).toBe('https://test.example.com/webhook')
  expect(created?.events).toEqual(['payment.created'])
})
```

### Parameterized Tests

Use `test.each` with table format for similar test cases:

```typescript
test.each`
  fieldName             | invalidData
  ${'nanoid'}           | ${() => ({ /* missing nanoid */ })}
  ${'company_nanoid'}   | ${() => ({ /* missing company_nanoid */ })}
  ${'url'}              | ${() => ({ /* missing url */ })}
`('should fail when missing required $fieldName field', async ({ invalidData }) => {
  // Arrange
  const webhook = invalidData()

  // Act & Assert
  await expect(
    db.insertInto('rise.webhook_endpoints').values(webhook).execute()
  ).rejects.toThrow()
})
```

### Database Cleanup

Always clean up test data in dependency order:

```typescript
describe('webhook_deliveries', () => {
  afterEach(async () => {
    // Clean up in reverse dependency order
    await db.deleteFrom('rise.webhook_deliveries').execute()
    await db.deleteFrom('rise.webhook_events').execute()
    await db.deleteFrom('rise.webhook_endpoints').execute()
  })
})
```

## Test Categories

### 1. **CRUD Operations**
- Create, Read, Update, Delete operations
- Entity retrieval by ID and filters
- Bulk operations and transactions

### 2. **Database Constraints**
- **Required Fields**: Use parameterized tests for all required fields
- **Nullable Fields**: Test that nullable fields accept null values
- **Unique Constraints**: Test duplicate prevention
- **Default Values**: Verify database defaults are applied

### 3. **Relationships & JSON Fields**
- **Foreign Keys**: Test constraint enforcement with invalid references
- **JSON Serialization**: Test complex object storage and retrieval
- **Cascade Operations**: Test deletion behavior and constraints

## Entity-Specific Patterns

### Webhook Endpoints
- URL validation and event type array serialization
- Company vs team-level webhook distinction
- Active/inactive status filtering

### Webhook Events
- Event type categorization and payload serialization
- Event ordering by timestamp

### Webhook Deliveries
- Status transitions (queued → retrying → success/failed)
- Retry attempt tracking and error handling
- Foreign key relationships with endpoints and events

## Best Practices

### Mock Data Usage
```typescript
// ✅ Good - Use centralized generators
const webhook = mockWebhookEndpoint({
  url: 'https://test-specific-url.com'
})

// ❌ Bad - Manual object creation
const webhook = {
  nanoid: 'manual-id',
  company_nanoid: 'manual-company'
  // ... error-prone and not type-safe
}
```

### Test Independence
- Each test should be independent
- Clean up data in `afterEach()` hooks
- Use unique identifiers to avoid conflicts

### Error Testing
- Test both success and failure scenarios
- Verify specific error types and messages
- Test constraint violations

### Database Error Code Assertions

For database constraint and error testing, use the shared `DatabaseErrorCodes` enum from `utils/database-errors.ts` to assert on MySQL error codes. This ensures your tests are readable and robust against DB constraint violations.

**Example:**
```typescript
import { DatabaseErrorCodes } from '../utils/database-errors.js'

try {
  await db.insertInto('rise.webhook_endpoints').values(invalidData).execute()
} catch (error: any) {
  expect(error).toBeDefined()
  expect(error.errno).toBe(DatabaseErrorCodes.ER_NO_DEFAULT_FOR_FIELD)
}
```

This pattern is used for NOT NULL, foreign key, unique, and other constraint violations. See `utils/database-errors.ts` for all available codes.

### Descriptive Test Names
```typescript
// ✅ Good
test('should update webhook endpoint URL and maintain other fields unchanged')
test('should fail when creating webhook with invalid company_nanoid')

// ❌ Bad
test('should update webhook')
test('should fail')
```

## Troubleshooting

**Common Issues:**
1. **Foreign Key Violations**: Ensure parent entities exist first
2. **JSON Serialization**: Use `JSON.stringify()` for insertion, expect parsed objects on retrieval
3. **Type Mismatches**: Use proper `Insertable<T>` types from database schema
4. **Cleanup Order**: Delete dependent entities before parent entities

**Adding New Entity Tests:**
1. Create mock generators in `/mocks` directory
2. Follow naming conventions (`entity-name.entity.test.ts`)
3. Implement all test categories (CRUD, constraints, relationships)
4. Update documentation with entity-specific patterns 