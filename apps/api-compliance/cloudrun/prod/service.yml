apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: api-compliance
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing

# https://doc.crds.dev/github.com/knative/serving/serving.knative.dev/Service/v1@knative-v1.2.2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-egress: all-traffic
        run.googleapis.com/cloudsql-instances: riseworksprod:us-central1:rise
        autoscaling.knative.dev/maxScale: "30"
        autoscaling.knative.dev/minScale: "1"
        run.googleapis.com/vpc-access-connector: projects/riseworksprod/locations/us-central1/connectors/rise-vpc-connector
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 50
      timeoutSeconds: 120
      containers:
      - image: service-image
        ports:
        - name: http1
          containerPort: "8080"
        resources:
          limits:
            cpu: 1000m
            memory: 3000Mi
        env:
        - name: NODE_ENV
          value: production
        - name: GOOGLE_PROJECT_ID
          value: riseworksprod
        - name: GOOGLE_PROJECT_NUMBER
          value: "***********"
        - name: LOGLEVEL
          value: INFO
        - name: DB_HOST
          value: "*********"
        - name: DB_PORT
          value: "3306"
        - name: DB_SCHEMA
          value: rise
        - name: DB_CONNECTION_LIMIT
          value: "50"
        - name: DB_CONNECTION_TIMEOUT
          value: "10000"
        - name: SEGMENT_SOURCE
          value: KGNQ2d8SvPlAXQdFR0mw8rVXX2KUF3Se
        - name: SENTRY_DSN
          value: https://<EMAIL>/****************
        - name: GOOGLE_LOCATION_ID
          value: us-central1
        - name: GOOGLE_OIDC_EMAIL
          value: <EMAIL>
        - name: GOOGLE_DRIVE_COMPANY_DOCUMENTS_FOLDER_ID
          value: 1O71gZDFTlQ6r0mPVoo-L3_Y3PHC9HIjz
        - name: GOOGLE_RECAPTCHA
          value: 6LfPLRMhAAAAAKEkfgDVD_RQfRb9oafyBs6lF4XF
        - name: APP_DOMAIN
          value: https://pay-api.riseworks.io
        - name: HTTP_PORT
          value: "8080"
        - name: REDIS_URI
          value: redis://************:6379
        - name: AVATARS_BUCKET
          value: prod-avatars-riseworks-io
        - name: AVATARS_BUCKET_URL
          value: https://storage.googleapis.com/prod-avatars-riseworks-io
        - name: ENABLE_METRICS
          value: "false"

  traffic:
  - percent: 100
    latestRevision: true
