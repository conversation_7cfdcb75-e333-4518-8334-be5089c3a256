{"name": "api-compliance", "version": "1.0.1", "description": "Integrations for our compliance needs", "main": "./src/index.js", "types": "./src/index.ts", "author": "rise", "license": "UNLICENSED", "private": true, "type": "module", "scripts": {"dev": "tsx --import ../../packages/backend/src/opentelemetry/index.mjs --env-file=../../.base.env --env-file=.env --watch src/index.ts", "build": "swc ./src -s --ignore **/*.js -d .", "clean": "rimraf \"dist\" \"./src/**/*.js\" \"./src/**/*.d.ts\" \"./src/**/*.d.ts.map\" \"./src/**/*.js.map\" -g", "start:production": "node --enable-source-maps --import \"../../packages/backend/src/opentelemetry/index.mjs\" src/index.js", "start:production:test": "node --env-file=../../.base.env --env-file=.env --import \"../../packages/backend/src/opentelemetry/index.mjs\" src/index.js", "lint": "biome lint --no-errors-on-unmatched --write src/**/*.ts --config-path ../../", "typecheck": "tsc -b --emitDeclarationOnly", "typecheck:watch": "tsc -b --emitDeclarationOnly --watch"}, "dependencies": {"@riseworks/contracts": "workspace:*", "backend": "workspace:*", "dayjs": "^1.11.13", "db": "workspace:*", "features": "workspace:*", "go-go-try": "^6.2.0", "remeda": "^2.23.0", "repositories": "workspace:*", "utils": "workspace:*", "zod": "^3.25.63"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.0", "@types/node": "^22.15.31", "rimraf": "^6.0.1", "tsx": "^4.20.2", "typescript": "^5.8.3"}}