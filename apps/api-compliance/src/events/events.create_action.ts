import { sendAction } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-events-create-action',
    name: 'unit21/events.create_action',
  },
  [{ event: 'unit21/events.create_action' }],
  async ({ event }) => {
    return await sendAction(event.data)
  },
)
