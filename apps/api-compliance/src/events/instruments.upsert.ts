import { upsertInstrument } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-instruments-upsert',
    name: 'unit21/instruments.upsert',
  },
  [{ event: 'unit21/instruments.upsert' }],
  async ({ event }) => {
    return await upsertInstrument(event.data)
  },
)
