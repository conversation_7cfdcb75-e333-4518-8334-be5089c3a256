import {
  type CompanyNanoid,
  type UserNanoid,
  companyNanoid,
  userNanoid,
} from '@riseworks/contracts/src/brands.js'
import { getCompanyByNanoid } from 'repositories/src/companies.js'
import { upsertComplianceData } from 'repositories/src/complianceData.js'
import { Firebase } from 'repositories/src/firebase.js'
import { track } from 'repositories/src/segment.js'
import { SumsubAlgType } from 'repositories/src/sumsub.js'
import {
  getFounderFromCompany,
  getNotificationToken,
  getUserByNanoid,
} from 'repositories/src/users.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { run } from 'utils/src/common/run.js'
import { isSignatureValid } from 'utils/src/common/signature.js'
import { getSecrets } from 'utils/src/google/getSecrets.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const { 'sumsub-tokens': sumsubTokens } = await getSecrets(['sumsub-tokens'])

const secrets = sumsubTokens.split(';')
const sumsubWebhookKey = secrets[secrets.length - 1]
assert(sumsubWebhookKey, 'Missing sumsub webhook key')

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'sumsub-applicant-pending',
    name: 'sumsub/sumsub.applicant_pending',
  },
  [{ event: 'sumsub/sumsub.applicant_pending' }],
  async ({ event: { data }, logger }) => {
    const {
      headers: {
        'X-Payload-Digest': signature,
        'X-Payload-Digest-Alg': signatureAlgorithm,
      },
      raw,
      externalUserId,
    } = data
    assert(
      isSignatureValid({
        data: raw,
        secret: sumsubWebhookKey,
        signature,
        cryptoType: SumsubAlgType(signatureAlgorithm),
      }),
      'Invalid Sumsub Signature',
    )
    logger.info(
      `New Sumsub Applicant Pending Webhook for user ${externalUserId}`,
    )
    const type = run(() => {
      if (is(externalUserId, companyNanoid)) return 'company'
      if (is(externalUserId, userNanoid)) return 'user'
      return 'unknown'
    })
    assert(
      type !== 'unknown',
      `Unknown entity type for externalUserId ${externalUserId}`,
      '404',
    )
    const entity =
      type === 'user'
        ? await getUserByNanoid(externalUserId as UserNanoid)
        : await getCompanyByNanoid(externalUserId as CompanyNanoid)
    assert(
      entity,
      `Entity not found for externalUserId ${externalUserId}`,
      '404',
    )
    await upsertComplianceData({
      nanoid: entity.nanoid,
      type,
      status: 'pending',
      external_data: JSON.stringify(data),
      compliance_level: data.levelName,
      compliance_id: data.applicantId,
    })
    const userId = await run(async () => {
      if (entity.type === 'company') {
        const user = await getFounderFromCompany(entity.nanoid)
        assert(user, `User ${entity.nanoid} not found`, '404')
        return user.nanoid
      }
      return entity.nanoid as UserNanoid
    })
    track(userId, 'SumsubStatus', { status: 'submitted' })
    const notificationToken = await getNotificationToken(userId)
    logger.info(
      `Notification token retrieved: 
      id: ${notificationToken?.id} - 
      event: SumsubStatus - 
      Nanoid: ${userId}`,
    )
    await Firebase.pushNotification({
      token: notificationToken?.token,
      notification: {
        title: 'Sumsub Status Updated',
        body: 'Sumsub applicant submitted',
      },
      data: {
        eventName: 'SumsubStatus',
        status: 'submitted',
      },
      userId,
    })
    return true
  },
)
