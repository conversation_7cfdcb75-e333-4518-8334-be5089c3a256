import { upsertClientProfile } from 'repositories/src/klaviyo.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'klaviyo-upsert-profile',
    name: 'klaviyo/upsert.profile',
  },
  [{ event: 'klaviyo/upsert.profile' }], // you can add more triggers here, like cron
  async ({ event, step, logger }) => {
    return await upsertClientProfile(event.data)
  },
)
