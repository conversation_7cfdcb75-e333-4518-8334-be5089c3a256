import { sendTransaction } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-events-create-transaction',
    name: 'unit21/events.create_transaction',
  },
  [{ event: 'unit21/events.create_transaction' }],
  async ({ event }) => {
    return await sendTransaction(event.data)
  },
)
