import { assignInstrumentToEntity } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-entities-assign-instrument',
    name: 'unit21/entities.assign_instrument',
  },
  [{ event: 'unit21/entities.assign_instrument' }],
  async ({ event }) => {
    return await assignInstrumentToEntity(event.data)
  },
)
