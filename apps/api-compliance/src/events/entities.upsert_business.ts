import { upsertBusiness } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-entities-upsert-business',
    name: 'unit21/entities.upsert_business',
  },
  [{ event: 'unit21/entities.upsert_business' }],
  async ({ event }) => {
    return await upsertBusiness(event.data)
  },
)
