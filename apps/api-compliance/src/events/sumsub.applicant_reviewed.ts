import {
  type CompanyNanoid,
  type UserNanoid,
  companyNanoid,
  userNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db } from 'db'
import {
  getCompanyByNanoid,
  getFullCompanyData,
} from 'repositories/src/companies.js'
import {
  updateCompanyDataFromSumsub,
  updateUserDataFromSumsub,
} from 'repositories/src/compliance.js'
import { upsertComplianceData } from 'repositories/src/complianceData.js'
import { Firebase } from 'repositories/src/firebase.js'
// import { upsertDocument } from 'repositories/src/documents.js'
import { track } from 'repositories/src/segment.js'
import { SumsubAlgType } from 'repositories/src/sumsub.js'
import {
  getFounderFromCompany,
  getNotificationToken,
  getUserByNanoid,
} from 'repositories/src/users.js'
import {
  getOnboard,
  updateOnboarding,
} from 'repositories/src/usersOnboarding.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { run } from 'utils/src/common/run.js'
import { isSignatureValid } from 'utils/src/common/signature.js'
import { getSecrets } from 'utils/src/google/getSecrets.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const { 'sumsub-tokens': sumsubTokens } = await getSecrets(['sumsub-tokens'])

const secrets = sumsubTokens.split(';')
const sumsubWebhookKey = secrets[secrets.length - 1]
assert(sumsubWebhookKey, 'Missing sumsub webhook key')

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'sumsub-applicant-reviewed',
    name: 'sumsub/sumsub.applicant_reviewed',
  },
  [{ event: 'sumsub/sumsub.applicant_reviewed' }], // you can add more triggers here, like cron
  async ({ event: { data }, logger }) => {
    const {
      headers: {
        'X-Payload-Digest': signature,
        'X-Payload-Digest-Alg': signatureAlgorithm,
      },
      raw,
      externalUserId,
      reviewResult: {
        reviewAnswer,
        rejectLabels,
        moderationComment,
        clientComment,
      },
    } = data
    assert(
      isSignatureValid({
        data: raw,
        secret: sumsubWebhookKey,
        signature,
        cryptoType: SumsubAlgType(signatureAlgorithm),
      }),
      'Invalid Sumsub Signature',
    )
    logger.info(
      `New Sumsub Applicant Reviewed Webhook: ${JSON.stringify(reviewAnswer)}`,
    )
    const type = run(() => {
      if (is(externalUserId, companyNanoid)) return 'company'
      if (is(externalUserId, userNanoid)) return 'user'
      return 'unknown'
    })
    assert(
      type !== 'unknown',
      `Unknown entity type for externalUserId ${externalUserId}`,
      '404',
    )
    const entity =
      type === 'user'
        ? await getUserByNanoid(externalUserId as UserNanoid)
        : await getCompanyByNanoid(externalUserId as CompanyNanoid)
    assert(
      entity,
      `Entity not found with externalUserId ${externalUserId}`,
      '404',
    )
    const user =
      type === 'company'
        ? await getFounderFromCompany(entity.nanoid as CompanyNanoid)
        : await getUserByNanoid(externalUserId as UserNanoid)
    assert(user, `User not found with externalUserId ${externalUserId}`, '404')

    const approved = reviewAnswer === 'GREEN'
    const status = approved ? 'approved' : 'rejected'
    const onboard = await getOnboard(user.nanoid as UserNanoid)

    await db.transaction().execute(async (trx) => {
      if (type === 'company') {
        await updateCompanyDataFromSumsub(entity.nanoid as CompanyNanoid, trx)
        const companyData = await getFullCompanyData(
          entity.nanoid as CompanyNanoid,
        )
        assert(companyData, `Company not found for nanoid ${entity.nanoid}`)
      }

      await updateUserDataFromSumsub({
        nanoid: user.nanoid,
        companyNanoid: entity.nanoid as CompanyNanoid,
        db: trx,
      })

      if (onboard?.step === 'details') {
        await updateOnboarding(
          user.nanoid,
          {
            step: approved ? 'compliance_process' : 'details',
            moderation_status: status,
            moderation_internal_note: clientComment ?? '',
            moderation_public_note: moderationComment ?? '',
          },
          trx,
        )
      }

      const sumsubData = {
        status,
        ...(reviewAnswer === 'RED'
          ? { rejectLabels, moderationComment }
          : undefined),
      }
      await upsertComplianceData({
        nanoid: entity.nanoid,
        type,
        status: 'complete',
        external_data: JSON.stringify(data),
        compliance_level: data.levelName,
        compliance_id: data.applicantId,
      })
      track(user.nanoid, 'ComplianceStatus', sumsubData)
      const notificationToken = await getNotificationToken(user.nanoid)
      logger.info(
        `Notification token retrieved: 
      id: ${notificationToken?.id} - 
      event: ComplianceStatus - 
      Nanoid: ${user.nanoid}`,
      )
      await Firebase.pushNotification({
        token: notificationToken?.token,
        notification: {},
        data: {
          eventName: 'ComplianceStatus',
          rejectLabels: sumsubData.rejectLabels?.join(',') || '',
          moderationComment: sumsubData.moderationComment || '',
          status: sumsubData.status,
        },
        userId: user.nanoid,
      })
    })
    return true
  },
)
