import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import { registerWithdrawAddress } from 'repositories/src/chainalysis.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'chainalysis-withdraw-address-register',
    name: 'chainalysis/withdraw_address.register',
    idempotency: 'event.data.address',
  },
  [{ event: 'chainalysis/withdraw_address.register' }],
  async ({
    event: {
      data: { nanoid, network, address },
    },
  }) => {
    return await registerWithdrawAddress(nanoid as UserNanoid, network, address)
  },
)
