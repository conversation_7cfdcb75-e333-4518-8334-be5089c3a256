import {
  type CompanyNanoid,
  type UserNanoid,
  companyNanoid,
  userNanoid,
} from '@riseworks/contracts/src/brands.js'
import { upsertComplianceData } from 'repositories/src/complianceData.js'
import { SumsubAlgType } from 'repositories/src/sumsub.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { run } from 'utils/src/common/run.js'
import { isSignatureValid } from 'utils/src/common/signature.js'
import { getSecrets } from 'utils/src/google/getSecrets.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

const { 'sumsub-tokens': sumsubTokens } = await getSecrets(['sumsub-tokens'])

const secrets = sumsubTokens.split(';')
const sumsubWebhookKey = secrets[secrets.length - 1]
assert(sumsubWebhookKey, 'Missing sumsub webhook key')

export default client.createFunction(
  {
    id: 'sumsub-applicant-created',
    name: 'sumsub/sumsub.applicant_created',
  },
  [{ event: 'sumsub/sumsub.applicant_created' }],
  async ({ event: { data } }) => {
    const {
      headers: {
        'X-Payload-Digest': signature,
        'X-Payload-Digest-Alg': signatureAlgorithm,
      },
      raw,
      externalUserId,
    } = data
    assert(
      isSignatureValid({
        data: raw,
        secret: sumsubWebhookKey,
        signature,
        cryptoType: SumsubAlgType(signatureAlgorithm),
      }),
      'Invalid Sumsub Signature',
    )
    const type = run(() => {
      if (is(externalUserId, companyNanoid)) return 'company'
      if (is(externalUserId, userNanoid)) return 'user'
      return 'unknown'
    })
    assert(
      type !== 'unknown',
      `Unknown entity type for externalUserId ${externalUserId}`,
      '404',
    )
    return await upsertComplianceData({
      nanoid: externalUserId as CompanyNanoid | UserNanoid,
      type,
      status: 'pending',
      external_data: JSON.stringify(data),
      compliance_level: data.levelName,
      compliance_id: data.applicantId,
    })
  },
)
