import { createEvent } from 'repositories/src/klaviyo.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'klaviyo-create-event',
    name: 'klaviyo/create.event',
  },
  [{ event: 'klaviyo/create.event' }],
  async ({ event, step, logger }) => {
    return await createEvent(event.data)
  },
)
