import { db } from 'db'
import { goTry } from 'go-go-try'
import { getApplicantDataWithNanoid } from 'repositories/src/sumsub.js'
import { getPendingModerationOnboardings } from 'repositories/src/usersOnboarding.js'
import { updateOnboarding } from 'repositories/src/usersOnboarding.js'
import { isProduction } from 'utils/src/common/env.js'
import {
  createInngestClient,
  disableCronInLocalEnv,
} from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'sumsub-check-pending',
    name: 'sumsub/sumsub.check_pending',
    retries: 0,
    rateLimit: {
      limit: 1,
      period: '5m',
    },
  },
  disableCronInLocalEnv([
    isProduction ? { cron: '*/5 * * * *' } : { cron: '*/120 * * * *' },
    { event: 'sumsub/sumsub.check_pending' },
  ]),
  async ({ logger }) => {
    const pendingChecks = await getPendingModerationOnboardings()
    for (const p of pendingChecks) {
      const [err, sumsubData] = await goTry(
        getApplicantDataWithNanoid(p.nanoid),
      )
      if (err !== undefined) {
        logger.error(`Failed to get Sumsub data for ${p.nanoid}: ${err}`)
        continue
      }
      const reviewResult = sumsubData?.reviewResult
      if (
        sumsubData.reviewStatus === 'completed' &&
        reviewResult &&
        reviewResult.reviewAnswer
      ) {
        await db.transaction().execute(async (trx) => {
          await updateOnboarding(
            p.nanoid,
            {
              step:
                reviewResult.reviewAnswer === 'GREEN'
                  ? 'compliance_process'
                  : 'details',
              moderation_status:
                reviewResult.reviewAnswer === 'GREEN' ? 'approved' : 'rejected',
              moderation_internal_note: reviewResult.clientComment ?? '',
              moderation_public_note: reviewResult.moderationComment ?? '',
            },
            trx,
          )
        })
      }
    }
    return true
  },
)
