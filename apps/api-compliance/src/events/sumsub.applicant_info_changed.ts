import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import { db } from 'db'
import { SumsubAlgType, getApplicantData } from 'repositories/src/sumsub.js'
import { getUserByNanoid, updateUserData } from 'repositories/src/users.js'
import {
  getOnboard,
  updateOnboarding,
} from 'repositories/src/usersOnboarding.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isSignatureValid } from 'utils/src/common/signature.js'
import { getSecrets } from 'utils/src/google/getSecrets.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

const { 'sumsub-tokens': sumsubTokens } = await getSecrets(['sumsub-tokens'])

const secrets = sumsubTokens.split(';')
const sumsubWebhookKey = secrets[secrets.length - 1]
assert(sumsubWebhookKey, 'Missing sumsub webhook key')
export default client.createFunction(
  {
    id: 'sumsub-applicant-info_changed',
    name: 'sumsub/sumsub.applicant_info_changed',
  },
  [{ event: 'sumsub/sumsub.applicant_info_changed' }],
  async ({ logger, event: { data } }) => {
    const {
      headers: {
        'X-Payload-Digest': signature,
        'X-Payload-Digest-Alg': signatureAlgorithm,
      },
      raw,
      externalUserId,
      applicantId,
      reviewResult,
    } = data
    assert(
      isSignatureValid({
        data: raw,
        secret: sumsubWebhookKey,
        signature,
        cryptoType: SumsubAlgType(signatureAlgorithm),
      }),
      'Invalid Sumsub Signature',
    )
    logger.info(
      `New Sumsub Applicant Personal Info Changed Webhook for user ${externalUserId}`,
    )
    const user = await getUserByNanoid(externalUserId as UserNanoid)
    assert(user, `User not found for externalUserId ${externalUserId}`, '404')
    await db.transaction().execute(async (trx) => {
      if (reviewResult?.reviewAnswer === 'RED') {
        const onboarding = await getOnboard(user.nanoid)
        assert(
          onboarding,
          `Onboarding not found for user ${user.nanoid}`,
          '404',
        )
        await updateOnboarding(
          user.nanoid,
          {
            moderation_status: 'submitted',
            moderation_internal_note: reviewResult.clientComment ?? '',
            moderation_public_note: reviewResult.moderationComment ?? '',
          },
          trx,
        )
      }
      const applicantData = await getApplicantData(applicantId)
      /* 
    - fixedInfo: application data submitted by the user, 
    - info: application data recognized from documents by sumsub 
    */
      await updateUserData(
        user.nanoid,
        {
          phone: applicantData.phone ?? undefined,
          first_name: applicantData?.fixedInfo?.firstName ?? undefined,
          last_name: applicantData?.fixedInfo?.lastName ?? undefined,
          middle_name: applicantData?.fixedInfo?.middleName ?? undefined,
          dob: applicantData?.fixedInfo?.dob
            ? new Date(applicantData?.fixedInfo.dob)
            : undefined,
          country: applicantData?.fixedInfo?.country ?? undefined,
        },
        trx,
      )
    })
    return true
  },
)
