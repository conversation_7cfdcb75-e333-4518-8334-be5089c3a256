import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import { registerWithdrawAttempt } from 'repositories/src/chainalysis.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'chainalysis-withdraw-attempt-register',
    name: 'chainalysis/withdraw_attempt.attempt',
  },
  [{ event: 'chainalysis/withdraw_attempt.register' }],
  async ({ event }) => {
    return await registerWithdrawAttempt({
      ...event.data,
      nanoid: event.data.nanoid as UserNanoid,
    })
  },
)
