import { upsertUser } from 'repositories/src/unit21.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'unit21-entities-upsert-user',
    name: 'unit21/entities.upsert_user',
  },
  [{ event: 'unit21/entities.upsert_user' }],
  async ({ event }) => {
    return await upsertUser(event.data)
  },
)
