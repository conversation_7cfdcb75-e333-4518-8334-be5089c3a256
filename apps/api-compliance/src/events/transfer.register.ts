import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import { registerTransfer } from 'repositories/src/chainalysis.js'
import { getUnit21EventData } from 'repositories/src/unit21.js'

import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default client.createFunction(
  {
    id: 'chainalysis-transfer-register',
    name: 'chainalysis/transfer.register',
    idempotency: 'event.data.txHash',
  },
  [{ event: 'chainalysis/transfer.register' }],
  async ({ event, step }) => {
    const { externalId } = await step.run('register-chainalysis-transfer', () =>
      registerTransfer({
        ...event.data,
        nanoid: event.data.nanoid as UserNanoid,
      }),
    )

    const { receiver_entity_id, receiver_entity_type, sender_entity_id } =
      await getUnit21EventData(event.data.nanoid, event.data.inputAddress)

    await step.sendEvent('send-event-to-unit21', {
      name: 'unit21/events.create_transaction',
      data: {
        type: event.data.type,
        event_time: Math.floor(
          new Date(event.data.timestampISO).getTime() / 1000,
        ),
        id: event.data.txHash,
        transaction_data: {
          amount: +event.data.amount,
          sent_amount: +event.data.amount,
          sent_currency: 'USD',
          sender_entity_id,
          sender_instrument_id: event.data.inputAddress
            ? `crypto-${event.data.inputAddress}`
            : 'crypto-RISE',
          received_amount: +event.data.amount,
          received_currency: 'USD',
          receiver_entity_id,
          receiver_instrument_id: `crypto-${event.data.outputAddress}`,
          transaction_hash: externalId,
          receiver_entity_type,
          sender_entity_type: 'business',
        },
      },
    })
  },
)
