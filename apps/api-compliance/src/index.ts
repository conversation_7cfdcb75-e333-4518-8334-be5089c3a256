import { bootstrap } from 'backend/src/index.js'
import { syncInngestFunctions } from 'backend/src/inngest/helpers.js'
import { createApp } from './app.js'

const K_SERVICE = process.env.K_SERVICE
const PORT = process.env.HTTP_PORT
const app = createApp(await bootstrap('api-compliance'))
await app.listen({
  port: +PORT,
  host: K_SERVICE ? '0.0.0.0' : 'localhost',
})

await syncInngestFunctions('compliance')
