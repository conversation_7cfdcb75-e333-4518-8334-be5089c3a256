// @ts-nocheck
// this file was generated with `pnpm codegen`, do not edit it manually
import type { App } from 'backend/src/index.js'
import tax_questionnaires from './routes/tax_questionnaires.js'
import taxes from './routes/taxes.js'
import entities_assign_instrument from './events/entities.assign_instrument.js'
import entities_upsert_business from './events/entities.upsert_business.js'
import entities_upsert_user from './events/entities.upsert_user.js'
import events_create_action from './events/events.create_action.js'
import events_create_transaction from './events/events.create_transaction.js'
import instruments_upsert from './events/instruments.upsert.js'
import klaviyo_create_event from './events/klaviyo.create_event.js'
import klaviyo_upsert_profile from './events/klaviyo.upsert_profile.js'
import sumsub_applicant_created from './events/sumsub.applicant_created.js'
import sumsub_applicant_info_changed from './events/sumsub.applicant_info_changed.js'
import sumsub_applicant_pending from './events/sumsub.applicant_pending.js'
import sumsub_applicant_reviewed from './events/sumsub.applicant_reviewed.js'
import sumsub_check_pending from './events/sumsub.check_pending.js'
import transfer_register from './events/transfer.register.js'
import withdraw_address_register from './events/withdraw_address.register.js'
import withdraw_attempt_register from './events/withdraw_attempt.register.js'
import { loadInngestFunctions } from 'backend/src/inngest/helpers.js'

export const createApp = (app: App) => {
  tax_questionnaires(app)

  taxes(app)
  loadInngestFunctions(app, 'compliance', [
    entities_assign_instrument,
    entities_upsert_business,
    entities_upsert_user,
    events_create_action,
    events_create_transaction,
    instruments_upsert,
    klaviyo_create_event,
    klaviyo_upsert_profile,
    sumsub_applicant_created,
    sumsub_applicant_info_changed,
    sumsub_applicant_pending,
    sumsub_applicant_reviewed,
    sumsub_check_pending,
    transfer_register,
    withdraw_address_register,
    withdraw_attempt_register,
  ])
  return app
}
