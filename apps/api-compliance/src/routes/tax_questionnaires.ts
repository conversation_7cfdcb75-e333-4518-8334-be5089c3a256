import { complianceTaxQuestionnairesRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { completeW4FormActionItem } from 'repositories/src/action_items.js'
import {
  checkEmployeePayrollRun,
  updateEmployeePayrollSettings,
} from 'repositories/src/employeePayrollSettings.js'
import {
  getEmployeeQuestionnaireValues,
  insertEmployeeQuestionnaireValues,
} from 'repositories/src/employeeTaxQuestionnaires.js'
import { validateOnboardedUserByAuth } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/compliance/tax_questionnaires'].post,
      handler: async ({ body, auth }, reply) => {
        const { user } = await validateOnboardedUserByAuth({ auth })
        assert(body.nanoid === user.nanoid, 'Invalid user', '403')
        await insertEmployeeQuestionnaireValues({
          ...body,
          questionnaire_values: JSON.stringify(body.questionnaire_values),
        })
        await updateEmployeePayrollSettings({
          user_nanoid: user.nanoid,
          team_nanoid: body.team_nanoid,
          data: {
            status: 'active',
          },
        })

        await completeW4FormActionItem(user.nanoid, body.team_nanoid)
        await checkEmployeePayrollRun(user.nanoid, body.team_nanoid)

        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/compliance/tax_questionnaires'].get,
      handler: async ({ auth, query: { team_nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({ auth })
        const data = await getEmployeeQuestionnaireValues(
          user.nanoid,
          team_nanoid,
        )
        assert(data, 'Employee tax questionnaire not found')
        reply.send({ success: true, data })
      },
    })
