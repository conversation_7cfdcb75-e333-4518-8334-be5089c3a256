import { complianceTaxesRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { TaxQuestionnaireValues } from '@riseworks/contracts/src/formats.js'
import type { App } from 'backend/src/index.js'
import dayjs from 'dayjs'
import { groupBy, prop } from 'remeda'
import { getAddress } from 'repositories/src/addresses.js'
import { getEmployeeQuestionnaireValues } from 'repositories/src/employeeTaxQuestionnaires.js'
import { locationCodes, locationTax } from 'repositories/src/symmetry.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/compliance/taxes/location_taxes'].get,
    handler: async ({ auth, query: { team_nanoid } }, reply) => {
      const { user } = await validateUserByAuth({ auth })
      const payDate = dayjs().format('YYYY-MM-DD')
      const federalData = await locationTax(
        'STATE:00-CNTY:000-CITY:0000',
        payDate,
      )
      const address = await getAddress(user.nanoid)
      assert(address, `User ${user.nanoid} address not found`)
      const userLocation = await locationCodes({
        streetAddress1: address.line_1,
        streetAddress2: address.line_2,
        city: address.city,
        state: address.state,
        zip: address.zip_code,
      })
      const userData = await locationTax(userLocation.locationCodeFull, payDate)
      const questionnaire = await getEmployeeQuestionnaireValues(
        user.nanoid,
        team_nanoid,
      )
      const jurisdictionFieldsToRemove = ['2020_W4']
      const finalResult = federalData.concat(userData)
      let questions: Record<string, TaxQuestionnaireValues> = {}
      if (questionnaire && questionnaire.questionnaire_values.length > 0) {
        questions = groupBy(
          questionnaire.questionnaire_values,
          prop('unique_tax_id'),
        )
      }
      const data = {
        dependents_count: questionnaire?.dependents_count ?? 0,
        items: finalResult.map((f) => {
          const uniqueTaxId = f.uniqueTaxID
          return {
            ...f,
            jurisdictionData: f.jurisdictionData
              ?.filter(
                (j) =>
                  j.parameterName &&
                  !jurisdictionFieldsToRemove.includes(j.parameterName),
              )
              .map((j) => {
                return {
                  ...j,
                  currentValue: uniqueTaxId
                    ? (questions[uniqueTaxId]?.find(
                        (q) => q.parameter_name === j.parameterName,
                      )?.value ?? '')
                    : '',
                }
              }),
          }
        }),
      }
      reply.send({
        success: true,
        data,
      })
    },
  })
