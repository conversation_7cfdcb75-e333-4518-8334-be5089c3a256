import type { App } from 'backend/src/index.js'

import { miscErrorRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/misc/error'].get,
    handler: async ({ auth, query }) => {
      await validateUserByAuth({ auth, apps: ['pay'] })

      assert(false, {
        message: 'creating error on misc/error',
        errorCode: query.error_code,
      })
    },
  })
