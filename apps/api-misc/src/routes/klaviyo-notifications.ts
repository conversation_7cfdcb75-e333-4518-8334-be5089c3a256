import { miscKlaviyoNotificationsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default (app: App): App =>
  app.route({
    ...routes['/klaviyo/notification'].post,
    handler: async ({ auth, body }, reply) => {
      const { user } = await validateUserByAuth({ auth })

      await client.send({
        name: 'klaviyo/create.event',
        data: {
          name: body.event,
          properties: body.payload,
          email: user.email,
          // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        } as any,
      })

      reply.send({ success: true })
    },
  })
