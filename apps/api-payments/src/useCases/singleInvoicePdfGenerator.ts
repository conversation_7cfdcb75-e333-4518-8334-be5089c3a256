import type { getCompanyByNanoid } from 'repositories/src/companies.js'
import type {
  getInvoiceData,
  queryAccountsPayment,
} from 'repositories/src/payments.js'
import type { getAddress } from 'repositories/src/addresses.js'

type Recipient = {
  type: 'company' | 'user'
  nanoid: string
  first_name?: string
  middle_name?: string
  last_name?: string
  name?: string
  address: {
    country: string
    state: string
    city: string
    line_1: string
    line_2: string
    zip_code: string
  }
}

export type BuildPdfParams = {
  invoice: Awaited<ReturnType<typeof getInvoiceData>>
  user?: Recipient
  companyData: Awaited<ReturnType<typeof getCompanyByNanoid>>
  companyAddress: Awaited<ReturnType<typeof getAddress>>
  payment?: Awaited<
    ReturnType<typeof queryAccountsPayment>
  >['items'][number]['payment']
}

export const buildPdfFromInvoice = (params: BuildPdfParams) => {
  const { invoice, user, companyData, companyAddress, payment } = params
  const splitDate = invoice!.created_at
    ? new Date(invoice!.created_at).toDateString().split(' ')
    : new Date().toDateString().split(' ')

  const scheduledPaymentDate = payment
    ? new Date(payment.pay_at_time * 1000).toDateString().split(' ')
    : ''

  const customCompanyData = payment ? user!.address : companyAddress

  const customRecipientData = payment ? companyAddress : user?.address
  const formattedDate = `${splitDate[1]} ${splitDate[2]}, ${splitDate[0]} ${splitDate[3]}`
  const formattedSchedulePaymentDate = payment
    ? `${scheduledPaymentDate[1]} ${scheduledPaymentDate[2]}, ${scheduledPaymentDate[0]} ${scheduledPaymentDate[3]}`
    : ''
  const dataForPdf = [
    {
      companyName: payment ? '' : companyData!.name,
      companyAddressLine1: customCompanyData!.line_1,
      companyAddressLine2: customCompanyData!.line_2 || '',
      companyAddressCity: customCompanyData!.city,
      companyAddressState: customCompanyData!.state,
      companyAddressZipCode: customCompanyData!.zip_code,
      companyAddressCountry: customCompanyData!.country,
      recipientName: payment
        ? companyData!.name
        : `${user!.first_name} ${user!.middle_name || ''} ${user!.last_name}`.trim(),
      recipientLine1: customRecipientData!.line_1,
      recipientLine2: customRecipientData!.line_2 || '',
      recipientCity: customRecipientData!.city,
      recipientState: customRecipientData!.state,
      recipientZipCode: customRecipientData!.zip_code,
      recipientCountry: customRecipientData!.country,
      'Due Date': formattedDate,
      payDate: '-',
      invoiceNumber: invoice!.invoice_number || '',
      Item: invoice!.invoice_description || '',
      Role: invoice!.role_description || '',
      Type: payment ? 'Payroll Cash Requirement' : invoice!.type,
      Amount: `$${((payment ? payment.amount_cents : invoice!.amount_cents) / 100).toFixed(2)} ${invoice!.currency}`,
      currency: invoice!.currency,
      formattedDueDate: formattedDate,
      scheduledPaymentDate: formattedSchedulePaymentDate,
    },
  ]

  return dataForPdf
}
