{"name": "api-ramps", "version": "1.0.1", "description": "Handle deposits, withdrawal, withdraw accounts, and sync operations with providers ", "main": "./src/index.js", "types": "./src/index.ts", "author": "rise", "license": "UNLICENSED", "private": true, "type": "module", "scripts": {"dev": "tsx --import ../../packages/backend/src/opentelemetry/index.mjs --env-file=../../.base.env --env-file=.env --watch src/index.ts", "build": "swc ./src -s --ignore **/*.js -d .", "clean": "rimraf \"dist\" \"./src/**/*.js\" \"./src/**/*.d.ts\" \"./src/**/*.d.ts.map\" \"./src/**/*.js.map\" -g", "start:production": "node --enable-source-maps --import \"../../packages/backend/src/opentelemetry/index.mjs\" src/index.js", "start:production:test": "node --env-file=../../.base.env --env-file=.env --import \"../../packages/backend/src/opentelemetry/index.mjs\" src/index.js", "lint": "biome lint --no-errors-on-unmatched --write src/**/*.ts --config-path ../../", "typecheck": "tsc -b --emitDeclarationOnly", "typecheck:watch": "tsc -b --emitDeclarationOnly --watch", "typecheck:ci": "tsc -p ./tsconfig.ci.json"}, "dependencies": {"@riseworks/contracts": "workspace:*", "axios": "^1.9.0", "backend": "workspace:*", "db": "workspace:*", "ethers": "^6.14.3", "features": "workspace:*", "go-go-try": "^6.2.0", "inngest": "^3.39.1", "remeda": "^2.23.0", "repositories": "workspace:*", "utils": "workspace:*", "zod": "^3.25.63"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.0", "@types/node": "^22.15.31", "rimraf": "^6.0.1", "tsx": "^4.20.2", "typescript": "^5.8.3"}}