import { rampsWithdrawalsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import { getWithdrawAccountById } from 'repositories/src/withdrawAccounts.js'
import assert from 'utils/src/common/assertHTTP.js'
import { ERROR_CODES } from 'utils/src/common/errorCodes.js'
import { getWithdrawAccountWaitTimeFields } from '../useCases/withdrawTime.js'

export default (app: App): App =>
  app.route({
    ...routes['/ramps/withdrawals/:account_nanoid/wait_time'].get,
    handler: async ({ auth, params: { account_nanoid } }, reply) => {
      const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
      const withdrawAccount = await getWithdrawAccountById(account_nanoid)

      assert(withdrawAccount, {
        errorCode: ERROR_CODES.WITHDRAW_ACCOUNT_NOT_FOUND,
      })

      assert(withdrawAccount.user_nanoid === user.nanoid, {
        errorCode: ERROR_CODES.WITHDRAW_USER_DOES_NOT_OWN_ACCOUNT,
      })

      const waitTimeFields =
        await getWithdrawAccountWaitTimeFields(withdrawAccount)

      reply.send({
        success: true,
        data: waitTimeFields,
      })
    },
  })
