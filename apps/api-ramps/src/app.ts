// @ts-nocheck
// this file was generated with `pnpm codegen`, do not edit it manually
import type { App } from 'backend/src/index.js'
import withdrawals from './routes/withdrawals.js'
import bitcoin_new_deposit from './events/bitcoin.new_deposit.js'
import provider_withdrawals_sync from './events/provider_withdrawals.sync.js'
import provider_withdrawals_sync_orum_transfer from './events/provider_withdrawals.sync_orum_transfer.js'
import provider_withdrawals_sync_routefusion_transfer from './events/provider_withdrawals.sync_routefusion_transfer.js'
import withdraw_cctp_attestation from './events/withdraw.cctp_attestation.js'
import withdraw_cctp_setfee from './events/withdraw.cctp_setfee.js'
import { loadInngestFunctions } from 'backend/src/inngest/helpers.js'

export const createApp = (app: App) => {
  withdrawals(app)
  loadInngestFunctions(app, 'ramps', [
    bitcoin_new_deposit,
    provider_withdrawals_sync,
    provider_withdrawals_sync_orum_transfer,
    provider_withdrawals_sync_routefusion_transfer,
    withdraw_cctp_attestation,
    withdraw_cctp_setfee,
  ])
  return app
}
