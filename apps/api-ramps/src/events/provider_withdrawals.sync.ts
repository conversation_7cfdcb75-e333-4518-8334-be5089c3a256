import { db } from 'db'
import { isDefined, isString, toLowerCase } from 'remeda'
import { isOneOf } from 'utils/src/common/array.js'
import { isProduction } from 'utils/src/common/env.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('ramps')

type Events = Parameters<typeof client.send>[0]
type SyncEvent = Extract<
  Events,
  {
    name:
      | 'ramps/provider_withdrawals.sync_orum_transfer'
      | 'ramps/provider_withdrawals.sync_routefusion_transfer'
  }
>

export default client.createFunction(
  {
    id: 'ramps-provider-withdrawals-sync',
    name: 'ramps/provider_withdrawals.sync',
  },
  [
    { event: 'ramps/provider_withdrawals.sync' as const },
    isProduction
      ? {
          // Everyday at 6AM
          cron: 'TZ=America/New_York 0 6 * * *',
        }
      : undefined,
  ].filter(isDefined),
  async ({ step }) => {
    const withdraws = await step.run('get-fiat-pending-withdraws', async () => {
      return await db
        .selectFrom('rise.withdrawals as wd')
        .innerJoin('rise.provider_withdrawals as pw', 'wd.nanoid', 'pw.nanoid')
        .innerJoin(
          'rise_private.users_withdraw_account as uwa',
          'wd.account_nanoid',
          'uwa.nanoid',
        )
        .select([
          'pw.id',
          'pw.name',
          'pw.nanoid',
          'pw.reference',
          'pw.status as provider_status',
          'uwa.ramp',
          'wd.created_at',
          'wd.status',
        ])
        .where('wd.status', 'in', ['fiat_pending', 'fiat_sent'])
        .orderBy('wd.created_at')
        .execute()
    })

    const events = withdraws?.reduce((events: SyncEvent[], withdraw) => {
      const isProvider = isOneOf(['Orum', 'Routefusion'])

      if (
        isString(withdraw.reference) &&
        isString(withdraw.id) &&
        isProvider(withdraw.name)
      ) {
        events.push({
          name: `ramps/provider_withdrawals.sync_${toLowerCase(withdraw.name)}_transfer`,
          data: {
            reference: withdraw.reference,
            id: withdraw.id,
            ramp: withdraw.ramp,
          },
        })
      }

      return events
    }, [])

    if (events?.length) {
      await step.sendEvent('sync-transfers', events)

      return {
        status: 'SUCCESS',
        events: events.length,
      }
    }

    return {
      status: 'NO_EVENTS',
    }
  },
)
