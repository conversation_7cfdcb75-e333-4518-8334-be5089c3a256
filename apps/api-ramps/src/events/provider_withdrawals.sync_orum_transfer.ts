import { db } from 'db'
import { getOrumTransfer } from 'repositories/src/orum.js'
import { updateOrumTransferStatus } from 'repositories/src/withdrawOrum.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('ramps')

export default client.createFunction(
  {
    id: 'ramps-provider-withdrawals-sync_orum_transfer',
    name: 'ramps/provider_withdrawals.sync_orum_transfer',
    concurrency: {
      limit: 5,
    },
    throttle: {
      limit: 1,
      period: '1s',
    },
  },
  [{ event: 'ramps/provider_withdrawals.sync_orum_transfer' }],
  async ({ event, step }) => {
    await step.run('update-transfer', async () => {
      const { data, error } = await getOrumTransfer({
        id: event.data.id,
      })

      if (error) {
        return {
          status: 'FAILED',
          error,
        }
      }

      await db.transaction().execute(async (trx) => {
        await updateOrumTransferStatus(data.transfer, trx)
      })

      return {
        status: 'UPDATED',
        transfer: data.transfer,
      }
    })
  },
)
