import type { WithdrawNanoid } from '@riseworks/contracts/src/brands.js'
import axios from 'axios'
import {
  getPendingCctpWithdrawals,
  updateCctpWithdrawAttestation,
} from 'repositories/src/cctpWithdraw.js'
import { getEthereumTransactionByNanoid } from 'repositories/src/ethereumTransactions.js'
import { getContract, sendTxn } from 'repositories/src/smartContracts.js'
import { getWithdrawAccountById } from 'repositories/src/withdrawAccounts.js'
import { getWithdrawById } from 'repositories/src/withdrawals.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isProduction } from 'utils/src/common/env.js'
import { getContext } from 'utils/src/common/requestContext.js'
import {
  createInngestClient,
  disableCronInLocalEnv,
} from 'utils/src/inngest/index.js'

const client = createInngestClient('ramps')

const circleApiUrl = isProduction
  ? 'https://iris-api.circle.com'
  : 'https://iris-api-sandbox.circle.com'

export default client.createFunction(
  {
    id: 'ramps-withdraw-cctp-attestation',
    name: 'ramps/withdraw.cctp_attestation',
  },
  disableCronInLocalEnv([
    { event: 'ramps/withdraw.cctp_attestation' },
    { cron: '*/5 * * * *' },
  ]),
  async () => {
    const ctx = getContext()
    const processedTransactions: Array<{
      withdrawNanoid: WithdrawNanoid
      transactionNanoid: string
      network: string
    }> = []

    const pendingWithdrawals = await getPendingCctpWithdrawals()
    ctx.logger.info(`Found ${pendingWithdrawals.length} pending withdrawals`)

    for (const withdrawal of pendingWithdrawals) {
      if (withdrawal.attestation) {
        ctx.logger.info(
          `Skipping withdrawal ${withdrawal.nanoid} as it already has attestation`,
        )
        continue
      }

      ctx.logger.info(
        `Processing withdrawal ${withdrawal.nanoid} with tx hash ${withdrawal.tx_hash}`,
      )

      // Get transaction hash from database
      const withdraw = await getWithdrawById(withdrawal.nanoid)
      assert(
        withdraw?.transaction_nanoid,
        'Transaction nanoid not found for withdrawal',
      )

      const transaction = await getEthereumTransactionByNanoid(
        withdraw.transaction_nanoid,
      )

      assert(transaction?.hash, 'Transaction hash not found')
      ctx.logger.info(`Transaction HASH: ${transaction.hash}`)

      const withdrawAccount = await getWithdrawAccountById(
        withdraw.account_nanoid,
      )
      assert(withdrawAccount, 'Withdraw account not found')
      assert(withdrawAccount.network, 'Withdraw account network not found')

      // Fetch message and attestation from Circle API
      ctx.logger.info(
        `Fetching message and attestation from Circle for transaction ${transaction.hash} (source domain: ${withdrawal.domain})...`,
      )

      const response = await axios.get(
        `${circleApiUrl}/v1/messages/3/${transaction.hash}`,
      )

      const hasValidResponse = response.data?.messages?.length > 0

      assert(hasValidResponse, 'No messages found in Circle API response')

      const messageData = response.data.messages[0]

      const hasRequiredData = messageData.message && messageData.attestation

      assert(
        hasRequiredData,
        'Missing message data or attestation in Circle API response',
      )

      assert(
        messageData.attestation !== 'PENDING',
        'Attestation is still pending. Please try again in a few minutes.',
      )

      const messageBytes = messageData.message
      const attestation = messageData.attestation
      // Initialize MessageTransmitter contract
      const messageTransmitter = await getContract(
        'MessageTransmitter',
        withdrawAccount.network,
      )
      ctx.logger.info(
        `Using MessageTransmitter at ${await messageTransmitter.getAddress()} on ${withdrawAccount.network}`,
      )

      // Call receiveMessage
      ctx.logger.info(
        `Calling receiveMessage for withdrawal ${withdrawal.nanoid} on ${withdrawAccount.network}...`,
      )

      const { to, data } =
        await messageTransmitter.receiveMessage.populateTransaction(
          messageBytes,
          attestation,
        )

      await updateCctpWithdrawAttestation(
        withdrawal.nanoid as WithdrawNanoid,
        attestation,
      )

      const txResult = await sendTxn({
        to,
        data,
        network: withdrawAccount.network,
      })

      processedTransactions.push({
        withdrawNanoid: withdrawal.nanoid as WithdrawNanoid,
        transactionNanoid: txResult,
        network: withdrawAccount.network,
      })
    }

    return {
      success: true,
      processedTransactions,
      totalProcessed: processedTransactions.length,
    }
  },
)
