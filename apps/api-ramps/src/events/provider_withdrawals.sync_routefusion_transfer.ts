import { db } from 'db'
import { NonRetriableError } from 'inngest'
import { routefusionGetTransfer } from 'repositories/src/routefusion.js'
import { updateRoutefusionTransferStatus } from 'repositories/src/withdrawRoutefusion.js'
import { isOneOf } from 'utils/src/common/array.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('ramps')

export default client.createFunction(
  {
    id: 'ramps-provider-withdrawals-sync_routefusion_transfer',
    name: 'ramps/provider_withdrawals.sync_routefusion_transfer',
  },
  [{ event: 'ramps/provider_withdrawals.sync_routefusion_transfer' }], // you can add more triggers here, like cron
  async ({ event, step }) => {
    const isRoutfusionRamp = isOneOf([
      'international_usd',
      'international_exchange',
    ])

    if (!isRoutfusionRamp(event.data.ramp)) {
      throw new NonRetriableError(`Invalid ramp provided ${event.data.ramp}`)
    }

    const accountType =
      event.data.ramp === 'international_usd' ? 'usd' : 'forex'

    await step.run('update-transfer', async () => {
      const transfer = await routefusionGetTransfer(event.data.id, accountType)

      await db.transaction().execute(async (trx) => {
        await updateRoutefusionTransferStatus(transfer, trx)
      })

      return {
        status: 'UPDATED',
        transfer: transfer,
      }
    })
  },
)
