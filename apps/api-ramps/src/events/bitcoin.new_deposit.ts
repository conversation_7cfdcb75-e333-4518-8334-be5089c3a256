import { createBitcoinUsdcSwap } from 'repositories/src/chainflip.js'
import {
  BitcoinAccount,
  bitcoinOwnerTransformer,
} from 'utils/src/blockchain/bitcoin/account.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('ramps')

export default client.createFunction(
  {
    id: 'ramps-bitcoin-new-deposit',
    name: 'ramps/bitcoin.new_deposit',
  },
  [{ event: 'ramps/bitcoin.new_deposit' }],
  async ({ event, logger }) => {
    const transformedOwner = bitcoinOwnerTransformer.parse(event.data.ownerId)
    const account = new BitcoinAccount(transformedOwner)
    await account.validateOwner()
    const swap = await createBitcoinUsdcSwap(account) // Minimum 30 seconds execution time
    const message = `Created new swap for Rise account ${transformedOwner.riseAccount}: https://scan.chainflip.io/channels/${swap.channel_id}`
    logger.info(message)
    return {
      swap,
      message,
    }
  },
)
