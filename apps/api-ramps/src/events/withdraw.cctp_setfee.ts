import {
  createInngestClient,
  disableCronInLocalEnv,
} from 'utils/src/inngest/index.js'
import {
  getContract,
  sendTxn,
  getProvider,
} from 'repositories/src/smartContracts.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { calculateTransactionPriceInUSDC } from 'repositories/src/rampFees.js'
import assert from 'utils/src/common/assertHTTP.js'
import { CCTP_DOMAINS } from 'repositories/src/cctpWithdraw.js'

const client = createInngestClient('ramps')

export default client.createFunction(
  {
    id: 'ramps-withdraw-cctp-setfee',
    name: 'ramps/withdraw.cctp_setfee',
  },
  disableCronInLocalEnv([
    { event: 'ramps/withdraw.cctp_setfee' },
    { cron: '0 * * * *' },
  ]),
  async ({ event, step, logger }) => {
    const ctx = getContext()

    // Get the network from the event data
    const network = 'arbitrum'
    assert(network, 'Network is required')

    const provider = await getProvider('ethereum')
    const feeData = await provider.getFeeData()
    assert(feeData.gasPrice, 'Gas price not returned from provider')
    const gasPrice = feeData.gasPrice
    ctx.logger.info(`Gas price: ${gasPrice}`)
    const destinationDomain = CCTP_DOMAINS.ethereum

    const gasLimit = BigInt(142345)

    const gasPriceBuffer = BigInt(**********) // 1 Gwei
    const gaspriceup = gasPrice + gasPriceBuffer

    ctx.logger.info(
      `Setting CCTP fee for network ${network} with gas limit ${gasLimit} and gas price ${gasPrice}`,
    )

    const providerArbitrum = await getProvider('arbitrum')

    // Get USDC token address
    const usdcToken = await getContract('USDC', network)
    const usdcAddress = await usdcToken.getAddress()
    ctx.logger.info(`USDC address: ${usdcAddress}`)

    // Get CCTP ramp contractBigInt
    const rampContract = await getContract('RiseRampWithdrawCCTP', network)
    const contract = rampContract.connect(providerArbitrum)
    const currentFee = await contract.getFee(destinationDomain, usdcAddress)

    ctx.logger.info(`Current fee for ${CCTP_DOMAINS.ethereum}: ${currentFee}`)

    const newFee = await calculateTransactionPriceInUSDC(gasLimit, gaspriceup)
    ctx.logger.info(`Calculated new fee: ${newFee}`)

    const THRESHOLD = BigInt(1_000_000) // 1 USDC

    // If current fee is 0, allow setting the first fee
    if (currentFee === BigInt(0)) {
      const { to, data } = await rampContract.setFee.populateTransaction(
        destinationDomain,
        usdcAddress,
        newFee,
      )
      // Send transaction
      const result = await sendTxn({ to, data, network })

      ctx.logger.info(
        `Setting initial fee for ${network} to ${newFee} and result ${result} and  to ${to} and data ${data}`,
      )

      return {
        success: true,
        network,
        newFee,
        gasLimit,
        gasPrice,
        result,
      }
    }

    const feeDifference =
      newFee > currentFee ? newFee - currentFee : currentFee - newFee

    if (feeDifference > THRESHOLD) {
      const { to, data } = await rampContract.setFee.populateTransaction(
        destinationDomain,
        usdcAddress,
        newFee,
      )
      // Send transaction
      const result = await sendTxn({ to, data, network })

      ctx.logger.info(
        `Successfully set new fee for ${network} to ${newFee} and result ${result} and  to ${to} and data ${data}`,
      )

      return {
        success: true,
        network,
        newFee,
        gasLimit,
        gasPrice,
        result,
      }
    }

    return {
      success: false,
      message: `Fee difference (${feeDifference}) does not exceed the minimum threshold of ${THRESHOLD}. No fee update is required at this time.`,
      network,
      currentFee,
      newFee,
      feeDifference,
    }
  },
)
