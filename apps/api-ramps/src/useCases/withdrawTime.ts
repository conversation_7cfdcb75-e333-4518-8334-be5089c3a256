import type { SelectableWithdrawOptionsCurrency } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type { SelectableUsersWithdrawAccount } from '@riseworks/contracts/src/codegen/db/models_rise_private.js'
import { db as mainDB } from 'db/src/index.js'
import { getWithdrawAccountPrivateBankDataById } from 'repositories/src/withdrawAccounts.js'
import { getFiatWithdrawOptionWaitTimeFields } from 'repositories/src/withdrawOptions.js'
import { getContext } from 'utils/src/common/requestContext.js'

type WaitTimeFields = Pick<
  SelectableWithdrawOptionsCurrency,
  | 'wait_time'
  | 'wait_minutes'
  | 'cut_off_hour_gmt'
  | 'exclude_weekends'
  | 'exclude_holidays'
  | 'exclude_fri_sat'
>

const baseWaitTimeFields: WaitTimeFields = {
  wait_time: 'Less than a day',
  wait_minutes: 1,
  cut_off_hour_gmt: null,
  exclude_weekends: false,
  exclude_holidays: false,
  exclude_fri_sat: false,
}

const getNetworkWaitTime = (
  network: SelectableUsersWithdrawAccount['network'],
) => {
  switch (network) {
    case 'arbitrum':
      return '2 seconds'
    case 'ethereum':
      return '12 seconds'
    case 'base':
      return '2 seconds'
    case 'polygon':
      return '2 seconds'
    case 'avalanche':
      return '2 seconds'
    case 'optimism':
      return '2 seconds'
    default:
      return 'Less than a day'
  }
}

export const getWithdrawAccountWaitTimeFields = async (
  withdrawAccount: SelectableUsersWithdrawAccount,
  db = mainDB,
) => {
  using _ = getContext()

  let waitTimeFields = baseWaitTimeFields

  if (withdrawAccount.network) {
    waitTimeFields.wait_time = getNetworkWaitTime(withdrawAccount.network)
  } else {
    const withdrawAccountData = await getWithdrawAccountPrivateBankDataById(
      withdrawAccount.nanoid,
      db,
    )
    if (withdrawAccountData?.bank_country && withdrawAccountData?.currency) {
      const fiatWaitTimeFields = await getFiatWithdrawOptionWaitTimeFields(
        withdrawAccount.ramp,
        withdrawAccountData.bank_country,
        withdrawAccountData.currency,
        db,
      )
      waitTimeFields = {
        ...baseWaitTimeFields,
        ...fiatWaitTimeFields,
        wait_time:
          fiatWaitTimeFields?.wait_time ||
          withdrawAccount.ramp === 'domestic_usd'
            ? 'Less than a day'
            : '1-2 days',
      }
    }
  }

  return waitTimeFields
}
