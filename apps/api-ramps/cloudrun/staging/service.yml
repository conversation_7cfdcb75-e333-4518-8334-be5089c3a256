apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: api-ramps
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing

# https://doc.crds.dev/github.com/knative/serving/serving.knative.dev/Service/v1@knative-v1.2.2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-egress: all-traffic
        run.googleapis.com/cloudsql-instances: riseworksstaging:us-central1:rise-tf-primary
        autoscaling.knative.dev/maxScale: "2"
        autoscaling.knative.dev/minScale: "1"
        run.googleapis.com/vpc-access-connector: projects/riseworksstaging/locations/us-central1/connectors/rise-vpc-connector
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 500
      timeoutSeconds: 300
      containers:
      - image: service-image
        ports:
        - name: http1
          containerPort: "8080"
        resources:
          limits:
            cpu: 1000m
            memory: 2048Mi
        env:
        - name: NODE_ENV
          value: staging
        - name: GOOGLE_PROJECT_ID
          value: riseworksstaging
        - name: GOOGLE_PROJECT_NUMBER
          value: "************"
        - name: LOGLEVEL
          value: INFO
        - name: DB_HOST
          value: "************"
        - name: DB_PORT
          value: "3306"
        - name: DB_SCHEMA
          value: rise
        - name: DB_CONNECTION_LIMIT
          value: "10"
        - name: DB_CONNECTION_TIMEOUT
          value: "120000"
        - name: SEGMENT_SOURCE
          value: bT2VPceDFCJrQBZRzKYDohzvIESokVtT
        - name: SENTRY_DSN
          value: https://<EMAIL>/****************
        - name: GOOGLE_LOCATION_ID
          value: us-central1
        - name: GOOGLE_OIDC_EMAIL
          value: <EMAIL>
        - name: GOOGLE_DRIVE_COMPANY_DOCUMENTS_FOLDER_ID
          value: 6Lc8Rp0qAAAAACP1o8_CCNWiFi3PWBzP_YNCK1q_
        - name: GOOGLE_RECAPTCHA
          value: 6LdI_hghAAAAAC0rS9gsEZuZcLxGsfK6nxLh92C1
        - name: APP_DOMAIN
          value: https://staging-pay-api.riseworks.io
        - name: HTTP_PORT
          value: "8080"
        - name: REDIS_URI
          value: redis://*************:6379
        - name: AVATARS_BUCKET
          value: staging-avatars-riseworks-io
        - name: INVOICE_ATTACHMENTS_BUCKET
          value: staging-invoice-attachments
        - name: AVATARS_BUCKET_URL
          value: https://storage.googleapis.com/staging-avatars-riseworks-io
        - name: INVOICE_ATTACHMENTS_BUCKET_URL
          value: https://storage.googleapis.com/staging-invoice-attachments
        - name: SOCKET_PATH
          value: /cloudsql/riseworksstaging:us-central1:rise-tf-primary
        - name: PAYMENT_LOOKUP_CONCURRENCY
          value: "100"
        - name: ENABLE_METRICS
          value: "false"
              
  traffic:
  - percent: 100
    latestRevision: true
