import { documentBuilderOrganizationsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { getOrganizationTeamsForUser } from 'repositories/src/teams.js'
import {
  validatePermissionByNanoid,
  validateUserByAuth,
} from 'repositories/src/validations.js'

export default (app: App): App =>
  app.route({
    ...routes['/document_builder/organizations/:company_nanoid/teams'].get,
    handler: async ({ auth, params: { company_nanoid } }, reply) => {
      const { user } = await validateUserByAuth({ auth })
      await validatePermissionByNanoid({
        user_nanoid: user.nanoid,
        entity_nanoid: company_nanoid,
        types: ['company', 'org_admin', 'org_finance_admin', 'org_viewer'],
      })

      const data = await getOrganizationTeamsForUser(
        company_nanoid,
        user.nanoid,
      )

      reply.send({ success: true, data })
    },
  })
