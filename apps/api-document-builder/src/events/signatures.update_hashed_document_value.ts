import type { DocumentSignatureNanoid } from '@riseworks/contracts/src/brands.js'
import { db } from 'db'
import { ethers } from 'ethers'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('document_builder')

export default client.createFunction(
  {
    id: 'document_builder-signatures-update-hashed_document_value',
    name: 'document_builder/signatures.update_hashed_document_value',
  },
  [{ event: 'document_builder/signatures.update_hashed_document_value' }],
  async ({ event, step, logger }) => {
    const { nanoids = [] } = event.data

    await db
      .transaction()
      .execute(async (trx) => {
        const signatures = await trx
          .selectFrom('rise.documents as d')
          .innerJoin(
            'rise.document_versions as dv',
            'dv.document_nanoid',
            'd.nanoid',
          )
          .innerJoin(
            'rise.signatures as s',
            's.doc_version_nanoid',
            'dv.nanoid',
          )
          .select(['s.nanoid', 'd.content'])
          .where(
            's.nanoid',
            'in',
            nanoids.map((n) => n as DocumentSignatureNanoid),
          )
          .execute()

        for (const signature of signatures) {
          const hashedDoc = ethers.keccak256(
            ethers.toUtf8Bytes(JSON.stringify(signature.content)),
          )

          if (hashedDoc) {
            await trx
              .updateTable('rise.signatures')
              .set({
                base64String: hashedDoc,
              })
              .where('nanoid', '=', signature.nanoid)
              .execute()
          }
        }
      })
      .catch((error) => {
        logger.error('Failed to update signature hashes', { error })
        throw error
      })
  },
)
