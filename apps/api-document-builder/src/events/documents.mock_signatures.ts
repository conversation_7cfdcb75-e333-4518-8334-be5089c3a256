import type {
  DocumentVersionNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db } from 'db/src/index.js'
import { updateHasSignedStatus } from 'repositories/src/documentsInternal.js'
import { getSignaturesByDocVersionId } from 'repositories/src/signatures.js'
import { getFullUserData } from 'repositories/src/users.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('document_builder')

export default client.createFunction(
  {
    id: 'document_builder-documents-mock-signatures',
    name: 'document_builder/documents.mock_signatures',
  },
  [{ event: 'document_builder/documents.mock_signatures' }],
  async ({ event, step, logger }) => {
    const { doc_version_id, user_nanoid } = event.data

    await db.transaction().execute(async (trx) => {
      const userData = await getFullUserData(user_nanoid as UserNanoid, trx)
      assert(userData, 'User not found', '404')

      const userAddress = `${userData.address.line_1} ${userData.address.line_2} ${userData.address.city}, ${userData.address.state} ${userData.address.zip_code}`
      await trx
        .updateTable('rise.signatures')
        .set({
          status: 'completed',
          ip: '127.0.0.1',
          signAddress: `0x${'1'.repeat(40)}`,
          base64String: 'documents.mock_signatures',
          signedHash: 'documents.mock_signatures',
          address: userAddress,
          device: 'documents.mock_signatures',
          updated_at: new Date(),
          updated_by: user_nanoid as UserNanoid,
        })
        .where(
          'doc_version_nanoid',
          '=',
          doc_version_id as DocumentVersionNanoid,
        )
        .where('user_nanoid', '=', user_nanoid as UserNanoid)
        .execute()

      const signatures = await getSignaturesByDocVersionId(
        doc_version_id as DocumentVersionNanoid,
        trx,
      )
      const hasAllSignersSigned = signatures.every(
        (signature) => signature.status === 'completed',
      )

      if (hasAllSignersSigned) {
        const version = await trx
          .selectFrom('rise.document_versions')
          .select(['document_nanoid'])
          .where('nanoid', '=', doc_version_id as DocumentVersionNanoid)
          .executeTakeFirst()

        assert(version, 'Document version not found', '404')

        await updateHasSignedStatus(
          version.document_nanoid,
          doc_version_id as DocumentVersionNanoid,
          user_nanoid as UserNanoid,
          trx,
        )
      }
    })
  },
)
