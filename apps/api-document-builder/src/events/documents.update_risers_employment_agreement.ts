import type {
  CompanyNanoid,
  TeamNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { AgreementType } from '@riseworks/contracts/src/documents.js'
import { db } from 'db/src/index.js'
import {
  getCompanySettings,
  upsertCompanySettings,
} from 'repositories/src/companySettings.js'
import {
  getActiveEmploymentAgreement,
  upsertEmploymentAgreement,
} from 'repositories/src/documentsInternal.js'
import { getEmployeePayrollSettings } from 'repositories/src/employeePayrollSettings.js'
import { getUserRelationships } from 'repositories/src/riseEntities.js'
import { mapAsync } from 'utils/src/common/array.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('document_builder')

export default client.createFunction(
  {
    id: 'document_builder-documents-update-risers_employment_agreement',
    name: 'document_builder/documents.update_risers_employment_agreement',
  },
  [{ event: 'document_builder/documents.update_risers_employment_agreement' }],
  async ({ event, step, logger }) => {
    const { userNanoids } = event.data

    try {
      await db.transaction().execute(async (trx) => {
        await mapAsync(userNanoids, async (nanoid) => {
          const relationships = await getUserRelationships(
            nanoid as UserNanoid,
            trx,
          )
          const teamRelationship = relationships.find(
            (r) => r.type === 'team_employee',
          )

          if (!teamRelationship?.parent) {
            logger.error(`No team relationship found for user: ${nanoid}`)
            return
          }

          const companySettings = await getCompanySettings(
            teamRelationship.parent.nanoid as CompanyNanoid,
          )
          if (!companySettings) {
            logger.error(`Invalid company for user: ${nanoid}`)
            return
          }
          if (!companySettings.enabled_document_types.includes('EA_RISE')) {
            await upsertCompanySettings(
              {
                ...companySettings,
                enabled_document_types: JSON.stringify([
                  ...companySettings.enabled_document_types,
                  'EA_RISE',
                ]),
                enabled_payroll_programs: JSON.stringify(
                  companySettings.enabled_payroll_programs || [],
                ),
                updated_at: new Date(),
              },
              trx,
            )
          }

          const employeePayrollSettings = await getEmployeePayrollSettings({
            user_nanoid: nanoid as UserNanoid,
            team_nanoid: teamRelationship.nanoid as TeamNanoid,
            db: trx,
          })
          if (!employeePayrollSettings) {
            logger.error(
              `No employee payroll settings found for user: ${nanoid}`,
            )
            return
          }

          if (employeePayrollSettings.payroll_program !== 'riseworks_inc') {
            logger.info(
              `User ${nanoid} is not on the Riseworks payroll program`,
            )
            return
          }

          const activeAgreement = await getActiveEmploymentAgreement(
            {
              companyNanoid: teamRelationship.parent.nanoid as CompanyNanoid,
              userNanoid: nanoid as UserNanoid,
              employmentAgreementType: 'ea_rise',
            },
            trx,
          )
          if (activeAgreement) {
            logger.info(`User ${nanoid} already has an active agreement`)
            return
          }

          await upsertEmploymentAgreement(
            teamRelationship.nanoid as TeamNanoid,
            nanoid as UserNanoid,
            '127.0.0.1',
            {
              jobTitle: employeePayrollSettings.job_title,
              annualBaseSalary: employeePayrollSettings.annual_base_salary,
              weeklyWorkHours: employeePayrollSettings.work_hours_per_week,
              startDate: employeePayrollSettings.start_date,
              jobScope: employeePayrollSettings.job_scope,
              variableCompensations:
                employeePayrollSettings.variable_compensations,
              stipends: employeePayrollSettings.stipends,
              currency: employeePayrollSettings.currency,
              restrictedPeriod: employeePayrollSettings.restricted_period_days,
              noticePeriod: employeePayrollSettings.notice_period_days,
              disputeResolutionMethod:
                employeePayrollSettings.dispute_resolution_method,
              employmentAgreementType: 'ea_rise' as AgreementType,
            },
            trx,
          )
          logger.info(`Created EA_RISE agreement for user: ${nanoid}`)
        })
      })
      return { success: true }
    } catch (error) {
      logger.error('Error updating employment agreements:', error)
      throw error
    }
  },
)
