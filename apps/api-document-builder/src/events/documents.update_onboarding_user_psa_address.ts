import type {
  CompanyNanoid,
  DocumentNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db } from 'db'
import { getAddress } from 'repositories/src/addresses.js'
import {
  getAllPSAContentForUser,
  updateDocumentContent,
} from 'repositories/src/documentsInternal.js'
import { mapAsync } from 'utils/src/common/array.js'
import { updateContentProviderAddress } from 'utils/src/common/documents.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('document_builder')

export default client.createFunction(
  {
    id: 'document_builder-documents-update-onboarding_user_psa_address',
    name: 'document_builder/documents.update_onboarding_user_psa_address',
  },
  [{ event: 'document_builder/documents.update_onboarding_user_psa_address' }],
  async ({ event, step, logger }) => {
    const { userNanoid, ownerNanoid } = event.data as {
      userNanoid: UserNanoid
      ownerNanoid: UserNanoid | CompanyNanoid
    }

    await db.transaction().execute(async (trx) => {
      const userPSAs = await getAllPSAContentForUser(
        { userNanoid: userNanoid },
        trx,
      )

      const userAddress = await getAddress(userNanoid)
      const address = userAddress
        ? `${userAddress?.line_1 || ''}, ${userAddress?.line_2 || ''}, ${userAddress?.city || ''}, ${userAddress?.state || ''}, ${userAddress?.country || ''}`
            .replace(/, ,/g, '')
            .trim()
        : ''

      const updatedDocuments: Array<{
        content: string
        prefill: string
        document_nanoid: DocumentNanoid
      }> = []
      for (const psa of userPSAs) {
        const isContentValid =
          typeof psa.content === 'object' &&
          psa.content !== null &&
          !Array.isArray(psa.content)
        const isPrefillValid =
          typeof psa.prefill === 'object' &&
          psa.prefill !== null &&
          !Array.isArray(psa.prefill)

        if (isContentValid && isPrefillValid) {
          const content = psa.content as Record<string, unknown>
          const prefill = psa.prefill as Record<string, unknown>

          const updatedContent = { ...content }
          if (updatedContent.root) {
            updatedContent.root = updateContentProviderAddress(
              updatedContent.root as Record<string, unknown>,
              address,
            )
          }

          const updatedPrefill = { ...prefill }
          updatedPrefill['Provider Address'] = address

          updatedDocuments.push({
            content: JSON.stringify(updatedContent),
            prefill: JSON.stringify(updatedPrefill),
            document_nanoid: psa.nanoid,
          })
        }
        await mapAsync(updatedDocuments, async (doc) => {
          await updateDocumentContent(
            doc.document_nanoid,
            doc.prefill,
            doc.content,
            ownerNanoid,
            userNanoid,
            trx,
          )
        })
      }
    })
  },
)
