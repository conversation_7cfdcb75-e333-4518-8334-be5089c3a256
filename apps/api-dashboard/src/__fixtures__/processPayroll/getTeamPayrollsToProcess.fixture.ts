import type {
  TeamNanoid,
  TeamPayrollRiseAccount,
} from '@riseworks/contracts/src/brands.js'

export const mockTeamPayrolls = [
  {
    nanoid: 'te-team1' as TeamNanoid,
    payroll_program: 'riseworks_inc',
    pay_schedule: 'bimonthly',
    created_at: new Date('2024-04-01T00:00:00Z'),
    country_code: '',
    healthcare_dependent_percentage: '',
    healthcare_employee_percentage: '',
    rise_account: 'acc-1' as TeamPayrollRiseAccount,
    updated_at: new Date('2024-04-01T00:00:00Z'),
  } as const,
  {
    nanoid: 'te-team2' as TeamNanoid,
    payroll_program: 'riseworks_pps_us',
    pay_schedule: 'monthly',
    created_at: new Date('2024-04-02T00:00:00Z'),
    country_code: '',
    healthcare_dependent_percentage: '',
    healthcare_employee_percentage: '',
    rise_account: 'acc-2' as TeamPayrollRiseAccount,
    updated_at: new Date('2024-04-01T00:00:00Z'),
  } as const,
]
