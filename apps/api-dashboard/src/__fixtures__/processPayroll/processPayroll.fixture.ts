import type { PayCycle } from '@riseworks/contracts/src/formats.js'

export const mockCronPayload = {
  trigger: 'cron' as const,
  cron: 'abc',
  cronIdToCheck: 'abc',
}

export const mockManualPayload = {
  trigger: 'manual' as const,
}
export const payCycleFixture: PayCycle = {
  year: 2024,
  month: 4,
  period: 2,
}

export const currentCycle: PayCycle = {
  year: 2024,
  month: 4,
  period: 1,
}

export const nextCycle: PayCycle = {
  year: 2024,
  month: 4,
  period: 2,
}

export const afterNextCycle: PayCycle = {
  year: 2024,
  month: 5,
  period: 1,
}
