export const allPayments = [
  { payment_id: 'p1', amount: 10, token: 'TOKEN1' },
  { payment_id: 'p2', amount: 20, token: 'TOKEN1' },
  { payment_id: 'p3', amount: 5, token: 'TOKEN2' },
]

export const groupedPaymentsByToken = {
  TOKEN1: {
    totalAmount: 30,
    payments: [
      { payment_id: 'p1', amount: 10, token: 'TOKEN1' },
      { payment_id: 'p2', amount: 20, token: 'TOKEN1' },
    ],
  },
  TOKEN2: {
    totalAmount: 5,
    payments: [{ payment_id: 'p3', amount: 5, token: 'TOKEN2' }],
  },
}

export const paymentsWithInsufficientBalance = {
  TOKEN1: {
    totalAmount: 50,
    payments: [
      { payment_id: 'p1', amount: 20, token: 'TOKEN1' },
      { payment_id: 'p2', amount: 30, token: 'TOKEN1' },
    ],
  },
}

export const paymentsWithSufficientBalance = {
  TOKEN2: {
    totalAmount: 50,
    payments: [{ payment_id: 'p3', amount: 50, token: 'TOKEN2' }],
  },
}
