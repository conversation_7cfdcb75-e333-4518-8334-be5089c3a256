import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'

import type * as usersRepo from 'repositories/src/users.js'

type UserData = Awaited<ReturnType<typeof usersRepo.getFullUserData>>

export const teamId = 'team123' as TeamNanoid

export const adminOneData: UserData = {
  email: '<EMAIL>',
} as UserData

export const adminTwoData: UserData = {
  email: '<EMAIL>',
} as UserData

export const adminOne = 'admin1'
export const adminOneNanoId = 'admin1' as UserNanoid
export const adminTwo = 'admin2'
export const adminTwoNanoId = 'admin2' as UserNanoid

export const fullAdmins = [adminOneNanoId, adminTwoNanoId]
export const partialAdmins = [adminOneNanoId]

export const expectedEmailsFull = ['<EMAIL>', '<EMAIL>']
export const expectedEmailsPartial = ['<EMAIL>']
