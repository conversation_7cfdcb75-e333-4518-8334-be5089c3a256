import type {
  OneOffBonusInput,
  PreviousBonuses,
} from 'src/useCases/employeeOneOffBonus.js'
import {
  mockTeamAdminNanoid,
  mockTeamNanoid,
  mockUserNanoid,
  newBonus,
  todayForTest,
} from './common.fixture.js'

export const existingBonus1 = {
  id: 1,
  user_nanoid: mockUserNanoid,
  team_nanoid: mockTeamNanoid,
  type: 'bonus',
  amount: '100.00',
  pay_at: todayForTest.add(5, 'day').toDate(), //first cycle
  created_by: mockTeamAdminNanoid,
  last_modified_by: mockTeamAdminNanoid,
  created_at: new Date(),
  updated_at: new Date(),
  deleted_at: null,
}

export const existingBonus2 = {
  id: 2,
  user_nanoid: mockUserNanoid,
  team_nanoid: mockTeamNanoid,
  type: 'reimbursement',
  amount: '50.00',
  pay_at: todayForTest.add(20, 'day').toDate(), //second cycle
  created_by: mockTeamAdminNanoid,
  last_modified_by: mockTeamAdminNanoid,
  created_at: new Date(),
  updated_at: new Date(),
  deleted_at: null,
}

export const existingBonus: OneOffBonusInput = {
  type: 'reimbursement',
  amount: '50.00',
  pay_at: todayForTest.add(20, 'day').toDate(),
}

export const modifiedExistingBonus: OneOffBonusInput = {
  type: 'bonus',
  amount: '250.00', // modified from existingBonus1
  pay_at: todayForTest.add(5, 'day').toDate(),
}

export const existingBonuses: PreviousBonuses = [existingBonus1, existingBonus2]
export const updateCreateBonusInput: OneOffBonusInput[] = [newBonus]

export const updateMixedBonusInput: OneOffBonusInput[] = [
  newBonus,
  existingBonus,
  modifiedExistingBonus,
]

export const updateWithoutChangesInput: OneOffBonusInput[] = [
  existingBonus1,
  existingBonus2,
]
