import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import dayjs from 'dayjs'
import type {
  CreateBonusResult,
  OneOffBonusInput,
  PreviousBonuses,
} from 'src/useCases/employeeOneOffBonus.js'

export const mockUserNanoid: UserNanoid = 'user1' as UserNanoid
export const mockTeamNanoid: TeamNanoid = 'team1' as TeamNanoid
export const mockTeamAdminNanoid: UserNanoid = 'teamAdmin1' as UserNanoid

export const mockCurrentPayCycle: PayCycle = { year: 2025, month: 5, period: 1 }
export const mockNextPayCycle: PayCycle = { year: 2025, month: 5, period: 2 }

export const todayForTest = dayjs('2025-05-07')

export const bonusInvalidDate: OneOffBonusInput = {
  type: 'bonus',
  amount: '100.00',
  pay_at: todayForTest.subtract(1, 'day').toDate(),
}

export const bonusInvalidAmount: OneOffBonusInput = {
  type: 'bonus',
  amount: 'notANumber',
  pay_at: todayForTest.add(5, 'day').toDate(),
}

export const newBonus: OneOffBonusInput = {
  type: 'comission',
  amount: '250.00',
  pay_at: todayForTest.add(5, 'day').toDate(),
}

export const mockInsertResultForCreate: CreateBonusResult = [
  {
    numInsertedOrUpdatedRows: 1n,
    insertId: 1n,
  },
]

export const notExistingBonus: PreviousBonuses = []
export const emptyBonusesInput: OneOffBonusInput[] = []
export const invalidDateInput: OneOffBonusInput[] = [bonusInvalidDate]
export const invalidAmountInput: OneOffBonusInput[] = [bonusInvalidAmount]
