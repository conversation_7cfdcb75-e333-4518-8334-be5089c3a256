import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'

export const mockTeamNanoid = 'te-team' as TeamNanoid

export const mockPayCycle: PayCycle = {
  year: 2025,
  month: 4,
  period: 1,
}

export const mockEmployeesPayslips = [
  {
    employee: {
      nanoid: 'user-1' as UserNanoid,
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      avatar: 'avatar.jpg',
      start_date: new Date('2024-04-01'),
      payroll_program: 'riseworks_eor_us' as const,
      payroll_program_country: 'US',
    },
    payslip: {
      grosspay: { amount_cents: 100000, currency: 'USD' } as const,
      netpay: { amount_cents: 80000, currency: 'USD' } as const,
      salaries: { amount_cents: 80000, currency: 'USD' } as const,
      extras: {
        signing_bonus: { amount_cents: 10000, currency: 'USD' } as const,
        stipends: { amount_cents: 5000, currency: 'USD' } as const,
        one_off_bonuses: { amount_cents: 2000, currency: 'USD' } as const,
        variable_compensations: {
          bonus: { amount_cents: 3000, currency: 'USD' } as const,
          commission: { amount_cents: 4000, currency: 'USD' } as const,
          reimbursement: { amount_cents: 2500, currency: 'USD' } as const,
        },
      },
      healthcare: {
        employee: { amount_cents: 1200, currency: 'USD' } as const,
        employer: { amount_cents: 1500, currency: 'USD' } as const,
      },
      retirement: {
        employee: { amount_cents: 2000, currency: 'USD' } as const,
        employer: { amount_cents: 2500, currency: 'USD' } as const,
      },
      taxes: {
        employee: [
          {
            name: 'Tax A',
            amount: { amount_cents: 1000, currency: 'USD' },
          } as const,
          {
            name: 'Tax B',
            amount: { amount_cents: 500, currency: 'USD' },
          } as const,
        ],
        employer: [],
      },
    },
  },
]
