import { dashboardSupportedCurrenciesRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { getActiveSupportedCurrencies } from 'repositories/src/supportedCurrencies.js'
import { validateOnboardedUserByAuth } from 'repositories/src/validations.js'

export default (app: App): App =>
  app.route({
    ...routes['/dashboard/supported_currencies'].get,
    handler: async ({ auth }, reply) => {
      await validateOnboardedUserByAuth({ auth, apps: ['pay'] })

      const data = await getActiveSupportedCurrencies()

      reply.send({ success: true, data })
    },
  })
