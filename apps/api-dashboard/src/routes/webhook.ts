import { dashboardWebhookRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import {
  createWebhookDelivery,
  getWebhookDeliveriesByWebhook,
  getWebhookDeliveryById,
  retryWebhookDelivery,
} from 'repositories/src/webhook_deliveries.js'
import { createWebhookEvent } from 'repositories/src/webhook_events.js'

import {
  authorizeAndFetchWebhook,
  validateCompanyAccess,
  validateTeamAccess,
} from 'repositories/src/entity_access/index.js'
import { getTeamNanoidsForUser } from 'repositories/src/teams.js'
import {
  createWebhookEndpoint,
  deleteWebhookEndpoint,
  getWebhookEndpointById,
  getWebhookEndpointsByCompany,
  updateWebhookEndpoint,
} from 'repositories/src/webhook_endpoints.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App => {
  // Register Webhook
  app.route({
    ...routes['/dashboard/webhooks/register'].post,
    handler: async ({ auth, body, log }, reply) => {
      log.info('Starting webhook registration')
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      assert(user_nanoid, 'User not logged in', '403')

      const { company_nanoid, events, secret, is_active, url, team_nanoid } =
        body
      log.info(
        `Registering webhook for company ${company_nanoid} with URL: ${url}`,
      )

      // Validate that the company exists and user has access to it
      await validateCompanyAccess(user_nanoid, company_nanoid)

      if (team_nanoid) {
        // Validate that the team exists and user has access to it
        await validateTeamAccess(user_nanoid, team_nanoid)
      }

      const webhook_nanoid = await createWebhookEndpoint({
        company_nanoid: company_nanoid,
        team_nanoid: team_nanoid,
        created_by: user_nanoid,
        url,
        events,
        secret,
        is_active,
      })

      log.info(`Created webhook endpoint with ID: ${webhook_nanoid}`)

      const webhook = await getWebhookEndpointById(webhook_nanoid)
      assert(webhook, 'Webhook endpoint not found')

      log.info(`Successfully registered webhook ${webhook_nanoid}`)

      const data = {
        webhook_nanoid: webhook.nanoid,
        company_nanoid: webhook.company_nanoid,
        url: webhook.url,
        events: webhook.events,
        is_active: webhook.is_active,
      }
      reply.code(201).send({ success: true, data })
    },
  })

  // Update Webhook
  app.route({
    ...routes['/dashboard/webhooks/:webhook_nanoid'].put,
    handler: async ({ auth, params, body, log }, reply) => {
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      assert(user_nanoid, 'User not logged in', '403')

      const { webhook_nanoid } = params
      const { url, events, secret, is_active } = body

      log.info(`Updating webhook ${webhook_nanoid}`)

      // Validate that the webhook exist and user have access to it
      await authorizeAndFetchWebhook(user_nanoid, webhook_nanoid)

      await updateWebhookEndpoint(webhook_nanoid, {
        url,
        events,
        secret,
        is_active,
        last_modified_by: user_nanoid,
      })

      log.info(`Updated webhook endpoint ${webhook_nanoid}`)

      const webhook = await getWebhookEndpointById(webhook_nanoid)
      assert(webhook, 'Webhook endpoint not found')

      log.info(`Successfully updated webhook ${webhook_nanoid}`)

      const res = {
        webhook_nanoid: webhook.nanoid,
        url: webhook.url,
        events: webhook.events,
        is_active: webhook.is_active,
      }
      reply.code(200).send({ success: true, data: res })
    },
  })

  // Delete Webhook
  app.route({
    ...routes['/dashboard/webhooks/:webhook_nanoid'].delete,
    handler: async ({ auth, params, log }, reply) => {
      const { webhook_nanoid } = params
      const user_nanoid = auth?.sessionClaims?.user_nanoid

      // Validate that the user is logged in
      assert(user_nanoid, 'User not logged in', '403')

      log.info(`Deleting webhook ${webhook_nanoid}`)

      // Validate that the webhook exists and user has access to it
      await authorizeAndFetchWebhook(user_nanoid, webhook_nanoid)

      await deleteWebhookEndpoint(webhook_nanoid, user_nanoid)

      log.info(`Successfully deleted webhook ${webhook_nanoid}`)

      reply.code(200).send({
        success: true,
      })
    },
  })

  // Get Webhook
  app.route({
    ...routes['/dashboard/webhooks/:webhook_nanoid'].get,
    handler: async ({ auth, params, log }, reply) => {
      const { webhook_nanoid } = params
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      // Validate that the user is logged in
      assert(user_nanoid, 'User not logged in', '403')

      log.info(`Fetching webhook ${webhook_nanoid}`)

      // Validate that the webhook exists and user has access to it
      const webhook = await authorizeAndFetchWebhook(
        user_nanoid,
        webhook_nanoid,
      )

      log.info(`Successfully retrieved webhook ${webhook_nanoid}`)

      const res = {
        webhook_nanoid: webhook.nanoid,
        company_nanoid: webhook.company_nanoid,
        url: webhook.url,
        events: webhook.events,
        is_active: webhook.is_active,
      }
      reply.code(200).send({ success: true, data: res })
    },
  })

  // List Webhooks
  app.route({
    ...routes['/dashboard/webhooks'].get,
    handler: async ({ auth, query, log }, reply) => {
      const { company_nanoid, cursor, limit = 50 } = query
      // Validate that the company_nanoid is provided
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      assert(user_nanoid, 'User not logged in', '403')

      log.info(
        `Listing webhooks for company ${company_nanoid} with limit ${limit}`,
      )

      // Validate that the company exists
      await validateCompanyAccess(user_nanoid, company_nanoid)

      // Get user's accessible teams for this company
      const userTeams = await getTeamNanoidsForUser(user_nanoid)
      const accessibleTeamNanoids = userTeams.map((team) => team.nanoid)

      log.info(
        `User ${user_nanoid} has access to ${accessibleTeamNanoids.length} teams`,
      )

      // Get company-level webhooks (no team restriction)
      const companyWebhooks = await getWebhookEndpointsByCompany(
        company_nanoid,
        {
          team_nanoids: null, // Only company-level webhooks
          cursor,
          limit,
        },
      )

      // Get team webhooks for accessible teams in a single query
      let teamWebhooks: typeof companyWebhooks = []
      if (accessibleTeamNanoids.length > 0) {
        teamWebhooks = await getWebhookEndpointsByCompany(company_nanoid, {
          team_nanoids: accessibleTeamNanoids,
          cursor,
          limit,
        })
      }

      // Combine and sort all webhooks by creation date (most recent first)
      const allWebhooks = [...companyWebhooks, ...teamWebhooks].sort(
        (a, b) => b.created_at.getTime() - a.created_at.getTime(),
      )

      // Apply cursor-based pagination after combining results
      let filteredWebhooks = allWebhooks
      if (cursor) {
        const cursorDate = new Date(cursor)
        filteredWebhooks = allWebhooks.filter(
          (webhook) => webhook.created_at < cursorDate,
        )
      }

      // Apply limit
      const webhooksToReturn = filteredWebhooks.slice(0, limit)
      const hasMore = filteredWebhooks.length > limit
      const nextCursor =
        hasMore && webhooksToReturn.length > 0
          ? (webhooksToReturn[
              webhooksToReturn.length - 1
            ]?.created_at.toISOString() ?? null)
          : null

      log.info(
        `Retrieved ${companyWebhooks.length} company webhooks and ${teamWebhooks.length} team webhooks`,
      )
      log.info(
        `Returning ${webhooksToReturn.length} webhooks, hasMore: ${hasMore}`,
      )

      const res = {
        webhooks: webhooksToReturn.map((webhook) => ({
          webhook_nanoid: webhook.nanoid,
          url: webhook.url,
          events: webhook.events,
          is_active: webhook.is_active,
          created_at: webhook.created_at.toISOString(),
          updated_at: webhook.updated_at.toISOString(),
        })),
        next_cursor: nextCursor,
      }
      reply.send({ success: true, data: res })
    },
  })

  // Test Webhook
  app.route({
    ...routes['/dashboard/webhooks/test/:webhook_nanoid'].post,
    handler: async ({ log, method, url, body, auth, params, query }, reply) => {
      const { webhook_nanoid } = params
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      // Validate that the user is logged in
      assert(user_nanoid, 'User not logged in', '403')

      log.info(
        `Testing webhook ${webhook_nanoid} with event type: ${body.event_type}`,
      )

      // Fetch the webhook data
      const webhook = await authorizeAndFetchWebhook(
        user_nanoid,
        webhook_nanoid,
      )

      // Validate that the webhook is active
      assert(webhook.is_active, 'Webhook is not active', '400')

      // Validate that the event type is supported
      assert(
        webhook.events.includes(body.event_type),
        'Event type not supported',
        '400',
      )

      log.info(`Found webhook ${webhook_nanoid}, creating test event`)

      const eventCreatedNanoId = await createWebhookEvent({
        event_type: body.event_type,
        payload: body,
      })

      log.info(`Created webhook event ${eventCreatedNanoId}`)

      const deliveryCreatedNanoId = await createWebhookDelivery({
        webhook_nanoid: webhook_nanoid,
        event_nanoid: eventCreatedNanoId,
        status: 'queued',
      })

      log.info(
        `Created webhook delivery ${deliveryCreatedNanoId} for event ${eventCreatedNanoId}`,
      )

      // TODO: In the future, implement logic to call another endpoint using the event data

      const res = {
        delivery_nanoid: deliveryCreatedNanoId, // Use webhook ID as delivery ID for simplicity
        message: 'Test event created successfully',
      }
      reply.code(201).send({ success: true, data: res })
    },
  })

  // Retry Delivery
  app.route({
    ...routes['/dashboard/webhooks/retry/:delivery_nanoid'].post,
    handler: async ({ auth, params, log }, reply) => {
      const { delivery_nanoid } = params
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      // Validate that the user is logged in
      assert(user_nanoid, 'User not logged in', '403')

      log.info(`Retrying webhook delivery ${delivery_nanoid}`)

      // Validate that the delivery exists before retrying
      const delivery = await getWebhookDeliveryById(delivery_nanoid)
      assert(delivery, 'Delivery not found', '404')

      // Validate that the user has access to the webhook associated with this delivery
      await authorizeAndFetchWebhook(user_nanoid, delivery.webhook_nanoid)

      await retryWebhookDelivery(delivery_nanoid)

      log.info(`Successfully queued retry for delivery ${delivery_nanoid}`)

      const status = 'queued'
      const res = {
        delivery_nanoid,
        status,
      }
      reply.code(201).send({ success: true, data: res })
    },
  })

  // Delivery History
  app.route({
    ...routes['/dashboard/webhooks/:webhook_nanoid/deliveries'].get,
    handler: async ({ auth, params, query, log }, reply) => {
      const user_nanoid = auth?.sessionClaims?.user_nanoid
      const { webhook_nanoid } = params
      const { cursor, limit = 50 } = query

      // Validate that the user is logged in
      assert(user_nanoid, 'User not logged in', '403')

      log.info(
        `Fetching delivery history for webhook ${webhook_nanoid} with limit ${limit}`,
      )

      // Validate that the webhook exists before fetching deliveries
      const webhook = await authorizeAndFetchWebhook(
        user_nanoid,
        webhook_nanoid,
      )
      assert(webhook, 'Webhook endpoint not found', '404')

      const deliveries = await getWebhookDeliveriesByWebhook(webhook_nanoid, {
        cursor,
        limit,
      })

      log.info(
        `Retrieved ${deliveries.length} deliveries for webhook ${webhook_nanoid}`,
      )

      // Check if there are more results
      const hasMore = deliveries.length > limit
      const deliveriesToReturn = hasMore ? deliveries.slice(0, -1) : deliveries
      const nextCursor = hasMore
        ? deliveriesToReturn[
            deliveriesToReturn.length - 1
          ]?.created_at.toISOString()
        : null

      log.info(
        `Returning ${deliveriesToReturn.length} deliveries, hasMore: ${hasMore}`,
      )

      const res = {
        deliveries: deliveriesToReturn.map((delivery) => ({
          delivery_nanoid: delivery.nanoid,
          event_nanoid: delivery.event_nanoid,
          status: delivery.status,
          response_code: delivery.response_code,
          created_at: delivery.created_at.toISOString(),
        })),
        next_cursor: nextCursor ?? null,
      }
      reply.code(200).send({ success: true, data: res })
    },
  })

  return app
}
