import { dashboardEntityRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import {
  getEntityMultiCurrencyConfigsByNanoid,
  storeEntityMultiCurrencyConfig,
} from 'repositories/src/riseEntities.js'
import { validateAuthUserHasPermissionOverEntityOperations } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes[
        '/dashboard/entity/:entity_nanoid/multi_currency_configurations'
      ].get,
      handler: async ({ params: { entity_nanoid }, auth }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, {
          message: 'no user_nanoid found in token claims',
          errorCode: 'USER_NOT_AUTHENTICATED',
        })

        await validateAuthUserHasPermissionOverEntityOperations({
          user_nanoid,
          entity_nanoid,
          scopes: ['user', 'company', 'team', 'team_employee'],
        })

        const data = await getEntityMultiCurrencyConfigsByNanoid({
          entityNanoid: entity_nanoid,
          userNanoid: user_nanoid,
        })
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes[
        '/dashboard/entity/:entity_nanoid/multi_currency_configurations'
      ].post,
      handler: async ({ params: { entity_nanoid }, auth, body }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, {
          message: 'no user_nanoid found in token claims',
          errorCode: 'USER_NOT_AUTHENTICATED',
        })

        await validateAuthUserHasPermissionOverEntityOperations({
          user_nanoid,
          entity_nanoid,
          scopes: ['user', 'company', 'team', 'team_employee'],
        })

        await storeEntityMultiCurrencyConfig({
          ...body,
          created_by: user_nanoid,
          last_modified_by: user_nanoid,
          nanoid: entity_nanoid,
        })

        reply.send({ success: true })
      },
    })
