import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween.js'

import { dashboardEmployeeRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import {
  getHistoryFromAddresses,
  getUserLedgerAddresses,
  getUserMonthlyTransanctions,
} from 'repositories/src/ledger.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/dashboard/employee/transactions/metrics/:team_nanoid'].get,
      handler: async ({ auth, params: { team_nanoid }, query }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        dayjs.extend(isBetween)

        const startDate = dayjs(query.start_date)
        const endDate = dayjs(query.end_date)
        assert(
          startDate.isBefore(endDate),
          'Start date must be before end date',
        )

        const addresses = await getUserLedgerAddresses(user.nanoid, team_nanoid)
        assert(addresses && addresses.length > 0, 'No addresses found')

        const history = await getHistoryFromAddresses({
          addresses,
          query: {
            status: 'confirmed',
            type: ['payment', 'withdraw'],
            start_date: startDate.toDate(),
            end_date: endDate.toDate(),
          },
        })

        let rangeStartDate = startDate
        let totalPaymentsAmount = 0
        let totalWithdrawAmount = 0
        const biweeklyTransactions: {
          from: Date
          to: Date
          payment_amount: number
          withdraw_amount: number
        }[] = []

        while (rangeStartDate.isBefore(endDate)) {
          const rangeEnd = rangeStartDate.add(13, 'day').endOf('day')
          const actualEnd = rangeEnd.isBefore(endDate) ? rangeEnd : endDate

          const [aggregatedPaymentAmount, aggregatedWithdrawAmount] = history
            .filter((transaction) =>
              dayjs(transaction.date).isBetween(
                rangeStartDate,
                actualEnd,
                null,
                '[]',
              ),
            )
            .reduce(
              ([paymentAmount, withdrawAmount], transaction) => [
                paymentAmount +
                  (transaction.type === 'payment'
                    ? Math.abs(+transaction.amount)
                    : 0),
                withdrawAmount +
                  (transaction.type === 'withdraw'
                    ? Math.abs(+transaction.amount)
                    : 0),
              ],
              [0, 0],
            )

          totalPaymentsAmount += aggregatedPaymentAmount
          totalWithdrawAmount += aggregatedWithdrawAmount

          biweeklyTransactions.push({
            from: rangeStartDate.toDate(),
            to: actualEnd.toDate(),
            payment_amount: aggregatedPaymentAmount,
            withdraw_amount: aggregatedWithdrawAmount,
          })

          rangeStartDate = actualEnd.add(1, 'day').startOf('day')
        }
        reply.send({
          success: true,
          data: {
            start_date: startDate.toDate(),
            end_date: endDate.toDate(),
            total_payments_amount: totalPaymentsAmount,
            total_withdraw_amount: totalWithdrawAmount,
            biweekly_transactions: biweeklyTransactions,
          },
        })
      },
    })
    .route({
      ...routes['/dashboard/employee/:nanoid/monthly/transactions'].get,
      handler: async (
        { auth, params: { nanoid }, query: { team_nanoid } },
        reply,
      ) => {
        await validateUserByAuth({ auth, apps: ['pay'] })
        const data = await getUserMonthlyTransanctions(nanoid, team_nanoid)
        reply.send({
          success: true,
          data,
        })
      },
    })
