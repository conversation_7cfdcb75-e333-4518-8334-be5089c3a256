import {
  gatherPayrollHealthcheckData,
  generatePayrollHealthcheckSlackBlocks,
} from 'repositories/src/payrollHealthcheck.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import { sendSlackMessage } from 'repositories/src/slack.js'
import { mapAsync } from 'utils/src/common/array.js'
import { getSecrets } from 'utils/src/google/getSecrets.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-payroll-healthcheck-report',
    name: 'dashboard/payroll.healthcheck_report',
  },
  [{ event: 'dashboard/payroll.healthcheck_report' }],
  async ({
    step,
    logger,
    event: {
      data: { run_id, pay_cycles },
    },
  }) => {
    const reportDataItems = await step.run(
      'gather-payroll-healthcheck-data',
      () =>
        mapAsync(pay_cycles, (pc) => gatherPayrollHealthcheckData(pc, run_id)),
    )

    const slackBlocks = generatePayrollHealthcheckSlackBlocks(reportDataItems)

    // Provide a summary text for notifications
    const notificationText =
      reportDataItems.length > 0
        ? `Payroll Healthcheck Report (${run_id}) - ${reportDataItems.map((item) => item.payCycleString).join(', ')}`
        : `Payroll Healthcheck Report (${run_id}) - No data processed.`

    const secrets = await step.run('get-secrets', () =>
      getSecrets([
        'slack-payroll-healthcheck-bot-token',
        'slack-payroll-healthcheck-channel-id',
      ]),
    )

    await step.run('send-slack-message', async () => {
      const [response, error] = await sendSlackMessage({
        blocks: slackBlocks,
        text: notificationText, // Fallback text for notifications
        botToken: secrets['slack-payroll-healthcheck-bot-token'],
        channelId: secrets['slack-payroll-healthcheck-channel-id'],
      })

      if (error) {
        logger.error('Failed to send Slack message', {
          error: error.message,
          run_id,
        })
      }
      return { response, error: error?.message }
    })
  },
)
