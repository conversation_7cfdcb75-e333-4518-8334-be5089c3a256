import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import {
  buildInsufficientBalanceNotificationsProperties,
  fetchScheduledPaymentsForNextTwoDays,
  getTeamAdminsEmails,
  groupPaymentsByToken,
  notifyTeamAdminsAboutInsufficientBalance,
} from '../useCases/teamBalanceCheckUseCase.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-riseaccounts-team-balance-check',
    name: 'dashboard/riseaccounts.team_balance_check',
    retries: 3,
    concurrency: {
      limit: 1,
    },
  },
  [{ event: 'dashboard/riseaccounts.team_balance_check' }],
  async ({
    event: {
      data: { account_address, network, team_nanoid },
    },
    step,
    logger,
  }) => {
    const allPayments = await step.run(
      'fetch-scheduled-payments-for-next-two-days',
      () => fetchScheduledPaymentsForNextTwoDays(account_address, network),
    )

    if (allPayments.length === 0)
      return 'No scheduled payments found for the next two days'

    const notifications = await step.run(
      'build-notification-properties',
      async () => {
        const paymentsByToken = groupPaymentsByToken(allPayments)

        return await buildInsufficientBalanceNotificationsProperties(
          paymentsByToken,
          account_address,
          network,
        )
      },
    )

    const adminsEmails = await step.run('get-admins-emails', () =>
      getTeamAdminsEmails(team_nanoid as TeamNanoid),
    )

    if (adminsEmails.length === 0)
      return 'No team admins found for the given team nanoid'

    const failedEmails = await notifyTeamAdminsAboutInsufficientBalance(
      notifications,
      adminsEmails,
    )
    if (failedEmails.length > 0) {
      logger.warn('Some notifications failed:', { failedEmails })
    }
  },
)
