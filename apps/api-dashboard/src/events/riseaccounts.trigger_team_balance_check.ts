import type { ValidNetworks } from '@riseworks/contracts/src/smartContractTypes.js'
import { chunk } from 'remeda'
import { getAllTeamsWithRiseAccountOrderedByCreation } from 'repositories/src/teams.js'
import {
  MAX_BATCH_EVENTS_SIZE,
  createInngestClient,
} from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-riseaccounts-trigger-balance-check',
    name: 'dashboard/riseaccounts.trigger_balance_check',
    retries: 0,
    concurrency: {
      limit: 1,
    },
  },
  [{ cron: '0 0 * * *' }],
  async ({ step }) => {
    const network = 'arbitrum' as ValidNetworks

    const teams = await step.run('fetch-teams-with-rise-account', () =>
      getAllTeamsWithRiseAccountOrderedByCreation(),
    )
    const validTeams = teams.filter((team) => team.rise_account.trim() !== '')

    const chunkedTeams = chunk(validTeams, MAX_BATCH_EVENTS_SIZE)

    for (const chunk of chunkedTeams) {
      await step.sendEvent(
        'dashboard/riseaccounts.team_balance_check',
        chunk.map((team) => ({
          name: 'dashboard/riseaccounts.team_balance_check',
          data: {
            account_address: team.rise_account,
            network,
            team_nanoid: team.nanoid,
          },
        })),
      )
    }
  },
)
