import { db } from 'db'
import {
  updateOnboarding,
  getUserAndOnboard,
} from 'repositories/src/usersOnboarding.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import assert from 'utils/src/common/assertHTTP.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-user-convert-payee_to_payer',
    name: 'dashboard/user.convert_payee_to_payer',
  },
  [{ event: 'dashboard/user.convert_payee_to_payer' }],
  async ({
    event: {
      data: { nanoid },
    },
    step,
    runId,
    logger,
  }) => {
    logger.info('Starting script to convert payee to payer')

    await db.transaction().execute(async (trx) => {
      const { user, onboard } = await getUserAndOnboard(nanoid, trx)
      assert(user, `User ${nanoid} not found`, '401')

      const isPayee = onboard?.role === 'payee'
      assert(isPayee, `User ${nanoid} must have role 'payee'`, '401')

      await updateOnboarding(
        nanoid,
        {
          role: 'payer',
          register_business: true,
        },
        trx,
      )
    })
  },
)
