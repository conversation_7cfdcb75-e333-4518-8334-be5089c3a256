import { createInngestClient } from 'utils/src/inngest/index.js'
import {
  employeePayrollGroupId,
  teamPayrollCashRequirementPaymentId,
} from 'repositories/src/payroll.js'
import { getTeamPayrollAccounts } from 'repositories/src/teams.js'
import { getPayrollEmployeesToProcess } from 'repositories/src/employeePayrollSettings.js'
import { mapAsync } from 'utils/src/common/array.js'
import {
  insertPayrollHealthcheckLog,
  paymentAmountsToBigInt,
  readAndFormatPayments,
  validatePaymentFlowBalance,
  validatePaymentTiming,
  type PayrollHealthcheckError,
} from 'repositories/src/payrollHealthcheck.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-team-payroll-healthcheck',
    name: 'dashboard/payroll.healthcheck_team',
    throttle: {
      limit: 20,
      period: '10s',
    },
  },
  [{ event: 'dashboard/payroll.healthcheck_team' }],
  async ({
    step,
    event: {
      data: { team_nanoid, payroll_program, pay_cycle, run_id },
    },
  }) => {
    const errors: PayrollHealthcheckError[] = []

    // Step 1: Get team payroll accounts
    const teamPayrollAccounts = await step.run(
      'get-team-payroll-accounts',
      () => getTeamPayrollAccounts(team_nanoid, payroll_program),
    )

    // Step 2: Check for ACCOUNT_MISSING_ERROR
    const accountMissingError = await step.run(
      'check-account-missing-error',
      async () => {
        if (
          !(
            teamPayrollAccounts?.team_payroll_account &&
            teamPayrollAccounts.team_account
          )
        ) {
          errors.push({
            name: 'ACCOUNT_MISSING_ERROR',
            data: {
              'teams_data.rise_account': teamPayrollAccounts?.team_account,
              'team_payroll.rise_account':
                teamPayrollAccounts?.team_payroll_account,
            },
          })

          await insertPayrollHealthcheckLog({
            runId: run_id,
            teamNanoid: team_nanoid,
            payrollProgram: payroll_program,
            targetAccount: 'team_payroll',
            riseAccount: teamPayrollAccounts?.team_payroll_account ?? '',
            payCycle: pay_cycle,
            errors,
          })

          return true
        }

        return false
      },
    )

    if (accountMissingError || !teamPayrollAccounts) {
      return {
        success: false,
        errors,
      }
    }

    const { team_account, team_payroll_account } = teamPayrollAccounts

    // Step 3: Get payments for this team for the pay cycle
    const payments = await step.run('get-payments', () =>
      readAndFormatPayments({
        incoming: {
          account: team_account,
          groupId: teamPayrollCashRequirementPaymentId(team_nanoid, pay_cycle),
        },
        outgoing: {
          account: team_payroll_account,
          groupId: employeePayrollGroupId(team_nanoid, pay_cycle),
        },
      }),
    )

    const incomingScheduled = paymentAmountsToBigInt(payments.incomingScheduled)
    const incomingComplete = paymentAmountsToBigInt(payments.incomingComplete)
    const outgoingScheduled = paymentAmountsToBigInt(payments.outgoingScheduled)
    const outgoingComplete = paymentAmountsToBigInt(payments.outgoingComplete)

    // Step 4: Check payment flow balance
    const balanceError = await step.run('validate-payment-flow-balance', () => {
      const paymentFlowData = {
        incoming: [...incomingScheduled, ...incomingComplete],
        outgoing: [...outgoingScheduled, ...outgoingComplete],
      }

      return validatePaymentFlowBalance(paymentFlowData)
    })
    if (balanceError) errors.push(balanceError)

    // Step 5: Check payment timing
    const timingError = await step.run('validate-payment-timing', () => {
      const scheduledPayments = [...incomingScheduled, ...outgoingScheduled]
      return validatePaymentTiming(scheduledPayments)
    })
    if (timingError) errors.push(timingError)

    // Step 6: Save log to database
    await step.run('insert-payroll-healthcheck-log', () =>
      insertPayrollHealthcheckLog({
        runId: run_id,
        teamNanoid: team_nanoid,
        payrollProgram: payroll_program,
        targetAccount: 'team_payroll',
        riseAccount: team_payroll_account,
        payCycle: pay_cycle,
        errors,
      }),
    )

    // Step 7: Retrieve employee payrolls and trigger healthcheck events
    await step.run('get-employee-payrolls', async () => {
      const employeePayrolls = await getPayrollEmployeesToProcess({
        team_nanoid: team_nanoid,
        pay_cycle: pay_cycle,
      })

      const events = await mapAsync(
        employeePayrolls,
        (employeePayroll) => {
          return client.send({
            name: 'dashboard/payroll.healthcheck_employee',
            data: {
              team_nanoid: team_nanoid,
              user_nanoid: employeePayroll.user_nanoid,
              payroll_program: payroll_program,
              pay_cycle: pay_cycle,
              team_payroll_account: team_payroll_account,
              run_id: run_id,
            },
          })
        },
        10,
      )

      return {
        employeePayrollsCount: employeePayrolls.length,
        eventIds: events.flatMap((e) => e.ids),
      }
    })

    return {
      success: errors.length === 0,
      errors,
    }
  },
)
