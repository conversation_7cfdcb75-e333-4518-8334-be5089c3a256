import { withdrawAccountNanoid } from '@riseworks/contracts/src/brands.js'
import { db } from 'db/src/index.js'
import { mapOrumExternalAccountStatusToRiseStatus } from 'repositories/src/orum.js'
import {
  getWithdrawAccountById,
  updateWithdrawAccountStatus,
} from 'repositories/src/withdrawAccounts.js'
import assert from 'utils/src/common/assertHTTP.js'
import { ERROR_CODES } from 'utils/src/common/errorCodes.js'
import { is } from 'utils/src/common/is.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')
export default client.createFunction(
  {
    id: 'dashboard-orum-external-account-validation',
    name: 'dashboard/orum.external_account_validation',
  },
  [{ event: 'dashboard/orum.external_account_validation' }],
  async ({ event: { data }, logger }) => {
    const {
      event_type,
      event_data: { external_account },
    } = data

    assert(
      external_account,
      `External account field is not present in webhook payload event_type: ${event_type}`,
    )

    // v1
    if (!external_account.account_reference_id.startsWith('v2_')) {
      logger.info('Found v1 external account, skipping.')
      return
    }

    const { account_reference_id, status } = external_account

    const account_status = mapOrumExternalAccountStatusToRiseStatus(status)

    // v2
    await db.transaction().execute(async (trx) => {
      logger.info('Retrieving v2 account information and updating status')
      const nanoid = account_reference_id.split('v2_')[1]
      assert(
        is(nanoid, withdrawAccountNanoid),
        'Invalid account reference ID format',
      )
      const withdrawAccount = await getWithdrawAccountById(nanoid, trx)
      assert(withdrawAccount, {
        errorCode: ERROR_CODES.WITHDRAW_ACCOUNT_NOT_FOUND,
      })

      await updateWithdrawAccountStatus(
        withdrawAccount.user_nanoid,
        withdrawAccount.nanoid,
        account_status,
      )
    })

    return {
      account_status,
      account_reference_id,
      event_type,
    }
  },
)
