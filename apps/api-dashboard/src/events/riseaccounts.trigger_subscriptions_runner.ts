import dayjs from 'dayjs'
import { getAllTeamsRiseAccounts } from 'repositories/src/smartContracts.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-riseaccounts-trigger-subscriptions-runner',
    name: 'dashboard/riseaccounts.trigger_subscriptions_runner',
    retries: 0,
    concurrency: {
      limit: 1,
    },
  },
  [{ cron: '0 6 2 * *' }],
  async ({ step }) => {
    const network = 'arbitrum'
    const allRiseAccounts = await step.run(
      'get_all_rise_accounts_by_payments',
      async () => {
        return await getAllTeamsRiseAccounts({
          network,
        })
      },
    )

    await step.sendEvent(
      'payment_lookup_trigger',
      allRiseAccounts.map((riseAccount) => ({
        name: 'dashboard/riseaccounts.runner_subscriptions',
        data: {
          rise_account: riseAccount.rise_account,
          network,
          epoch: dayjs().subtract(1, 'month').unix(),
        },
      })),
    )
  },
)
