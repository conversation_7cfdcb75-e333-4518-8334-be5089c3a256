import { createInngestClient } from 'utils/src/inngest/index.js'
import { employeePayrollGroupId } from 'repositories/src/payroll.js'
import { getEmployeePayrollSettings } from 'repositories/src/employeePayrollSettings.js'
import {
  insertPayrollHealthcheckLog,
  readAndFormatPayments,
  validatePaymentFlowBalance,
  validatePaymentTiming,
  validateCalculatedPaymentAmountMismatch,
  type PayrollHealthcheckError,
  paymentAmountsToBigInt,
} from 'repositories/src/payrollHealthcheck.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-payroll-healthcheck-employee',
    name: 'dashboard/payroll.healthcheck_employee',
    throttle: {
      limit: 30,
      period: '10s',
    },
  },
  [{ event: 'dashboard/payroll.healthcheck_employee' }], // you can add more triggers here, like cron
  async ({
    step,
    event: {
      data: {
        team_nanoid,
        user_nanoid,
        payroll_program,
        pay_cycle,
        run_id,
        team_payroll_account,
      },
    },
  }) => {
    const errors: PayrollHealthcheckError[] = []

    // Step 1: Get employee payroll settings
    const employeePayroll = await step.run('get-employee-payroll', () =>
      getEmployeePayrollSettings({
        team_nanoid: team_nanoid,
        pay_cycle: pay_cycle,
        user_nanoid: user_nanoid,
      }),
    )

    // Step 2: Check for ACCOUNT_MISSING_ERROR
    const accountMissingError = await step.run(
      'check-account-missing-error',
      async () => {
        if (!employeePayroll?.rise_account) {
          await insertPayrollHealthcheckLog({
            runId: run_id,
            teamNanoid: team_nanoid,
            payrollProgram: payroll_program,
            targetAccount: 'employee_payroll',
            riseAccount: employeePayroll?.rise_account ?? '',
            userNanoid: user_nanoid,
            payCycle: pay_cycle,
            errors: [
              {
                name: 'ACCOUNT_MISSING_ERROR',
                data: {
                  'employee_payroll.rise_account':
                    employeePayroll?.rise_account,
                },
              },
            ],
          })

          return true
        }

        return false
      },
    )

    if (accountMissingError || !employeePayroll) {
      return {
        success: false,
        errors,
      }
    }

    // Step 3: Get payments for this employee for the pay cycle
    const payments = await step.run('get-payments', () =>
      readAndFormatPayments({
        incoming: {
          account: team_payroll_account,
          groupId: employeePayrollGroupId(team_nanoid, pay_cycle),
        },
        outgoing: {
          account: employeePayroll.rise_account,
          groupId: employeePayrollGroupId(team_nanoid, pay_cycle),
        },
      }),
    )

    const incomingScheduled = paymentAmountsToBigInt(payments.incomingScheduled)
    const incomingComplete = paymentAmountsToBigInt(payments.incomingComplete)
    const outgoingScheduled = paymentAmountsToBigInt(payments.outgoingScheduled)
    const outgoingComplete = paymentAmountsToBigInt(payments.outgoingComplete)

    // Step 4: Check payment flow balance
    const balanceError = await step.run('validate-payment-flow-balance', () => {
      const paymentFlowData = {
        incoming: [...incomingScheduled, ...incomingComplete],
        outgoing: [...outgoingScheduled, ...outgoingComplete],
      }

      return validatePaymentFlowBalance(paymentFlowData)
    })
    if (balanceError) errors.push(balanceError)

    // Step 5: Check payment timing
    const timingError = await step.run('validate-payment-timing', () => {
      const scheduledPayments = [...incomingScheduled, ...outgoingScheduled]
      return validatePaymentTiming(scheduledPayments)
    })
    if (timingError) errors.push(timingError)

    // Step 6: Check for CALCULATED_PAYMENT_AMOUNT_MISMATCH_ERROR
    const calculatedPaymentAmountMismatchError = await step.run(
      'check-calculated-payment-amount-mismatch-error',
      () => {
        return validateCalculatedPaymentAmountMismatch({
          employeePayroll,
          teamNanoid: team_nanoid,
          payCycle: pay_cycle,
          payrollProgram: payroll_program,
          outgoingPayments: [...outgoingScheduled, ...outgoingComplete],
        })
      },
    )
    if (calculatedPaymentAmountMismatchError) {
      errors.push(calculatedPaymentAmountMismatchError)
    }

    // Step 7: Save log to database
    await step.run('insert-payroll-healthcheck-log', () =>
      insertPayrollHealthcheckLog({
        runId: run_id,
        teamNanoid: team_nanoid,
        payrollProgram: payroll_program,
        targetAccount: 'employee_payroll',
        riseAccount: employeePayroll.rise_account,
        payCycle: pay_cycle,
        userNanoid: employeePayroll.user_nanoid,
        errors,
      }),
    )

    return {
      success: errors.length === 0,
      errors,
    }
  },
)
