import { db } from 'db'
import {
  createOrumVerification,
  getOrumVerification,
  mapOrumStatusToRiseStatus,
} from 'repositories/src/orum.js'
import { getWithdrawAccountPrivateBankData } from 'repositories/src/privateData.js'
import { getUserByNanoid } from 'repositories/src/users.js'
import {
  getWithdrawAccountById,
  getWithdrawAccountVerificationByStatus,
} from 'repositories/src/withdrawAccounts.js'
import { updateDomesticUSDAccountWithValidation } from 'repositories/src/withdrawOrum.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-users-withdraw_account_verification-sync_pending',
    name: 'dashboard/users_withdraw_account_verification.sync_pending',
  },
  [
    {
      event: 'dashboard/users_withdraw_account_verification.sync_pending',
    },
    {
      // Everyday at 5AM
      cron: 'TZ=America/New_York 0 5 * * *',
    },
  ],
  async ({ step, logger }) => {
    // Get pending verification events from users_withdraw_account_verification
    const withdrawAccountVerifications = await step.run(
      'get-pending-withdraw-account-verification',
      async () => {
        const withdrawAccountVerifications =
          await getWithdrawAccountVerificationByStatus('pending')

        return withdrawAccountVerifications.map((verificationStatus) => ({
          verification_external_id: verificationStatus.verification_external_id,
          nanoid: verificationStatus.nanoid,
        }))
      },
    )

    for (const withdrawAccountVerification of withdrawAccountVerifications) {
      await step.run(
        `sync-verification-${withdrawAccountVerification.nanoid}`,
        async () => {
          const account = await getWithdrawAccountById(
            withdrawAccountVerification.nanoid,
          )

          if (!account) {
            logger.info(
              `No account found for verification ${withdrawAccountVerification.nanoid}`,
            )
            return 'NO_ACCOUNT'
          }

          // Only sync if the account is pending
          if (account.status !== 'pending') {
            logger.info(
              `Account ${account.nanoid} is not pending, skipping verification sync`,
            )
            return 'NOT_PENDING'
          }

          const user = await getUserByNanoid(account.user_nanoid)
          if (!user) {
            logger.info(
              `No user found for account ${account.nanoid}, skipping verification sync`,
            )
            return 'NO_USER'
          }

          const bankData = await getWithdrawAccountPrivateBankData(
            account.nanoid,
          )

          if (!bankData) {
            logger.info(
              `No bank data found for account ${account.nanoid}, skipping verification sync`,
            )
            return 'NO_BANK_DATA'
          }

          if (bankData.provider_name !== 'Orum') {
            logger.info(
              `Provider ${bankData.provider_name} is not supported at this time, skipping verification sync`,
            )
            return 'UNSUPPORTED_PROVIDER'
          }

          if (!(bankData.account_number && bankData.routing_number)) {
            logger.info(
              `Bank data is missing account number or routing number for account ${account.nanoid}, skipping verification sync`,
            )
            return 'NO_BANK_DATA_NUMBERS'
          }

          const { data: verification, error } = await getOrumVerification({
            id: withdrawAccountVerification.verification_external_id,
          })

          // Resend verification if the verification is not found
          if (error && error.error_code === 'unknown_id') {
            const verification = await createOrumVerification({
              account_number: bankData.account_number,
              routing_number: bankData.routing_number,
              account_holder_name: [
                bankData.beneficiary_first_name,
                bankData.beneficiary_middle_name,
                bankData.beneficiary_last_name,
              ]
                .filter(Boolean)
                .join(' '),
              email: user.email,
              person: {
                first_name: user.first_name,
                last_name: user.last_name,
              },
            })

            // Update the verification_external_id in the withdraw_account_verification table
            await db
              .updateTable('rise_private.users_withdraw_account_verification')
              .set({
                verification_external_id: verification.id,
              })
              .where('nanoid', '=', withdrawAccountVerification.nanoid)
              .execute()

            logger.info(
              `Resent verification for ${withdrawAccountVerification.verification_external_id}: ${verification.id}`,
            )
            return 'RESEND_VERIFICATION'
          }

          if (error) {
            logger.error(
              `Error fetching verification status for ${withdrawAccountVerification.verification_external_id}: ${error.error_code}`,
            )
            return 'ERROR_FETCHING_VERIFICATION'
          }

          if (verification.account.verification_status !== 'pending') {
            await db.transaction().execute(async (trx) => {
              await updateDomesticUSDAccountWithValidation({
                verification_id: withdrawAccountVerification.nanoid,
                status: mapOrumStatusToRiseStatus(
                  verification.account.verification_status,
                ),
                status_reason: verification.account.status_reason,
                db: trx,
              })
            })

            logger.info(
              `Updated account ${withdrawAccountVerification.nanoid} to status ${verification.account.verification_status}`,
            )
            return 'UPDATE_ACCOUNT_STATUS'
          }

          logger.info(
            `Verification not updated for ${withdrawAccountVerification.verification_external_id} with status ${verification.account.verification_status}`,
          )
          return 'NO_UPDATE_REQUIRED'
        },
      )
    }
  },
)
