import dayjs from 'dayjs'
import { createInngestClient } from 'utils/src/inngest/index.js'
import {
  createOrUpdatePayrollPayments,
  getContract,
  tokenBalanceOf,
} from 'repositories/src/smartContracts.js'
import { getAllPaymentsByDayRangeV2 } from 'repositories/src/riseAccounts.js'
import { sumBy } from 'remeda'
import {
  getTeamPayroll,
  payrollBalanceCleanupGroupId,
  uniquePaymentId,
} from 'repositories/src/payroll.js'
import { PAYROLL_BALANCE_CLEANUP } from 'utils/src/blockchain/paymentIdentifiers.js'
import { friendlyFormatPayment } from 'utils/src/common/payments.js'
import { getTeamData } from 'repositories/src/teams.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'team-payroll-balance-cleanup',
    name: 'dashboard/payroll.team_payroll_balance_cleanup',
  },
  [{ event: 'dashboard/payroll.team_payroll_balance_cleanup' }],
  async ({
    event: {
      data: { team_nanoid },
    },
    step,
    logger,
  }) => {
    const teamPayroll = await step.run('get-team-payroll', async () => {
      return await getTeamPayroll(team_nanoid)
    })

    if (!teamPayroll) {
      logger.error('Team payroll not found')
      return {
        success: false,
        message: 'Team payroll not found',
      }
    }

    const startDate = dayjs().subtract(2, 'months')
    const endDate = dayjs().add(2, 'month')

    const paymentsScheduledOutcome = await step.run(
      'get-payments-scheduled-outcome',
      async () => {
        const payments = await getAllPaymentsByDayRangeV2(
          teamPayroll.rise_account,
          'Scheduled',
          startDate,
          endDate,
          'arbitrum',
        )

        logger.info(
          `Payments scheduled outcome (${payments.length}):
          ${JSON.stringify(
            payments.map((p) => friendlyFormatPayment(p)),
            null,
            2,
          )}`,
        )

        return sumBy(payments, (p) => Number(p.amount) / 1e6)
      },
    )

    const paymentsScheduledIncome = await step.run(
      'get-payments-scheduled-income',
      async () => {
        const payments = await getAllPaymentsByDayRangeV2(
          teamPayroll.rise_account,
          'Scheduled',
          startDate,
          endDate,
          'arbitrum',
          teamPayroll.rise_account,
        )

        logger.info(
          `Payments scheduled income (${payments.length}):
          ${JSON.stringify(
            payments.map((p) => friendlyFormatPayment(p)),
            null,
            2,
          )}`,
        )

        return sumBy(payments, (p) => Number(p.amount) / 1e6)
      },
    )

    const balance = await step.run('get-balance', async () => {
      const token = await getContract('RiseUSD', 'arbitrum')
      const tokenAddress = await token.getAddress()

      const balance = await tokenBalanceOf(
        tokenAddress,
        teamPayroll.rise_account,
        'arbitrum',
      )

      return Number(balance) / 1e6
    })

    const paymentsNetResult = paymentsScheduledIncome - paymentsScheduledOutcome
    const result = balance + paymentsNetResult

    const results = {
      paymentsScheduledIncome: paymentsScheduledIncome.toFixed(2),
      paymentsScheduledOutcome: paymentsScheduledOutcome.toFixed(2),
      paymentsNetResult: paymentsNetResult.toFixed(2),
      balance: balance.toFixed(2),
      result: result.toFixed(2),
    }

    logger.info(`Results: ${JSON.stringify(results, null, 2)}`)

    if (result < 0) {
      return {
        success: false,
        message: 'Balance is negative',
        results,
      }
    }

    // If the difference is less than 0.01, we consider it correct
    if (result < 0.01) {
      return {
        success: true,
        message: 'Balance is correct',
        results,
      }
    }

    const teamData = await getTeamData(team_nanoid)
    if (!teamData) {
      logger.error(`Team data not found for team '${team_nanoid}'`)
      return {
        success: false,
        message: `Team data not found for team '${team_nanoid}'`,
        results,
      }
    }

    const amount = Math.min(result, balance)

    await step.run('create-cleanup-payment', async () => {
      const payment = await createOrUpdatePayrollPayments({
        sender: teamPayroll.rise_account,
        payments: [
          {
            id: uniquePaymentId(),
            groupId: payrollBalanceCleanupGroupId(),
            amount: amount,
            recipient: teamData.rise_account,
            payAtTime: dayjs().unix(),
            payType: PAYROLL_BALANCE_CLEANUP,
          },
        ],
      })

      return {
        success: true,
        message: 'Cleanup payment created',
        createdPayment: {
          amount,
          ...payment,
          recipient: teamData.rise_account,
        },
        results,
      }
    })

    return {
      success: true,
      message: 'Cleanup payment created',
    }
  },
)
