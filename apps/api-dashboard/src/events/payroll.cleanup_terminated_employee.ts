import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import { getLatestEmployeePayrollSettings } from 'repositories/src/employeePayrollSettings.js'
import {
  employeePayrollGroupId,
  getTeamPayroll,
} from 'repositories/src/payroll.js'
import { groupPaymentsRead } from 'repositories/src/riseAccounts.js'
import { removePaymentsByIds } from 'repositories/src/smartContracts.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getNextPayCycle, getPayCycle } from 'utils/src/common/payCycles.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const FUTURE_CYCLES_TO_BE_CLEANED = 24

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-payroll-cleanup-terminated_employee',
    name: 'dashboard/payroll.cleanup_terminated_employee',
  },
  [{ event: 'dashboard/payroll.cleanup_terminated_employee' }],
  async ({
    event: {
      data: { employee_nanoid, team_nanoid },
    },
    step,
  }) => {
    // Step 1: Get the latest employee payroll settings
    const employeePayrollSettings = await step.run(
      'get-employee-payroll-settings',
      () =>
        getLatestEmployeePayrollSettings({
          user_nanoid: employee_nanoid,
          team_nanoid,
        }),
    )

    // Validate if the employee is terminated
    assert(employeePayrollSettings, 'Employee payroll settings not found')
    assert(
      employeePayrollSettings.status === 'terminated',
      'Employee is not terminated',
    )
    assert(
      employeePayrollSettings.end_date !== null,
      'Employee end date is not set',
    )

    // Calculate the cycles to be checked
    let cycle = getPayCycle(employeePayrollSettings.end_date)
    const cyclesToCheck: PayCycle[] = []
    for (let i = 0; i < FUTURE_CYCLES_TO_BE_CLEANED; i++) {
      cycle = getNextPayCycle(cycle)
      cyclesToCheck.push(cycle)
    }

    // Step 2: Get the team payroll account
    const teamPayroll = await step.run('get-team-payroll', () =>
      getTeamPayroll(team_nanoid),
    )
    assert(teamPayroll, 'Team payroll not found')

    // Step 3: Retrieve incoming payments to be removed
    const incomingPaymentIds = await step.run(
      'get-incoming-payments-to-remove',
      async () => {
        const payments = await mapAsync(
          cyclesToCheck,
          async (payCycle) => {
            const payments = await groupPaymentsRead(
              teamPayroll.rise_account,
              employeePayrollGroupId(team_nanoid, payCycle),
              ['Scheduled'],
              'arbitrum',
            )

            return payments
              .filter(
                (p) => p.recipient === employeePayrollSettings.rise_account,
              )
              .flatMap((p) => p.id)
          },
          6, // concurrency limit
        )

        return payments.flat()
      },
    )

    // Step 4: Retrieve outgoing payments to be removed
    const outgoingPaymentIds = await step.run(
      'get-outgoing-payments-to-remove',
      async () => {
        const payments = await mapAsync(
          cyclesToCheck,
          async (payCycle) => {
            const payments = await groupPaymentsRead(
              employeePayrollSettings.rise_account,
              employeePayrollGroupId(team_nanoid, payCycle),
              ['Scheduled'],
              'arbitrum',
            )

            return payments.flatMap((p) => p.id)
          },
          6, // concurrency limit
        )

        return payments.flat()
      },
    )

    // Step 5: Remove payments
    await step.run('remove-payments', async () => {
      const incomingTxn = await removePaymentsByIds({
        sender: teamPayroll.rise_account,
        ids: incomingPaymentIds,
      })

      const outgoingTxn = await removePaymentsByIds({
        sender: employeePayrollSettings.rise_account,
        ids: outgoingPaymentIds,
      })

      return {
        incomingTxn,
        outgoingTxn,
      }
    })
  },
)
