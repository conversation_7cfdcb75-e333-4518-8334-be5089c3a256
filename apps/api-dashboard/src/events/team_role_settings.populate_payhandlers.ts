import { db } from 'db'
import { goTry } from 'go-go-try'
import { issueContractorOrEmployeeAddressesIfNeeded } from 'repositories/src/riseEntities.js'
import { getTeamRoleWithNoPayHandler } from 'repositories/src/teamRoleSettings.js'
import { mapAsync } from 'utils/src/common/array.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'admin-team-role_settings-create_employee_rise_account',
    name: 'admin/team_role_settings.create_employee_rise_account',
  },
  [{ event: 'admin/team_role_settings.create_employee_rise_account' }],
  async ({ step, logger }) => {
    const teamRoleWithNoPayHandler = await step.run(
      'get_employee_with_no_account',
      async () => {
        return await getTeamRoleWithNoPayHandler()
      },
    )

    logger.info(
      `Found ${teamRoleWithNoPayHandler.length} Employees with no Rise Accounts`,
    )

    return await mapAsync(teamRoleWithNoPayHandler, async (trs) =>
      step.run('create_employee_rise_account', async () => {
        logger.info(
          `Creating Employee RiseAccount Handler for ${trs.user_nanoid} and ${trs.team_nanoid}`,
        )

        const [error] = await db.transaction().execute(async (trx) => {
          return await goTry(async () =>
            issueContractorOrEmployeeAddressesIfNeeded(
              trs.user_nanoid,
              trs.team_nanoid,
              'arbitrum',
              trx,
            ),
          )
        })

        if (error) {
          return {
            user_nanoid: trs.user_nanoid,
            team_nanoid: trs.team_nanoid,
            error: error,
          }
        }

        return {
          user_nanoid: trs.user_nanoid,
          team_nanoid: trs.team_nanoid,
        }
      }),
    )
  },
)
