import type {
  CompanyNanoid,
  CompanyRiseid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db } from 'db'
import { runMigrationStep } from 'repositories/src/trackMigration.js'
import {
  getOrCreateV1EntityMigration,
  getV1EntityMigration,
  migrateCompanyData,
  migratePayerCompanyAccounts,
  migrateSumsub,
  setEntityMigrationStep,
  v1StrapiCompanyMembers,
} from 'repositories/src/v1StrapiMigration.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-migration-company',
    name: 'dashboard/migration.company',
  },
  [{ event: 'dashboard/migration.company' }],
  async ({
    event: {
      data: {
        company: { company_id, owner_id, owner_type, type },
        teams,
      },
    },
    step,
    runId,
  }) => {
    const { entityMigration, ownerMigration } = await step.run(
      'start-migration',
      async () => {
        const ownerMigration = await getV1EntityMigration(owner_id, owner_type)
        assert(ownerMigration, 'Owner is not migrated', '404')

        const entityMigration = await getOrCreateV1EntityMigration(
          company_id,
          type,
          ownerMigration.riseid,
          owner_id,
          owner_type,
          null,
          null,
          runId ?? null,
        )
        assert(entityMigration, 'Entity migration not found', '404')

        return {
          entityMigration,
          ownerMigration,
        }
      },
    )

    await step.run('migrate-company-data', async () => {
      await runMigrationStep(
        db,
        'migrate-company-data',
        'user',
        ownerMigration.entity_v1_id,
        async () => {
          await migrateCompanyData(
            entityMigration.entity_v1_id,
            entityMigration.entity_v2_nanoid as CompanyNanoid,
            entityMigration.riseid as CompanyRiseid,
            ownerMigration.riseid as CompanyRiseid,
            type,
            ownerMigration.entity_v2_nanoid as UserNanoid,
          )
        },
      )
    })

    if (!entityMigration.migrated_sumsub_ids && type === 'company')
      await step.run('migrate-sumsub', async () => {
        await runMigrationStep(
          db,
          'migrate-sumsub',
          'user',
          ownerMigration.entity_v1_id,
          async () => {
            await migrateSumsub(
              company_id,
              'company',
              entityMigration.entity_v2_nanoid as CompanyNanoid,
            )
          },
        )
      })

    if (type === 'team') {
      await step.run('migrate-fund-accounts', async () => {
        await runMigrationStep(
          db,
          'migrate-fund-accounts',
          'user',
          ownerMigration.entity_v1_id,
          async () => {
            await db
              .transaction()
              .execute((trx) =>
                migratePayerCompanyAccounts(
                  company_id,
                  entityMigration.entity_v2_nanoid as CompanyNanoid,
                  trx,
                ),
              )
          },
        )
      })
    }

    if (!entityMigration.migrated_completed) {
      const members = await step.run('migrate-company-members', async () => {
        return await v1StrapiCompanyMembers(
          company_id,
          type === 'company' ? ['client'] : ['client', 'contractor'],
        )
      })

      if (members.length > 0) {
        await step.sendEvent(
          'migrate-company-members',
          members.map((member) => {
            return {
              name: 'dashboard/migration.user',
              data: {
                user_id: member.user_id as number,
                company_relationship_id: member.company_id as number,
                company_relationship_type: type,
              },
            }
          }),
        )
      }

      await step.run('migrate-company-completed', async () => {
        await setEntityMigrationStep(entityMigration.entity_v1_id, type, {
          migrated_completed: true,
          migration_status: 'completed',
        })
      })

      if (teams.length > 0) {
        await step.sendEvent(
          'migrate-teams',
          teams.map((t) => ({
            name: 'dashboard/migration.company',
            data: {
              company: t,
              teams: [],
            },
          })),
        )
      }
    }
  },
)
