import type {
  OrumEventTypes,
  orumExternalAccountValidationBody,
  orumMultiEventBody,
} from '@riseworks/contracts/src/orum_formats.js'
import { getOrumWebhookSecret } from 'repositories/src/orum.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isSignatureValidWithPublicKey } from 'utils/src/common/signature.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import type { z } from 'zod'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-orum-subscribe-all',
    name: 'dashboard/orum.subscribe_all',
  },
  [{ event: 'dashboard/orum.subscribe_all' }],
  async ({ event: { data }, logger }) => {
    logger.info('Processing Orum event type')
    const {
      created_at,
      event_type,
      headers: { Signature },
      raw,
    } = data
    assert(event_type, 'Invalid event type from orum webhook')

    if (process.env.NODE_ENV !== 'localhost') {
      const publicKey = await getOrumWebhookSecret()
      assert(
        isSignatureValidWithPublicKey({
          data: raw + created_at,
          signature: Signature,
          publicKey,
          algorithm: 'RSA-SHA256',
          signatureFormat: 'base64',
        }),
        'Invalid Orum Signature',
      )
    }

    switch (event_type as OrumEventTypes) {
      case 'external_account_closed':
      case 'external_account_created':
      case 'external_account_rejected':
      case 'external_account_restricted':
      case 'external_account_unverified':
      case 'external_account_verified':
        await client.send({
          name: 'dashboard/orum.external_account_validation',
          data: data as z.infer<typeof orumExternalAccountValidationBody>,
        })
        break
      case 'transfer_updated':
        await client.send({
          name: 'dashboard/orum.transfer_updated',
          data: data as z.infer<typeof orumMultiEventBody>,
        })
        break
      case 'verify_account_updated':
        await client.send({
          name: 'dashboard/orum.verify_account_updated',
          data: data as z.infer<typeof orumMultiEventBody>,
        })
        break
      default:
        return {
          event_type,
          state: 'unhandled',
        }
    }

    return {
      event_type,
      state: 'forwarded',
    }
  },
)
