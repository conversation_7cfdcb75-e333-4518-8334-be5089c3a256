import {
  getUsersRequiringPasskey,
  updateUserRsk,
} from 'repositories/src/usersRsk.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-users-update-has-passkey',
    name: 'dashboard/users.update_has_passkey',
  },
  [{ event: 'dashboard/users.update_has_passkey' }],
  async ({ logger }) => {
    logger.info('Starting script to update rise.users_rsk has_passkey')

    let result = await getUsersRequiringPasskey()
    let count = 0

    while (result.length) {
      count += result.length
      logger.info(`Updating ${count} rise.users_rsk records`)
      await Promise.all(
        result.map((user) =>
          updateUserRsk(user.nanoid, {
            has_passkey: true,
            updated_at: new Date(),
          }),
        ),
      )
      result = await getUsersRequiringPasskey()
    }

    logger.info(`A total ${count} were updated into rise.users_data`)

    return { executed: true }
  },
)
