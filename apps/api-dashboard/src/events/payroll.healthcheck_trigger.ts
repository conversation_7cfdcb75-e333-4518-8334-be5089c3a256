import { getTeamPayrollsToProcess } from 'repositories/src/payroll.js'
import { generateRunId } from 'repositories/src/payrollHealthcheck.js'
import { mapAsync } from 'utils/src/common/array.js'
import {
  getNextPayCycle,
  getPayCycle,
  payCycleToString,
} from 'utils/src/common/payCycles.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-team-payroll-healthcheck-trigger',
    name: 'dashboard/payroll.healthcheck_trigger',
  },
  [
    { event: 'dashboard/payroll.healthcheck_trigger' },
    { cron: '0 11 * * *' }, // every day at 11:00 UTC
  ],
  async ({ step }) => {
    // don't run in staging/prod for now
    if (process.env.NODE_ENV !== 'development') {
      return {
        skip: true,
        env: process.env.NODE_ENV,
        message: 'Not running in staging/prod',
      }
    }

    const currentPayCycle = getPayCycle()
    const nextPayCycle = getNextPayCycle(currentPayCycle)
    const payCyclesToCheck = [currentPayCycle, nextPayCycle]

    const runId = await step.run('generate-run-id', generateRunId)

    for (const payCycle of payCyclesToCheck) {
      const payrolls = await step.run(
        `get-team-payrolls:${payCycleToString(payCycle)}`,
        () => getTeamPayrollsToProcess(payCycle),
      )

      await step.run(
        `trigger-healthcheck-events:${payCycleToString(payCycle)}`,
        async () => {
          const events = await mapAsync(
            payrolls,
            ({ nanoid, payroll_program }) => {
              return client.send({
                name: 'dashboard/payroll.healthcheck_team',
                data: {
                  team_nanoid: nanoid,
                  payroll_program,
                  pay_cycle: payCycle,
                  run_id: runId,
                },
              })
            },
            10,
          )

          return {
            pay_cycle: payCycle,
            event_ids: events.flatMap((event) => event.ids),
          }
        },
      )
    }

    await client.send({
      name: 'dashboard/payroll.healthcheck_report',
      data: {
        run_id: runId,
        pay_cycles: payCyclesToCheck,
      },
      // run the report after 15 minutes
      ts: Date.now() + 15 * 60 * 1000,
    })

    return {
      run_id: runId,
      pay_cycles: payCyclesToCheck,
    }
  },
)
