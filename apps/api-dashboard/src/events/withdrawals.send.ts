import { db } from 'db'
import { getUserByNanoid } from 'repositories/src/users.js'
import {
  getWithdrawAccountById,
  getWithdrawAccountPrivateBankDataById,
} from 'repositories/src/withdrawAccounts.js'
import { createOrumTransfer } from 'repositories/src/withdrawOrum.js'
import { createRoutefusionTransfer } from 'repositories/src/withdrawRoutefusion.js'
import {
  getProviderWithdraw,
  getWithdrawById,
} from 'repositories/src/withdrawals.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import BN from 'bignumber.js'

const client = createInngestClient('dashboard')

export default client.createFunction(
  {
    id: 'dashboard-withdrawals:send',
    name: 'dashboard/withdrawals.send',
  },
  [{ event: 'dashboard/withdrawals.send' }],
  async ({
    logger,
    step,
    event: {
      data: { withdraw_id },
    },
  }) => {
    const withdraw = await step.run('get-withdraw-and-validate', async () => {
      return await getWithdrawById(withdraw_id)
    })
    assert(withdraw, `Withdraw not found with id ${withdraw_id}`, '404')

    if (withdraw.status === 'fiat_sent') {
      return {
        success: false,
        reason: 'Withdraw status is fiat_sent',
      }
    }

    assert(
      withdraw.status === 'blockchain_completed',
      `Invalid status with id ${withdraw_id}: ${withdraw.status}`,
      '400',
    )

    const allowedRamps = [
      'domestic_usd',
      'international_usd',
      'international_exchange',
    ]
    const withdrawAccount = await step.run('get-withdraw-account', async () => {
      return await getWithdrawAccountById(withdraw.account_nanoid)
    })
    assert(
      withdrawAccount,
      `Withdraw account not found with account_nanoid ${withdraw.account_nanoid}`,
      '404',
    )
    assert(
      ['active', 'default'].includes(withdrawAccount.status),
      `Withdraw account not active for nanoid ${withdraw.nanoid}: ${withdrawAccount.status}`,
    )
    assert(
      allowedRamps.includes(withdrawAccount.ramp),
      `Invalid account ramp for nanoid ${withdraw.nanoid}`,
    )

    const bankData = await step.run(
      'get-withdraw-account-bank-data',
      async () => {
        return await getWithdrawAccountPrivateBankDataById(
          withdrawAccount.nanoid,
        )
      },
    )
    assert(
      bankData,
      `Withdraw account bank data not found for nanoid ${withdrawAccount.nanoid}`,
      '404',
    )

    const provider = await step.run('get-withdraw-provider-data', async () => {
      return await getProviderWithdraw(withdraw.nanoid)
    })
    assert(
      provider,
      `Withdraw provider not found for nanoid ${withdraw.nanoid}`,
      '404',
    )
    assert(
      provider.name === bankData.provider_name,
      `Withdraw provider not found for nanoid ${withdraw.nanoid}`,
      '404',
    )

    assert(
      ['Orum', 'Routefusion'].includes(bankData.provider_name),
      `Invalid provider ${bankData.provider_name}`,
      '404',
    )

    await step.run('create-transfer', async () => {
      await db.transaction().execute(async (trx) => {
        if (bankData.provider_name === 'Orum') {
          await createOrumTransfer(withdraw_id, trx)
        } else if (bankData.provider_name === 'Routefusion') {
          await createRoutefusionTransfer(withdraw_id, trx)
        }
      })
    })

    const user = await step.run('get-user', async () => {
      return await getUserByNanoid(withdrawAccount.user_nanoid)
    })

    if (user) {
      const amountFormatted = Number(
        BN(withdraw.destination_amount_cents).div(100).toFixed(2),
      ).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
      await step.sendEvent('withdraw-success-klaviyo-event', {
        name: 'klaviyo/create.event',
        data: {
          name: 'WITHDRAW_SUCCESS',
          properties: {
            amount: amountFormatted,
            withdraw_account_type: withdrawAccount.type,
            withdraw_account_name: withdrawAccount.account_name,
            user_nanoid: user.nanoid,
          },
          email: user.email,
        },
      })
    } else {
      logger.error(`User not found for nanoid: ${withdrawAccount.user_nanoid}`)
    }
  },
)
