import type {
  TeamNanoid,
  TeamPayrollRiseAccount,
} from '@riseworks/contracts/src/brands.js'
import type {
  SelectableEmployeePayrollSettings,
  SelectableEmployeeTaxQuestionnaires,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type {
  PayCycle,
  RisePayrollProgram,
} from '@riseworks/contracts/src/formats.js'
import type { PayrollProcessData } from '@riseworks/contracts/src/payrollFormats.js'
import dayjs from 'dayjs'
import { getOrCreateBucketAccount } from 'repositories/src/bucketAccounts.js'
import { defaultSignerFromTeam } from 'repositories/src/companies.js'
import {
  employeePayrollGroupId,
  getTeamPayrollProgram,
  teamPayrollSubscriptionFeesPaymentId,
} from 'repositories/src/payroll.js'
import { EmployeePayrollCalc } from 'repositories/src/payrollExecutions.js'
import {
  createOrUpdatePayrollPayments,
  getContract,
} from 'repositories/src/smartContracts.js'
import { PAYROLL_SUBSCRIPTION_FEE } from 'utils/src/blockchain/paymentIdentifiers.js'
import assert from 'utils/src/common/assertHTTP.js'
import { applyPercentageDiscount } from 'utils/src/common/math.js'
import {
  getPayCycleDates,
  getPayCycleStartEnd,
} from 'utils/src/common/payCycles.js'
import { processCashRequirements } from './processCashRequirements.js'

type Context = Awaited<ReturnType<typeof prepareContext>>

export const prepareContext = async (
  teamNanoid: TeamNanoid,
  payrollProgram: RisePayrollProgram,
  payCycle: PayCycle,
) => {
  const teamPayrollSettings = await getTeamPayrollProgram(
    teamNanoid,
    payrollProgram,
  )
  assert(teamPayrollSettings, 'Team payroll settings not found')

  const teamPayrollRiseAccount = teamPayrollSettings.rise_account
  const periodDates = getPayCycleDates(payCycle)

  const subscriptionFee = await getSubscriptionFee(teamPayrollRiseAccount)

  return {
    teamNanoid,
    payrollProgram,
    payCycle,
    teamPayrollRiseAccount,
    periodDates,
    subscriptionFee,
  }
}

export const getSubscriptionFee = async (
  teamPayrollRiseAccount: TeamPayrollRiseAccount,
) => {
  const subscriptions = await getContract('RiseAccountSubscriptionUsage')
  const config = await subscriptions.getAccountSubscriptionConfig(
    dayjs().unix(),
    teamPayrollRiseAccount,
  )

  const discountPercent = Number(config.percentDiscount) / 1e2
  const rate = Number(config.rate) / 1e6

  return applyPercentageDiscount(rate, discountPercent)
}

export const calculateEmployeePayroll = async (
  employeePayroll: Omit<
    SelectableEmployeePayrollSettings,
    | 'created_at'
    | 'effective_start'
    | 'effective_end'
    | 'start_date'
    | 'end_date'
  > & {
    created_at: string
    effective_start: string
    effective_end: string | null
    start_date: string
    end_date: string | null
    country_code: string
    dependents_count: number
    questionnaire_values: SelectableEmployeeTaxQuestionnaires['questionnaire_values']
    one_off_bonuses: {
      id: number
      amount: number
      type: string
      pay_at: string
    }[]
  },
  context: Context,
) => {
  const employeePayrollSettings = {
    ...employeePayroll,
    created_at: new Date(employeePayroll.created_at),
    effective_start: new Date(employeePayroll.effective_start),
    start_date: new Date(employeePayroll.start_date),
    effective_end: employeePayroll.effective_end
      ? new Date(employeePayroll.effective_end)
      : null,
    end_date: employeePayroll.end_date
      ? new Date(employeePayroll.end_date)
      : null,
  }

  const employeeQuestionnaire = {
    id: employeePayroll.id,
    nanoid: employeePayroll.user_nanoid,
    created_at: new Date(employeePayroll.created_at),
    team_nanoid: context.teamNanoid,
    country_code: employeePayroll.country_code,
    dependents_count: employeePayroll.dependents_count,
    questionnaire_values: employeePayroll.questionnaire_values,
  }

  const employeePayrollCalc = new EmployeePayrollCalc(
    employeePayroll.user_nanoid,
    context.teamNanoid,
    context.payrollProgram,
    context.payCycle,
    employeePayrollSettings,
    employeeQuestionnaire,
    employeePayroll.one_off_bonuses,
  )

  return await employeePayrollCalc.processPayroll()
}

export const buildTeamEmployeePayrollEvents = (calcs: PayrollProcessData[]) => {
  return calcs.flatMap((calc) => {
    // biome-ignore lint/suspicious/noEvolvingTypes: Due to the dynamic nature of the event payload, it is not feasible to statically type the event object
    const events = []
    if (calc.payments.taxes.total > 0) {
      events.push({
        name: 'dashboard/payroll.process_employee_taxes',
        data: calc,
      } as const)
    }
    if (calc.payments.healthcare.total > 0) {
      events.push({
        name: 'dashboard/payroll.process_employee_healthcare',
        data: calc,
      } as const)
    }
    if (calc.payments.retirement.total > 0) {
      events.push({
        name: 'dashboard/payroll.process_employee_retirement',
        data: calc,
      } as const)
    }
    events.push({
      name: 'dashboard/payroll.process_employee_net_pay',
      data: calc,
    } as const)
    return events
  })
}

export const handleCashRequirements = async (
  calcs: PayrollProcessData[],
  context: Context,
) => {
  const { subscriptionFee, teamNanoid, payCycle } = context

  return await processCashRequirements(
    calcs,
    subscriptionFee,
    teamNanoid,
    context.teamPayrollRiseAccount,
    payCycle,
  )
}

export const handleSubscriptionPayment = async (
  context: Context,
  employeeCount: number,
) => {
  const payload = await buildSubscriptionPaymentPayload(context, employeeCount)
  await createOrUpdatePayrollPayments({
    sender: payload.teamPayrollRiseAccount,
    payments: [payload.payment],
  })
  return payload.payment.amount
}
const buildSubscriptionPaymentPayload = async (
  context: Context,
  employeeCount: number,
) => {
  const payDate = new Date(context.periodDates.pay_date)
  const id = teamPayrollSubscriptionFeesPaymentId(
    context.teamNanoid,
    context.payCycle,
  )
  const groupId = employeePayrollGroupId(context.teamNanoid, context.payCycle)
  const payAtTime = dayjs(payDate).unix()
  const totalAmount = context.subscriptionFee * employeeCount
  const subscriptionAccount = await getOrCreateBucketAccount({
    id: `${context.payrollProgram}-subscription-fees`,
    name: 'Subscription Bucket Account',
  })
  return {
    teamPayrollRiseAccount: context.teamPayrollRiseAccount,
    payment: {
      id,
      groupId,
      payAtTime,
      recipient: subscriptionAccount.account,
      payType: PAYROLL_SUBSCRIPTION_FEE,
      amount: totalAmount,
    },
  }
}

export const buildPayCycleCompleteEvent = async (
  context: Context,
  cashRequirementsAmount: number,
  employeePayrollsCount: number,
) => {
  const defaultSigner = await defaultSignerFromTeam(context.teamNanoid)
  const { start, end } = getPayCycleStartEnd(context.payCycle)
  return {
    name: 'klaviyo/create.event',
    data: {
      name: 'PAY_CYCLE_COMPLETE',
      properties: {
        total_payroll_amount: cashRequirementsAmount.toFixed(2),
        pay_cycle: `${start}-${end}`,
        pay_date: new Date(context.periodDates.pay_date),
        headcount: employeePayrollsCount,
      },
      email: defaultSigner.email,
    },
  } as const
}
