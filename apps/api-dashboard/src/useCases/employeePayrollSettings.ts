import {
  type EmployeePayrollRiseAccount,
  teamRoleNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableEmployeePayrollSettings } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import dayjs from 'dayjs'
import type { DBTransaction } from 'db/src/index.js'
import { insertW4formActionItem } from 'repositories/src/action_items.js'
import {
  checkEmployeePayrollRun,
  createEmployeePayrollSettings as createEmployeePayrollSettingsRepository,
  getEmployeePayrollSettings as getEmployeePayrollSettingsRepository,
  updateEmployeePayrollSettings as updateEmployeePayrollSettingsRepository,
} from 'repositories/src/employeePayrollSettings.js'
import {
  getRelationshipWithNanoid,
  issueContractorOrEmployeeAddressesIfNeeded,
  setTeamRoleType,
} from 'repositories/src/riseEntities.js'
import {
  createRiseId,
  getRiseAddress,
} from 'repositories/src/smartContracts.js'
import { getUserByNanoid } from 'repositories/src/users.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import {
  getPayCycleDates,
  getPayCycleToTakeEffect,
  getPreviousPayCycle,
} from 'utils/src/common/payCycles.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { run } from 'utils/src/common/run.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

import {
  type CreateInitialBonusesParams,
  type OneOffBonusInput,
  type UpdateOneOffBonusesParams,
  createInitialOneOffBonuses,
  updatePayrollOneOffBonuses,
} from './employeeOneOffBonus.js'
import {
  type RetirementSettingsInput,
  createRetirementSettingsForEmployee,
  updateEmployeeRetirementSettings,
} from './retirementPayrollSettings.js'

export const createEmployeePayrollSettings = async (
  data: Pick<
    InsertableEmployeePayrollSettings,
    | 'user_nanoid'
    | 'team_nanoid'
    | 'employment_type'
    | 'work_hours_per_week'
    | 'job_title'
    | 'annual_base_salary'
    | 'currency'
    | 'job_scope'
    | 'signing_bonus_amount'
    | 'start_date'
    | 'variable_compensations'
    | 'stipends'
    | 'time_off'
    | 'notice_period_days'
    | 'restricted_period_days'
    | 'dispute_resolution_method'
    | 'created_by'
    | 'payroll_program'
  >,
  db: DBTransaction,
  oneOffBonusesData: OneOffBonusInput[] = [],
  retirementSettings: RetirementSettingsInput[] = [],
) => {
  using _ = getContext()

  const { user_nanoid, team_nanoid, created_by } = data

  const user = await getUserByNanoid(user_nanoid, db)
  assert(user, `User ${user_nanoid} not found`, '404')

  const relationship = await getRelationshipWithNanoid(
    team_nanoid,
    user_nanoid,
    db,
  )
  if (
    relationship?.type === 'contractor' &&
    is(relationship.nanoid, teamRoleNanoid)
  ) {
    await setTeamRoleType(relationship.nanoid, 'team_employee', db)
    await issueContractorOrEmployeeAddressesIfNeeded(
      user_nanoid,
      team_nanoid,
      'arbitrum',
      db,
    )
  }

  const today = dayjs()
  const startDate = dayjs(data.start_date)
  assert(startDate.isSameOrAfter(today), 'Start date is in the past')

  // create rise account
  const riseAccount = await run(async () => {
    const riseAddress = await getRiseAddress('rise_riseid_deposit', db)
    assert(riseAddress, 'No rise deposit owner found', '404')

    const address = (await createRiseId({
      type: 'rise_account',
      owner_address: riseAddress.address,
      parent_account: null,
      network: 'arbitrum',
      purpose: 'employee_payroll',
      owner_nanoid: team_nanoid,
      db,
    })) as unknown as EmployeePayrollRiseAccount

    const client = createInngestClient('dashboard')
    await client.send({
      name: 'dashboard/riseid.activate',
      data: {
        riseid: address,
        network: 'arbitrum',
      },
    })

    return address
  })

  await insertW4formActionItem(user_nanoid, team_nanoid, db)

  const effective_start = startDate.toDate()
  await createEmployeePayrollSettingsRepository({
    data: {
      user_nanoid,
      team_nanoid,
      count: 0,

      payroll_program: data.payroll_program,
      employment_type: data.employment_type,
      work_hours_per_week: data.work_hours_per_week,
      job_title: data.job_title,
      annual_base_salary: data.annual_base_salary,
      job_scope: data.job_scope,
      signing_bonus_amount: data.signing_bonus_amount,
      start_date: data.start_date,
      variable_compensations: data.variable_compensations,
      stipends: data.stipends,
      time_off: data.time_off,

      status: 'inactive', // inactive until w4 form is completed
      rise_account: riseAccount,
      created_by: created_by,
      effective_start,
      currency: 'USD',
      country_code: 'US',
    },
    db,
  })

  const initialBonusesParams: CreateInitialBonusesParams = {
    oneOffBonusesData,
    user_nanoid,
    team_nanoid,
    created_by,
  }
  await Promise.all([
    createInitialOneOffBonuses(initialBonusesParams, db),
    createRetirementSettingsForEmployee({
      userNanoid: user_nanoid,
      teamNanoid: team_nanoid,
      retirementSettings,
      effective_start,
      db,
    }),
  ])
  await checkEmployeePayrollRun(user_nanoid, team_nanoid, db)
}

export const updateEmployeePayrollSettings = async (
  data: Pick<
    InsertableEmployeePayrollSettings,
    | 'user_nanoid'
    | 'team_nanoid'
    | 'employment_type'
    | 'work_hours_per_week'
    | 'job_title'
    | 'annual_base_salary'
    | 'job_scope'
    | 'variable_compensations'
    | 'stipends'
    | 'notice_period_days'
    | 'restricted_period_days'
    | 'dispute_resolution_method'
    | 'created_by'
  >,
  db: DBTransaction,
  oneOffBonusesData: OneOffBonusInput[] = [],
  retirementSettings: RetirementSettingsInput[] = [],
) => {
  using _ = getContext()

  const { user_nanoid, team_nanoid, created_by } = data

  const user = await getUserByNanoid(user_nanoid, db)
  assert(user, `User ${user_nanoid} not found`, '404')

  const payCycle = getPayCycleToTakeEffect()
  const previousPayCycle = getPreviousPayCycle(payCycle)

  const { end: previousCycleEnd } = getPayCycleDates(previousPayCycle)
  const { start: nextCycleStart } = getPayCycleDates(payCycle)
  const effective_end = new Date(previousCycleEnd)
  const effective_start = new Date(nextCycleStart)

  const employeePayrollSettings = await getEmployeePayrollSettingsRepository({
    user_nanoid,
    team_nanoid,
    db,
  })
  assert(employeePayrollSettings, 'Employee payroll settings not found', '404')
  await updateEmployeePayrollSettingsRepository({
    user_nanoid,
    team_nanoid,
    data: { effective_end },
    db,
  })

  await createEmployeePayrollSettingsRepository({
    data: {
      user_nanoid,
      team_nanoid,
      count: employeePayrollSettings.count + 1,

      // cannot change
      rise_account: employeePayrollSettings.rise_account,
      currency: employeePayrollSettings.currency,
      signing_bonus_amount: employeePayrollSettings.signing_bonus_amount,
      start_date: employeePayrollSettings.start_date,
      time_off: employeePayrollSettings.time_off,

      // possible changes
      employment_type: data.employment_type,
      work_hours_per_week: data.work_hours_per_week,
      job_title: data.job_title,
      annual_base_salary: String(data.annual_base_salary),
      job_scope: data.job_scope,
      variable_compensations: data.variable_compensations,
      stipends: data.stipends,
      notice_period_days: data.notice_period_days,
      restricted_period_days: data.restricted_period_days,
      dispute_resolution_method: data.dispute_resolution_method,

      // set by system
      created_by: created_by,
      effective_start,
    },
    db,
  })
  const updateBonusParams: UpdateOneOffBonusesParams = {
    user_nanoid,
    team_nanoid,
    oneOffBonusesData,
    created_by,
  }
  await Promise.all([
    updatePayrollOneOffBonuses(updateBonusParams, db),
    updateEmployeeRetirementSettings(
      retirementSettings,
      user_nanoid,
      team_nanoid,
      effective_end,
      effective_start,
      db,
    ),
  ])
  await checkEmployeePayrollRun(user_nanoid, team_nanoid, db)
}
