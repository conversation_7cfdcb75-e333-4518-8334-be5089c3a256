import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import BN from 'bignumber.js'
import dayjs from 'dayjs'
import { generatePayCyclesForPage } from 'features/src/payroll/generatePayCycles.js'
import { getAllCashRequirementsForCycle } from 'features/src/payroll/getCashRequirements.js'
import { getTeamPayroll } from 'repositories/src/payroll.js'
import { tokenMetadata } from 'repositories/src/smartContracts.js'
import { getTeamByNanoid } from 'repositories/src/teams.js'
import { fromDecimals } from 'utils/src/blockchain/helpers.js'
import { RisePaymentStorageTypes } from 'utils/src/blockchain/paymentIdentifiers.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import {
  comparePayCycles,
  getPayCycle,
  payCycleToString,
} from 'utils/src/common/payCycles.js'

export const getCashRequirementHistory = async ({
  team_nanoid,
  page_size,
  page_offset,
  payCycle,
}: {
  team_nanoid: TeamNanoid
  page_size: number
  page_offset: number
  payCycle?: PayCycle
}) => {
  const teamData = await getTeamByNanoid(team_nanoid)
  assert(teamData, 'Team not found')

  const teamPayroll = await getTeamPayroll(team_nanoid)
  assert(teamPayroll, 'team payroll not exist')

  const baseCycle = getPayCycle(teamPayroll.created_at)
  const cyclesForProcessing: PayCycle[] = determineCyclesForProcessing(
    baseCycle,
    page_size,
    page_offset,
    payCycle,
  )

  const itemsArrays = await mapAsync(cyclesForProcessing, async (cycle) => {
    const rawPayments = await getAllCashRequirementsForCycle(
      teamData.rise_account,
      team_nanoid,
      cycle,
    )
    const pay_cycle_str = payCycleToString(cycle)
    return Promise.all(
      rawPayments.map(async (payment) => {
        if (!payment) {
          return {
            pay_cycle: pay_cycle_str,
            pay_date: '-',
            amount: '-',
            type: 'Payroll' as const,
            status: 'Pending' as const,
          }
        }

        const state = payment.storage
        const status =
          state === BigInt(RisePaymentStorageTypes.Complete)
            ? ('Completed' as const)
            : ('Pending' as const)

        const tokenData = await getTokenMetadataSafe(payment.token)
        const tokenDecimals = Number.parseInt(tokenData.decimals.toString())

        const rawAmount = payment.amount.toString()
        const formattedAmount = fromDecimals(rawAmount, tokenDecimals)
        const amountInCents = Math.round(
          Number.parseFloat(formattedAmount) * 100,
        )
        const pay_date = dayjs(Number(payment.payAtTime) * 1000).format(
          'YYYY-MM-DD HH:mm:ss',
        )
        return {
          pay_cycle: pay_cycle_str,
          pay_date,
          amount: amountInCents.toString(),
          type: 'Payroll' as const,
          status,
        }
      }),
    )
  })
  const items = itemsArrays.flat()
  const responsePageSize: number = payCycle ? 1 : page_size
  const responsePageOffset: number = payCycle ? 0 : page_offset
  return {
    pageSize: responsePageSize,
    pageOffset: responsePageOffset,
    totalItems: items.length,
    items,
  }
}

const getTokenMetadataSafe = async (token: string) => {
  try {
    return await tokenMetadata(token, 'arbitrum')
  } catch (err) {
    console.warn(`Token metadata fallback para ${token}`, err)
    return {
      token,
      symbol: 'UNKNOWN',
      decimals: BigInt(18),
      decimals_multiplier: BN(10).pow(18),
      name: 'Unknown Token',
    }
  }
}

const determineCyclesForProcessing = (
  baseCycle: PayCycle,
  page_size: number,
  page_offset: number,
  specificPayCycle?: PayCycle,
): PayCycle[] => {
  if (specificPayCycle) {
    if (comparePayCycles(specificPayCycle, baseCycle) >= 0) {
      return [specificPayCycle]
    }
    return []
  }
  const generatedPayCycles = generatePayCyclesForPage(page_size, page_offset)
  return generatedPayCycles.filter((c) => {
    return comparePayCycles(c, baseCycle) >= 0
  })
}
