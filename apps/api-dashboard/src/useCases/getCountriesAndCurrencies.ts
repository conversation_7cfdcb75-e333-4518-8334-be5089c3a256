import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { RoutefusionAccountType } from 'repositories/src/routefusion.js'
import {
  getRouteFusionResourceCorridors,
  getWithdrawAvailableCountriesAndCurrencies,
} from 'repositories/src/withdrawOptions.js'

export async function getCountriesAndCurrencies(
  entity_nanoid: UserNanoid,
  ramp: 'international_exchange' | 'international_usd',
  beneficiary_type: 'business' | 'individual',
) {
  const account_type: RoutefusionAccountType =
    ramp === 'international_exchange' ? 'forex' : 'usd'

  const [corridors, availableCountries] = await Promise.all([
    getRouteFusionResourceCorridors(account_type),
    getWithdrawAvailableCountriesAndCurrencies(
      entity_nanoid,
      ramp,
      beneficiary_type,
    ),
  ])

  // Corridors available via routefusion
  const corridorsMap = new Set(
    corridors.map(({ country, currency }) => `${country}-${currency}`),
  )

  // Filter countries by the routefusion corridors
  const filteredData = availableCountries.filter(({ iso, currency }) =>
    corridorsMap.has(`${iso}-${currency}`),
  )

  // Group the filtered countries on iso string to produce one row per
  // country with an array of currencies
  const countryMap = new Map<
    string,
    { name: string; iso: string; currencies: { symbol: string }[] }
  >()

  for (const { name, iso, currency } of filteredData) {
    if (!countryMap.has(iso)) {
      countryMap.set(iso, { name, iso, currencies: [] })
    }
    countryMap.get(iso)!.currencies.push({ symbol: currency })
  }

  return Array.from(countryMap.values())
}
