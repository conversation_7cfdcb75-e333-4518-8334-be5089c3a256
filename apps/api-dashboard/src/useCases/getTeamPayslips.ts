import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import type { PaymentsByCategory } from '@riseworks/contracts/src/formats.js'
import { generatePayCyclesForPage } from 'features/src/payroll/generatePayCycles.js'
import { getTeamPayslipsForPeriod } from 'features/src/payroll/getTeamPayslipsForPeriod.js'
import { getTeamPayroll } from 'repositories/src/payroll.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getPayCycleStartEnd } from 'utils/src/common/payCycles.js'

type GetTeamPayslipsInput = {
  teamNanoid: TeamNanoid
  pageOffset: number
  pageSize: number
}

export const getPreviousTeamPayslips = async (input: GetTeamPayslipsInput) => {
  const teamPayroll = await getTeamPayroll(input.teamNanoid)
  assert(teamPayroll, 'team payroll not exist')

  const payCycles = generatePayCyclesForPage(input.pageSize, input.pageOffset)

  const filteredPayCycles = payCycles.filter((cycle) => {
    const { start } = getPayCycleStartEnd(cycle)
    return new Date(start) >= teamPayroll.created_at
  })

  return await mapAsync(filteredPayCycles, async (pay_cycle) => {
    const teamPayslips = await getTeamPayslipsForPeriod({
      teamNanoid: input.teamNanoid,
      payCycle: pay_cycle,
    })
    const payslipList: PaymentsByCategory[] = teamPayslips.map(
      ({ payslip }) => payslip,
    )

    const payCycleDates = getPayCycleStartEnd(pay_cycle)
    return {
      period: {
        start: payCycleDates.start,
        end: payCycleDates.end,
      },
      headcount: teamPayslips.length,
      payslip: aggregatePaymentsByCategory(payslipList),
    }
  })
}

const aggregatePaymentsByCategory = (
  list: PaymentsByCategory[],
): PaymentsByCategory => {
  return list.reduce<PaymentsByCategory>(
    (acc, cur) => ({
      netpay: sumCurrencyAmount(acc.netpay, cur?.netpay),
      grosspay: sumCurrencyAmount(acc.grosspay, cur?.grosspay),
      salaries: sumCurrencyAmount(acc.salaries, cur?.salaries),
      extras: {
        signing_bonus: sumCurrencyAmount(
          acc.extras?.signing_bonus,
          cur?.extras?.signing_bonus,
        ),
        stipends: sumCurrencyAmount(
          acc.extras?.stipends,
          cur?.extras?.stipends,
        ),
        one_off_bonuses: sumCurrencyAmount(
          acc.extras?.one_off_bonuses,
          cur?.extras?.one_off_bonuses,
        ),
        variable_compensations: {
          bonus: sumCurrencyAmount(
            acc.extras?.variable_compensations?.bonus,
            cur?.extras?.variable_compensations?.bonus,
          ),
          commission: sumCurrencyAmount(
            acc.extras?.variable_compensations?.commission,
            cur?.extras?.variable_compensations?.commission,
          ),
          reimbursement: sumCurrencyAmount(
            acc.extras?.variable_compensations?.reimbursement,
            cur?.extras?.variable_compensations?.reimbursement,
          ),
        },
      },
      healthcare: {
        employee: sumCurrencyAmount(
          acc.healthcare?.employee,
          cur?.healthcare?.employee,
        ),
        employer: sumCurrencyAmount(
          acc.healthcare?.employer,
          cur?.healthcare?.employer,
        ),
      },
      retirement: {
        employee: sumCurrencyAmount(
          acc.retirement?.employee,
          cur?.retirement?.employee,
        ),
        employer: sumCurrencyAmount(
          acc.retirement?.employer,
          cur?.retirement?.employer,
        ),
      },
      taxes: {
        employee: sumTaxArray([acc.taxes?.employee, cur?.taxes?.employee]),
        employer: sumTaxArray([acc.taxes?.employer, cur?.taxes?.employer]),
      },
    }),
    {
      netpay: { amount_cents: 0, currency: 'USD' },
      grosspay: { amount_cents: 0, currency: 'USD' },
      salaries: { amount_cents: 0, currency: 'USD' },
      extras: {
        signing_bonus: { amount_cents: 0, currency: 'USD' },
        stipends: { amount_cents: 0, currency: 'USD' },
        one_off_bonuses: { amount_cents: 0, currency: 'USD' },
        variable_compensations: {
          bonus: { amount_cents: 0, currency: 'USD' },
          commission: { amount_cents: 0, currency: 'USD' },
          reimbursement: { amount_cents: 0, currency: 'USD' },
        },
      },
      healthcare: {
        employee: { amount_cents: 0, currency: 'USD' },
        employer: { amount_cents: 0, currency: 'USD' },
      },
      retirement: {
        employee: { amount_cents: 0, currency: 'USD' },
        employer: { amount_cents: 0, currency: 'USD' },
      },
      taxes: {
        employee: [],
        employer: [],
      },
    },
  )
}

type CurrencyAmount = { amount_cents: number; currency: 'USD' }
const safeAmount = (val?: CurrencyAmount | null): number =>
  val?.amount_cents ?? 0

const sumCurrencyAmount = (
  a?: CurrencyAmount | null,
  b?: CurrencyAmount | null,
): CurrencyAmount => ({
  amount_cents: safeAmount(a) + safeAmount(b),
  currency: 'USD',
})

const sumTaxArray = (arrs: PaymentsByCategory['taxes']['employee'][]) => {
  const result: Record<string, number> = {}

  for (const arr of arrs.filter(Boolean)) {
    for (const { name, amount } of arr || []) {
      result[name] = (result[name] || 0) + safeAmount(amount)
    }
  }

  return Object.entries(result).map(([name, amount_cents]) => ({
    name,
    amount: { amount_cents, currency: 'USD' as const },
  }))
}
