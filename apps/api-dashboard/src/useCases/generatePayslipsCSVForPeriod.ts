import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import { getTeamPayslipsForPeriod } from 'features/src/payroll/getTeamPayslipsForPeriod.js'

type GeneratePayslipsCSVForPeriodInput = {
  teamNanoid: TeamNanoid
  payCycle: { year: number; month: number; period: number }
}

export const generatePayslipsCSVForPeriod = async (
  input: GeneratePayslipsCSVForPeriodInput,
) => {
  const employeesPayslips = await getTeamPayslipsForPeriod({
    teamNanoid: input.teamNanoid,
    payCycle: input.payCycle,
  })

  const header = [
    'Name',
    'Email',
    'Gross Pay',
    'Employer Taxes',
    'Employee Taxes',
    'Healthcare',
    'Retirement',
    'Signing Bonus',
    'Stipends',
    'One-off Bonuses',
    'Bonus',
    'Commission',
    'Reimbursement',
    'Net Pay',
  ]

  const rows = employeesPayslips.map(({ employee, payslip }) => {
    const signingBonus = payslip.extras.signing_bonus.amount_cents
    const stipends = payslip.extras.stipends.amount_cents
    const oneOffBonuses = payslip.extras.one_off_bonuses.amount_cents
    const bonus = payslip.extras.variable_compensations.bonus.amount_cents
    const commission =
      payslip.extras.variable_compensations.commission.amount_cents
    const reimbursement =
      payslip.extras.variable_compensations.reimbursement.amount_cents
    const employeeTaxesTotal = (payslip.taxes.employee ?? []).reduce(
      (acc, t) => acc + t.amount.amount_cents,
      0,
    )
    const employerTaxesTotal = (payslip.taxes.employer ?? []).reduce(
      (acc, t) => acc + t.amount.amount_cents,
      0,
    )

    return [
      `"${employee.first_name} ${employee.last_name}"`,
      `"${employee.email}"`,
      toDollars(payslip.grosspay.amount_cents),
      toDollars(employerTaxesTotal),
      toDollars(employeeTaxesTotal),
      toDollars(payslip.healthcare.employee.amount_cents),
      toDollars(payslip.retirement.employee.amount_cents),
      toDollars(signingBonus),
      toDollars(stipends),
      toDollars(oneOffBonuses),
      toDollars(bonus),
      toDollars(commission),
      toDollars(reimbursement),
      toDollars(payslip.netpay.amount_cents),
    ]
  })

  const csvString = [header, ...rows].map((row) => row.join(',')).join('\n')
  const base64File = Buffer.from(csvString, 'utf-8').toString('base64')

  return base64File
}

const toDollars = (amountInCents: number): string => {
  return (amountInCents / 100).toFixed(2)
}
