import {
  type TeamNanoid,
  type UserNanoid,
  teamNanoid,
  userNanoid,
} from '@riseworks/contracts/src/brands.js'
import { queryAccountsPayment } from 'repositories/src/payments.js'
import { getTeamByNanoid } from 'repositories/src/teams.js'
import { getTeamsForUser } from 'repositories/src/teams.js'
import type { RisePaymentStorageType } from 'utils/src/blockchain/paymentIdentifiers.js'
import { mapAsync } from 'utils/src/common/array.js'
import { is } from 'utils/src/common/is.js'

type GetAccountsPaymentParams = {
  workspace: TeamNanoid | UserNanoid
  user_nanoid: UserNanoid
  state: RisePaymentStorageType
  start_date: Date
  end_date: Date
}

export const getAccountsPaymentForWorkspace = async (
  params: GetAccountsPaymentParams,
) => {
  const { workspace, user_nanoid, state, start_date, end_date } = params

  if (is(workspace, teamNanoid)) {
    const team = await getTeamByNanoid(workspace)
    if (!team) return []

    const { items } = await queryAccountsPayment(
      team.nanoid,
      state,
      start_date,
      end_date,
      user_nanoid,
    )
    return items
  }

  if (is(workspace, userNanoid)) {
    const teams = await getTeamsForUser(user_nanoid, ['contractor'])
    const results = await mapAsync(teams, async (team) => {
      const { items } = await queryAccountsPayment(
        team.nanoid,
        state,
        start_date,
        end_date,
        user_nanoid,
      )
      return items
    })
    return results.flat()
  }

  return []
}
