import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { SelectableEmployeePayrollSettings } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type {
  PayCycle,
  RisePayrollProgram,
} from '@riseworks/contracts/src/formats.js'
import dayjs from 'dayjs'
import { getTeamPayrollProgram } from 'repositories/src/payroll.js'
import {
  EmployeePayrollCalc,
  PayrollMode,
} from 'repositories/src/payrollExecutions.js'
import assert from 'utils/src/common/assertHTTP.js'
import { dollarsToCents } from 'utils/src/common/math.js'
import {
  getNextPayCycle,
  getPayCycle,
  getPayCycleDates,
} from 'utils/src/common/payCycles.js'
import { getSubscriptionFee } from './processTeamPayroll.js'

interface QuestionnaireValues {
  id: number
  country_code: string
  nanoid: UserNanoid
  team_nanoid: TeamNanoid
  questionnaire_values: Array<{
    value: string
    parameter_name: string
    unique_tax_id: string
    parameter_type: 'INTEGER' | 'DOLLAR' | 'PERCENTAGE' | 'BOOLEAN'
  }>
  dependents_count: number
  created_at: Date
}

const EmployeeAdditionType = {
  CANNOT_ADD: 'cannot_add',
  FREE_TO_ADD: 'free_to_add',
  ADD_WITH_WARNING: 'add_with_warning',
} as const

type EmployeeAdditionType =
  (typeof EmployeeAdditionType)[keyof typeof EmployeeAdditionType]

interface EmployeeAdditionResult {
  type: EmployeeAdditionType
  message: string
}

/**
 * Calculate the total cost of adding a new employee mid-payroll cycle
 */
export const calculateEmployeeCost = async ({
  user_nanoid,
  team_nanoid,
  payroll_program,
  employeePayrollSettings,
  oneOffBonuses,
}: {
  user_nanoid: UserNanoid
  team_nanoid: TeamNanoid
  payroll_program: RisePayrollProgram
  employeePayrollSettings: Omit<SelectableEmployeePayrollSettings, 'id'>
  oneOffBonuses: Array<{
    id: number
    amount: number
    type: string
    pay_at: string
  }>
}) => {
  // Format only the input dates to YYYY-MM-DD for direct string comparison
  const currentDate = dayjs().format('YYYY-MM-DD')
  const employeeStartDate = dayjs(employeePayrollSettings.start_date).format(
    'YYYY-MM-DD',
  )

  const currentPayCycle = getPayCycle(currentDate)
  const nextPayCycle = getNextPayCycle(currentPayCycle)

  // Get pay cycle dates for both current and next cycle
  const [currentPayCycleDates, nextPayCycleDates] = await Promise.all([
    getPayCycleDates(currentPayCycle),
    getPayCycleDates(nextPayCycle),
  ])

  // Validate if employee can be added
  const result = validateEmployeeAddition(
    currentDate,
    employeeStartDate,
    currentPayCycleDates,
    nextPayCycleDates,
  )

  if (result.type === EmployeeAdditionType.CANNOT_ADD) {
    assert(false, result.message, '400')
  }

  if (result.type === EmployeeAdditionType.FREE_TO_ADD) {
    return 0
  }

  const teamPayrollSettings = await getTeamPayrollProgram(
    team_nanoid,
    payroll_program,
  )

  assert(teamPayrollSettings, 'Team payroll settings not found', '500')

  // Get subscription fee
  const subscriptionFee = await getSubscriptionFee(
    teamPayrollSettings.rise_account,
  )
  assert(
    subscriptionFee !== undefined && subscriptionFee !== null,
    'Failed to get subscription fee',
    '500',
  )

  // While mid employee cost estimation, we don't have questionnaire values
  const questionnaireValues: QuestionnaireValues = {
    id: 0,
    country_code: 'US',
    nanoid: user_nanoid,
    team_nanoid: team_nanoid,
    questionnaire_values: [],
    dependents_count: 0,
    created_at: new Date(),
  }

  let totalCost = 0

  // Build array of period cost calculations to run in parallel
  const periodCostRequests = [
    // Always calculate current period cost
    calculatePeriodCost(
      user_nanoid,
      team_nanoid,
      payroll_program,
      currentPayCycle,
      employeePayrollSettings,
      questionnaireValues,
      oneOffBonuses,
    ),
  ]

  // Check if we need to calculate next period cost based on current date being between due date and pay date
  const shouldCalculateNextPeriodCost =
    currentDate >= nextPayCycleDates.due_date &&
    currentDate <= nextPayCycleDates.pay_date

  if (shouldCalculateNextPeriodCost) {
    periodCostRequests.push(
      calculatePeriodCost(
        user_nanoid,
        team_nanoid,
        payroll_program,
        nextPayCycle,
        employeePayrollSettings,
        questionnaireValues,
        oneOffBonuses,
      ),
    )
  }

  // Calculate all period costs in parallel
  const periodCosts = await Promise.all(periodCostRequests)

  // Add costs to total if they are greater than 0
  for (const cost of periodCosts) {
    if (cost > 0) {
      totalCost += cost + subscriptionFee
    }
  }

  return dollarsToCents(totalCost)
}

const validateEmployeeAddition = (
  currentDate: string,
  employeeStartDate: string,
  currentPayCycleDates: ReturnType<typeof getPayCycleDates>,
  nextPayCycleDates: ReturnType<typeof getPayCycleDates>,
): EmployeeAdditionResult => {
  const currentCycle = getPayCycle(currentDate)

  // cannot add an employee with a start date before the current cycle
  if (employeeStartDate < currentPayCycleDates.start) {
    return {
      type: EmployeeAdditionType.CANNOT_ADD,
      message: 'Cannot add employee with start date before current cycle',
    }
  }

  const employeesFirstCycle = getPayCycle(employeeStartDate)
  const employeesFirstCycleDates = getPayCycleDates(employeesFirstCycle)

  // if the current date is before the LDMC (last date to make changes), we can add the employee without warning
  if (currentDate < employeesFirstCycleDates.due_date) {
    return {
      type: EmployeeAdditionType.FREE_TO_ADD,
      message: 'Employee can be added without additional cost',
    }
  }

  // the current date is after the LDMC, so we have to check if it's a month shift
  if (
    currentCycle.period === 2 &&
    employeesFirstCycle.period === 2 &&
    currentDate >= nextPayCycleDates.due_date
  ) {
    return {
      type: EmployeeAdditionType.CANNOT_ADD,
      message: 'Cannot add employee during month shift period',
    }
  }

  return {
    type: EmployeeAdditionType.ADD_WITH_WARNING,
    message: 'Employee can be added with additional cost',
  }
}

const calculatePeriodCost = async (
  user_nanoid: UserNanoid,
  team_nanoid: TeamNanoid,
  payroll_program: RisePayrollProgram,
  payCycle: PayCycle,
  employeePayrollSettings: Omit<SelectableEmployeePayrollSettings, 'id'>,
  questionnaireValues: QuestionnaireValues,
  oneOffBonuses: Array<{
    id: number
    amount: number
    type: string
    pay_at: string
  }>,
): Promise<number> => {
  const employeePayrollCalc = new EmployeePayrollCalc(
    user_nanoid,
    team_nanoid,
    payroll_program,
    payCycle,
    employeePayrollSettings,
    questionnaireValues,
    oneOffBonuses,
    PayrollMode.ESTIMATE,
  )

  const employeePayrollData = await employeePayrollCalc.processPayroll()
  assert(employeePayrollData?.payments, 'Failed to process payroll', '500')

  const {
    gross_wages = 0,
    taxes = { employer_total: 0 },
    healthcare = { employer_total: 0 },
    retirement = { employer_total: 0 },
    extras = { total: 0 },
  } = employeePayrollData.payments

  return (
    gross_wages +
    taxes.employer_total +
    healthcare.employer_total +
    retirement.employer_total +
    extras.total
  )
}
