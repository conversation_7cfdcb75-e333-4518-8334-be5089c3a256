import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { DBTransaction } from 'db'
import {
  finalizeRetirementSettingById,
  getCurrentRetirementSettings,
  getLastRetirementSettingCount,
  insertRetirementPayrollSetting,
} from 'repositories/src/retirementPayrollSettings.js'

export type RetirementSettingsInput = {
  retirement_type: 'loan_repayment' | 'roth_401k' | 'traditional_401k'
  ee_contribution_type?: 'percentage' | 'fixed'
  ee_contribution_value?: string
  er_match_contribution_type?: 'percentage' | 'fixed'
  er_match_contribution_value?: string
  er_grant_contribution_type?: 'percentage' | 'fixed'
  er_grant_contribution_value?: string
  repayment_amount?: string | null
}

export const getRetirementSettingsForEmployee = async (
  userNanoid: UserNanoid,
  teamNanoid: TeamNanoid,
) => {
  return await getCurrentRetirementSettings({
    userNanoid,
    teamNanoid,
  })
}

export const createRetirementSettingsForEmployee = async ({
  userNanoid,
  teamNanoid,
  retirementSettings,
  effective_start,
  db,
}: {
  userNanoid: UserNanoid
  teamNanoid: TeamNanoid
  retirementSettings: RetirementSettingsInput[]
  effective_start: Date
  db: DBTransaction
}) => {
  if (retirementSettings.length > 0) {
    for (const setting of retirementSettings) {
      await insertRetirementPayrollSetting({
        values: {
          user_nanoid: userNanoid,
          team_nanoid: teamNanoid,
          retirement_type: setting.retirement_type,
          ee_contribution_type: setting.ee_contribution_type ?? 'fixed',
          ee_contribution_value: setting.ee_contribution_value ?? '0',
          er_match_contribution_type:
            setting.er_match_contribution_type ?? 'percentage',
          er_match_contribution_value:
            setting.er_match_contribution_value ?? '0',
          er_grant_contribution_type:
            setting.er_grant_contribution_type ?? 'percentage',
          er_grant_contribution_value:
            setting.er_grant_contribution_value ?? '0',
          count: 0,
        },
        effective_start,
        db,
      })
    }
  }
}

export const updateEmployeeRetirementSettings = async (
  retirementSettings: RetirementSettingsInput[],
  userNanoid: UserNanoid,
  teamNanoid: TeamNanoid,
  effectiveEnd: Date,
  effectiveStart: Date,
  db: DBTransaction,
) => {
  const currentRetirementSetting = await getCurrentRetirementSettings({
    userNanoid,
    teamNanoid,
  })

  const existingMap = new Map(
    currentRetirementSetting.map((setting) => [
      setting.retirement_type,
      setting,
    ]),
  )

  const incomingTypes = new Set(
    retirementSettings.map((r) => r.retirement_type),
  )

  for (const setting of currentRetirementSetting) {
    if (!incomingTypes.has(setting.retirement_type)) {
      await finalizeRetirementSettingById(setting.id, effectiveEnd, db)
    }
  }

  for (const input of retirementSettings) {
    const existing = existingMap.get(input.retirement_type)

    if (!hasRetirementChanged(input, existing)) continue

    if (existing) {
      await finalizeRetirementSettingById(existing.id, effectiveEnd, db)
    }

    const lastCount = await getLastRetirementSettingCount(
      userNanoid,
      teamNanoid,
      input.retirement_type,
      db,
    )
    await insertRetirementPayrollSetting({
      values: {
        user_nanoid: userNanoid,
        team_nanoid: teamNanoid,
        retirement_type: input.retirement_type,
        ee_contribution_type: input.ee_contribution_type,
        ee_contribution_value: input.ee_contribution_value,
        er_match_contribution_type: input.er_match_contribution_type,
        er_match_contribution_value: input.er_match_contribution_value,
        er_grant_contribution_type: input.er_grant_contribution_type,
        er_grant_contribution_value: input.er_grant_contribution_value,
        count: lastCount + 1,
      },
      effective_start: effectiveStart,
      db,
    })
  }
}

const hasRetirementChanged = (
  input: RetirementSettingsInput,
  existing?: Awaited<ReturnType<typeof getCurrentRetirementSettings>>[number],
) => {
  if (!existing) return true
  return (
    input.ee_contribution_type !== existing.ee_contribution_type ||
    Number(input.ee_contribution_value) !==
      Number(existing.ee_contribution_value) ||
    input.er_match_contribution_type !== existing.er_match_contribution_type ||
    Number(input.er_match_contribution_value) !==
      Number(existing.er_match_contribution_value) ||
    input.er_grant_contribution_type !== existing.er_grant_contribution_type ||
    Number(input.er_grant_contribution_value) !==
      Number(existing.er_grant_contribution_value)
  )
}
