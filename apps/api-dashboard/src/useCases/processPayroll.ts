import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { getTeamPayrollsToProcess } from 'repositories/src/payroll.js'
import { getNextPayCycle, getPayCycle } from 'utils/src/common/payCycles.js'

export const canProcessPayroll = ({
  trigger,
  cron,
  cronIdToCheck,
  today = dayjs(),
}: {
  trigger: 'cron' | 'manual'
  cron?: string
  cronIdToCheck?: string
  today?: Dayjs
}) => {
  if (trigger === 'manual') return true

  if (cron === cronIdToCheck) {
    const isLastDay = today.diff(today.endOf('month'), 'day') === 0
    return isLastDay
  }

  return true
}

export const getPayCyclesToProcess = (payCycle?: PayCycle) => {
  if (payCycle) return [payCycle]

  const current = getPayCycle()
  const next = getNextPayCycle(current)
  return [next, getNextPayCycle(next)]
}

export const buildProcessTeamEvents = async (payCycle: PayCycle) => {
  const teamPayrolls = await getTeamPayrollsToProcess(payCycle)

  return teamPayrolls.map(
    (t) =>
      ({
        name: 'dashboard/payroll.process_team',
        data: {
          team_nanoid: t.nanoid,
          payroll_program: t.payroll_program,
          pay_cycle: payCycle,
        },
      }) as const,
  )
}
