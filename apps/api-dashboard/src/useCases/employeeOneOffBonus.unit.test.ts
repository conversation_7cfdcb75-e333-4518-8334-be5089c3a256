import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import dayjs from 'dayjs'
import type { DBTransaction } from 'db'
import * as oneOffBonusRepo from 'repositories/src/employeePayrollSettings.js'
import * as payCycleUtils from 'utils/src/common/payCycles.js'
import {
  emptyBonusesInput,
  invalidAmountInput,
  invalidDateInput,
  mockCurrentPayCycle,
  mockInsertResultForCreate,
  mockTeamAdminNanoid,
  mockTeamNanoid,
  mockUserNanoid,
  newBonus,
  notExistingBonus,
  todayForTest,
} from '../__fixtures__/employeeOneOffBonus.fixture.ts/common.fixture.js'
import { createNewBonusInput } from '../__fixtures__/employeeOneOffBonus.fixture.ts/createEmployee.fixture.js'
import {
  existingBonus1,
  existingBonus2,
  existingBonuses,
  modifiedExistingBonus,
  updateCreateBonusInput,
  updateMixedBonusInput,
  updateWithoutChangesInput,
} from '../__fixtures__/employeeOneOffBonus.fixture.ts/updateEmployee.fixture.js'
import {
  createInitialOneOffBonuses,
  updatePayrollOneOffBonuses,
} from './employeeOneOffBonus.js'
beforeEach(() => {
  vi.resetAllMocks()
  vi.setSystemTime(todayForTest.toDate())

  vi.spyOn(payCycleUtils, 'payCycleToString').mockImplementation(
    (pc: PayCycle) => `${pc.year}-${pc.month}-${pc.period}`,
  )

  vi.spyOn(payCycleUtils, 'getPayCycle').mockImplementation(
    (date?: Date | string): PayCycle => {
      if (!date) {
        return mockCurrentPayCycle
      }
      const d = dayjs(date)
      return {
        year: d.year(),
        month: d.month() + 1,
        period: d.date() <= 15 ? 1 : 2,
      }
    },
  )
  vi.spyOn(payCycleUtils, 'getPayCycleDates').mockImplementation(
    (
      pc: PayCycle,
    ): {
      pay_date: string
      due_date: string
      last_date_to_make_changes: string
      start: string
      end: string
      days: string[]
    } => {
      const year = pc.year
      const monthIndex = pc.month - 1
      const startDayjs =
        pc.period === 1
          ? dayjs(todayForTest)
              .year(year)
              .month(monthIndex)
              .date(1)
              .startOf('day')
          : dayjs(todayForTest)
              .year(year)
              .month(monthIndex)
              .date(16)
              .startOf('day')
      const endDayjs =
        pc.period === 1
          ? dayjs(todayForTest)
              .year(year)
              .month(monthIndex)
              .date(15)
              .endOf('day')
          : dayjs(todayForTest)
              .year(year)
              .month(monthIndex)
              .endOf('month')
              .endOf('day')
      const mockPayDate = endDayjs.add(5, 'days').format('YYYY-MM-DD')
      const mockDueDate = startDayjs.subtract(2, 'days').format('YYYY-MM-DD')
      const lastDateToMakeChanges = endDayjs
        .subtract(4, 'day')
        .format('YYYY-MM-DD')

      const mockDays: string[] = []
      let currentDate = startDayjs.clone()
      while (currentDate.isSameOrBefore(endDayjs, 'day')) {
        mockDays.push(currentDate.format('YYYY-MM-DD'))
        currentDate = currentDate.add(1, 'day')
      }

      return {
        start: startDayjs.format('YYYY-MM-DD'),
        end: endDayjs.format('YYYY-MM-DD'),
        last_date_to_make_changes: lastDateToMakeChanges,
        pay_date: mockPayDate,
        due_date: mockDueDate,
        days: mockDays,
      }
    },
  )
})

afterEach(() => {
  vi.useRealTimers()
})

const mockDb = {} as DBTransaction

describe('Update employee one off bonuses', () => {
  test('should delete all existing bonuses if input is empty', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValueOnce(existingBonuses)

    const deleteSpy = vi
      .spyOn(oneOffBonusRepo, 'deleteOneOffBonusById')
      .mockResolvedValue(true)

    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')
    await updatePayrollOneOffBonuses(
      {
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        oneOffBonusesData: emptyBonusesInput,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )

    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        pay_cycle: mockCurrentPayCycle,
      }),
    )

    expect(deleteSpy).toHaveBeenCalledTimes(existingBonuses.length)
    expect(deleteSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        id: existingBonus1.id,
        last_modified_by: mockTeamAdminNanoid,
      }),
      expect.anything(),
    )

    expect(deleteSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        id: existingBonus2.id,
        last_modified_by: mockTeamAdminNanoid,
      }),
      expect.anything(),
    )
    expect(createSpy).not.toHaveBeenCalled()
  })

  test('should create new bonuses if none exist and input has bonus', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValueOnce(notExistingBonus)

    const createSpy = vi
      .spyOn(oneOffBonusRepo, 'createOneOffBonus')
      .mockResolvedValue(mockInsertResultForCreate)

    const deleteSpy = vi.spyOn(oneOffBonusRepo, 'deleteOneOffBonusById')

    const newBonusInput = updateCreateBonusInput[0]

    await updatePayrollOneOffBonuses(
      {
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        oneOffBonusesData: updateCreateBonusInput,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )

    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        pay_cycle: mockCurrentPayCycle,
      }),
    )
    expect(deleteSpy).not.toHaveBeenCalled()
    expect(createSpy).toHaveBeenCalledTimes(1)
    expect(createSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        type: newBonusInput?.type,
        amount: Number.parseFloat(newBonusInput!.amount!),
        pay_at: dayjs(newBonusInput?.pay_at).startOf('day').toDate(),
        created_by: mockTeamAdminNanoid,
        last_modified_by: mockTeamAdminNanoid,
      }),
      mockDb,
    )
  })

  test('should correctly sync mixed changes', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValueOnce(existingBonuses)

    const deleteSpy = vi
      .spyOn(oneOffBonusRepo, 'deleteOneOffBonusById')
      .mockResolvedValue(true)

    const createSpy = vi
      .spyOn(oneOffBonusRepo, 'createOneOffBonus')
      .mockResolvedValue(mockInsertResultForCreate)

    await updatePayrollOneOffBonuses(
      {
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        oneOffBonusesData: updateMixedBonusInput,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )
    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        pay_cycle: mockCurrentPayCycle,
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
      }),
    )
    expect(deleteSpy).toHaveBeenCalledTimes(1)
    expect(deleteSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        id: existingBonus1.id,
        last_modified_by: mockTeamAdminNanoid,
      }),
      mockDb,
    )

    expect(createSpy).toHaveBeenCalledTimes(2)
    expect(createSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: modifiedExistingBonus.type,
        amount: Number.parseFloat(modifiedExistingBonus.amount),
        pay_at: dayjs(modifiedExistingBonus.pay_at).startOf('day').toDate(),
      }),
      mockDb,
    )

    expect(createSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: newBonus.type,
        amount: Number.parseFloat(newBonus.amount),
        pay_at: dayjs(newBonus.pay_at).startOf('day').toDate(),
      }),
      mockDb,
    )
  })

  test('should throw error if any bonus amount is invalid', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValue(existingBonuses)
    const deleteSpy = vi.spyOn(oneOffBonusRepo, 'deleteOneOffBonusById')
    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')

    const params = {
      user_nanoid: mockUserNanoid,
      team_nanoid: mockTeamNanoid,
      oneOffBonusesData: invalidAmountInput,
      created_by: mockTeamAdminNanoid,
    }

    await expect(updatePayrollOneOffBonuses(params, mockDb)).rejects.toThrow(
      'Invalid amount format for one-off bonus',
    )

    expect(getSpy).toHaveBeenCalledTimes(1)
    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        pay_cycle: mockCurrentPayCycle,
      }),
    )
    expect(deleteSpy).not.toHaveBeenCalled()
    expect(createSpy).not.toHaveBeenCalled()
  })

  test('should throw error if any bonus pay_at is in the past', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValue(existingBonuses)
    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')

    const params = {
      user_nanoid: mockUserNanoid,
      team_nanoid: mockTeamNanoid,
      oneOffBonusesData: invalidDateInput,
      created_by: mockTeamAdminNanoid,
    }

    await expect(updatePayrollOneOffBonuses(params, mockDb)).rejects.toThrow(
      `One-off bonus pay_at must be a future date (type: ${invalidDateInput[0]!.type})`,
    )

    expect(getSpy).toHaveBeenCalledTimes(1)
    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        pay_cycle: mockCurrentPayCycle,
      }),
    )
    expect(createSpy).not.toHaveBeenCalled()
  })

  test('should not perform DB writes if input and DB are aligned', async () => {
    const getSpy = vi
      .spyOn(oneOffBonusRepo, 'getOneOffBonusesStartingFromPayCycle')
      .mockResolvedValue(existingBonuses)
    const deleteSpy = vi.spyOn(oneOffBonusRepo, 'deleteOneOffBonusById')
    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')

    await updatePayrollOneOffBonuses(
      {
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        oneOffBonusesData: updateWithoutChangesInput,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )
    expect(getSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        pay_cycle: mockCurrentPayCycle,
      }),
    )
    expect(deleteSpy).not.toHaveBeenCalled()
    expect(createSpy).not.toHaveBeenCalled()
  })
})

describe('Create initial one off bonuses', () => {
  test('should return empty array if input is empty', async () => {
    const createSpy = vi
      .spyOn(oneOffBonusRepo, 'createOneOffBonus')
      .mockResolvedValue(mockInsertResultForCreate)

    const results = await createInitialOneOffBonuses(
      {
        oneOffBonusesData: emptyBonusesInput,
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )
    expect(createSpy).not.toHaveBeenCalled()
    expect(results).toEqual([])
  })
  test('should create bonus if input has bonus', async () => {
    const createSpy = vi
      .spyOn(oneOffBonusRepo, 'createOneOffBonus')
      .mockResolvedValue(mockInsertResultForCreate)

    const results = await createInitialOneOffBonuses(
      {
        oneOffBonusesData: createNewBonusInput,
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        created_by: mockTeamAdminNanoid,
      },
      mockDb,
    )
    expect(createSpy).toHaveBeenCalledTimes(createNewBonusInput.length)
    const firstBonus = createNewBonusInput[0]
    const secondBonus = createNewBonusInput[1]
    expect(createSpy).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        type: firstBonus!.type,
        amount: Number.parseFloat(firstBonus!.amount),
        pay_at: dayjs(firstBonus!.pay_at).startOf('day').toDate(),
        created_by: mockTeamAdminNanoid,
        last_modified_by: mockTeamAdminNanoid,
      }),
      mockDb,
    )

    expect(createSpy).toHaveBeenNthCalledWith(
      2,
      expect.objectContaining({
        user_nanoid: mockUserNanoid,
        team_nanoid: mockTeamNanoid,
        type: secondBonus!.type,
        amount: Number.parseFloat(secondBonus!.amount),
        pay_at: dayjs(secondBonus!.pay_at).startOf('day').toDate(),
        created_by: mockTeamAdminNanoid,
        last_modified_by: mockTeamAdminNanoid,
      }),
      mockDb,
    )

    expect(results.length).toBe(createNewBonusInput.length)
    for (const result of results) {
      expect(result).toEqual(mockInsertResultForCreate)
    }
  })

  test('should throw error if any bonus pay_at is in the past', async () => {
    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')
    const params = {
      oneOffBonusesData: invalidDateInput,
      user_nanoid: mockUserNanoid,
      team_nanoid: mockTeamNanoid,
      created_by: mockTeamAdminNanoid,
    }

    await expect(createInitialOneOffBonuses(params, mockDb)).rejects.toThrow(
      `One-off bonus pay_at must be a future date (type: ${invalidDateInput[0]!.type})`,
    )
    expect(createSpy).not.toHaveBeenCalled()
  })

  test('should throw error if any bonus amount is invalid', async () => {
    const createSpy = vi.spyOn(oneOffBonusRepo, 'createOneOffBonus')
    const params = {
      oneOffBonusesData: invalidAmountInput,
      user_nanoid: mockUserNanoid,
      team_nanoid: mockTeamNanoid,
      created_by: mockTeamAdminNanoid,
    }

    await expect(createInitialOneOffBonuses(params, mockDb)).rejects.toThrow(
      'Invalid amount format for one-off bonus',
    )
    expect(createSpy).not.toHaveBeenCalled()
  })
})
