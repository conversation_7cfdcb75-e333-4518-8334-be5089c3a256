import type {
  InternationalAccount,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db } from 'db/src/index.js'
import {
  createInternationalAccount,
  getInternationalAccountRequirements,
} from 'repositories/src/withdrawRoutefusion.js'
import assert from 'utils/src/common/assertHTTP.js'

export const verifyAndCreateInternationalAccount = async (
  account_data: InternationalAccount,
  user_nanoid: UserNanoid,
  user_country: string,
) => {
  const { is_company, account_name, currency, ...remainingAccountData } =
    account_data

  const requirements = await getInternationalAccountRequirements(
    user_nanoid,
    is_company,
    account_data.bank_country,
    currency,
    user_country,
  )
  for (const req of requirements) {
    const value =
      remainingAccountData[req.variable as keyof typeof remainingAccountData]
    assert(
      value,
      `Missing required field for nanoid ${user_nanoid}: ${req.variable}`,
      '400',
    )
    if (req.regex)
      assert(
        new RegExp(req.regex).test(value),
        `Invalid value for nanoid ${user_nanoid} for field: ${req.variable}`,
        '400',
      )
    if (req.options)
      assert(
        req.options.includes(value),
        `Invalid value for nanoid ${user_nanoid} for field: ${req.variable}`,
        '400',
      )
  }
  const ramp =
    currency === 'USD' ? 'international_usd' : 'international_exchange'

  const account = await db.transaction().execute(async (trx) => {
    return await createInternationalAccount(
      user_nanoid,
      account_name,
      is_company,
      account_data.bank_country,
      currency,
      {
        account_number: account_data.account_number,
        routing_code: account_data.routing_code,
        swift_bic: account_data.swift_bic,
        account_type: account_data.account_type,
        bank_name: account_data.bank_name,
        bank_address1: account_data.bank_address1,
        bank_city: account_data.bank_city,
        bank_state_province_region: account_data.bank_state_province_region,
        bank_postal_code: account_data.bank_postal_code,
      },
      ramp,
      trx,
    )
  })

  return { account, ramp }
}
