import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import type { ValidNetworks } from '@riseworks/contracts/src/smartContractTypes.js'
import dayjs from 'dayjs'
import { createEvent } from 'repositories/src/klaviyo.js'
import { getContract, tokenBalanceOf } from 'repositories/src/smartContracts.js'
import { getTeamAdministratorsNanoid } from 'repositories/src/teams.js'
import { getFullUserData } from 'repositories/src/users.js'
import { RisePaymentStorageTypes } from 'utils/src/blockchain/paymentIdentifiers.js'
import { mapAsync } from 'utils/src/common/array.js'

export const fetchScheduledPaymentsForNextTwoDays = async (
  account_address: string,
  network: ValidNetworks,
) => {
  const futureDate = dayjs().add(2, 'day').unix()
  const chunkSize = 50
  const allPayments: { payment_id: string; amount: number; token: string }[] =
    []
  let chunkIndex = 0
  let hasMorePayments = true
  const riseAccountGovernor = await getContract('RiseAccountGovernor', network)

  while (hasMorePayments) {
    const paymentsChunk = await riseAccountGovernor
      .paymentsByDay(
        account_address,
        RisePaymentStorageTypes.Scheduled,
        futureDate,
        chunkIndex * chunkSize,
        chunkSize,
      )
      .then((payments) =>
        payments.map((p) => ({
          payment_id: p.id,
          amount: Number(p.amount),
          token: p.token,
        })),
      )

    allPayments.push(...paymentsChunk)

    hasMorePayments = paymentsChunk.length === chunkSize
    chunkIndex++
  }

  return allPayments
}

export const groupPaymentsByToken = (
  allPayments: { payment_id: string; amount: number; token: string }[],
) =>
  allPayments.reduce(
    (acc, payment) => {
      const tokenData = acc[payment.token]

      if (tokenData) {
        tokenData.totalAmount += payment.amount
        tokenData.payments.push(payment)
      } else {
        acc[payment.token] = {
          totalAmount: payment.amount,
          payments: [payment],
        }
      }

      return acc
    },
    {} as Record<
      string,
      {
        totalAmount: number
        payments: { payment_id: string; amount: number; token: string }[]
      }
    >,
  )

export const getTeamAdminsEmails = async (team_nanoid: TeamNanoid) => {
  const adminsNanoids = await getTeamAdministratorsNanoid(team_nanoid)
  const adminsEmails = await mapAsync(adminsNanoids, async (adminNanoid) => {
    const userData = await getFullUserData(adminNanoid)
    return userData?.email
  })
  return adminsEmails.filter((email): email is string => !!email)
}

export const buildInsufficientBalanceNotificationsProperties = async (
  paymentsByToken: Record<
    string,
    {
      totalAmount: number
      payments: { payment_id: string; amount: number; token: string }[]
    }
  >,
  account_address: string,
  network: ValidNetworks,
): Promise<
  { required_balance: number; available_balance: number; token: string }[]
> => {
  const tokenEntries = Object.entries(paymentsByToken)
  const balances = await mapAsync(tokenEntries, async ([token]) =>
    tokenBalanceOf(token, account_address, network),
  )
  const insufficentBalanceNotifications = tokenEntries
    .map(([token, { totalAmount }], index) => ({
      required_balance: totalAmount,
      available_balance: Number(balances[index]),
      token,
    }))
    .filter(
      ({ available_balance, required_balance }) =>
        available_balance < required_balance,
    )

  return insufficentBalanceNotifications
}

export const notifyTeamAdminsAboutInsufficientBalance = async (
  notifications: {
    required_balance: number
    available_balance: number
    token: string
  }[],
  adminsEmails: string[],
) => {
  const failedEmails: { email: string; token: string; error: string }[] = []

  await mapAsync(notifications, async (notification) => {
    await mapAsync(adminsEmails, async (email) => {
      try {
        await createEvent({
          name: 'INSUFFICIENT_FUNDS',
          properties: {
            required_balance: notification.required_balance,
            available_balance: notification.available_balance,
            token: notification.token,
          },
          email,
        })
      } catch (error) {
        failedEmails.push({
          email,
          token: notification.token,
          error: (error as Error).message,
        })
      }
    })
  })

  return failedEmails
}
