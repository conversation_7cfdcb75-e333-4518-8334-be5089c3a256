import { beforeEach, describe, expect, vi, test } from 'vitest'
import { canProcessPayroll, getPayCyclesToProcess } from './processPayroll.js'
import dayjs from 'dayjs'
import * as payCycleUtils from 'utils/src/common/payCycles.js'
import {
  afterNextCycle,
  currentCycle,
  mockCronPayload,
  mockManualPayload,
  nextCycle,
  payCycleFixture,
} from '../__fixtures__/processPayroll/processPayroll.fixture.js'

beforeEach(() => {
  vi.resetModules()
  vi.clearAllMocks()
})

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))

describe('canProcessPayroll', () => {
  test('should return true for manual trigger', () => {
    const result = canProcessPayroll(mockManualPayload)
    expect(result).toBe(true)
  })

  test('should return true for cron when cron !== cronIdToCheck', () => {
    const result = canProcessPayroll({
      ...mockCronPayload,
      cronIdToCheck: 'different',
      today: dayjs('2024-04-15'),
    })
    expect(result).toBe(true)
  })

  test('should return true for cron on last day of the month', () => {
    const result = canProcessPayroll({
      ...mockCronPayload,
      today: dayjs('2024-04-30'),
    })
    expect(result).toBe(true)
  })

  test('should return false for cron when not last day of the month', () => {
    const result = canProcessPayroll({
      ...mockCronPayload,
      today: dayjs('2024-04-29'),
    })
    expect(result).toBe(false)
  })
})

describe('getPayCyclesToProcess', () => {
  test('should return the provided payCycle when it exists', () => {
    const result = getPayCyclesToProcess(payCycleFixture)
    expect(result).toEqual([payCycleFixture])
  })

  test('should return two next cycles when payCycle is not provided', () => {
    vi.spyOn(payCycleUtils, 'getPayCycle').mockReturnValue(currentCycle)
    vi.spyOn(payCycleUtils, 'getNextPayCycle')
      .mockImplementationOnce(() => nextCycle)
      .mockImplementationOnce(() => afterNextCycle)

    const result = getPayCyclesToProcess()
    expect(result).toEqual([nextCycle, afterNextCycle])
  })
})
