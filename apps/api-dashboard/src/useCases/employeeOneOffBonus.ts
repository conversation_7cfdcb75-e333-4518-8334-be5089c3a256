import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import dayjs from 'dayjs'
import type { DBTransaction } from 'db'
import {
  createOneOffBonus,
  deleteOneOffBonusById,
  getOneOffBonusesStartingFromPayCycle,
} from 'repositories/src/employeePayrollSettings.js'
import assert from 'utils/src/common/assertHTTP.js'
import {
  getPayCycle,
  getPayCycleDates,
  payCycleToString,
} from 'utils/src/common/payCycles.js'

export type OneOffBonusInput = {
  type: string
  amount: string
  pay_at: Date
}

export type CreateInitialBonusesParams = {
  oneOffBonusesData: OneOffBonusInput[]
  user_nanoid: UserNanoid
  team_nanoid: TeamNanoid
  created_by: UserNanoid
}

export type UpdateOneOffBonusesParams = {
  user_nanoid: UserNanoid
  team_nanoid: TeamNanoid
  oneOffBonusesData: OneOffBonusInput[]
  created_by: UserNanoid
}

type DeleteBonusesParams = {
  existingBonuses: PreviousBonuses
  bonusesMap: Map<string, { type: string; amount: string; pay_at: Date }>
  lastModifiedBy: UserNanoid
}

type CreateBonusesParams = {
  existingBonuses: PreviousBonuses
  bonusesMap: Map<string, { type: string; amount: string; pay_at: Date }>
  user_nanoid: UserNanoid
  team_nanoid: TeamNanoid
  created_by: UserNanoid
}

export type PreviousBonuses = Awaited<
  ReturnType<typeof getOneOffBonusesStartingFromPayCycle>
>
export type DeleteBonusResult = Awaited<
  ReturnType<typeof deleteOneOffBonusById>
>
export type CreateBonusResult = Awaited<ReturnType<typeof createOneOffBonus>>

export const updatePayrollOneOffBonuses = async (
  params: UpdateOneOffBonusesParams,
  db: DBTransaction,
) => {
  const { user_nanoid, team_nanoid, oneOffBonusesData, created_by } = params

  const allBonusPromises: Promise<DeleteBonusResult | CreateBonusResult>[] = []
  const firstPayCycle = getPayCycle()

  const existingBonus = await getOneOffBonusesStartingFromPayCycle({
    user_nanoid,
    team_nanoid,
    pay_cycle: firstPayCycle,
  })

  const cycleKeys = new Set<string>()
  const inputPayCycles = getUniquePayCyclesFromBonus(
    oneOffBonusesData,
    firstPayCycle,
  )
  for (const pc of inputPayCycles) {
    cycleKeys.add(payCycleToString(pc))
  }

  const existingPayCycles = getUniquePayCyclesFromBonus(
    existingBonus,
    firstPayCycle,
  )
  for (const pc of existingPayCycles) {
    cycleKeys.add(payCycleToString(pc))
  }

  for (const cycleKey of cycleKeys) {
    const bonusesForCycleFromInput = oneOffBonusesData.filter((bonus) => {
      const pc = getPayCycle(bonus.pay_at)
      return pc && payCycleToString(pc) === cycleKey
    })

    const existingBonusesForCycle = existingBonus.filter((bonus) => {
      const pc = getPayCycle(bonus.pay_at)
      return pc && payCycleToString(pc) === cycleKey
    })

    const bonusMapForCycle = prepareInputBonusesMap(bonusesForCycleFromInput)

    const deleteParams: DeleteBonusesParams = {
      existingBonuses: existingBonusesForCycle,
      bonusesMap: bonusMapForCycle,
      lastModifiedBy: created_by,
    }

    const deletePromises = deleteChangedBonuses(deleteParams, db)
    allBonusPromises.push(...deletePromises)

    if (bonusMapForCycle.size > 0) {
      const createParams: CreateBonusesParams = {
        existingBonuses: existingBonusesForCycle,
        bonusesMap: bonusMapForCycle,
        user_nanoid,
        team_nanoid,
        created_by,
      }
      const createPromises = createBonuses(createParams, db)
      allBonusPromises.push(...createPromises)
    }
  }

  if (allBonusPromises.length > 0) {
    const results = await Promise.all(allBonusPromises)
    for (const result of results) {
      if (typeof result === 'boolean') {
        assert(
          result,
          'One or more one-off bonus deletion operations failed',
          '400',
        )
      } else {
        assert(
          result,
          'One or more one-off bonus creation operations failed',
          '400',
        )
      }
    }
  }
}

export const createInitialOneOffBonuses = async (
  params: CreateInitialBonusesParams,
  db: DBTransaction,
): Promise<CreateBonusResult[]> => {
  const { oneOffBonusesData, user_nanoid, team_nanoid, created_by } = params
  const bonusCreationPromises: Promise<CreateBonusResult>[] = []

  if (!oneOffBonusesData || oneOffBonusesData.length === 0) {
    return []
  }

  for (const bonus of oneOffBonusesData) {
    const payAtDate = dayjs(bonus.pay_at)
    assert(
      payAtDate.isAfter(dayjs().startOf('day')),
      `One-off bonus pay_at must be a future date (type: ${bonus.type})`,
      '400',
    )
    const amountValue = Number.parseFloat(bonus.amount)
    assert(
      !Number.isNaN(amountValue),
      'Invalid amount format for one-off bonus',
    )
    bonusCreationPromises.push(
      createOneOffBonus(
        {
          user_nanoid,
          team_nanoid,
          type: bonus.type,
          amount: amountValue,
          pay_at: payAtDate.startOf('day').toDate(),
          last_modified_by: created_by,
          created_by: created_by,
        },
        db,
      ),
    )
  }
  const results = await Promise.all(bonusCreationPromises)
  for (const result of results) {
    assert(
      result,
      'One or more one-off bonus creation operations failed',
      '400',
    )
  }

  return results
}

const prepareInputBonusesMap = (
  oneOffBonusesData: OneOffBonusInput[],
): Map<string, { type: string; amount: string; pay_at: Date }> => {
  const map = new Map<string, { type: string; amount: string; pay_at: Date }>()

  for (const bonus of oneOffBonusesData) {
    const payAtDate = dayjs(bonus.pay_at)
    const amountValue = Number.parseFloat(bonus.amount)
    assert(
      !Number.isNaN(amountValue),
      'Invalid amount format for one-off bonus',
      '400',
    )

    const inputPayCycle = getPayCycle(payAtDate.toDate())
    assert(inputPayCycle, 'Could not determine pay cycle for bonus', '400')

    const bonusKeyString = `${bonus.type}|${payAtDate.startOf('day').toISOString()}`
    map.set(bonusKeyString, {
      type: bonus.type,
      amount: bonus.amount,
      pay_at: payAtDate.toDate(),
    })
  }
  return map
}

const getUniquePayCyclesFromBonus = (
  bonuses: { pay_at: Date }[],
  startingFromCycle: PayCycle,
): PayCycle[] => {
  const uniquePcs: PayCycle[] = []
  const seenKeys = new Set<string>()
  const startDateOfCycle = dayjs(getPayCycleDates(startingFromCycle).start)

  for (const bonus of bonuses) {
    const bonusDate = dayjs(bonus.pay_at)
    if (bonusDate.isSameOrAfter(startDateOfCycle, 'day')) {
      const pc = getPayCycle(bonusDate.toDate())
      if (pc) {
        const key = payCycleToString(pc)
        if (!seenKeys.has(key)) {
          seenKeys.add(key)
          uniquePcs.push(pc)
        }
      }
    }
  }
  return uniquePcs
}

const deleteChangedBonuses = (
  params: DeleteBonusesParams,
  db: DBTransaction,
): Promise<DeleteBonusResult>[] => {
  const { existingBonuses, bonusesMap, lastModifiedBy } = params
  const deletePromises: Promise<DeleteBonusResult>[] = []

  for (const existing of existingBonuses) {
    const existingBonusKey = `${existing.type}|${dayjs(existing.pay_at).startOf('day').toISOString()}`
    const desiredBonus = bonusesMap.get(existingBonusKey)
    if (!desiredBonus || desiredBonus.amount !== existing.amount) {
      deletePromises.push(
        deleteOneOffBonusById(
          { id: existing.id, last_modified_by: lastModifiedBy },
          db,
        ),
      )
    }
  }
  return deletePromises
}

const createBonuses = (
  params: CreateBonusesParams,
  db: DBTransaction,
): Promise<CreateBonusResult>[] => {
  const { bonusesMap, existingBonuses, user_nanoid, team_nanoid, created_by } =
    params
  const createPromises: Promise<CreateBonusResult>[] = []

  for (const [bonusKey, desiredBonus] of bonusesMap) {
    const existingMatch = existingBonuses.find((existingBonus) => {
      const existingBonusKeyCheck = `${existingBonus.type}|${dayjs(existingBonus.pay_at).startOf('day').toISOString()}`
      return (
        existingBonusKeyCheck === bonusKey &&
        existingBonus.amount === desiredBonus.amount
      )
    })

    if (!existingMatch) {
      const payAtDate = dayjs(desiredBonus.pay_at).startOf('day').toDate()
      assert(
        dayjs(payAtDate).isAfter(dayjs().startOf('day')),
        `One-off bonus pay_at must be a future date (type: ${desiredBonus.type})`,
        '400',
      )

      const amountValue = Number.parseFloat(desiredBonus.amount)
      assert(
        !Number.isNaN(amountValue),
        'Invalid amount format for one-off bonus',
        '400',
      )
      createPromises.push(
        createOneOffBonus(
          {
            user_nanoid,
            team_nanoid,
            type: desiredBonus.type,
            amount: amountValue,
            pay_at: payAtDate,
            created_by: created_by,
            last_modified_by: created_by,
          },
          db,
        ),
      )
    }
  }
  return createPromises
}
