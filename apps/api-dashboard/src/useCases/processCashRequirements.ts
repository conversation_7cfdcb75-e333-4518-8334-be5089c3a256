import type {
  TeamNanoid,
  TeamPayrollRiseAccount,
  TeamRiseAccount,
} from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import type { PayrollProcessData } from '@riseworks/contracts/src/payrollFormats.js'
import BN from 'bignumber.js'
import dayjs from 'dayjs'
import { getCompletedCashRequirementsForCycle } from 'features/src/payroll/getCashRequirements.js'
import { sumBy } from 'remeda'
import type { RiseRequests } from 'repositories/src/codegen/abi_types/RiseAccount_impl_arbitrum.js'
import { teamPayrollCashRequirementPaymentId } from 'repositories/src/payroll.js'
import {
  createOrUpdatePayrollPayments,
  tokenMetadata,
} from 'repositories/src/smartContracts.js'
import { getTeamByNanoid } from 'repositories/src/teams.js'
import { fromDecimals } from 'utils/src/blockchain/helpers.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getDueDate } from 'utils/src/common/payCycles.js'

type CashRequirementParams = {
  teamRiseAccount: TeamRiseAccount
  teamPayrollRiseAccount: TeamPayrollRiseAccount
  amount: number
  teamNanoid: TeamNanoid
  payCycle: PayCycle
}

export const processCashRequirements = async (
  calcs: PayrollProcessData[],
  subscriptionFee: number,
  teamNanoid: TeamNanoid,
  teamPayrollRiseAccount: TeamPayrollRiseAccount,
  payCycle: PayCycle,
) => {
  const teamData = await getTeamByNanoid(teamNanoid)
  assert(teamData, 'Team not found')

  const amount = calculateCashRequirementsAmount(calcs, subscriptionFee)
  const params: CashRequirementParams = {
    teamRiseAccount: teamData.rise_account,
    teamPayrollRiseAccount,
    amount,
    teamNanoid,
    payCycle,
  }
  const completedCashRequirements = await getCompletedCashRequirementsForCycle(
    teamData.rise_account,
    teamNanoid,
    payCycle,
  )

  const isMidCycle = completedCashRequirements.length > 0

  if (isMidCycle) {
    return createCashRequirementMidCycle(params, completedCashRequirements)
  }
  return createCashRequirement(params)
}

const calculateCashRequirementsAmount = (
  calcs: PayrollProcessData[],
  subscriptionFee: number,
) => {
  return sumBy(
    calcs,
    ({ payments }) =>
      subscriptionFee +
      payments.gross_wages +
      payments.taxes.employer_total +
      payments.healthcare.employer_total +
      payments.retirement.employer_total +
      payments.extras.total,
  )
}

const createCashRequirementMidCycle = async (
  params: CashRequirementParams,
  completedCashRequirements: RiseRequests.RisePaymentStructOutput[],
) => {
  const {
    teamRiseAccount,
    teamPayrollRiseAccount,
    amount,
    teamNanoid,
    payCycle,
  } = params
  let previousCashRequirementsAmountCents = 0

  for (const payment of completedCashRequirements) {
    const amountInCents = await calculateAmountInCents(payment)
    previousCashRequirementsAmountCents += amountInCents
  }

  const amount_cents = amount * 100
  const difference = (amount_cents - previousCashRequirementsAmountCents) / 100
  if (difference <= 0) {
    return { status: 'no_payment', txns: [], amount: difference }
  }
  const index = completedCashRequirements.length
  const txns = await createOrUpdatePayrollPayments({
    sender: teamRiseAccount,
    payments: [
      {
        id: teamPayrollCashRequirementPaymentId(teamNanoid, payCycle, index),
        groupId: teamPayrollCashRequirementPaymentId(teamNanoid, payCycle),
        payAtTime: dayjs(new Date()).unix(),
        recipient: teamPayrollRiseAccount,
        amount: difference,
      },
    ],
  })

  return { status: 'payment_created', txns, amount: difference }
}

const createCashRequirement = async (params: CashRequirementParams) => {
  const {
    teamRiseAccount,
    teamPayrollRiseAccount,
    amount,
    teamNanoid,
    payCycle,
  } = params

  const txns = await createOrUpdatePayrollPayments({
    sender: teamRiseAccount,
    payments: [
      {
        id: teamPayrollCashRequirementPaymentId(teamNanoid, payCycle),
        groupId: teamPayrollCashRequirementPaymentId(teamNanoid, payCycle),
        payAtTime: dayjs(getDueDate(payCycle)).unix(),
        recipient: teamPayrollRiseAccount,
        amount,
      },
    ],
  })

  return { status: 'payment_created', txns, amount }
}

const calculateAmountInCents = async (
  payment: RiseRequests.RisePaymentStructOutput,
) => {
  const token =
    payment.token !== undefined
      ? payment.token.toString()
      : payment[5].toString()
  const tokenData = await getTokenMetadataSafe(token)
  const tokenDecimals = Number.parseInt(tokenData.decimals.toString())

  const rawAmount =
    payment.amount !== undefined
      ? payment.amount.toString()
      : payment[7].toString()
  const formattedAmount = fromDecimals(rawAmount, tokenDecimals)
  return Math.round(Number.parseFloat(formattedAmount) * 100)
}

const getTokenMetadataSafe = async (token: string) => {
  try {
    return await tokenMetadata(token, 'arbitrum')
  } catch (err) {
    console.warn(`Token metadata fallback para ${token}`, err)
    return {
      token,
      symbol: 'UNKNOWN',
      decimals: BigInt(18),
      decimals_multiplier: BN(10).pow(18),
      name: 'Unknown Token',
    }
  }
}
