import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import { getEmployeePayrollSettings } from 'repositories/src/employeePayrollSettings.js'

export const checkEmployeeEnrolledInPayroll = async (
  userNanoid: UserNanoid,
  teamNanoid: TeamNanoid,
) => {
  const settings = await getEmployeePayrollSettings({
    user_nanoid: userNanoid,
    team_nanoid: teamNanoid,
  })

  return !!settings && settings.status !== 'terminated'
}
