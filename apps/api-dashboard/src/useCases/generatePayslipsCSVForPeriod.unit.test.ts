import { describe, test, expect, vi, beforeEach } from 'vitest'
import { generatePayslipsCSVForPeriod } from './generatePayslipsCSVForPeriod.js'
import { getTeamPayslipsForPeriod } from 'features/src/payroll/getTeamPayslipsForPeriod.js'
import {
  mockEmployeesPayslips,
  mockPayCycle,
  mockTeamNanoid,
} from '../__fixtures__/generatePayslipsCSV/generatePayslipsCSV.fixture.js'

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))

vi.mock('features/src/payroll/getTeamPayslipsForPeriod')

describe('generatePayslipsCSVForPeriod', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('should generate only header if no employees found', async () => {
    vi.mocked(getTeamPayslipsForPeriod).mockResolvedValue([])

    const result = await generatePayslipsCSVForPeriod({
      teamNanoid: mockTeamNanoid,
      payCycle: mockPayCycle,
    })

    const decoded = Buffer.from(result, 'base64').toString('utf-8')

    expect(decoded.trim()).toBe(
      'Name,Email,Gross Pay,Employer Taxes,Employee Taxes,Healthcare,Retirement,Signing Bonus,Stipends,One-off Bonuses,Bonus,Commission,Reimbursement,Net Pay',
    )
  })

  test('should generate CSV with employees payslips', async () => {
    vi.mocked(getTeamPayslipsForPeriod).mockResolvedValue(mockEmployeesPayslips)

    const result = await generatePayslipsCSVForPeriod({
      teamNanoid: mockTeamNanoid,
      payCycle: mockPayCycle,
    })

    const decoded = Buffer.from(result, 'base64').toString('utf-8')

    expect(decoded).toContain('John Doe')
    expect(decoded).toContain('<EMAIL>')
    expect(decoded).toContain('1000.00')
  })
})
