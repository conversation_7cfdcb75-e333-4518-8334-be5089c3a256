import { describe, expect, test, vi, beforeEach } from 'vitest'

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))

import {
  buildInsufficientBalanceNotificationsProperties,
  getTeamAdminsEmails,
  groupPaymentsByToken,
  notifyTeamAdminsAboutInsufficientBalance,
} from './teamBalanceCheckUseCase.js'

import * as smartContractsRepo from 'repositories/src/smartContracts.js'
import * as klaviyo from 'repositories/src/klaviyo.js'
import * as teamsRepo from 'repositories/src/teams.js'
import * as usersRepo from 'repositories/src/users.js'
import {
  allPayments,
  groupedPaymentsByToken,
  paymentsWithInsufficientBalance,
  paymentsWithSufficientBalance,
} from '../__fixtures__/teamBalanceCheck/groupedByToken.fixture.js'
import { expectedNotifications } from '../__fixtures__/teamBalanceCheck/expectedNotifications.fixture.js'
import {
  emailsList,
  expectedFailedEmails,
  notificationList,
  singleEmail,
} from '../__fixtures__/teamBalanceCheck/notifyTeamAdmins.fixture.js'
import {
  adminOne,
  adminOneData,
  adminOneNanoId,
  adminTwo,
  adminTwoData,
  adminTwoNanoId,
  expectedEmailsFull,
  expectedEmailsPartial,
  teamId,
} from '../__fixtures__/teamBalanceCheck/getTeamAdminsEmails.fixture.js'

describe('buildInsufficientBalanceNotificationsProperties', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  test('should return notifications for tokens with insufficient balance', async () => {
    vi.spyOn(smartContractsRepo, 'tokenBalanceOf').mockResolvedValue(30n)

    const result = await buildInsufficientBalanceNotificationsProperties(
      paymentsWithInsufficientBalance,
      '0x123',
      'arbitrum',
    )

    expect(result).toEqual(expectedNotifications)
  })

  test('should return empty array when all tokens have sufficient balance', async () => {
    vi.spyOn(smartContractsRepo, 'tokenBalanceOf').mockResolvedValue(100n)

    const result = await buildInsufficientBalanceNotificationsProperties(
      paymentsWithSufficientBalance,
      '0x456',
      'arbitrum',
    )

    expect(result).toEqual([])
  })
})

describe('groupPaymentsByToken', () => {
  test('should group payments by token and sum their amounts', () => {
    const result = groupPaymentsByToken(allPayments)
    expect(result).toEqual(groupedPaymentsByToken)
  })

  test('should return empty object when input is empty', () => {
    const result = groupPaymentsByToken([])
    expect(result).toEqual({})
  })
})

describe('notifyTeamAdminsAboutInsufficientBalance', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  test('should call createEvent for each email and notification', async () => {
    const mockCreateEvent = vi
      .spyOn(klaviyo, 'createEvent')
      .mockResolvedValue(true)

    const result = await notifyTeamAdminsAboutInsufficientBalance(
      notificationList,
      emailsList,
    )

    expect(mockCreateEvent).toHaveBeenCalledTimes(2)
    expect(result).toEqual([])
  })

  test('should return failedEmails when createEvent throws', async () => {
    const mockCreateEvent = vi
      .spyOn(klaviyo, 'createEvent')
      .mockRejectedValue(new Error('error'))

    const result = await notifyTeamAdminsAboutInsufficientBalance(
      notificationList,
      singleEmail,
    )

    expect(mockCreateEvent).toHaveBeenCalledTimes(1)
    expect(result).toEqual(expectedFailedEmails)
  })
})

describe('getTeamAdminsEmails', () => {
  beforeEach(() => {
    vi.resetAllMocks()
    vi.spyOn(usersRepo, 'getFullUserData').mockImplementation(async (id) => {
      if (id === adminOne) return await adminOneData
      if (id === adminTwo) return await adminTwoData
    })
  })

  test('should return list of valid emails from team admins', async () => {
    vi.spyOn(teamsRepo, 'getTeamAdministratorsNanoid').mockResolvedValue([
      adminOneNanoId,
      adminTwoNanoId,
    ])

    const result = await getTeamAdminsEmails(teamId)

    expect(result).toEqual(expectedEmailsFull)
  })

  test('should filter out admins with no email', async () => {
    vi.spyOn(teamsRepo, 'getTeamAdministratorsNanoid').mockResolvedValue([
      adminOneNanoId,
    ])

    const result = await getTeamAdminsEmails(teamId)

    expect(result).toEqual(expectedEmailsPartial)
  })
})
