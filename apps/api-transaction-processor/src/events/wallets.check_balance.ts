import { getAllWalletsWithBalance } from 'repositories/src/smartContracts.js'
import { updateWalletsBalance } from 'repositories/src/wallets.js'
import { MINIMUM_BALANCE } from 'utils/src/blockchain/ethers.js'
import {
  createInngestClient,
  disableCronInLocalEnv,
} from 'utils/src/inngest/index.js'

const client = createInngestClient('transaction_processor')

export default client.createFunction(
  {
    id: 'transaction_processor-wallets-check-balance',
    name: 'transaction_processor/wallets.check_balance',
  },
  disableCronInLocalEnv([
    { event: 'transaction_processor/wallets.check_balance' },
    {
      cron: '*/2 * * * *',
    },
  ]),
  async ({ step, logger }) => {
    const wallets = await step.run('get-all-wallets', getAllWalletsWithBalance)
    await step.run('update-wallets-balance', async () => {
      for (const w of wallets) {
        const isLow = BigInt(w.current_balance) < MINIMUM_BALANCE
        if (isLow) {
          logger.error({
            message: 'WALLET HAS LOW BALANCE!',
            address: w.address,
            balance: w.current_balance,
          })
        }
      }
      return await updateWalletsBalance(wallets)
    })
  },
)
