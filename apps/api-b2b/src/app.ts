// @ts-nocheck
// this file was generated with `pnpm codegen`, do not edit it manually
import type { App } from 'backend/src/index.js'
import auth from './routes/auth.js'
import company from './routes/company.js'
import me from './routes/me.js'
import payroll from './routes/payroll.js'
import team from './routes/team.js'
import user from './routes/user.js'
import webhook from './routes/webhook.js'
import withdraw from './routes/withdraw.js'

export const createApp = (app: App) => {
  auth(app)

  company(app)

  me(app)

  payroll(app)

  team(app)

  user(app)

  webhook(app)

  withdraw(app)

  return app
}
