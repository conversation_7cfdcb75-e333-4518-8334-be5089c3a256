import { v2MeRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { getCompaniesByFounder } from 'repositories/src/companies.js'
import { validateOnboardedUserByAuth } from 'repositories/src/validations.js'
export default (app: App): App =>
  app.route({
    ...routes['/v2/me'].get,
    handler: async ({ auth }, reply) => {
      const { user } = await validateOnboardedUserByAuth({
        auth,
        apps: ['pay'],
      })
      const companies = await getCompaniesByFounder(user.nanoid)
      const data = { user, company: companies[0] }
      reply.send({ success: true, data })
    },
  })
