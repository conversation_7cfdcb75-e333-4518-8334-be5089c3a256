import { v2AuthRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { UserRiseid } from '@riseworks/contracts/src/brands.js'
import { HTTP_URL } from 'backend/src/common/constants.js'
import type { App } from 'backend/src/index.js'
import dayjs from 'dayjs'
import { ethers } from 'ethers'
import { goTry } from 'go-go-try'
import { validateAddressToRiseid } from 'repositories/src/permissions.js'
import { getEntity } from 'repositories/src/riseEntities.js'
import { getEntityRiseAccount } from 'repositories/src/smartContracts.js'
import { SiweMessage, generateNonce } from 'siwe'
import { AllowedB2BAuthRoles } from 'utils/src/blockchain/riseRequests.js'
import assert from 'utils/src/common/assertHTTP.js'
import { jwtSign } from 'utils/src/common/jwt.js'

const httpsRegex = /^https?:\/\//
export default (app: App): App =>
  app
    .route({
      ...routes['/v2/auth/siwe'].get,
      handler: async ({ siwe }, reply) => {
        const riseid = siwe?.riseid
        const wallet = siwe?.wallet

        assert(wallet, 'Wallet address is required', '400')
        assert(riseid, 'riseid is required', '400')

        const entity = await getEntity<'user'>({
          riseid: riseid,
          parent_riseid: riseid,
        })
        assert(entity, `No entity found with riseid ${riseid}`, '404')
        const account = await getEntityRiseAccount(entity.nanoid)
        assert(account, `No rise account found for ${entity.nanoid}`)
        const validAccount = await validateAddressToRiseid({
          memberAddress: wallet,
          roles: AllowedB2BAuthRoles,
          riseid: riseid,
        })
        assert(validAccount, `Invalid Rise account ${account.address}`, '403')

        const nonce = generateNonce()
        const ENV = process.env.NODE_ENV

        const siweMsg = new SiweMessage({
          domain: HTTP_URL?.replace(httpsRegex, ''),
          address: ethers.getAddress(wallet),
          statement: 'RiseSIWE',
          uri: HTTP_URL,
          version: '1',
          chainId: ENV === 'production' ? 1 : 5,
          issuedAt: dayjs().toISOString(),
          resources: [`riseid:${riseid}`],
          expirationTime: dayjs().add(60, 'minutes').toISOString(),
          nonce,
        }).prepareMessage()

        reply.send({ success: true, data: { siwe: siweMsg } })
      },
    })
    .route({
      ...routes['/v2/auth/verify'].post,
      handler: async ({ body: { message, sig, nonce } }, reply) => {
        const siweObject = new SiweMessage(message)
        const [err, valid] = await goTry(
          siweObject.verify({
            signature: sig,
            nonce,
          }),
        )
        assert(valid, `SIWE error: ${err}`)
        const address = valid.data.address
        const [, riseid] = (valid.data?.resources?.[0]?.split(':') ?? []) as [
          string,
          UserRiseid,
        ]
        assert(riseid, `No riseid found in SIWE: ${address}`)
        const entity = await getEntity<'user'>({
          riseid,
          parent_riseid: riseid,
        })
        assert(entity, `No entity found with riseid ${riseid}`, '404')
        const account = await getEntityRiseAccount(entity.nanoid)
        assert(account, `No rise account found for ${entity.nanoid}`)
        const signerRole = await validateAddressToRiseid({
          memberAddress: address,
          roles: AllowedB2BAuthRoles,
          riseid,
        })
        assert(signerRole, `Invalid Rise account ${account.address}`, '403')
        const jwt = await jwtSign({
          riseid,
          nanoid: entity.nanoid,
          address, // signer address
          role: signerRole, // signer role
          exp: Date.now() + 1000 * 60 * 60, // 1 hour
        })
        reply.send({ success: true, data: { jwt } })
      },
    })
