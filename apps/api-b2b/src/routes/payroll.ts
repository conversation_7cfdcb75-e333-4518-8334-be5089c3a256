import { v2PayrollRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { getCompanyFromTeam } from 'repositories/src/companies.js'
import {
  getPayrollPeriodCard,
  getPayrollPeriodTable,
  getTeamPayroll,
  upsertPayroll,
} from 'repositories/src/payroll.js'
import { getSuperAdminSettings } from 'repositories/src/superAdminSettings.js'
import { validatePermissionByNanoid } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'
import { run } from 'utils/src/common/run.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/v2/payroll/team/:team_nanoid'].post,
      handler: async ({ auth, params: { team_nanoid }, body }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'Unauthorized', '401')
        await validatePermissionByNanoid({
          user_nanoid,
          entity_nanoid: team_nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })
        const company = await getCompanyFromTeam(team_nanoid)
        assert(company, 'Company not found', '404')

        const superAdminSettings = await getSuperAdminSettings()
        assert(superAdminSettings, 'Superadmin settings not found', '404')
        const payroll_program = run(() => {
          if (superAdminSettings.company_nanoid === company.nanoid)
            return 'riseworks_inc'
          return 'riseworks_eor_us'
        })
        const { rise_account } = await upsertPayroll({
          payroll_program,
          nanoid: team_nanoid,
          ...body,
        })
        reply.send({ success: true, data: { rise_account } })
      },
    })
    .route({
      ...routes['/v2/payroll/team/:team_nanoid'].get,
      handler: async ({ auth, params: { team_nanoid } }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'Unauthorized', '401')
        await validatePermissionByNanoid({
          user_nanoid,
          entity_nanoid: team_nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })

        const data = await getTeamPayroll(team_nanoid)
        assert(data, 'Team payroll not found', '404')
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/payroll/team/:team_nanoid/period'].get,
      handler: async ({ auth, params: { team_nanoid } }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'Unauthorized', '401')
        await validatePermissionByNanoid({
          user_nanoid,
          entity_nanoid: team_nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })

        const payroll = await getPayrollPeriodTable(team_nanoid)

        reply.send({ success: true, data: payroll })
      },
    })
    .route({
      ...routes['/v2/payroll/team/:team_nanoid/period/card'].get,
      handler: async ({ auth, params: { team_nanoid } }, reply) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'Unauthorized', '401')
        await validatePermissionByNanoid({
          user_nanoid,
          entity_nanoid: team_nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })

        const payroll = await getPayrollPeriodCard(team_nanoid)

        reply.send({ success: true, data: payroll })
      },
    })
