import { v2WithdrawRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { validateUserByAuth } from 'repositories/src/validations.js'
import {
  getWithdrawFeeByAccount,
  withdrawTransaction,
  withdrawTransactionRequest,
} from 'repositories/src/withdrawals.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/v2/withdraw/:account_nanoid'].post,
      handler: async (
        {
          auth,
          params: { account_nanoid },
          body: { from, workspace_nanoid, amount_cents },
        },
        reply,
      ) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'User not logged', '403')

        await validateUserByAuth({ auth, apps: ['pay'] })
        const { tokenTransfer } = await withdrawTransaction({
          nanoid: user_nanoid,
          from,
          account_nanoid,
          workspace_nanoid,
          amount_cents,
        })
        reply.send({
          success: true,
          data: tokenTransfer,
        })
      },
    })
    .route({
      ...routes['/v2/withdraw/:account_nanoid'].put,
      handler: async (
        {
          auth,
          params: { account_nanoid },
          body: {
            signer,
            from,
            amount_cents,
            workspace_nanoid,
            typed_data,
            signature,
          },
        },
        reply,
      ) => {
        const user_nanoid = auth?.sessionClaims?.user_nanoid
        assert(user_nanoid, 'User not logged', '403')

        await validateUserByAuth({ auth, apps: ['pay'] })

        const { tokenTransfer } = await db.transaction().execute((trx) =>
          withdrawTransactionRequest(
            signer,
            {
              nanoid: user_nanoid,
              from,
              account_nanoid,
              workspace_nanoid,
              amount_cents,
            },
            typed_data,
            signature,
            trx,
          ),
        )

        reply.send({
          success: true,
          data: {
            transaction: tokenTransfer,
          },
        })
      },
    })
    .route({
      ...routes['/v2/withdraw/:account_nanoid/fee'].get,
      handler: async ({ auth, params: { account_nanoid } }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })

        const data = await getWithdrawFeeByAccount(
          user.nanoid,
          account_nanoid,
          'arbitrum',
        )

        reply.send({
          success: true,
          data,
        })
      },
    })
