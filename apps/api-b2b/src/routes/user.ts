import { v2UserRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { updateAddress } from 'repositories/src/addresses.js'
import { getCompaniesByUser } from 'repositories/src/companies.js'
import { updateEntity } from 'repositories/src/riseEntities.js'
import { getTeamsForUser } from 'repositories/src/teams.js'
import {
  validateOnboardedUserByAuth,
  validateUserByAuth,
} from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/v2/user/organizations'].get,
      handler: async ({ auth }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        const companies = await getCompaniesByUser(user.nanoid)
        const data = { companies }
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/user/teams'].get,
      handler: async ({ auth }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        const teams = await getTeamsForUser(user.nanoid)
        const data = { teams }
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/user/address'].put,
      handler: async ({ auth, body }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await updateAddress(user.nanoid, {
          ...body,
          last_modified_by: user.nanoid,
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/user/avatar/update'].post,
      handler: async ({ auth, body: { file_name } }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        const url = `${process.env.AVATARS_BUCKET_URL}/users/${file_name}`
        const response = await fetch(url)
        assert(response.status === 200, `Avatar URL ${url} does not exist`)
        await updateEntity({
          riseid: user.riseid,
          data: {
            avatar: url,
            last_modified_by: user.nanoid,
          },
        })
        reply
          .status(201)
          .send({ success: true, data: 'Your avatar was updated' })
      },
    })
