import { v2CompanyRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { CompanyNanoid } from '@riseworks/contracts/src/brands.js'
import type { getOrganizationOwnershipResponse } from '@riseworks/contracts/src/routes/dashboard/organizations.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db/src/index.js'
import { entries, mapToObj, omit } from 'remeda'
import { updateAddress } from 'repositories/src/addresses.js'
import {
  getCompanyByNanoid,
  getCompanyUsers,
  getFullCompanyData,
  updateCompanyData,
  updateCompanyWithPrivateData,
} from 'repositories/src/companies.js'
import { getContacts, updateContact } from 'repositories/src/companyContacts.js'
import { getOwners, upsertOwner } from 'repositories/src/companyOwners.js'
import { getCompanyRoleSettings } from 'repositories/src/companyRoleSettings.js'
import {
  getCompanySettings,
  upsertCompanySettings,
} from 'repositories/src/companySettings.js'
import { getDocument } from 'repositories/src/documents.js'
import {
  validateOnboardedUserByAuth,
  validatePermissionByNanoid,
  validateUserByAuth,
} from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/v2/company/:nanoid/users'].get,
      handler: async ({ auth, params: { nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['org_viewer', 'team_employee', 'contractor'],
        })
        const users = await getCompanyUsers(nanoid as CompanyNanoid)
        const data = { users }
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/address'].put,
      handler: async ({ auth, params: { nanoid }, body }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        const company = await getCompanyByNanoid(nanoid)
        assert(company, `Company ${nanoid} not found`, '404')
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['org_admin', 'company', 'contractor'],
        })
        await db.transaction().execute(async (trx) => {
          await updateCompanyData(
            nanoid,
            {
              country: body.country,
              state: body.state,
            },
            trx,
          )
          await updateAddress(nanoid, body, trx)
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/details'].put,
      handler: async ({ auth, params: { nanoid }, body }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        const company = await getFullCompanyData(nanoid)
        assert(company, `Company ${nanoid} not found`, '404')
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['company', 'org_admin'],
          apps: ['pay'],
        })
        await db.transaction().execute((trx) =>
          updateCompanyWithPrivateData({
            nanoid: nanoid,
            company: omit(body, ['private_data']),
            private_data: body.private_data,
            db: trx,
          }),
        )
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/details'].get,
      handler: async ({ auth, params: { nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        const company = await getFullCompanyData(nanoid)
        assert(company, `Company ${nanoid} not found`, '404')
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['company', 'org_admin'],
          apps: ['pay'],
        })
        reply.send({
          success: true,
          data: company,
        })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/members/:user_nanoid/settings'].get,
      handler: async ({ params: { nanoid, user_nanoid }, auth }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['company', 'org_admin'],
          apps: ['pay'],
        })
        const data = await getCompanyRoleSettings(nanoid, user_nanoid)
        assert(
          data,
          `Missing company role settings for user ${user_nanoid} and company ${nanoid}`,
          '404',
        )
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/settings'].get,
      handler: async ({ params: { nanoid }, auth }, reply) => {
        await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: auth?.sessionClaims?.user_nanoid,
          entity_nanoid: nanoid,
          types: ['company', 'org_admin', 'org_finance_admin'],
          apps: ['pay'],
        })
        const data = await getCompanySettings(nanoid)
        assert(data, 'Invalid company')
        reply.send({
          success: true,
          data,
        })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/settings'].put,
      handler: async ({ params: { nanoid }, body, auth }, reply) => {
        await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: auth?.sessionClaims?.user_nanoid,
          entity_nanoid: nanoid,
          types: ['company', 'org_admin', 'org_finance_admin'],
          apps: ['pay'],
        })
        await db.transaction().execute(async (trx) => {
          await upsertCompanySettings(
            {
              ...body,
              nanoid,
              last_modified_by: auth?.sessionClaims?.user_nanoid,
              enabled_document_types: JSON.stringify(
                body.enabled_document_types,
              ),
              enabled_payroll_programs: JSON.stringify([]),
            },
            trx,
          )
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/contacts'].get,
      handler: async ({ auth, params: { nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['org_admin', 'company'],
          apps: ['pay'],
        })
        const contacts = await getContacts(nanoid)
        const data = mapToObj(contacts, (contact) => [contact.type, contact])
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/contacts'].put,
      handler: async ({ auth, params: { nanoid }, body }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['org_admin', 'company'],
          apps: ['pay'],
        })
        await db.transaction().execute(async (trx) => {
          for (const [type, contact] of entries(body)) {
            await updateContact({
              nanoid: nanoid,
              type,
              data: contact,
              db: trx,
            })
          }
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/ownership'].get,
      handler: async ({ auth, params: { nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['company', 'org_admin'],
          apps: ['pay'],
        })
        const company = await getCompanyByNanoid(nanoid)
        assert(company, `Company ${nanoid} not found`, '404')
        const owners = await getOwners(nanoid)
        const data = {
          is_dao: company.is_dao,
          owners: [],
        } as unknown as (typeof getOrganizationOwnershipResponse._type)['data']
        for (const o of owners) {
          const doc = await getDocument(o.nanoid, 'ownership')
          assert(
            doc?.document_nanoid,
            `Ownership document for ${o.nanoid} not found`,
            '404',
          )
          data.owners.push({
            ...o,
            dob: o.dob.toString(),
            document_id: doc.document_nanoid,
          })
        }
        reply.send({
          success: true,
          data,
        })
      },
    })
    .route({
      ...routes['/v2/company/:nanoid/ownership'].put,
      handler: async ({ auth, params: { nanoid }, body }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          entity_nanoid: nanoid,
          user_nanoid: user.nanoid,
          types: ['company', 'org_admin'],
          apps: ['pay'],
        })
        await db.transaction().execute(async (trx) => {
          await updateCompanyData(nanoid, body, trx)
          for (const owner of body.owners) {
            await upsertOwner(
              {
                ...owner,
                company_nanoid: nanoid,
                dob: new Date(owner.dob),
              },
              trx,
            )
          }
        })
        reply.send({ success: true })
      },
    })
