import { v2TeamRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db/src/index.js'
import { pick } from 'remeda'
import {
  getCompanyFromTeam,
  getFullCompanyData,
  getSingleCompanyByFounder,
} from 'repositories/src/companies.js'
import { getPaySchedules } from 'repositories/src/paySchedules.js'
import {
  addCompanyAdminsToTeam,
  getEntityByNanoid,
  getRelationshipWithNanoid,
  upsertEntity,
} from 'repositories/src/riseEntities.js'
import {
  getTeamRoleSettings,
  upsertTeamRoleSettings,
} from 'repositories/src/teamRoleSettings.js'
import {
  getTeamSettings,
  upsertTeamSettings,
} from 'repositories/src/teamSettings.js'
import {
  deleteTeam,
  getTeamByNanoid,
  getTeamUsers,
  getTeamWithAdministrators,
  insertTeam,
  updateTeam,
} from 'repositories/src/teams.js'
import { getFullUserData } from 'repositories/src/users.js'
import {
  validateOnboardedUserByAuth,
  validatePermissionByNanoid,
  validateUserByAuth,
} from 'repositories/src/validations.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { run } from 'utils/src/common/run.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/v2/team/:nanoid/users'].get,
      handler: async ({ auth, params: { nanoid } }, reply) => {
        const { user } = await validateOnboardedUserByAuth({
          auth,
          apps: ['pay'],
        })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['org_viewer', 'team_employee', 'contractor', 'team_viewer'],
        })
        const users = await getTeamUsers(nanoid as TeamNanoid)
        const data = { users }
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/v2/team/:nanoid/settings'].get,
      handler: async ({ params: { nanoid }, auth }, reply) => {
        await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: auth?.sessionClaims?.user_nanoid,
          entity_nanoid: nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })
        const data = await getTeamSettings(nanoid)
        assert(data, 'Invalid team')
        reply.send({
          success: true,
          data,
        })
      },
    })
    .route({
      ...routes['/v2/team/:nanoid/settings'].put,
      handler: async ({ params: { nanoid }, body, auth }, reply) => {
        await validateUserByAuth({ auth, apps: ['pay'] })
        const team = await getTeamByNanoid(nanoid)
        assert(team, `Team ${nanoid} not found`)

        await validatePermissionByNanoid({
          user_nanoid: auth?.sessionClaims?.user_nanoid,
          entity_nanoid: nanoid,
          types: ['team_admin', 'team_finance_admin'],
          apps: ['pay'],
        })
        await upsertTeamSettings({
          nanoid,
          last_modified_by: auth?.sessionClaims?.user_nanoid,
          ...body,
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/v2/team/:nanoid'].get,
      handler: async ({ params: { nanoid }, auth }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: [],
          apps: ['pay'],
        })
        const data = await getTeamWithAdministrators(nanoid)
        assert(data, `Team ${nanoid} not found`, '404')
        reply.send({
          success: true,
          data,
        })
      },
    })
    .route({
      ...routes['/v2/team/:nanoid'].delete,
      handler: async ({ params: { nanoid }, auth }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['team_admin'],
          apps: ['pay'],
        })
        await deleteTeam(nanoid)
        reply.send({
          success: true,
        })
      },
    })
    .route({
      ...routes['/v2/team/:nanoid'].put,
      handler: async ({ params: { nanoid }, body: { name }, auth }, reply) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: nanoid,
          types: ['team_admin'],
          apps: ['pay'],
        })
        const team = await getTeamByNanoid(nanoid)
        assert(team, `Team ${nanoid} not found`, '404')
        await db.transaction().execute(async (trx) => {
          await updateTeam({
            nanoid,
            data: {
              name,
              last_modified_by: user.nanoid,
            },
            db: trx,
          })
        })
        reply.send({
          success: true,
          data: team,
        })
      },
    })
    .route({
      ...routes['/v2/team'].post,
      handler: async (
        {
          auth,
          body: {
            name,
            company_nanoid,
            admins,
            withdraw_fee_coverage,
            avatar_file_name,
          },
        },
        reply,
      ) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: company_nanoid,
          types: [],
          apps: ['pay'],
        })
        const team = await db.transaction().execute(async (trx) => {
          const avatar = avatar_file_name
            ? `${process.env.AVATARS_BUCKET_URL}/teams/${avatar_file_name}`
            : ''
          if (avatar !== '') {
            const response = await fetch(avatar)
            assert(
              response.status === 200,
              `Avatar URL ${avatar} does not exist`,
            )
          }
          const team = await insertTeam({
            company_nanoid,
            name,
            avatar,
            last_modified_by: user.nanoid,
            db: trx,
          })
          const client = createInngestClient('dashboard')
          await client.send({
            name: 'dashboard/riseid.activate',
            data: {
              riseid: team.riseid,
              network: 'arbitrum',
            },
          })
          await mapAsync(admins, async ({ nanoid, type }) => {
            await validatePermissionByNanoid({
              user_nanoid: nanoid,
              entity_nanoid: company_nanoid,
              types: [
                'company',
                'org_admin',
                'org_finance_admin',
                'org_viewer',
              ],
              apps: ['pay'],
            })
            const entity = await getEntityByNanoid(nanoid)
            assert(entity, `Entity ${nanoid} not found`, '404')
            await upsertEntity(
              {
                riseid: team.riseid,
                parent_riseid: entity.riseid,
                type,
                last_modified_by: user.nanoid,
              },
              {
                withdraw_fee_coverage,
              },
              trx,
            )
          })
          await addCompanyAdminsToTeam(team.nanoid, trx)

          return team
        })
        reply.send({
          success: true,
          data: pick(team, ['avatar', 'name', 'nanoid']),
        })
      },
    })
    .route({
      ...routes['/v2/team/:team_nanoid/member/:user_nanoid/summary'].get,
      handler: async (
        { params: { team_nanoid, user_nanoid }, auth },
        reply,
      ) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        const company = await getCompanyFromTeam(team_nanoid)
        assert(company, `Company not found for team ${team_nanoid}`, '404')
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: company.nanoid,
          types: ['company', 'org_admin', 'org_viewer'],
          apps: ['pay'],
        })
        const relationship = await getRelationshipWithNanoid(
          team_nanoid,
          user_nanoid,
        )
        assert(
          relationship,
          `User ${user_nanoid} relationship with team ${team_nanoid} not found`,
          '404',
        )
        assert(
          ['team_employee', 'contractor'].includes(relationship.type),
          `User ${user_nanoid} relationship with team ${team_nanoid} is not team_employee neither contractor: ${relationship.type}`,
          '404',
        )
        const fullUser = await getFullUserData(user_nanoid)
        assert(fullUser, `User ${user_nanoid} not found`, '404')
        const userCompany = await run(async () => {
          const ownedCompany = await getSingleCompanyByFounder(fullUser.nanoid)
          if (ownedCompany) return getFullCompanyData(ownedCompany.nanoid)
          return
        })
        const pay_schedules = await getPaySchedules(team_nanoid, user_nanoid)
        const data = {
          user: { ...fullUser, dob: fullUser.dob?.toString() ?? null },
          company: userCompany,
          relationship_type: relationship.type as
            | 'team_employee'
            | 'contractor',
          pay_schedules,
        }
        reply.send({
          success: true,
          data,
        })
      },
    })
    .route({
      ...routes['/v2/team/:team_nanoid/member/:user_nanoid/settings'].get,
      handler: async (
        { params: { team_nanoid, user_nanoid }, auth },
        reply,
      ) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        const company = await getCompanyFromTeam(team_nanoid)
        assert(company, `Company not found for team ${team_nanoid}`, '404')
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: company.nanoid,
          types: ['company', 'org_admin', 'org_viewer'],
          apps: ['pay'],
        })
        const data = await getTeamRoleSettings({ team_nanoid, user_nanoid })
        const teamSetting = data[0]
        assert(
          teamSetting,
          `Missing team role settings for user ${user_nanoid} and team ${team_nanoid}`,
          '404',
        )
        reply.send({ success: true, data: teamSetting })
      },
    })
    .route({
      ...routes['/v2/team/:team_nanoid/member/:user_nanoid/settings'].put,
      handler: async (
        {
          params: { team_nanoid, user_nanoid },
          body: { withdraw_fee_coverage },
          auth,
        },
        reply,
      ) => {
        const { user } = await validateUserByAuth({ auth, apps: ['pay'] })
        const company = await getCompanyFromTeam(team_nanoid)
        assert(company, `Company not found for team ${team_nanoid}`, '404')
        await validatePermissionByNanoid({
          user_nanoid: user.nanoid,
          entity_nanoid: company.nanoid,
          types: ['company', 'org_admin', 'org_viewer'],
          apps: ['pay'],
        })
        await upsertTeamRoleSettings({
          team_nanoid,
          user_nanoid,
          withdraw_fee_coverage,
          last_modified_by: user.nanoid,
        })
        reply.send({ success: true })
      },
    })
