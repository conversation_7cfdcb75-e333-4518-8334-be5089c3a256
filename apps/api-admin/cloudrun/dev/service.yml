apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: api-admin
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing

# https://doc.crds.dev/github.com/knative/serving/serving.knative.dev/Service/v1@knative-v1.2.2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-egress: all-traffic
        autoscaling.knative.dev/maxScale: "2"
        autoscaling.knative.dev/minScale: "0"
        run.googleapis.com/cloudsql-instances: riseworksdev:us-central1:rise-tf-primary
        run.googleapis.com/vpc-access-connector: projects/riseworksdev/locations/us-central1/connectors/rise-vpc-connector
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 500
      timeoutSeconds: 300
      containers:
        - image: service-image
          ports:
            - name: http1
              containerPort: "8080"
          resources:
            limits:
              cpu: 1000m
              memory: 2048Mi
          env:
            - name: NODE_ENV
              value: development
            - name: GOOGLE_PROJECT_ID
              value: riseworksdev
            - name: GOOGLE_PROJECT_NUMBER
              value: "************"
            - name: GOOGLE_LOCATION_ID
              value: us-central1
            - name: GOOGLE_OIDC_EMAIL
              value: <EMAIL>
            - name: LOGLEVEL
              value: INFO
            - name: DB_HOST
              value: "*************"
            - name: DB_PORT
              value: "3306"
            - name: DB_SCHEMA
              value: rise
            - name: DB_CONNECTION_LIMIT
              value: "30"
            - name: DB_CONNECTION_TIMEOUT
              value: "120000"
            - name: SEGMENT_SOURCE
              value: uzewxkVCVA1fDxtMLasS5QkpBLveIZ5E
            - name: GOOGLE_DRIVE_COMPANY_DOCUMENTS_FOLDER_ID
              value: 1P8Z7B_X61dmOZA1pUjq5XTcPBPcjzlzH
            - name: GOOGLE_RECAPTCHA
              value: 6LeRfT8pAAAAAFVq1cc-NTRo5sA7NDvoCx2Zazg7
            - name: DOMAIN
              value: "https://dev-api.riseworks.dev"
            - name: APP_DOMAIN
              value: https://dev-pay.riseworks.io
            - name: HTTP_PORT
              value: "8080"
            - name: REDIS_URI
              value: redis://**************:6379
            - name: AVATARS_BUCKET
              value: dev-avatars-riseworks-io
            - name: AVATARS_BUCKET_URL
              value: https://storage.googleapis.com/dev-avatars-riseworks-io
            - name: SOCKET_PATH
              value: /cloudsql/riseworksdev:us-central1:rise-tf-primary
            - name: ENABLE_METRICS
              value: "false"
  traffic:
    - percent: 100
      latestRevision: true
