import path from 'node:path'
import fastifyStatic from '@fastify/static'
import { bootstrap } from 'backend/src/index.js'
import { createApp } from './app.js'

const K_SERVICE = process.env.K_SERVICE
const PORT = process.env.HTTP_PORT

const app = createApp(await bootstrap('api-admin'))

const __dirname = `${process.cwd()}/public`
app.register(fastifyStatic, {
  root: path.join(__dirname),
  prefix: '/admin/public',
  // constraints: {
  // 	host:
  // 		process.env.NODE_ENV === 'localhost'
  // 			? 'localhost'
  // 			: 'riseworks.retool.com',
  // },
})

app.listen({
  port: +PORT,
  host: K_SERVICE ? '0.0.0.0' : 'localhost',
})
