// @ts-nocheck
// this file was generated with `pnpm codegen`, do not edit it manually
import type { App } from 'backend/src/index.js'
import access from './routes/access.js'
import accounts from './routes/accounts.js'
import auth from './routes/auth.js'
import blockchain_transactions from './routes/blockchain_transactions.js'
import companies from './routes/companies.js'
import deposits from './routes/deposits.js'
import mastertax from './routes/mastertax.js'
import middlewares from './routes/middlewares.js'
import migration from './routes/migration.js'
import orum from './routes/orum.js'
import people from './routes/people.js'
import risk_level from './routes/risk_level.js'
import routefusion from './routes/routefusion.js'
import subscriptions from './routes/subscriptions.js'
import test_users from './routes/test_users.js'
import users from './routes/users.js'
import withdraw from './routes/withdraw.js'
import withdraw_downtimes from './routes/withdraw_downtimes.js'

export const createApp = (app: App) => {
  access(app)

  accounts(app)

  auth(app)

  blockchain_transactions(app)

  companies(app)

  deposits(app)

  mastertax(app)

  middlewares(app)

  migration(app)

  orum(app)

  people(app)

  risk_level(app)

  routefusion(app)

  subscriptions(app)

  test_users(app)

  users(app)

  withdraw(app)

  withdraw_downtimes(app)

  return app
}
