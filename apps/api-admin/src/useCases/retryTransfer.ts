import type { WithdrawNanoid } from '@riseworks/contracts/src/brands.js'
import type { SelectableProviderWithdrawalsType } from '@riseworks/contracts/src/codegen/zod/rise/provider_withdrawals.js'
import { type DBTransaction, db } from 'db'
import { createOrumTransfer } from 'repositories/src/withdrawOrum.js'
import { createRoutefusionTransfer } from 'repositories/src/withdrawRoutefusion.js'
import assert from 'utils/src/common/assertHTTP.js'

const providerTransferCreators: Record<
  Extract<SelectableProviderWithdrawalsType['name'], 'Orum' | 'Routefusion'>,
  (withdrawId: WithdrawNanoid, trx: DBTransaction) => Promise<void>
> = {
  Orum: createOrumTransfer,
  Routefusion: createRoutefusionTransfer,
}

export async function retryTransfer(withdraw_id: WithdrawNanoid) {
  const provider = await db
    .selectFrom('rise.provider_withdrawals')
    .select('name')
    .where('nanoid', '=', withdraw_id)
    .executeTakeFirst()

  assert(provider, `Provider withdraw ${withdraw_id} not found`, '404')

  assert(
    'Routefusion' === provider.name || 'Orum' === provider.name,
    `Unsupported provider: ${provider.name}`,
    '400',
  )

  const transferCreator = providerTransferCreators[provider.name]

  await db.transaction().execute(async (trx) => {
    return await transferCreator(withdraw_id, trx)
  })
}
