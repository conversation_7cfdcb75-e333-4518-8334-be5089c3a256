import type {
  SelectableProviderWithdrawals,
  SelectableWithdrawals,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { getWithdrawalsByProvider } from 'repositories/src/withdrawals.js'

export async function getTransfers(
  provider: SelectableProviderWithdrawals['name'],
  provider_status?: SelectableProviderWithdrawals['status'],
  withdraw_status?: SelectableWithdrawals['status'],
) {
  return await getWithdrawalsByProvider(
    provider,
    provider_status,
    withdraw_status,
  )
}
