import { adminRiskLevelRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { setRiskLevel } from 'repositories/src/entityRiskLevel.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/risk_level'].post,
    handler: async (
      { admin_auth, body: { nanoid, risk_level, notes } },
      reply,
    ) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(
        hasRoles(admin_auth, [
          'admin',
          'editor',
          'Compliance',
          'Customer Support',
        ]),
        'Unauthorized',
        '401',
      )
      await setRiskLevel(nanoid, risk_level, notes)
      reply.send({ success: true })
    },
  })
