import { adminRoutefusionRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import {
  disableRoutefusionWebhook,
  getWebhookSubscriptions,
  registerRoutefusionWebhook,
  sandboxRoutefusionIncomingTransfer,
} from 'repositories/src/routefusion.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getTransfers } from '../useCases/getAndTransformTransfers.js'
import { retryTransfer } from '../useCases/retryTransfer.js'
export default (app: App): App =>
  app
    .route({
      ...routes['/admin/routefusion/transfers'].get,
      handler: async (
        { admin_auth, query: { provider_status, withdraw_status } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )

        reply.send({
          success: true,
          data: await getTransfers(
            'Routefusion',
            provider_status,
            withdraw_status,
          ),
        })
      },
    })
    .route({
      ...routes['/admin/routefusion/retry'].post,
      handler: async ({ admin_auth, body: { withdraw_id } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        assert(withdraw_id, 'withdraw_id is missing')
        await retryTransfer(withdraw_id)
        reply.send({
          success: true,
        })
      },
    })
    .route({
      ...routes['/admin/routefusion/webhooks'].get,
      handler: async ({ admin_auth, query }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        reply.send({
          success: true,
          data: await getWebhookSubscriptions(query.key_type),
        })
      },
    })
    .route({
      ...routes['/admin/routefusion/webhooks'].post,
      handler: async ({ admin_auth, body }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        reply.send({
          success: true,
          data: await registerRoutefusionWebhook(
            body.url,
            body.type,
            body.key_type,
          ),
        })
      },
    })
    .route({
      ...routes['/admin/routefusion/webhooks/:subscriptionId'].delete,
      handler: async (
        { admin_auth, params: { subscriptionId }, query: { key_type } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        assert(key_type, 'Missing required parameter: key_type')
        reply.send({
          success: true,
          data: await disableRoutefusionWebhook(subscriptionId, key_type),
        })
      },
    })
    .route({
      ...routes['/admin/routefusion/sandbox/incoming-transfer'].post,
      handler: async (
        {
          admin_auth,
          body: {
            key_type,
            amount,
            entity_nanoid,
            reference,
            counterparty_name,
          },
        },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        reply.send({
          success: true,
          data: await sandboxRoutefusionIncomingTransfer(
            entity_nanoid,
            amount,
            key_type,
            reference,
            counterparty_name,
          ),
        })
      },
    })
