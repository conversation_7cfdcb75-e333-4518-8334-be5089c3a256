import { adminTestUsersRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { upsertAddress } from 'repositories/src/addresses.js'
import { hasRoles } from 'repositories/src/admin.js'
import { agree, getCurrentAgreement } from 'repositories/src/agreements.js'
import {
  createUser,
  getClerkUserByEmail,
  updateUser,
} from 'repositories/src/clerk.js'
import { insertCompany } from 'repositories/src/companies.js'
import { setRiskLevelFromLevelOrCountry } from 'repositories/src/entityRiskLevel.js'
import { insertEntity } from 'repositories/src/riseEntities.js'
import { insertTeam } from 'repositories/src/teams.js'
import {
  getUserByEmail,
  insertUser,
  updateUserWithPrivateData,
} from 'repositories/src/users.js'
import {
  updateOnboarding,
  upsertOnboarding,
} from 'repositories/src/usersOnboarding.js'

import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/test_user'].post,
    handler: async ({ admin_auth, body }, reply) => {
      assert(process.env.NODE_ENV !== 'production', 'Unauthorized', '401')
      assert(admin_auth, 'Unauthorized', '401')
      assert(hasRoles(admin_auth, ['admin']), 'Unauthorized', '401')

      const {
        email_address,
        phone,
        first_name,
        middle_name,
        last_name,
        role,
        register_business,
        address,
        city,
        state,
        country,
        company_name,
        tax_id = Date.now().toString(),
      } = body

      let user = await getUserByEmail(email_address)

      assert(!user, 'User already exists', '400')

      await db.transaction().execute(async (trx) => {
        user = await insertUser({
          user: {
            email: email_address,
            phone: phone ?? '',
            first_name: first_name ?? '',
            middle_name: middle_name ?? '',
            last_name: last_name ?? '',
          },
          entity: {
            avatar: '',
          },
          app: 'pay',
          db: trx,
        })
        assert(user, `User creation failed for email address ${email_address}`)
        const clerkUser = await getClerkUserByEmail(email_address)
        if (clerkUser) {
          await updateUser(clerkUser.id, {
            firstName: first_name,
            lastName: last_name,
            publicMetadata: {
              user_nanoid: user.nanoid,
            },
          })
        } else {
          await createUser({
            emailAddress: [email_address],
            firstName: first_name,
            lastName: last_name,
            publicMetadata: {
              user_nanoid: user.nanoid,
            },
          })
        }

        const currentAgreement = await getCurrentAgreement()
        assert(currentAgreement, 'Agreements not found')

        await agree(
          {
            agreement_id: currentAgreement.id,
            nanoid: user.nanoid,
            ip: '',
            last_modified_by: user.nanoid,
          },
          trx,
        )
        const registered_business: boolean | undefined =
          role === 'payer' || (register_business && role === 'payee')

        await upsertOnboarding(
          {
            role,
            nanoid: user.nanoid,
            step: 'details',
            register_business: registered_business,
            last_modified_by: user.nanoid,
            accepted_invites: false,
            moderation_status: 'pending',
          },
          trx,
        )
        if (registered_business) {
          const userCompany = await insertCompany({
            user_nanoid: user.nanoid,
            company: {
              name: company_name || '',
              incorporation_country: country,
              phone: phone || '',
              country,
              state,
            },
            companyPrivateData: {
              tax_id: tax_id,
              us_work: country === 'US',
              us_based: country === 'US',
            },
            db: trx,
          })

          assert(userCompany, 'Failed to create company')

          await upsertAddress(
            {
              nanoid: userCompany.nanoid,
              city,
              state,
              country,
              line_1: address,
              line_2: 'address line 2',
              zip_code: '13654',
            },
            trx,
          )

          await trx
            .insertInto('rise_private.rise_entity_compliance')
            .values({
              nanoid: userCompany.nanoid,
              status: 'approved',
              reason: null,
              external_time: new Date(),
            })
            .onDuplicateKeyUpdate({
              status: 'approved',
              reason: null,
              external_time: new Date(),
            })
            .execute()

          if (role === 'payer') {
            const team = await insertTeam({
              company_nanoid: userCompany.nanoid,
              name: 'Default Team',
              avatar: '',
              last_modified_by: user.nanoid,
              db: trx,
            })

            await insertEntity(
              {
                riseid: team.riseid,
                parent_riseid: user.riseid,
                type: 'team_admin',
                last_modified_by: user.nanoid,
              },
              trx,
            )
          }
        }

        await updateUserWithPrivateData(
          {},
          {
            nanoid: user.nanoid,
            last_modified_by: user.nanoid,
            tax_id: tax_id,
            us_work: country === 'US',
            us_based: country === 'US',
          },
          trx,
        )
        await upsertAddress(
          {
            nanoid: user.nanoid,
            last_modified_by: user.nanoid,
            city,
            state,
            country,
            line_1: address,
            line_2: 'address line 2',
            zip_code: '13654',
          },
          trx,
        )

        await trx
          .insertInto('rise_private.rise_entity_compliance')
          .values({
            nanoid: user.nanoid,
            status: 'approved',
            reason: null,
            external_time: new Date(),
          })
          .onDuplicateKeyUpdate({
            status: 'approved',
            reason: null,
            external_time: new Date(),
          })
          .execute()

        await setRiskLevelFromLevelOrCountry(
          user.nanoid,
          'low',
          country,
          '',
          user.nanoid,
          db,
        )

        await updateOnboarding(
          user.nanoid,
          {
            step: 'compliance_process',
            moderation_status: 'approved',
            moderation_internal_note: '',
            moderation_public_note: '',
          },
          trx,
        )

        reply.send({ success: true })
      })
    },
  })
