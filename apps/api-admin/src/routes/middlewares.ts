import fastifyCookie from '@fastify/cookie'
import type { App } from 'backend/src/index.js'
import { COOKIE_NAME, getSession } from 'repositories/src/admin.js'
import { run } from 'utils/src/common/run.js'

export default (app: App): App =>
  app.register(fastifyCookie).addHook('onRequest', async (request, reply) => {
    if (request.url.includes('inngest')) return
    const cookie = request.cookies[COOKIE_NAME]
    const authorization = request.headers.authorization

    const uibRole = run(() => {
      const uibRole = request.headers['ui-bakery-roles']
      if (Array.isArray(uibRole)) {
        return uibRole
      }

      if (typeof uibRole === 'string') {
        return uibRole.split(',')
      }

      return undefined
    })
    const uibUser = request.headers['ui-bakery-user']?.toString()

    request.log.info(`UI Bakery Role: ${uibRole} and User ${uibUser}`)

    const session = await getSession(cookie, authorization)
    if (session) {
      request.admin_auth = {
        userEmail: session.email,
        uibRoles: uibRole,
        uibUser: uibUser,
      }
    }
  })
