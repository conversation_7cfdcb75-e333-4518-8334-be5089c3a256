import { adminWithdrawDowntimesRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import {
  getWithdrawDowntime,
  insertWithdrawDowntime,
  updateWithdrawDowntime,
} from 'repositories/src/withdrawDowntimes.js'
import assert from 'utils/src/common/assertHTTP.js'
export default (app: App): App =>
  app
    .route({
      ...routes['/admin/withdraw_downtimes'].post,
      handler: async (
        { body: { country_code, from, to, description }, admin_auth },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const id = await insertWithdrawDowntime({
          country_code,
          from: new Date(from),
          to: new Date(to),
          description,
        })
        reply.send({ success: true, data: { id } })
      },
    })
    .route({
      ...routes['/admin/withdraw_downtimes'].get,
      handler: async ({ query: { country_code }, admin_auth }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const data = await getWithdrawDowntime(country_code)
        assert(data, `Withdraw downtime not found for ${country_code}`, '404')
        reply.send({
          success: true,
          data: {
            ...data,
            from: data.from.toISOString(),
            to: data.to.toISOString(),
            created_at: data.created_at.toISOString(),
            updated_at: data.updated_at.toISOString(),
          },
        })
      },
    })
    .route({
      ...routes['/admin/withdraw_downtimes/:id'].put,
      handler: async (
        { params: { id }, body: { from, to, description }, admin_auth },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        await updateWithdrawDowntime(id, {
          from: new Date(from),
          to: new Date(to),
          description,
        })
        reply.send({ success: true })
      },
    })
