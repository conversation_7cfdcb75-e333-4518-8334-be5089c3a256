import { adminAuthRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { createJWT, oAuth2Client } from 'repositories/src/admin.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/auth'].get,
      handler: ({ query: { redirect_app } }, reply) => {
        const redirectPath = redirect_app
          ? '/admin/public/auth/callback.html'
          : '/admin/callback'
        const authorizeUrl = oAuth2Client.generateAuthUrl({
          access_type: 'offline',
          scope: ['email', 'profile'],
          prompt: 'consent',
          redirect_uri: `${process.env.DOMAIN}${redirectPath}`,
        })
        reply.redirect(authorizeUrl)
      },
    })
    .route({
      ...routes['/admin/callback'].get,
      handler: async ({ query: { code, redirect_app } }, reply) => {
        const redirectPath = redirect_app
          ? '/admin/public/auth/callback.html'
          : '/admin/callback'
        const { tokens } = await oAuth2Client.getToken({
          code: code,
          redirect_uri: `${process.env.DOMAIN}${redirectPath}`,
        })
        assert(tokens.access_token, 'No access token')
        const cookie = await createJWT(tokens.access_token)
        reply.setCookie(cookie.cookie_name, cookie.session, cookie.data)
        reply.send({ success: true })
      },
    })
