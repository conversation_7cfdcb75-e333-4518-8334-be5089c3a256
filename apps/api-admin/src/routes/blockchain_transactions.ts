import { adminBlockchainTransactionsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import {
  getBlockchainTransaction,
  getBlockchainTransactions,
  updateBlockchainTransaction,
} from 'repositories/src/adminBlockchainTransactions.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/blockchain_transactions'].get,
      handler: async ({ admin_auth, query: { limit, offset } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const transactions = await getBlockchainTransactions(limit, offset)
        reply.send({ success: true, data: transactions })
      },
    })
    .route({
      ...routes['/admin/blockchain_transactions/:id'].get,
      handler: async ({ admin_auth, params: { id } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const transaction = await getBlockchainTransaction(id)
        assert(transaction, `Transaction not found with id ${id}`, '404')
        assert(
          admin_auth.userEmail === transaction.email,
          'Unauthorized',
          '401',
        )
        reply.send({ success: true, data: transaction })
      },
    })
    .route({
      ...routes['/admin/blockchain_transactions/:id'].put,
      handler: async (
        { admin_auth, params: { id }, body: { transaction_hash, status } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        assert(
          status !== 'confirmed' || transaction_hash,
          'Missing transaction_hash',
          '400',
        )
        const transaction = await getBlockchainTransaction(id)
        assert(transaction, `Transaction not found with id ${id}`, '404')
        assert(
          admin_auth.userEmail === transaction.email,
          'Unauthorized',
          '401',
        )
        await updateBlockchainTransaction(id, status, transaction_hash)
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
