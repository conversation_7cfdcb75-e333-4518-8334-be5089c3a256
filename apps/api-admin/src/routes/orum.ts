import { adminOrumRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import {
  deleteOrumWebhook,
  getOrumWebhooks,
  registerOrumWebhook,
  updateOrumWebhook,
} from 'repositories/src/orum.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getTransfers } from '../useCases/getAndTransformTransfers.js'
import { retryTransfer } from '../useCases/retryTransfer.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/orum/transfers'].get,
      handler: async (
        { admin_auth, query: { provider_status, withdraw_status } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        reply.send({
          success: true,
          data: await getTransfers('Orum', provider_status, withdraw_status),
        })
      },
    })
    .route({
      ...routes['/admin/orum/retry'].post,
      handler: async ({ admin_auth, body: { withdraw_id } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        await retryTransfer(withdraw_id)
        reply.send({
          success: true,
        })
      },
    })
    .route({
      ...routes['/admin/orum/webhooks'].post,
      handler: async ({ admin_auth, body }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        await registerOrumWebhook(body)
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/admin/orum/webhooks'].get,
      handler: async ({ admin_auth }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        reply.send({ success: true, data: await getOrumWebhooks() })
      },
    })
    .route({
      ...routes['/admin/orum/webhooks/:id'].put,
      handler: async ({ admin_auth, params: { id }, body }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        await updateOrumWebhook(id, body)
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/admin/orum/webhooks/:id'].delete,
      handler: async ({ admin_auth, params: { id } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        await deleteOrumWebhook(id)
        reply.send({ success: true })
      },
    })
