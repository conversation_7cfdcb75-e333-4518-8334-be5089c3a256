import { adminAccessRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend'
import { hasRoles } from 'repositories/src/admin.js'
import {
  countAddressesForAccessRole,
  createAdminGrantRolesBatchTransaction,
  createAdminGrantRolesTransaction,
  createArbTableRegisterAdminTransaction,
  createRoleAdminTransaction,
  getAddressRiseAccessRoles,
  getAddressesForAccessRole,
  getAllRoles,
  lookupAddressIdx,
} from 'repositories/src/smartContracts.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/arbtable/:address/register'].post,
      handler: async ({ admin_auth, params: { address } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createArbTableRegisterAdminTransaction(
          address,
          admin_auth.userEmail,
        )
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/arbtable/:address/lookup'].get,
      handler: async ({ admin_auth, params: { address } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        reply.send({
          success: true,
          data: { index: await lookupAddressIdx(address) },
        })
      },
    })
    .route({
      ...routes['/admin/access/roles'].get,
      handler: async ({ admin_auth }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        reply.send({ success: true, data: { roles: await getAllRoles() } })
      },
    })
    .route({
      ...routes['/admin/access/roles'].post,
      handler: async ({ admin_auth, body: { role, admin_role } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createRoleAdminTransaction(
          admin_auth.userEmail,
          role,
          admin_role,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/access/:address/roles'].get,
      handler: async ({ admin_auth, params: { address } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        reply.send({
          success: true,
          data: { roles: await getAddressRiseAccessRoles(address) },
        })
      },
    })
    .route({
      ...routes['/admin/access/:address/roles/grant'].post,
      handler: async (
        { admin_auth, params: { address }, body: { roles } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createAdminGrantRolesTransaction(
          admin_auth.userEmail,
          roles,
          address,
          true,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/access/roles/:role/grant/batch'].post,
      handler: async (
        { admin_auth, params: { role }, body: { addresses } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createAdminGrantRolesBatchTransaction(
          admin_auth.userEmail,
          role,
          addresses,
          true,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/access/:address/roles/revoke'].post,
      handler: async (
        { admin_auth, params: { address }, body: { roles } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createAdminGrantRolesTransaction(
          admin_auth.userEmail,
          roles,
          address,
          false,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/access/roles/:role/revoke/batch'].post,
      handler: async (
        { admin_auth, params: { role }, body: { addresses } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const id = await createAdminGrantRolesBatchTransaction(
          admin_auth.userEmail,
          role,
          addresses,
          false,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/access/roles/:role/addresses/count'].get,
      handler: async ({ admin_auth, params: { role } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const count = await countAddressesForAccessRole(role)
        reply.send({
          success: true,
          data: {
            count: count.toString(),
          },
        })
      },
    })
    .route({
      ...routes['/admin/access/roles/:role/addresses'].get,
      handler: async ({ admin_auth, params: { role } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const data = await getAddressesForAccessRole(role)
        reply.send({
          success: true,
          data: {
            count: data.count.toString(),
            addresses: data.members,
          },
        })
      },
    })
