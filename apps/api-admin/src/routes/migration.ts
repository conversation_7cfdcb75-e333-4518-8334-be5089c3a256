import { adminMigrationRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { getV1UsersWithMigrationStatus } from 'repositories/src/v1StrapiMigration.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const inngest = createInngestClient('dashboard')

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/migration/user'].post,
      handler: async (
        {
          admin_auth,
          body: { user_id, company_relationship_id, company_relationship_type },
        },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
          ]),
          'Unauthorized',
          '401',
        )
        await inngest.send({
          name: 'dashboard/migration.user',
          data: {
            user_id,
            company_relationship_id,
            company_relationship_type,
          },
        })
        reply.send({ success: true })
      },
    })
    .route({
      ...routes['/admin/migration/users'].get,
      handler: async (
        {
          admin_auth,
          query: { page_index, page_size, search, sort_by, sort_dir },
        },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
          ]),
          'Unauthorized',
          '401',
        )
        const users = await getV1UsersWithMigrationStatus(
          Number(page_index),
          Number(page_size),
          search,
          sort_by,
          sort_dir,
        )

        reply.send({
          success: true,
          data: users,
        })
      },
    })
