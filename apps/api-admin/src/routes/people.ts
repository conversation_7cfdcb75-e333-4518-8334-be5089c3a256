import { adminPeopleRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { hasRoles } from 'repositories/src/admin.js'
import {
  createUser,
  deleteUser,
  getClerkUserByEmail,
  updateUser,
} from 'repositories/src/clerk.js'
import { getEntityRiskLevel } from 'repositories/src/entityRiskLevel.js'
import {
  getFullUserData,
  getUserByEmail,
  getUserByNanoid,
  getUsersBySearch,
  updateUserData,
} from 'repositories/src/users.js'

import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/people/:nanoid'].get,
      handler: async ({ admin_auth, params: { nanoid } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const user = await getFullUserData(nanoid)
        const riskLevel = await getEntityRiskLevel(nanoid)
        assert(user, `User ${nanoid} not found`, '404')
        reply.send({
          success: true,
          data: {
            ...user,
            risk_level: riskLevel
              ? {
                  risk_level: riskLevel.risk_level,
                  notes: riskLevel.notes,
                  created_at: riskLevel.created_at,
                }
              : undefined,
          },
        })
      },
    })
    .route({
      ...routes['/admin/people'].get,
      handler: async ({ admin_auth, query: { search } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const users = await getUsersBySearch(search)
        reply.send({ success: true, data: users })
      },
    })
    .route({
      ...routes['/admin/people/:nanoid'].put,
      handler: async ({ admin_auth, params: { nanoid }, body }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin']), 'Unauthorized', '401')

        const { email_address, phone, first_name, middle_name, last_name } =
          body

        const user = await getUserByNanoid(nanoid)
        const admin = await getUserByEmail(admin_auth.userEmail)
        if (email_address) {
          const existingUser = await getUserByEmail(email_address)
          assert(!existingUser, 'User with this email already exists', '400')
        }
        assert(user, 'User Data Change - user not found')
        await db.transaction().execute(async (trx) => {
          const hasUpdate =
            email_address || phone || first_name || middle_name || last_name
          const existingClerkUser = await getClerkUserByEmail(user.email)
          if (hasUpdate) {
            await updateUserData(
              nanoid as UserNanoid,
              {
                email: email_address ? email_address : undefined,
                phone: phone ? phone : undefined,
                first_name: first_name ? first_name : undefined,
                middle_name: middle_name ? middle_name : undefined,
                last_name: last_name ? last_name : undefined,
                last_modified_by: admin
                  ? (admin?.nanoid as UserNanoid)
                  : undefined,
              },
              trx,
            )
            if (process.env.NODE_ENV !== 'localhost') {
              if (email_address && email_address !== user.email) {
                if (existingClerkUser) {
                  await deleteUser(existingClerkUser.id)
                }

                const clerkUser = await getClerkUserByEmail(email_address)
                if (clerkUser) {
                  await updateUser(clerkUser.id, {
                    publicMetadata: {
                      user_nanoid: user.nanoid,
                    },
                  })
                } else {
                  await createUser({
                    emailAddress: [email_address],
                    publicMetadata: {
                      user_nanoid: nanoid,
                    },
                  })
                }
              }
            }
          }

          reply.send({ success: true })
        })
      },
    })
