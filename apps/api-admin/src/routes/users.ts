import { adminUsersRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { Firebase } from 'repositories/src/firebase.js'
import { getNotificationToken, getUserByEmail } from 'repositories/src/users.js'
import { getUserRskByNanoid, updateUserRsk } from 'repositories/src/usersRsk.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('compliance')

export default (app: App): App =>
  app.route({
    ...routes['/admin/users/:user_nanoid/rsk-reset'].post,
    handler: async (
      {
        admin_auth,
        params: { user_nanoid },
        body: { reset_status = 'completed' },
      },
      reply,
    ) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(hasRoles(admin_auth, ['admin']), 'Unauthorized', '401')

      const userRsk = await getUserRskByNanoid(user_nanoid)
      const admin = await getUserByEmail(admin_auth.userEmail)
      assert(userRsk, `RSK not found for ${user_nanoid}`, '404')

      if (reset_status === 'completed') {
        assert(
          userRsk.reset_status === 'pending',
          `User ${user_nanoid} hasn't started the rsk reset yet`,
          '400',
        )

        await updateUserRsk(user_nanoid, {
          reset_status: 'completed',
          last_modified_by: admin?.nanoid,
        })

        // notify user that rsk reset is completed
        await client.send({
          name: 'klaviyo/create.event',
          data: {
            name: 'RSK_RESET_COMPLETED',
            properties: {
              first_name: userRsk.first_name,
              last_name: userRsk.last_name,
              reset_status: userRsk.reset_status,
            },
            email: userRsk.email,
          },
        })

        const notificationToken = await getNotificationToken(user_nanoid)
        await Firebase.pushNotification({
          token: notificationToken?.token,
          notification: {
            title: 'RSK Recovery',
            body: 'Your RSK has been reset successfully',
          },
          data: {
            eventName: 'rskReset',
            status: 'completed',
          },
          userId: user_nanoid,
        })
      }

      reply.send({ success: true })
    },
  })
