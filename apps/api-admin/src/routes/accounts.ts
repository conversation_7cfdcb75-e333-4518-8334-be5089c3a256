import { adminAccountsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { createTransferRulesAdminTransaction } from 'repositories/src/paymentHandlers.js'
import {
  type ValidNetworks,
  createRiseId,
  dailyTransactionLimit,
  getAccountBalance,
  getConfigs,
  getMemberRoles,
  getMonthlyPaymentsVolume,
  getPaymentsByDayOverTwoWeeks,
  getPaymentsByWeek,
  getRiseAddress,
  getSenders,
  getTokensUsed,
  getWallets,
  transactionLimit,
  transactionLimitHash,
} from 'repositories/src/smartContracts.js'
import { getSettingsInfo } from 'repositories/src/smartContracts.js'
import assert from 'utils/src/common/assertHTTP.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/accounts/riseid/deploy'].post,
      handler: async ({ admin_auth, body: { network } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(hasRoles(admin_auth, ['admin', 'editor']), 'Unauthorized', '401')
        const riseWallets = await getWallets('txn')
        assert(riseWallets.length > 0, 'No wallets found', '500')
        const riseWallet = riseWallets[0]
        assert(riseWallet, 'No wallet found', '500')

        const riseDepositOwner = await getRiseAddress('rise_riseid_deposit')
        assert(!riseDepositOwner, 'Deposit owner already exists', '400')

        const address = await createRiseId({
          type: 'rise_riseid_deposit',
          owner_address: `0x${riseWallet.address.startsWith('0x') ? riseWallet.address.substring(2) : riseWallet.address}`,
          parent_account: null,
          network: network as ValidNetworks,
          purpose: 'rise',
        })

        const client = createInngestClient('dashboard')
        await client.send({
          name: 'dashboard/riseid.activate',
          data: {
            riseid: address,
            network: network ?? 'arbitrum',
          },
        })

        reply.send({
          success: true,
          data: address,
        })
      },
    })
    .route({
      ...routes['/admin/accounts/setTransferRules/:payhandler'].post,
      handler: async (
        {
          admin_auth,
          query: { network },
          params: { payhandler },
          body: { configuration },
        },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const id = await createTransferRulesAdminTransaction(
          admin_auth.userEmail,
          payhandler,
          network,
          configuration,
        )
        assert(id, 'Failed to create transaction', '500')
        reply.send({ success: true, data: { transaction_id: `${id}` } })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/paymentsByDay'].get,
      handler: async (
        { admin_auth, query: { network, payment_type }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const response = await getPaymentsByDayOverTwoWeeks(
          account,
          payment_type,
          network,
        )

        assert(response, 'No payments found', '404')
        type Day = {
          USD: string
          EUR: string
        }

        const days: Day[] = []

        const tokenMap = {
          '0xbdce8357F54962D47A2Ec1312c67B88762DC96f6': 'USD',
        }

        for (const day of response) {
          const currentDay = {
            USD: 0n,
            EUR: 0n,
          }

          for (const payment of day) {
            const tokenSymbol = tokenMap[
              payment[5] as '0xbdce8357F54962D47A2Ec1312c67B88762DC96f6'
            ] as 'EUR' | 'USD'
            currentDay[tokenSymbol] += payment[7]
          }

          days.push({
            USD: currentDay.USD.toString(),
            EUR: currentDay.EUR.toString(),
          })
        }
        reply.send({ success: true, data: days })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/settingsInfo'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const settingsInfo = await getSettingsInfo(account, network)

        const data = {
          accountType: settingsInfo[0],
          parentAccount: settingsInfo[1],
          sourceOfFunds: settingsInfo[2],
          hiddenRiseTokenTransfers: settingsInfo[3],
          sponsorAccount: settingsInfo[4],
          feeRecipient: settingsInfo[5],
        }

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/membersRoles'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const memberRoles = await getMemberRoles(account, network)
        const rolesMap = {
          '0': 'None',
          '1': 'Viewer',
          '2': 'Owner',
          '3': 'Payer',
          '4': 'Treasurer',
          '5': 'System',
        }

        const data = memberRoles.map((memberRole) => {
          return {
            account: memberRole[0],
            role:
              memberRole[1].toString() in rolesMap
                ? rolesMap[memberRole[1].toString() as keyof typeof rolesMap]
                : 'Unknown',
          }
        })

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/configs'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const data: {
          token: string
          amount: string
          transferType: string
          fixedOrPercent: string
          ramp: string
          source: string
          destination: string
          offChainReference: string
          data: string
        }[] = []

        const tokens = ['0x910581AE553698622b9b4f6041dFB53AD751e691']

        // Use the new getConfigs function that accepts multiple tokens
        const allConfigsData = await getConfigs(account, network, tokens)

        for (let i = 0; i < tokens.length; i++) {
          const token = tokens[i]
          const configData = allConfigsData[i]

          if (token && configData) {
            for (const config of configData) {
              data.push({
                token: token,
                amount: config[0].toString(),
                transferType: config[1].toString(),
                fixedOrPercent: config[2].toString(),
                ramp: config[3],
                source: config[4],
                destination: config[5],
                offChainReference: config[6],
                data: config[7],
              })
            }
          }
        }

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/getSenders'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const data = await getSenders(account, network)

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/getTokensUsed'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const data = await getTokensUsed(account, network)

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/transactionLimits'].get,
      handler: async (
        { admin_auth, query: { network }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const memberRoles = await getMemberRoles(account, network)

        const spenders = [...memberRoles.map((e) => e[0]), account]
        const tokens = ['0xbdce8357F54962D47A2Ec1312c67B88762DC96f6']

        type TransactionLimit = {
          spender: string
          token: string
          tx_limit: string
          daily_limit: string
        }

        const data: TransactionLimit[] = []
        for (const spender of spenders) {
          for (const token of tokens) {
            const hash = await transactionLimitHash(
              account,
              network,
              spender,
              token,
            )
            const tx_limit = await transactionLimit(account, network, hash)
            const daily_limit = await dailyTransactionLimit(
              account,
              network,
              hash,
            )
            data.push({
              spender,
              token,
              tx_limit: tx_limit.toString(),
              daily_limit: daily_limit.toString(),
            })
          }
        }

        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/getPaymentsByWeek'].get,
      handler: async (
        {
          admin_auth,
          query: { network, startTime, payment_type },
          params: { account },
        },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        type Payment = {
          id: string
          groupId: string
          payAtTime: string
          validMinutes: string
          payType: string
          token: string
          recipient: string
          amount: string
          data: string
        }

        const payments: Payment[] = []
        const response = await getPaymentsByWeek(
          account,
          payment_type,
          network,
          Number.parseInt(startTime),
        )

        assert(response, 'No payments found')

        for (const day of response) {
          const dayPayments = day.payments
          for (const payment of dayPayments) {
            payments.push({
              id: payment[0]?.toString(),
              groupId: payment[1]?.toString(),
              payAtTime: new Date(Number(payment[2]) * 1000).toISOString(),
              validMinutes: payment[3]?.toString(),
              payType: payment[4]?.toString(),
              token: payment[5]?.toString(),
              recipient: payment[6]?.toString(),
              amount: payment[7]?.toString(),
              data: payment[8]?.toString(),
            })
          }
        }

        reply.send({ success: true, data: payments })
      },
    })
    .route({
      ...routes['/admin/accounts/:payhandler/monthlyVolume'].get,
      handler: async (
        { admin_auth, query: { network }, params: { payhandler } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const data = await getMonthlyPaymentsVolume(payhandler, network)
        reply.send({ success: true, data })
      },
    })
    .route({
      ...routes['/admin/accounts/:account/balance'].get,
      handler: async (
        { admin_auth, query: { network, token }, params: { account } },
        reply,
      ) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
          'Unauthorized',
          '401',
        )
        const balance = await getAccountBalance(account, network, token)
        reply.send({ success: true, data: Number(balance) / 1e18 })
      },
    })
