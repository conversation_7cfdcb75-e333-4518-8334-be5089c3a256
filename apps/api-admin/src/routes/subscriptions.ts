import { adminSubscriptionsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { getSubscriptionsConfig } from 'repositories/src/smartContracts.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/subscriptions/config'].get,
    handler: async ({ admin_auth, query: { network, account } }, reply) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(
        hasRoles(admin_auth, ['admin', 'editor', 'Finance', 'Payroll-HR']),
        'Unauthorized',
        '401',
      )
      const response = await getSubscriptionsConfig(account, network)
      const data = {
        token: response[0],
        percentDiscount: Number(response[1]) / 100,
        rate: Number(response[2]) / 1e18,
      }
      reply.send({ success: true, data })
    },
  })
