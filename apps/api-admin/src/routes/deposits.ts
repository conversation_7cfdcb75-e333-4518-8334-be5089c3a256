import { adminDepositsRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { hasRoles } from 'repositories/src/admin.js'
import { insertDepositAndBankAccountSeen } from 'repositories/src/depositManual.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/deposits'].post,
    handler: async (
      { admin_auth, body: { entity_nanoid, deposit, bank_account_seen } },
      reply,
    ) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(
        hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
        'Unauthorized',
        '401',
      )
      const id = await db.transaction().execute(async (trx) => {
        return await insertDepositAndBankAccountSeen({
          deposit: {
            ...deposit,
            entity_nanoid,
          },
          bank_account_seen,
          db: trx,
        })
      })
      assert(id, 'Failed to create transaction', '500')
      reply.send({ success: true, data: Number.parseInt(id.toString()) })
    },
  })
