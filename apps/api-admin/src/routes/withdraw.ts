import { adminWithdrawRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { getBytes, verifyMessage } from 'utils/src/blockchain/helpers.js'
import assert from 'utils/src/common/assertHTTP.js'
import { retryTransfer } from '../useCases/retryTransfer.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/withdraw/retry'].post,
    handler: async (
      { admin_auth, body: { withdraw_id, message, signature, signer_address } },
      reply,
    ) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(
        hasRoles(admin_auth, ['admin', 'editor', 'Finance']),
        'Unauthorized',
        '401',
      )

      //  verify message
      const recovered = verifyMessage(getBytes(message), signature)
      const isValid = recovered.toLowerCase() === signer_address.toLowerCase()
      assert(isValid, 'Invalid message signature')

      assert(withdraw_id, 'withdraw_id is missing')
      await retryTransfer(withdraw_id)
      reply.send({
        success: true,
      })
    },
  })
