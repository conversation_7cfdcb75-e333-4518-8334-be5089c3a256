import { adminCompaniesRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { hasRoles } from 'repositories/src/admin.js'
import {
  getCompaniesBySearch,
  getCompanyByNanoid,
} from 'repositories/src/companies.js'
import { getFundInstructions } from 'repositories/src/fundInstructions.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app
    .route({
      ...routes['/admin/companies'].get,
      handler: async ({ admin_auth, query: { search } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const companies = await getCompaniesBySearch(search)
        reply.send({ success: true, data: companies })
      },
    })
    .route({
      ...routes['/admin/companies/:company_nanoid'].get,
      handler: async ({ admin_auth, params: { company_nanoid } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const company = await getCompanyByNanoid(company_nanoid)
        assert(company, `Company ${company_nanoid} not found`, '404')
        reply.send({ success: true, data: company })
      },
    })
    .route({
      ...routes['/admin/companies/:company_nanoid/routefusion/fund-account']
        .get,
      handler: async ({ admin_auth, params: { company_nanoid } }, reply) => {
        assert(admin_auth, 'Unauthorized', '401')
        assert(
          hasRoles(admin_auth, [
            'admin',
            'editor',
            'Compliance',
            'Customer Support',
            'Sales',
            'Marketing',
            'Payroll-HR',
            'Finance',
          ]),
          'Unauthorized',
          '401',
        )
        const instructions = await db.transaction().execute(async (trx) => {
          return await getFundInstructions({
            nanoid: company_nanoid,
            provider: 'routefusion',
            db: trx,
          })
        })
        reply.send({ success: true, data: instructions })
      },
    })
