import { adminMastertaxRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type { CompanyAddressType } from '@riseworks/contracts/src/codegen/zod/mastertax/company_address.js'
import type { CompanyMailingAddressType } from '@riseworks/contracts/src/codegen/zod/mastertax/company_mailing_address.js'
import type { CompanyTaxType } from '@riseworks/contracts/src/codegen/zod/mastertax/company_tax.js'
import type { FileHeaderType } from '@riseworks/contracts/src/codegen/zod/mastertax/file_header.js'
import type { FileTrailerType } from '@riseworks/contracts/src/codegen/zod/mastertax/file_trailer.js'
import type { PayrollHeaderType } from '@riseworks/contracts/src/codegen/zod/mastertax/payroll_header.js'
import type { PayrollTaxDetailType } from '@riseworks/contracts/src/codegen/zod/mastertax/payroll_tax_detail.js'
import type { PayrollTrailerType } from '@riseworks/contracts/src/codegen/zod/mastertax/payroll_trailer.js'
import type { App } from 'backend/src/index.js'
import { hasRoles } from 'repositories/src/admin.js'
import { getFullCompanyData } from 'repositories/src/companies.js'
import {
  getCafeteriaPlanBenefits,
  getCompanyTaxData,
  getFullMasterTaxReport,
  upsertCompanyAddress,
  upsertCompanyMailingAddress,
  upsertCompanyTax,
  upsertFileHeader,
  upsertFileTrailer,
  upsertPayrollHeader,
  upsertPayrollTaxDetails,
  upsertPayrollTrailer,
} from 'repositories/src/masterTax.js'
import {
  companyNanoidToPayrollCode,
  getStateAbbreviation,
  payCycleFromDateSymmetry,
  processingTimeFromDate,
  referenceEINRules,
  symmetryToMasterTaxCodeMapper,
} from 'repositories/src/masterTaxUtils.js'
import { taxInfoByUniqueTaxId } from 'repositories/src/symmetry.js'
import assert from 'utils/src/common/assertHTTP.js'

export default (app: App): App =>
  app.route({
    ...routes['/admin/mastertax'].post,
    handler: async (
      {
        body: { company_nanoid, check_date, payroll_description, naic },
        admin_auth,
      },
      reply,
    ) => {
      assert(admin_auth, 'Unauthorized', '401')
      assert(
        hasRoles(admin_auth, ['admin', 'editor', 'Payroll-HR', 'Finance']),
        'Unauthorized',
        '401',
      )
      // TODO: Implement the logic to create a new mastertax PTS report in DB
      // helpful data:
      const company_data = await getFullCompanyData(company_nanoid)
      assert(company_data, 'Company not found', '404')

      // convert check_date to date object
      const check_date_obj = new Date(check_date)
      // get pay cycle from check_date
      const pay_cycle = payCycleFromDateSymmetry(check_date_obj)

      // file header
      // convert the company_nanoid to a payroll code
      const payroll_code = companyNanoidToPayrollCode(company_nanoid)
      // get processing date
      const process_date = new Date()
      const process_time = processingTimeFromDate(process_date)
      const file_header: Partial<FileHeaderType> = {
        payroll_code,
        check_date: check_date_obj,
        process_date,
        process_time,
      }
      const upsert_file_header = await upsertFileHeader(file_header)
      assert(upsert_file_header, 'File header not inserted or updated', '404')
      assert(
        upsert_file_header.length > 0,
        'File header not inserted or updated',
        '404',
      )
      // end file header

      // payroll header
      const tax_liabilities = 'yes'
      const company_setup = 'yes'
      // const bank_setup = 'yes' we already have this in the system, so should not be necessary
      const company_start_date = new Date('2025-03-01') // this is the date we start using mastertax to pay taxes
      const company_status = 'active'
      const company_name = company_data.name.slice(0, 40)
      const service_level = 'full_service'
      const fein = company_data.private_data.tax_id.replace(/-/g, '')
      const fein_type = fein ? 'registered' : 'applied_for'

      // // FUTA Exempt Categories
      // const cafeteria_plan_benefits = 'applicable'
      // const group_term_life_insurance = 'applicable'
      // const dependent_care_assistance = 'applicable'
      // const business_expense_reimbursement = 'applicable'
      // const employer_contribution_401k = 'applicable'
      // const employer_contribution_sep_ira = 'applicable'
      // const employer_contribution_simple = 'applicable'
      // const accident_health_insurance_premiums = 'applicable'
      // const sick_pay = 'applicable'
      // const workers_compensation = 'not_applicable'
      // const payments_to_family_employees = 'not_applicable'
      // const payments_to_hospital_interns = 'not_applicable'
      // const payments_to_hospital_patients = 'not_applicable'
      // const payments_to_general_partnership = 'applicable'
      // const state_govt_employee_salaries = 'not_applicable'
      // const payments_to_election_workers = 'not_applicable'
      // const supplemental_unemployment_benefits = 'not_applicable'
      // const nonqualified_deferred_comp = 'not_applicable'
      // const meals_furnished_in_kind = 'not_applicable'
      // const qualified_moving_expense = 'applicable'
      // const hsa = 'applicable'
      // const exempt_501c3_organization = 'not_applicable'
      // const employee_stock_purchase_plan = 'applicable'
      // const non_taxable_fringe_payments = 'applicable'
      // const public_transportation_non_tax = 'applicable'
      // const wc_housing_employment_condition = 'not_applicable'
      // const chaplain_housing = 'not_applicable'
      // const clergy_housing_poverty_vow = 'not_applicable'
      // const foreign_source_income = 'not_applicable'
      // const student_exempt = 'not_applicable'

      // const agent_client_type = 'none'
      // const filer_944 = 'yes'
      // const quarterly_wage_reporting = 'yes'
      // const year_end_employee_filing = 'yes'
      // const cash_service_level = 'full'
      // const worksite_reporting = 'yes'
      // const wage_attachment_flag = 'yes'

      const short_reporting_payroll_code = payroll_code
      // // const company_effective_date need to talk to Kai and then Vni and Paulo
      // // naic_code: passed in for now, or defaulted to RISE NAIC code, but need to capture this
      // // for each company going forward.
      // const naics_code = naic ?? '541214'
      const reporting_payroll_code = company_nanoid

      // get cafeteria plan benefits
      const cafeteria_plan_benefits =
        await getCafeteriaPlanBenefits(company_nanoid)

      const payroll_header: Partial<PayrollHeaderType> = {
        payroll_code,
        check_date: check_date_obj,
        tax_liabilities,
        company_setup,
        payroll_description,
        company_start_date,
        company_status,
        company_name,
        service_level,
        fein_type,
        fein,
        ...cafeteria_plan_benefits,
        // cafeteria_plan_benefits,
        // group_term_life_insurance,
        // dependent_care_assistance,
        // business_expense_reimbursement,
        // employer_contribution_401k,
        // employer_contribution_sep_ira,
        // employer_contribution_simple,
        // accident_health_insurance_premiums,
        // sick_pay,
        // workers_compensation,
        // payments_to_family_employees,
        // payments_to_hospital_interns,
        // payments_to_hospital_patients,
        // payments_to_general_partnership,
        // state_govt_employee_salaries,
        // payments_to_election_workers,
        // supplemental_unemployment_benefits,
        // nonqualified_deferred_comp,
        // meals_furnished_in_kind,
        // qualified_moving_expense,
        // hsa,
        // exempt_501c3_organization,
        // employee_stock_purchase_plan,
        // non_taxable_fringe_payments,
        // public_transportation_non_tax,
        // wc_housing_employment_condition,
        // chaplain_housing,
        // clergy_housing_poverty_vow,
        // foreign_source_income,
        // student_exempt,
        // agent_client_type,
        // filer_944,
        // quarterly_wage_reporting,
        // year_end_employee_filing,
        // cash_service_level,
        // worksite_reporting,
        // wage_attachment_flag,
        short_reporting_payroll_code,
        // naics_code,
        reporting_payroll_code,
      }
      const upsert_payroll_header = await upsertPayrollHeader(payroll_header)
      assert(
        upsert_payroll_header,
        'Payroll header not inserted or updated',
        '404',
      )
      assert(
        upsert_payroll_header.length > 0,
        'Payroll header not inserted or updated',
        '404',
      )
      // end payroll header

      // company disbursement banks
      // NOTE: not currently needed
      // end disbursement banks

      // company cash care banks
      // NOTE: not currently needed
      // end cash care banks

      // company general ledger
      // NOTE from Ben:
      // while we are small and just RWI and EOR/EORCA I think we can punt,
      // but before PPS we should be fully automated if we can
      // end general ledger

      // secondary company groups
      // NOTE: not currently needed
      // end secondary company groups

      // company address
      const company_dba = company_data.doing_business_as
        ? company_data.doing_business_as.slice(0, 40)
        : company_name
      const address_line_1 = company_data.address.line_1.slice(0, 40)
      const address_line_2 = company_data.address.line_2.slice(0, 40)
      const city = company_data.address.city.slice(0, 25)
      const state_code = getStateAbbreviation(company_data.address.state)
      const zip_code = company_data.address.zip_code
      const country_code = company_data.address.country
      assert(country_code === 'US', 'Country not supported', '400')

      assert(company_data.admin_contact, 'Company contact not found', '404')

      const company_contact_full_name = company_data.admin_contact.fullname
      // split the name into first, middle, and last
      const company_contact_name = company_contact_full_name.split(' ')
      const first_name = company_contact_name[0]
      const middle_initial = company_contact_name[1]?.charAt(0) ?? ''
      const last_name = company_contact_name[2]

      const area_code = company_data.admin_contact.phone.slice(0, 3)
      const telephone_number = company_data.admin_contact.phone.slice(3, 6)
      const extension = company_data.admin_contact.phone.slice(6, 10) ?? ''
      const email_address = company_data.admin_contact.email.slice(0, 40)
      const company_address: Partial<CompanyAddressType> = {
        payroll_code,
        check_date: check_date_obj,
        company_dba,
        address_line_1,
        address_line_2,
        city,
        state_code,
        zip_code,
        country_code,
        first_name,
        middle_initial,
        last_name,
        area_code,
        telephone_number,
        extension,
        email_address,
      }
      const upsert_company_address = await upsertCompanyAddress(company_address)
      assert(
        upsert_company_address,
        'Company address not inserted or updated',
        '404',
      )
      assert(
        upsert_company_address.length > 0,
        'Company address not inserted or updated',
        '404',
      )
      // end company address

      // company mailing address
      // NOTE: currently not collected, main difference from company address
      // is the route code
      const company_mailing_address: Partial<CompanyMailingAddressType> = {
        payroll_code,
        check_date: check_date_obj,
        company_dba,
        address_line_1,
        address_line_2,
        city,
        state_code,
        zip_code,
        country_code,
        first_name,
        middle_initial,
        last_name,
        area_code,
        telephone_number,
        extension,
        email_address,
      }
      const upsert_company_mailing_address = await upsertCompanyMailingAddress(
        company_mailing_address,
      )
      assert(
        upsert_company_mailing_address,
        'Company mailing address not inserted or updated',
        '404',
      )
      assert(
        upsert_company_mailing_address.length > 0,
        'Company mailing address not inserted or updated',
        '404',
      )
      // end company mailing address

      // get all the employees from the company
      const company_tax_data = await getCompanyTaxData(
        company_nanoid,
        check_date_obj,
      )
      assert(company_tax_data, 'Company tax data not found', '404')
      // get tax info for each unique tax id
      const tax_id_search = company_tax_data.taxes.map((tax) => {
        return { taxIDSearchString: tax.unique_tax_id, payDate: pay_cycle }
      })

      const tax_info_by_unique_tax_id =
        await taxInfoByUniqueTaxId(tax_id_search)

      // company tax
      const company_taxes = company_tax_data.taxes.map((tax) => {
        const tax_info = tax_info_by_unique_tax_id.find(
          (info) => info.uniqueTaxID === tax.unique_tax_id,
        )
        assert(tax_info, 'Tax info not found', '404')

        const company_tax_info: Partial<CompanyTaxType> = {
          payroll_code,
          tax_code: symmetryToMasterTaxCodeMapper(tax.unique_tax_id),
          check_date: check_date_obj,
          company_tax_status: 'active',
          ein_type: 'applied_for',
          // ein: each state might have it's own ein or use the federal, need to figure this out.
          tax_rate: tax_info.rate.toString(),
          // the payment frequency references the the schedule on which taxes must be paid, not the pay schedule
          payment_method: 'check', // double check this value with Ben
          // if eft we need to store the password as a secret if its needed
          // county_code: need to implement these rules.
        }

        const reference_ein = referenceEINRules(tax.unique_tax_id)
        if (reference_ein) {
          company_tax_info.reference_ein = reference_ein
        }
        return company_tax_info
      })

      const upsert_company_tax = await upsertCompanyTax(company_taxes)
      assert(upsert_company_tax, 'Company tax not inserted or updated', '404')
      assert(
        upsert_company_tax.length > 0,
        'Company tax not inserted or updated',
        '404',
      )
      // end company tax

      // company workers comp codes
      // NOTE: this is only needed for Washington and Wyoming, skipping for now
      // end company workers comp codes

      // payroll tax detail
      let tax_total = 0
      const company_tax_detail = company_tax_data.taxes.map((tax) => {
        tax_total += tax.total_tax_amount
        const tax_info = tax_info_by_unique_tax_id.find(
          (info) => info.uniqueTaxID === tax.unique_tax_id,
        )
        assert(tax_info, 'Tax info not found', '404')
        const payroll_tax_detail: Partial<PayrollTaxDetailType> = {
          payroll_code,
          tax_code: symmetryToMasterTaxCodeMapper(tax.unique_tax_id),
          check_date: check_date_obj,
          tax_rate: tax_info.rate.toString(),
          tax: tax.total_tax_amount.toString(),
          taxable_wages: tax.total_gross_wages.toString(),
          employee_count: company_tax_data.employee_count,
          employee_count_sign: 'positive',
        }

        return payroll_tax_detail
      })
      const upsert_payroll_tax_detail =
        await upsertPayrollTaxDetails(company_tax_detail)
      assert(
        upsert_payroll_tax_detail,
        'Payroll tax detail not inserted or updated',
        '404',
      )
      assert(
        upsert_payroll_tax_detail.length > 0,
        'Payroll tax detail not inserted or updated',
        '404',
      )
      // end payroll tax detail

      // payroll tax user defined fields
      // NOTE: not currently needed
      // end payroll tax user defined fields

      // payroll trailer
      // record count is the number of records in the payroll section, including, header and trailer
      // 1  - payroll header
      // 1 - company address
      // 1 - company mailing address
      // n - company taxes
      // n  - payroll tax detail
      // 1  - payroll trailer
      const payroll_record_count =
        1 + 1 + 1 + company_taxes.length + company_tax_detail.length + 1
      const payroll_trailer: Partial<PayrollTrailerType> = {
        payroll_code,
        check_date: check_date_obj,
        record_count: payroll_record_count,
        tax_total: tax_total.toString(),
      }

      const upsert_payroll_trailer = await upsertPayrollTrailer(payroll_trailer)
      assert(
        upsert_payroll_trailer,
        'Payroll trailer not inserted or updated',
        '404',
      )
      assert(
        upsert_payroll_trailer.length > 0,
        'Payroll trailer not inserted or updated',
        '404',
      )
      // end payroll trailer

      // file trailer
      // record count for the file section is all the record count in the payroll section, plus the file header and file trailer
      // 1 - file header
      // 1 - file trailer
      const file_record_count = payroll_record_count + 1 + 1
      const file_trailer: Partial<FileTrailerType> = {
        payroll_code,
        check_date: check_date_obj,
        record_count: file_record_count,
        tax_total: tax_total.toString(),
      }
      const upsert_file_trailer = await upsertFileTrailer(file_trailer)
      assert(upsert_file_trailer, 'File trailer not inserted or updated', '404')
      assert(
        upsert_file_trailer.length > 0,
        'File trailer not inserted or updated',
        '404',
      )
      // end file trailer

      // finally generate the report
      const report = await getFullMasterTaxReport(payroll_code, check_date_obj)
      //redundant, but needed to ensure the report is encoded to latin1/ISO-8859-1
      const data = Buffer.from(report, 'latin1')

      reply
        .header('Content-Type', 'text/plain; charset=ISO-8859-1') // Set the Content-Type to text/plain
        .send(data) // Send the report content as the response
    },
  })
