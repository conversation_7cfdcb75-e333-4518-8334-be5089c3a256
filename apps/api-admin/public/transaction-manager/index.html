<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/daisyui@2.43.2/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="/admin/public/scripts/utils.js"></script>
    <script src="/admin/public/scripts/wallet-init.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/3.4.21/vue.global.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ethers/5.7.2/ethers.umd.min.js"
        integrity="sha512-FDcVY+g7vc5CXANbrTSg1K5qLyriCsGDYCE02Li1tXEYdNQPvLPHNE+rT2Mjei8N7fZbe0WLhw27j2SrGRpdMg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/@metamask/detect-provider@1.2.0/dist/detect-provider.min.js"
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <title>Transaction Manager</title>
    <script>
        window.RETOOL_ACCT = '93566d60-821d-11ed-b86c-df4469783dba'
    </script>
</head>

<body>
    <div id="app" class="border-b flex flex-col gap-2 border-gray-200 bg-white px-4 py-5 sm:px-6">
        <h3 class="text-4xl font-medium text-gray-900">Transaction Manager</h3>
        <div class="text-md py-1 px-2 uppercase font-semibold rounded bg-blue-200 p-2"
            v-for="key in Object.keys(params)" key="key">{{ key }}: <span class="font-mono font-thin text-sm">{{
                params[key] }}</span></div>
        <button class="btn btn-primary" @click="sendTransaction">Send Transaction</button>
    </div>
</body>
<script>
    const { createApp } = Vue
    createApp({
        data() {
            return {
                id: null,
                params: {
                    to: '0x00000000000000000000000000',
                    data: 'example data'
                }
            }
        },
        methods: {
            async sendTransaction() {
                await window.wallet.signer.sendTransaction({
                    to: this.params.to,
                    data: this.params.calldata
                }).then(tx => {
                    console.log('Transaction:', tx)
                    alert(tx.hash)
                }).catch(error => {
                    console.error('Error:', error)
                    alert(error.message)
                })
            }
        },

        async created() {
            const urlParams = new URLSearchParams(window.location.search)
            this.id = urlParams.get('id')

            await window.rise.auth(`/admin/public/transaction-manager/${window.location.search}`)
            await window.wallet.init()


            const response = await fetch(`/admin/blockchain_transactions/${this.id}`).then(j => j.json());

            if (!response.success) {
                alert('Failed fetching transaction')
                return
            }

            this.params = response.data

        },
    }).mount('#app');
</script>

</html>