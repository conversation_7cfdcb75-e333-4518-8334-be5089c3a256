/* eslint-disable */

window.contracts = {}
window.abis = {}
window.wallet = {
  signer: null,
  walletAddress: null,
  networks: {},

  async init() {
    if (!(window.ethereum || window.ethers)) {
      const txt = 'Ethers or MetaMask not found'
      alert(txt)
      return console.warn(txt)
    }
    const _walletProvider = await window.detectEthereumProvider()
    const provider = new window.ethers.providers.Web3Provider(_walletProvider)
    const [walletAddress] = await provider.send('eth_requestAccounts', [])
    if (!walletAddress) return console.warn('Please connect a wallet')
    const chainId = await _walletProvider.request({ method: 'eth_chainId' })
    //const { arbitrum } = window.wallet.networks = await fetch(`/blockchain/networks`).then(j => j.json())
    const arbitrum = {
      chainIdHex: '0x66EEE',
      chainName: 'Arbitrum Sepolia',
      nativeCurrency: 'ETH',
      rpcUrls: ['https://sepolia-rollup.arbitrum.io/rpc'],
    }

    if (chainId.toLowerCase() !== arbitrum.chainIdHex.toLowerCase()) {
      try {
        await _walletProvider.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: arbitrum.chainIdHex }],
        })
        // window.location.reload()
      } catch (switchError) {
        // This error code indicates that the chain has not been added to MetaMask.
        if (switchError.code === 4902) {
          try {
            await _walletProvider.request({
              method: 'wallet_addEthereumChain',
              params: [
                {
                  chainId: arbitrum.chainIdHex.toLowerCase(),
                  chainName: arbitrum.chainName,
                  nativeCurrency: arbitrum.nativeCurrency,
                  rpcUrls: [arbitrum.rpcUrl] /* ... */,
                },
              ],
            })
            // window.location.reload()
          } catch (addError) {
            console.error(addError)
            // handle "add" error
          }
        } else {
          throw switchError
        }
      }
    }

    window.wallet.walletAddress = walletAddress
    console.info('walletAddress:', walletAddress)
    window.wallet.signer = await provider.getSigner()

    //window.contracts = await fetch(`/blockchain/contracts`).then(j => j.json())
    //window.abis = await fetch(`/blockchain/abis`).then(j => j.json())
    console.log(window.contracts, window.abis)
  },

  arbAddressTable: '******************************************',
}
