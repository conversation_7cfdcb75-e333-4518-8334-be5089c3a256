window.rise = {
  getURLParams,
  parseCookie,

  async auth(appPath) {
    const _c = parseCookie(document.cookie)
    if (!_c['rise-support-auth']) {
      sessionStorage.setItem(
        'support-admin-redirect-path',
        appPath || window.location.pathname,
      )
      window.location = '/admin/auth?redirect_app=true'
      return new Promise((resolve) => setTimeout(resolve, 5000))
    }
  },

  async authRedirect(code) {
    if (code) {
      await fetch(`/admin/callback?code=${code}&redirect_app=true`)
      const _c = parseCookie(document.cookie)
      if (_c['rise-support-auth']) {
        const _p = sessionStorage.getItem('support-admin-redirect-path')
        if (_p) {
          sessionStorage.removeItem('support-admin-redirect-path')
          window.location = _p
        }
      }
    }
  },
}

function getURLParams() {
  return new Proxy(new URLSearchParams(window.location.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  })
}

// https://www.geekstrick.com/snippets/how-to-parse-cookies-in-javascript/
function parseCookie(str) {
  return str
    .split(';')
    .map((v) => v.split('='))
    .reduce((acc, v) => {
      if (v?.length === 2)
        acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim())
      return acc
    }, {})
}
