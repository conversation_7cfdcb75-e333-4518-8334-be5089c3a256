import {
  type BitcoinAddress,
  type BitcoinOutput,
  type OwnerId,
  type TransformedBitcoinOutput,
  bitcoinOutputTransformer,
  segwitRegex,
} from '@riseworks/contracts/src/bitcoin.js'
import type { SelectableChainflipSwaps as SelectableSwap } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { eventDataSchema } from '@riseworks/contracts/src/events/transaction_events/bitcoin.new_outputs.js'
import {
  getPendingSwaps,
  processNewSwapChannelOutputs,
} from 'repositories/src/chainflip.js'
import { getBitcoinEntityDepositAccountIds } from 'repositories/src/entityDepositAccounts.js'
import { getEventData, setEventData } from 'repositories/src/events.js'
import { bitcoinOwnerTransformer } from 'utils/src/blockchain/bitcoin/account.js'
import { is } from 'utils/src/common/is.js'
import { createInngestClient } from 'utils/src/inngest/index.js'

const client = createInngestClient('transaction_events')

export default client.createFunction(
  {
    id: 'transaction_events-bitcoin-new-outputs',
    name: 'transaction_events/bitcoin.new_outputs',
    batchEvents: {
      maxSize: 100, // Each event contains up to 2500 consolidated outputs (maximum of 2 per address), so 100 allows at least 125,000 outputs.
      timeout: '1s',
    },
    retries: 2, // 3 tries total
  },
  [{ event: 'transaction_events/bitcoin.new_outputs' }],
  async ({ events, step, logger }) => {
    const { lastProcessedBlockHeight } = await step
      .run('get-last-processed-block-height', async () => {
        const latestEventData = await getEventData(
          'transaction_events-bitcoin-new-outputs',
        )
        if (!latestEventData) {
          return {
            warning: 'No event data found',
            lastProcessedBlockHeight: -1,
          }
        }
        const lastProcessedBlockHeight = eventDataSchema.parse(
          latestEventData.data,
        ).lastProcessedBlockHeight
        if (lastProcessedBlockHeight === -1) {
          return {
            warning:
              'Last saved processed block height is -1: this should only ever happen during initial run or after a wipe of event_data',
            lastProcessedBlockHeight,
          }
        }
        return { lastProcessedBlockHeight }
      })
      .catch((error) => {
        return {
          error: `Error getting last processed block height: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          lastProcessedBlockHeight: -1,
        }
      })
    const { highestBlockHeight } = await step
      .run('save-highest-block-height', async () => {
        const highestBlockHeight = Math.max(
          ...events.map((event) => event.data.highestBlockHeight ?? -1),
          lastProcessedBlockHeight,
        )
        if (lastProcessedBlockHeight === highestBlockHeight) {
          return {
            warning: `Last processed block height (${lastProcessedBlockHeight}) is equal to the highest block height (${highestBlockHeight}). If you get a lot of these, it means we are receiving duplicate events.`,
            highestBlockHeight,
          }
        }
        await setEventData(
          'transaction_events-bitcoin-new-outputs',
          eventDataSchema.parse({
            lastProcessedBlockHeight: highestBlockHeight,
            // TODO: Figure out why updated_at is not being set automatically, then remove this
            lastUpdated: new Date(),
          }),
        )
        return { highestBlockHeight }
      })
      .catch((error) => {
        return {
          error: `Error saving highest block height: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          highestBlockHeight: -1,
        }
      })
    if (highestBlockHeight === -1) {
      return {
        warning:
          'Highest block height is not available: this indicates an empty payload with no prior event data',
      }
    }
    const outputCount = events.reduce(
      (total, event) => total + (event.data.outputs?.length ?? 0),
      0,
    )
    if (!outputCount) {
      return {
        warning: 'No outputs found in event data',
      }
    }
    const { pendingSwaps } = await step
      .run('get-pending-swaps', async () => {
        // Can't directly return the Promise here for some reason -- something to do with Inngest serialization?
        const pendingSwaps = await getPendingSwaps()
        return { pendingSwaps }
      })
      .catch((error) => {
        return {
          error: `Error getting pending swaps: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          pendingSwaps: [] as SelectableSwap[],
        }
      })
    const { swapChannelOutputs, possibleRiseOutputs } = await step
      .run('filter-outputs', async () => {
        const swapAddresses = new Set<BitcoinAddress>(
          pendingSwaps.map((swap) => swap.channel_address),
        )
        const swapChannelOutputs = new Set<BitcoinOutput>()
        const possibleRiseOutputs = new Set<BitcoinOutput>()
        for (const event of events) {
          const { outputs } = event.data
          if (!outputs?.length) {
            logger.warn('No outputs found in event')
            continue
          }
          for (const outputString of outputs) {
            const output = bitcoinOutputTransformer.parse(outputString)
            if (swapAddresses.has(output.address)) {
              if (output.outputType === 'deposit') {
                swapChannelOutputs.add(outputString)
              }
              // If the above condition is not met, this is a matched swap payment input, so we don't need to do any processing.
              continue
            }
            if (is(output.address, segwitRegex)) {
              possibleRiseOutputs.add(outputString)
            }
          }
        }
        return {
          swapChannelOutputs: Array.from(swapChannelOutputs.values()),
          possibleRiseOutputs: Array.from(possibleRiseOutputs.values()),
        }
      })
      .catch((error) => {
        return {
          error: `Error filtering outputs: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          swapChannelOutputs: [] as BitcoinOutput[],
          possibleRiseOutputs: [] as BitcoinOutput[],
        }
      })
    const { updatedSwaps } = await step
      .run('update-pending-swaps', async () => {
        if (!pendingSwaps.length) {
          return {
            warning: 'No pending swaps found -- nothing to update',
            expectedUpdatedSwaps: [] as SelectableSwap[],
            unexpectedUpdatedSwaps: [] as SelectableSwap[],
            updatedSwaps: [] as SelectableSwap[],
          }
        }
        const { expectedUpdatedSwaps, unexpectedUpdatedSwaps } =
          await processNewSwapChannelOutputs(swapChannelOutputs)
        return {
          expectedUpdatedSwaps,
          unexpectedUpdatedSwaps,
          updatedSwaps: [...expectedUpdatedSwaps, ...unexpectedUpdatedSwaps],
        }
      })
      .catch((error) => {
        return {
          error: `Error updating pending swaps: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          updatedSwaps: [] as SelectableSwap[],
        }
      })
    const updatedSwapChannelIds = updatedSwaps.map((swap) => swap.channel_id)
    await step
      .run('save-updated-swaps', async () => {
        if (!updatedSwapChannelIds.length) {
          return {
            warning: 'No updated swaps found -- nothing to save',
          }
        }
        await setEventData(
          'transaction_events-bitcoin-new-outputs',
          eventDataSchema.parse({
            updatedSwaps: updatedSwapChannelIds,
            lastProcessedBlockHeight: highestBlockHeight,
            // TODO: Figure out why updated_at is not being set automatically, then remove this
            lastUpdated: new Date(),
          }),
        )
        return await getEventData('transaction_events-bitcoin-new-outputs')
      })
      .catch((error) => {
        return {
          error: `Error saving updated swaps: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
        }
      })
    if (!possibleRiseOutputs.length) {
      return {
        warning: 'No possible Rise outputs found -- finishing early',
        updatedSwaps: updatedSwapChannelIds,
      }
    }
    const { validatedRiseOwnerIdsWithDeposits, validatedRiseOutputs } =
      await step
        .run('validate-rise-outputs', async () => {
          const bitcoinOwnerIds = await getBitcoinEntityDepositAccountIds()
          const bitcoinOwners = bitcoinOwnerIds.map((ownerId) =>
            bitcoinOwnerTransformer.parse(ownerId),
          )
          const riseAddresses = new Set(
            bitcoinOwners.flatMap((transformedOwner) => [
              transformedOwner.depositAddress,
              transformedOwner.changeAddress,
            ]),
          )
          const validatedRiseOutputs = possibleRiseOutputs
            .map((outputString) => {
              const output = bitcoinOutputTransformer.parse(outputString)
              if (riseAddresses.has(output.address)) return output
            })
            .filter((output) => output !== undefined)
          const validatedRiseOwnerIdsWithDeposits = new Set(
            Array.from(validatedRiseOutputs)
              .map(({ address, outputType }) =>
                outputType === 'deposit'
                  ? bitcoinOwnerIds.find((ownerId) => ownerId.includes(address))
                  : undefined,
              )
              .filter((ownerId) => ownerId !== undefined),
          )
          return {
            riseAddresses: Array.from(riseAddresses),
            validatedRiseOutputs,
            validatedRiseOwnerIdsWithDeposits: Array.from(
              validatedRiseOwnerIdsWithDeposits,
            ),
          }
        })
        .catch((error) => {
          return {
            error: `Error validating Rise outputs: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
            riseAddresses: [] as BitcoinAddress[],
            validatedRiseOutputs: [] as TransformedBitcoinOutput[],
            validatedRiseOwnerIdsWithDeposits: [] as OwnerId[],
          }
        })
    if (!validatedRiseOwnerIdsWithDeposits.length) {
      return {
        warning: 'No pending new swap owner IDs found -- nothing to save',
        updatedSwaps: updatedSwapChannelIds,
      }
    }
    await step
      .run('save-pending-new-swap-owner-ids', async () => {
        await setEventData(
          'transaction_events-bitcoin-new-outputs',
          eventDataSchema.parse({
            updatedSwaps: updatedSwapChannelIds,
            pendingNewSwapOwnerIds: validatedRiseOwnerIdsWithDeposits,
            lastProcessedBlockHeight: highestBlockHeight,
            // TODO: Figure out why updated_at is not being set automatically, then remove this
            lastUpdated: new Date(),
          }),
        )
        return await getEventData('transaction_events-bitcoin-new-outputs')
      })
      .catch((error) => {
        return {
          error: `Error saving pending new swap owner IDs: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
        }
      })
    if (
      validatedRiseOutputs.some((output) => output.outputType === 'payment')
    ) {
      return {
        error:
          'Found unexpected payment outputs (i.e. not matched to a swap). This could indicate a bug in the swap creation logic, or a leak of our private keys. Please investigate.',
        updatedSwaps: updatedSwapChannelIds,
        pendingNewSwapOwnerIds: validatedRiseOwnerIdsWithDeposits,
      }
    }
    return await step.sendEvent(
      'ramps/bitcoin.new_deposit',
      validatedRiseOwnerIdsWithDeposits.map((ownerId) => ({
        name: 'ramps/bitcoin.new_deposit',
        data: {
          ownerId,
        },
      })),
    )
  },
)
