import type { BitcoinOutput } from '@riseworks/contracts/src/bitcoin.js'
import { eventDataSchema } from '@riseworks/contracts/src/events/transaction_events/bitcoin.new_outputs.js'
import { getFallbackOutputs } from 'repositories/src/bitcoin.js'
import { getEventData } from 'repositories/src/events.js'
import {
  createInngestClient,
  disableCronInLocalEnv,
} from 'utils/src/inngest/index.js'

const client = createInngestClient('transaction_events')

const splitIntoBatches = (outputs: BitcoinOutput[], maxBatchSize = 2500) => {
  const batches: BitcoinOutput[][] = []
  for (let i = 0; i < outputs.length; i += maxBatchSize) {
    batches.push(outputs.slice(i, i + maxBatchSize))
  }
  return batches
}

export default client.createFunction(
  {
    id: 'transaction_events-bitcoin-new-outputs-fallback',
    name: 'transaction_events/bitcoin.new_outputs_fallback',
    retries: 0,
  },
  disableCronInLocalEnv([{ cron: 'TZ=America/New_York */5 * * * *' }]),
  async ({ step }) => {
    const { minutesSinceLastEvent, lastProcessedBlockHeight } = await step
      .run('parse-latest-event-data', async () => {
        const latestEventData = await getEventData(
          'transaction_events-bitcoin-new-outputs',
        )
        if (!latestEventData) {
          return {
            warning: 'No event data found',
            minutesSinceLastEvent: -1,
            lastProcessedBlockHeight: -1,
          }
        }
        const now = new Date()
        // TODO: Figure out why updated_at is not being set automatically, then replace with updated_at
        const parsedEventData = eventDataSchema.parse(latestEventData.data)
        const lastProcessedBlockHeight =
          parsedEventData.lastProcessedBlockHeight
        let lastUpdated = new Date(parsedEventData.lastUpdated)
        let timeSinceLastEvent = now.getTime() - lastUpdated.getTime()
        // Time since last event should never be in the future, this indicates a timezone mismatch
        const oneHourInMs = 60 * 60 * 1000
        while (timeSinceLastEvent < 0) {
          lastUpdated = new Date(lastUpdated.getTime() - oneHourInMs)
          timeSinceLastEvent = now.getTime() - lastUpdated.getTime()
        }
        const minutesSinceLastEvent = Math.floor(
          timeSinceLastEvent / (1000 * 60),
        )
        return {
          minutesSinceLastEvent,
          lastProcessedBlockHeight,
        }
      })
      .catch((error) => {
        return {
          error: `Error parsing latest event data: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          minutesSinceLastEvent: -1,
          lastProcessedBlockHeight: -1,
        }
      })
    // Situations in which this will trigger:
    // 1. Block height is available (lastProcessedBlockHeight !== -1)
    // 2. No event data exists (minutesSinceLastEvent === -1)
    // 3. (Should not happen): Block height is unavailable, but event data exists, and it's been more than 15 minutes since the last event
    // This could be simplified by removing lastUpdated, but then we'd need to add better checks when setting the last processed block height
    const { fallbackOutputs, highestBlockHeight } = await step
      .run('get-fallback-outputs', async () => {
        if (
          lastProcessedBlockHeight === -1 &&
          minutesSinceLastEvent !== -1 &&
          minutesSinceLastEvent < 15
        ) {
          return {
            fallbackOutputs: [] as BitcoinOutput[],
            highestBlockHeight: -1,
          }
        }
        return await getFallbackOutputs(lastProcessedBlockHeight)
      })
      .catch((error) => {
        return {
          error: `Error getting fallback outputs: ${error instanceof Error && error.message ? error.message : 'unknown'}`,
          fallbackOutputs: [] as BitcoinOutput[],
          highestBlockHeight: -1,
        }
      })
    if (!fallbackOutputs?.length) {
      return {
        warning: 'No fallback outputs found',
        minutesSinceLastEvent,
        lastProcessedBlockHeight,
      }
    }
    const batches = splitIntoBatches(fallbackOutputs)
    return await step.sendEvent(
      'transaction_events/bitcoin.new_outputs',
      batches.map((batch) => ({
        name: 'transaction_events/bitcoin.new_outputs',
        data: {
          outputs: batch,
          highestBlockHeight,
        },
      })),
    )
    // ^ no catch -- will throw error
  },
)
