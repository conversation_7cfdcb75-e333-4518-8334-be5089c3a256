import { getContract } from 'repositories/src/smartContracts.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import { updateCctpWithdrawStatus } from 'repositories/src/cctpWithdraw.js'

import { filter } from 'remeda'
import assert from 'utils/src/common/assertHTTP.js'

const client = createInngestClient('transaction_events')

export default client.createFunction(
  {
    id: 'transaction_events-withdraw-cctp-attestation',
    name: 'transaction_events/withdraw.cctp_attestation',
  },
  [{ event: 'transaction_validator-v2/transactions.validated' }],
  async ({
    event: {
      data: {
        nanoid,
        txn: { network, hash },
        contract_calls,
      },
    },
    logger,
  }) => {
    logger.info('Processing CCTP withdraw validation', { nanoid })

    const messageTransmitter = await getContract('MessageTransmitter', network)
    const messageTransmitterAddress = await messageTransmitter.getAddress()

    const messageTransmitterCall = filter(
      contract_calls,
      (c) =>
        c.call_method === 'receiveMessage' &&
        c.to === messageTransmitterAddress,
    )[0]

    if (!messageTransmitterCall) {
      return {
        message: 'Nothing to process',
      }
    }

    const attestation = messageTransmitterCall.call_params?.attestation

    assert(attestation, 'No attestation found in contract call')
    await updateCctpWithdrawStatus(nanoid, 'complete', attestation)

    logger.info('CCTP withdraw status updated to complete', {
      attestation,
    })

    return {
      success: true,
      txHash: hash,
    }
  },
)
