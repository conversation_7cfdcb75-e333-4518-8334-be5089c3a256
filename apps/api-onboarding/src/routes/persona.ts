import { onboardingPersonaRoutes as routes } from '@riseworks/contracts/src/allRoutes.js'
import type {
  CompanyNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { App } from 'backend/src/index.js'
import { db } from 'db'
import { getCompaniesByFounder } from 'repositories/src/companies.js'
import {
  createPersonaInquiry,
  resumeInquirySession,
} from 'repositories/src/persona.js'
import { updateOnboarding } from 'repositories/src/usersOnboarding.js'
import { validateUserByAuth } from 'repositories/src/validations.js'
import assert from 'utils/src/common/assertHTTP.js'
export default (app: App): App =>
  app.route({
    ...routes['/onboarding/persona/inquiries'].post,
    handler: async ({ auth }, reply) => {
      const { user, onboard } = await validateUserByAuth({
        auth,
        apps: ['pay'],
      })

      assert(onboard, 'Invalid user', '400')

      // Determine if user requires KYB
      let inquiryType: 'kyc' | 'kyb' = 'kyc'

      // reference_id can either be a user or company nanoid
      let reference_id: UserNanoid | CompanyNanoid = user.nanoid
      if (onboard.register_business) {
        inquiryType = 'kyb'
        const companies = await getCompaniesByFounder(user.nanoid)
        assert(companies.length, 'Invalid user did not create a company', '400')
        const company = companies[0]
        assert(company, 'Invalid user did not create a company', '400')
        reference_id = company.nanoid
      }

      // If user has an existign inquiry, resume inquiry
      if (onboard.ext_inquiry_id) {
        // Resume session if inquiry has not been completed
        if (
          onboard.ext_inquiry_status &&
          ['created', 'expired', 'pending'].includes(onboard.ext_inquiry_status)
        ) {
          const result = await resumeInquirySession(onboard.ext_inquiry_id)
          return reply.send({
            success: true,
            data: {
              inquiry_id: result.data.id,
              status: result.data.attributes.status,
              token: result.meta.session_token,
            },
          })
        }
        return reply.send({
          success: true,
          data: {
            inquiry_id: onboard.ext_inquiry_id,
            status: onboard.ext_inquiry_status,
            token: null,
          },
        })
      }

      const result = await createPersonaInquiry(reference_id, inquiryType)
      assert(result, 'Failed to create Persona inquiry', '400')

      db.transaction().execute(async (trx) => {
        await updateOnboarding(
          user.nanoid,
          {
            ext_inquiry_id: result.data.id,
            ext_inquiry_status: result.data.attributes.status,
          },
          trx,
        )
      })
      reply.send({
        success: true,
        data: {
          inquiry_id: result.data.id,
          status: result.data.attributes.status,
          // For new inquiries, no need to pass the session token
          token: null,
        },
      })
    },
  })
