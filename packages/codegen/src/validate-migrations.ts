#!/usr/bin/env node
import {
  findSchemaFiles,
  validateMigrations,
} from './helpers/prismaSchemaComparison.js'

async function main() {
  const args = process.argv.slice(2)
  const targetBranch = args[0] || 'origin/staging'

  console.log('🔍 Finding Prisma schema files...')
  const schemaFiles = findSchemaFiles()

  if (schemaFiles.length === 0) {
    console.log('✅ No schema.prisma files found, skipping check')
    return
  }

  console.log(`📋 Found ${schemaFiles.length} schema files:`, schemaFiles)

  console.log(
    `\n🔍 Validating migrations against target branch: ${targetBranch}`,
  )

  const validation = validateMigrations(targetBranch)

  for (const result of validation.results) {
    console.log(`\n🔍 Checking schema: ${result.schemaFile}`)

    if (result.error) {
      console.log(`❌ Error: ${result.error}`)
      continue
    }

    if (result.hasChanges) {
      console.log(`📦 Schema changes detected in ${result.schemaFile}`)
      console.log(`🔍 Checking migrations directory: ${result.migrationsDir}`)
      console.log(
        `📍 Current migrations checksum: ${result.currentMigrationsChecksum}`,
      )
      console.log(
        `📍 Target migrations checksum: ${result.targetMigrationsChecksum}`,
      )

      if (result.migrationsMissing) {
        console.log(
          `❌ Schema changed but migrations directory was not updated!`,
        )
      } else {
        console.log(`✅ Migrations directory was properly updated`)
      }
    } else {
      console.log(`✅ No changes detected in ${result.schemaFile}`)
    }
  }

  // Final validation result
  if (validation.isValid) {
    const hasAnyChanges = validation.results.some((r) => r.hasChanges)
    if (hasAnyChanges) {
      console.log(
        '\n✅ All schema changes have corresponding migration updates',
      )
    } else {
      console.log('\n✅ No schema changes detected')
    }
  } else {
    console.log('\n❌ Migration validation failed!')
    console.log('\n📋 Errors found:')
    validation.errors.forEach((error) => {
      console.log(`  - ${error}`)
    })

    console.log('\n🔧 To fix this issue:')
    console.log(
      '1. Run `pnpm migrate-all` locally to generate new migration files',
    )
    console.log('2. Commit the generated migration files')
    console.log('3. Push the changes to your PR branch')

    process.exit(1)
  }
}

main().catch((error) => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
