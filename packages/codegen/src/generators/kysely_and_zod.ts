import { writeFileSync } from 'node:fs'
import path from 'node:path'
import { emptyDirSync } from 'fs-extra'
import { default<PERSON><PERSON>elyHeader, defaultZodHeader, generate } from 'mutano'
import {
  createOrAppendToLockfile,
  removeTaskFromLockfile,
} from 'src/helpers/lockfile.js'
import {
  hasSchemaChanged,
  readChecksums,
  writeChecksums,
} from '../helpers/checksum.js'
import { generateImportOverrides } from '../helpers/generateImportOverrides.js'
import { riseFont } from '../helpers/riseFont.js'
import { PRISMA_SCHEMAS } from '../helpers/schemas.js'

export const execute = async () => {
  const shouldRun = await createOrAppendToLockfile('kysely_and_zod')
  if (!shouldRun) return
  try {
    // Read existing checksums
    const checksums = readChecksums()
    let schemasProcessed = 0
    let schemasSkipped = 0

    // Get the repo root path
    const repoRoot = process.cwd().includes('packages/codegen')
      ? path.resolve(process.cwd(), '../..')
      : process.cwd()

    for (const schema of PRISMA_SCHEMAS) {
      try {
        // Use the same path resolution approach as verify-checksums.ts
        const schemaPath = path.join(
          repoRoot,
          'prisma',
          schema,
          'schema.prisma',
        )

        // Check if the schema has changed
        console.log('chegou aqui')
        if (!hasSchemaChanged(schemaPath, checksums)) {
          console.log(`Schema ${schema} has not changed, skipping codegen.`)
          schemasSkipped++
          continue
        }

        console.log(`Generating types for schema ${schema}...`)
        schemasProcessed++

        const folder = path.join(
          repoRoot,
          'packages/contracts/src/codegen/zod',
          schema,
        )
        emptyDirSync(folder)
        const outFile = path.join(
          repoRoot,
          'packages/contracts/src/codegen/db',
          `models_${schema}.ts`,
        )
        const dests = await generate({
          origin: {
            type: 'prisma',
            path: schemaPath,
          },
          destinations: [
            {
              type: 'kysely',
              header: `
// this file was generated with \`pnpm codegen\`, do not edit it manually
${defaultKyselyHeader}
`,
              outFile,
            },
            {
              type: 'zod',
              header: `
// this file was generated with \`pnpm codegen\`, do not edit it manually
${defaultZodHeader}
`,
              folder,
              nullish: true,
              requiredString: true,
              useTrim: true,
              useDateType: true,
            },
          ],
          silent: true,
          magicComments: true,
        })
        for (const [filePath, content] of Object.entries(dests)) {
          if (filePath.includes('zod')) {
            const file = generateImportOverrides(content, 'zod')
            writeFileSync(filePath, file)
          }
          if (filePath.includes('db')) {
            const file = generateImportOverrides(content, 'ts')
            writeFileSync(filePath, file)
          }
        }
      } catch (e) {
        console.log(e)
      }
    }

    // Save updated checksums
    writeChecksums(checksums)

    if (schemasProcessed > 0) {
      console.log(riseFont('Kysely and Zod types generated.'))
      console.log(
        `Processed ${schemasProcessed} schemas, skipped ${schemasSkipped} unchanged schemas.`,
      )
    } else if (schemasSkipped > 0) {
      console.log(
        `All ${schemasSkipped} schemas are unchanged, no types were generated.`,
      )
    }
  } finally {
    removeTaskFromLockfile('kysely_and_zod')
  }
}
