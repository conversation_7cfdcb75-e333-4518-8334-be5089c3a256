import path from 'node:path'
import { globSync } from 'glob'
import { calculateChecksum, readChecksums } from './helpers/checksum.js'

const findSchemaFiles = () => {
  const repoRoot = process.cwd().includes('packages/codegen')
    ? path.resolve(process.cwd(), '../..')
    : process.cwd()

  return globSync('prisma/*/schema.prisma', { cwd: repoRoot, absolute: true })
}

const verifyChecksums = () => {
  const existingChecksums = readChecksums()
  const schemaFiles = findSchemaFiles()
  const newChecksums: Record<string, string> = {}
  const mismatches: Array<{
    file: string
    expected: string
    actual: string
  }> = []
  const missing: Array<{
    file: string
    checksum: string
  }> = []

  console.log(`Found ${schemaFiles.length} schema files.`)

  const repoRoot = process.cwd().includes('packages/codegen')
    ? path.resolve(process.cwd(), '../..')
    : process.cwd()

  for (const absolutePath of schemaFiles) {
    const currentChecksum = calculateChecksum(absolutePath)
    const relativePath = path.relative(repoRoot, absolutePath)
    newChecksums[relativePath] = currentChecksum
    if (existingChecksums[relativePath]) {
      if (existingChecksums[relativePath] !== currentChecksum) {
        mismatches.push({
          file: relativePath,
          expected: existingChecksums[relativePath],
          actual: currentChecksum,
        })
      }
    } else {
      missing.push({
        file: relativePath,
        checksum: currentChecksum,
      })
    }
  }

  return { mismatches, missing, newChecksums }
}

const main = () => {
  const { mismatches, missing } = verifyChecksums()

  if (mismatches.length > 0) {
    console.log('\n🔴 Checksum mismatches found:')
    for (const mismatch of mismatches) {
      console.log(`\nFile: ${mismatch.file}`)
      console.log(`Expected: ${mismatch.expected}`)
      console.log(`Actual: ${mismatch.actual}`)
    }
    console.log(
      '\nThis indicates that schema.prisma files have been modified but the generated types have not been updated.',
    )
    console.log(
      'Please run `pnpm codegen` to update the generated types and commit the changes.',
    )
    process.exit(1)
  }

  if (missing.length > 0) {
    console.log('\n🟡 New schema files found:')
    for (const file of missing) {
      console.log(`\nFile: ${file.file}`)
      console.log(`Checksum: ${file.checksum}`)
    }
    console.log('\nThese files are not yet tracked in the checksums file.')
    console.log(
      'Please run `pnpm codegen` to generate types for these schemas and commit the changes.',
    )
    process.exit(1)
  }

  console.log('\n✅ All schema checksums match!')
  process.exit(0)
}

main()
