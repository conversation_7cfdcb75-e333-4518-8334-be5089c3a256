import { execute as executeKysely } from './generators/kysely_and_zod.js'
import { execute as executeSmartContracts } from './generators/smart_contract_types.js'
import { execute as executeTypedData } from './generators/typed_data.js'

const allTasks = process.argv.indexOf('--all') !== -1
const kyselyTask = process.argv.indexOf('--kysely') !== -1 || allTasks
const smartContractTask =
  process.argv.indexOf('--smart-contracts') !== -1 || allTasks
const contractsMergeTask =
  process.argv.indexOf('--contracts-merge') !== -1 || allTasks
const openapiTask = process.argv.indexOf('--openapi') !== -1 || allTasks
const eventsMergeTask =
  process.argv.indexOf('--events-merge') !== -1 || allTasks
const endpointsMergeTask =
  process.argv.indexOf('--endpoints-merge') !== -1 || allTasks
const typedDataTask = process.argv.indexOf('--typed-data') !== -1 || allTasks

const someOption =
  allTasks ||
  kyselyTask ||
  smartContractTask ||
  contractsMergeTask ||
  openapiTask ||
  eventsMergeTask ||
  endpointsMergeTask ||
  typedDataTask

if (!someOption) {
  console.log('Please specify a task to run')
  process.exit(1)
}

await Promise.allSettled([
  kyselyTask ? executeKysely() : Promise.resolve(),
  smartContractTask ? executeSmartContracts() : Promise.resolve(),
  typedDataTask ? executeTypedData() : Promise.resolve(),
])

if (contractsMergeTask) {
  await import('./generators/contracts_merge.js').then((e) => e.execute())
}
await Promise.allSettled([
  eventsMergeTask
    ? import('./generators/events_merge.js').then((e) => e.execute())
    : Promise.resolve(),
  endpointsMergeTask
    ? import('./generators/endpoints_merge.js').then((e) => e.execute())
    : Promise.resolve(),
])

if (openapiTask) {
  const openapiTask = await import('./generators/openapi_clients.js')
  await openapiTask.execute()
}

process.exit(0)
