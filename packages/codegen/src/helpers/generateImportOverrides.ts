import { loadFile } from 'magicast'
import { capitalize } from 'remeda'

const mod = await loadFile(`${process.cwd()}/../contracts/src/brands.ts`)

const brands = Object.keys(mod.exports)

const formatsMod = await loadFile(
  `${process.cwd()}/../contracts/src/formats.ts`,
)

const smartContractTypesMod = await loadFile(
  `${process.cwd()}/../contracts/src/smartContractTypes.ts`,
)

const formats = Object.keys(formatsMod.exports)
const smartContractTypes = Object.keys(smartContractTypesMod.exports)
export const generateImportOverrides = (
  fileContent: string,
  type: 'ts' | 'zod',
) => {
  const isTs = type === 'ts'

  // For TS, we look for capitalized brand names, for Zod we look for exact matches
  const brandsImported = brands
    .filter((b) => fileContent.indexOf(isTs ? capitalize(b) : b) > -1)
    .map((b) => (isTs ? capitalize(b) : b))

  const formatsImported = formats
    .filter(
      (b) =>
        fileContent.indexOf(isTs ? capitalize(b) : b) > -1 &&
        brandsImported.indexOf(isTs ? capitalize(b) : b) === -1,
    )
    .map((b) => (isTs ? capitalize(b) : b))

  const smartContractTypesImported = smartContractTypes
    .filter(
      (b) =>
        fileContent.indexOf(isTs ? capitalize(b) : b) > -1 &&
        brandsImported.indexOf(isTs ? capitalize(b) : b) === -1 &&
        formatsImported.indexOf(isTs ? capitalize(b) : b) === -1,
    )
    .map((b) => (isTs ? capitalize(b) : b))

  // Generate new import statements
  let newImports = ''
  if (isTs) {
    newImports = [
      brandsImported.length > 0
        ? `// @ts-ignore
import type { ${brandsImported.join(', ')} } from '../../brands.js'`
        : '',
      formatsImported.length > 0
        ? `// @ts-ignore
import type { ${formatsImported.join(', ')} } from '../../formats.js'`
        : '',
      smartContractTypesImported.length > 0
        ? `// @ts-ignore
import type { ${smartContractTypesImported.join(', ')} } from '../../smartContractTypes.js'`
        : '',
      `// @ts-ignore
import type { Selectable, Insertable, Updateable${fileContent.indexOf('JSONColumnType') > -1 ? ', JSONColumnType }' : ' }'} from 'kysely'`,
    ]
      .filter(Boolean)
      .join('\n')
  } else {
    newImports = [
      brandsImported.length > 0
        ? `// @ts-ignore
import { ${brandsImported.join(', ')} } from '../../../brands.js'`
        : '',
      formatsImported.length > 0
        ? `// @ts-ignore
import { ${formatsImported.join(', ')} } from '../../../formats.js'`
        : '',
      smartContractTypesImported.length > 0
        ? `// @ts-ignore
import { ${smartContractTypesImported.join(', ')} } from '../../../smartContractTypes.js'`
        : '',
    ]
      .filter(Boolean)
      .join('\n')
  }

  // Remove existing imports that we're going to replace
  let cleanedContent = fileContent

  // Define patterns for imports to remove
  const importPatterns = [
    // TS imports
    /\/\/ @ts-ignore\s*\nimport type \{[^}]*\} from '\.\.\/\.\.\/brands\.js'/g,
    /\/\/ @ts-ignore\s*\nimport type \{[^}]*\} from '\.\.\/\.\.\/formats\.js'/g,
    /\/\/ @ts-ignore\s*\nimport type \{[^}]*\} from '\.\.\/\.\.\/smartContractTypes\.js'/g,
    /\/\/ @ts-ignore\s*\nimport type \{[^}]*\} from 'kysely'/g,

    // Zod imports
    /\/\/ @ts-ignore\s*\nimport \{[^}]*\} from '\.\.\/\.\.\/\.\.\/brands\.js'/g,
    /\/\/ @ts-ignore\s*\nimport \{[^}]*\} from '\.\.\/\.\.\/\.\.\/formats\.js'/g,
    /\/\/ @ts-ignore\s*\nimport \{[^}]*\} from '\.\.\/\.\.\/\.\.\/smartContractTypes\.js'/g,
  ]

  // Remove existing imports
  for (const pattern of importPatterns) {
    cleanedContent = cleanedContent.replace(pattern, '')
  }

  return `// @ts-nocheck\n${newImports}\n${cleanedContent.trim()}`
}
