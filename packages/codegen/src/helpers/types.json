{"RiseAccountForwarder": {"domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "V2"}, "types": {"RisePayment": [{"name": "id", "type": "bytes32"}, {"name": "groupID", "type": "bytes32"}, {"name": "payAtTime", "type": "uint128"}, {"name": "validMinutes", "type": "uint32"}, {"name": "payType", "type": "uint16"}, {"name": "token", "type": "address"}, {"name": "recipient", "type": "address"}, {"name": "amount", "type": "uint256"}, {"name": "data", "type": "bytes32"}], "CreatePaymentForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RisePayment"}], "RisePaymentScheduleRequest": [{"name": "count", "type": "uint32"}, {"name": "minuteInterval", "type": "uint64"}, {"name": "payment", "type": "RisePayment"}], "CreatePaymentByScheduleForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RisePaymentScheduleRequest"}], "RisePaymentsRequest": [{"name": "payments", "type": "RisePayment[]"}], "CreatePaymentsForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RisePaymentsRequest"}], "RiseIntentPaymentsToScheduledRequest": [{"name": "paymentIDs", "type": "bytes32[]"}, {"name": "payAtTime", "type": "uint128[]"}], "IntentPaymentToScheduledForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseIntentPaymentsToScheduledRequest"}], "RiseRemovePaymentsRequest": [{"name": "paymentIDs", "type": "bytes32[]"}], "RemovePaymentsForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseRemovePaymentsRequest"}], "RiseRemovePaymentByGroupRequest": [{"name": "groupID", "type": "bytes32"}, {"name": "idx", "type": "uint256"}, {"name": "count", "type": "uint256"}], "RemovePaymentsByGroupIDForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseRemovePaymentByGroupRequest"}], "RiseEtherTransferRequest": [{"name": "recipient", "type": "address"}, {"name": "amount", "type": "uint256"}], "SendEtherForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseEtherTransferRequest"}], "SetRole": [{"name": "role", "type": "uint8"}, {"name": "account", "type": "address"}], "SetRolesForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "SetRole[]"}], "RiseTokenApprovalRequest": [{"name": "token", "type": "address"}, {"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"}], "SetTokenTransferApprovalForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseTokenApprovalRequest"}], "RiseTransactionLimitRequest": [{"name": "spender", "type": "address"}, {"name": "token", "type": "address"}, {"name": "dailyLimit", "type": "uint256"}, {"name": "transactionLimit", "type": "uint256"}], "SetTransactionLimitsForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseTransactionLimitRequest"}], "RiseTokenTransferRequest": [{"name": "token", "type": "address"}, {"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "amount", "type": "uint256"}], "TokenTransferForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RiseTokenTransferRequest"}]}}, "RiseIDForwarder": {"domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "V2"}, "types": {"ApprovalChange": [{"name": "incOrDec", "type": "bool"}, {"name": "token", "type": "address"}, {"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"}], "Approve": [{"name": "token", "type": "address"}, {"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"}], "ApproveForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "Approve"}], "Execution": [{"name": "to", "type": "address"}, {"name": "method", "type": "bytes"}, {"name": "data", "type": "bytes"}], "CallForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "Execution"}], "ExecuteForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "Execution"}], "Dataset": [{"name": "dataKey", "type": "bytes32"}, {"name": "dataValue", "type": "bytes"}], "SetDataForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "Dataset"}], "SetRole": [{"name": "role", "type": "uint8"}, {"name": "account", "type": "address"}], "SetRolesForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "SetRole[]"}], "Transfer": [{"name": "token", "type": "address"}, {"name": "to", "type": "address"}, {"name": "amount", "type": "uint256"}], "TransferForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "Transfer"}], "TransferFrom": [{"name": "token", "type": "address"}, {"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "amount", "type": "uint256"}], "TransferFromForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "TransferFrom"}]}}, "RisePaymentHandlerForwarder": {"domain": {"name": "RisePaymentHandler<PERSON>or<PERSON>er", "version": "V2"}, "types": {"RisePaymentHandlerConfigRequest": [{"name": "token", "type": "address"}, {"name": "configs", "type": "RisePaymentHandlerConfig[]"}], "RisePaymentHandlerConfig": [{"name": "amount", "type": "uint256"}, {"name": "transferType", "type": "uint8"}, {"name": "fixedOrPercent", "type": "uint8"}, {"name": "ramp", "type": "address"}, {"name": "source", "type": "address"}, {"name": "destination", "type": "address"}, {"name": "offChainReference", "type": "bytes32"}, {"name": "data", "type": "bytes"}], "ProcessTokenTransfersWithConfigForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RisePaymentHandlerConfigRequest"}], "SetTransferRulesForwardRequest": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "salt", "type": "uint64"}, {"name": "expires", "type": "uint48"}, {"name": "data", "type": "RisePaymentHandlerConfigRequest[]"}]}}}