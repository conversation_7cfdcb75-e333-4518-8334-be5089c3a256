import { createHash } from 'node:crypto'
import { existsSync, readFileSync, writeFileSync } from 'node:fs'
import path from 'node:path'

export const calculateChecksum = (filePath: string) => {
  const fileContent = readFileSync(filePath, 'utf8')
  return createHash('md5').update(fileContent).digest('hex')
}

// Store the checksums file inside the prisma folder
export const CHECKSUMS_FILE = path.join(
  process.cwd().includes('packages/codegen')
    ? path.resolve(process.cwd(), '../..')
    : process.cwd(),
  'prisma',
  '.prisma_checksums',
)

export const readChecksums = (): Record<string, string> => {
  if (!existsSync(CHECKSUMS_FILE)) {
    return {}
  }

  try {
    const content = readFileSync(CHECKSUMS_FILE, 'utf8')
    return JSON.parse(content)
  } catch (error) {
    console.warn(
      'Error reading checksums file, will regenerate all schemas:',
      error,
    )
    return {}
  }
}

export const writeChecksums = (checksums: Record<string, string>) => {
  writeFileSync(CHECKSUMS_FILE, JSON.stringify(checksums, null, 2), 'utf8')
}

export const hasSchemaChanged = (
  schemaPath: string,
  checksums: Record<string, string>,
) => {
  if (!existsSync(schemaPath)) {
    console.warn(`Schema file not found: ${schemaPath}`)
    return false
  }

  // Get the repo root path
  const repoRoot = process.cwd().includes('packages/codegen')
    ? path.resolve(process.cwd(), '../..')
    : process.cwd()

  // Get the absolute path for file operations
  const absolutePath = path.resolve(schemaPath)

  // Get the relative path from the repo root for storage in checksums
  const relativePath = path.relative(repoRoot, absolutePath)

  const currentChecksum = calculateChecksum(absolutePath)
  const previousChecksum = checksums[relativePath]

  // Update the checksum in the cache using the relative path
  checksums[relativePath] = currentChecksum

  // If there's no previous checksum or it's different, the schema has changed
  return !previousChecksum || previousChecksum !== currentChecksum
}
