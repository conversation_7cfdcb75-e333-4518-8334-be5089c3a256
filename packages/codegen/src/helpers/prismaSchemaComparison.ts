import { execSync } from 'node:child_process'
import { createHash } from 'node:crypto'
import { existsSync, readFileSync, readdirSync } from 'node:fs'
import path from 'node:path'

export function sanitizeSchema(content: string): string {
  return (
    content
      // Remove Prisma triple-slash comments (/// comments)
      .replace(/\/\/\/.*$/gm, '')
      // Remove single-line comments (// comments)
      .replace(/\/\/.*$/gm, '')
      // Remove multi-line comments (/* comments */)
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // Remove all whitespace and newlines
      .replace(/\s+/g, '')
      // Remove empty lines
      .split('\n')
      .filter((line) => line.trim() !== '')
      .join('')
  )
}

export function calculateDirectoryChecksum(dirPath: string): string {
  if (!existsSync(dirPath)) {
    return 'DIRECTORY_NOT_EXISTS'
  }

  const files: string[] = []

  function walkDir(dir: string): void {
    const items = readdirSync(dir, { withFileTypes: true })
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      if (item.isDirectory()) {
        walkDir(fullPath)
      } else {
        files.push(fullPath)
      }
    }
  }

  walkDir(dirPath)
  files.sort()

  const hash = createHash('md5')
  for (const file of files) {
    const relativePath = path.relative(dirPath, file)
    const content = readFileSync(file, 'utf8')
    // Normalize line endings for consistent checksums
    const normalizedContent = content.replace(/\r\n/g, '\n')
    hash.update(relativePath + ':' + normalizedContent)
  }

  return hash.digest('hex')
}

export function findSchemaFiles(
  baseDir = `${import.meta.dirname}/../../../../`,
): string[] {
  const schemas: string[] = []
  const prismaDir = path.join(baseDir, 'prisma')
  console.log(prismaDir)
  if (!existsSync(prismaDir)) {
    return schemas
  }

  function walkDir(dir: string): void {
    const items = readdirSync(dir, { withFileTypes: true })
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      if (item.isDirectory()) {
        walkDir(fullPath)
      } else if (item.name === 'schema.prisma') {
        schemas.push(fullPath)
      }
    }
  }

  walkDir(prismaDir)
  return schemas
}

export function getFileFromBranch(filePath: string, branch: string): string {
  try {
    const command = `git show ${branch}:${filePath}`
    console.log(command)
    return execSync(command, { encoding: 'utf8' })
  } catch (error) {
    // File doesn't exist in the target branch
    return ''
  }
}

export function getDirectoryChecksumFromBranch(
  dirPath: string,
  branch: string,
): string {
  try {
    const tempDir = `/tmp/target_content_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    execSync(`mkdir -p ${tempDir}`)

    try {
      execSync(`git archive ${branch} ${dirPath} | tar -x -C ${tempDir}`, {
        stdio: 'pipe',
      })
      const checksum = calculateDirectoryChecksum(path.join(tempDir, dirPath))
      return checksum
    } catch (archiveError) {
      return 'DIRECTORY_NOT_EXISTS'
    } finally {
      // Cleanup
      execSync(`rm -rf ${tempDir}`)
    }
  } catch (error) {
    return 'DIRECTORY_NOT_EXISTS'
  }
}

export interface SchemaComparisonResult {
  schemaFile: string
  hasChanges: boolean
  migrationsDir: string
  currentMigrationsChecksum: string
  targetMigrationsChecksum: string
  migrationsMissing: boolean
  error?: string
}

export function compareSchemaAndMigrations(
  schemaFile: string,
  targetBranch: string,
): SchemaComparisonResult {
  const result: SchemaComparisonResult = {
    schemaFile,
    hasChanges: false,
    migrationsDir: '',
    currentMigrationsChecksum: '',
    targetMigrationsChecksum: '',
    migrationsMissing: false,
  }

  try {
    const currentSchema = readFileSync(schemaFile, 'utf8')
    const sanitizedCurrent = sanitizeSchema(currentSchema)
    const targetSchema = getFileFromBranch(schemaFile, targetBranch)
    const sanitizedTarget = sanitizeSchema(targetSchema)

    result.hasChanges = sanitizedCurrent !== sanitizedTarget

    if (result.hasChanges) {
      const schemaDir = path.dirname(schemaFile)
      result.migrationsDir = path.join(schemaDir, 'migrations')

      result.currentMigrationsChecksum = calculateDirectoryChecksum(
        result.migrationsDir,
      )
      result.targetMigrationsChecksum = getDirectoryChecksumFromBranch(
        result.migrationsDir,
        targetBranch,
      )
      result.migrationsMissing =
        result.currentMigrationsChecksum === result.targetMigrationsChecksum

      if (result.migrationsMissing) {
        result.error = `Schema changed in ${schemaFile} but migrations directory ${result.migrationsDir} was not updated!`
      }
    }

    return result
  } catch (error) {
    result.error = `Error comparing schema ${schemaFile}: ${error instanceof Error ? error.message : String(error)}`
    return result
  }
}

export function compareAllSchemasAndMigrations(
  targetBranch: string,
  baseDir = `${import.meta.dirname}/../../../../`,
): SchemaComparisonResult[] {
  const schemaFiles = findSchemaFiles(baseDir)
  const results: SchemaComparisonResult[] = []

  for (const schemaFile of schemaFiles) {
    const result = compareSchemaAndMigrations(schemaFile, targetBranch)
    results.push(result)
  }

  return results
}

export function validateMigrations(
  targetBranch: string,
  baseDir = `${import.meta.dirname}/../../../../`,
): { isValid: boolean; errors: string[]; results: SchemaComparisonResult[] } {
  const results = compareAllSchemasAndMigrations(targetBranch, baseDir)
  const errors: string[] = []

  for (const result of results) {
    if (result.error) {
      errors.push(result.error)
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    results,
  }
}
