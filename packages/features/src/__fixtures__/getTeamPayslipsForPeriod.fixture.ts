import type {
  EmployeePayrollRiseAccount,
  TeamNanoid,
  UserNanoid,
  UserRiseAccount,
  UserRiseid,
} from '@riseworks/contracts/src/brands.js'
import type { PaymentsByCategory } from '@riseworks/contracts/src/formats.js'
import type { getTeamPayslipsForPeriod } from 'src/payroll/getTeamPayslipsForPeriod.js'
import { expect } from 'vitest'

export const mockTeamNanoid = 'te-fake' as TeamNanoid

export const mockPayCycle = {
  year: 2025,
  month: 4,
  period: 1,
}

export const mockEmployeePayrollSettings = [
  {
    user_nanoid: 'user-1' as UserNanoid,
    team_nanoid: mockTeamNanoid,
    count: 0,
    rise_account: 'acc-1' as EmployeePayrollRiseAccount,
    status: 'active',
    annual_base_salary: '',
    country_code: 'US',
    created_at: new Date(),
    created_by: 'user-1' as UserNanoid,
    currency: 'USD',
    dispute_resolution_method: '',
    effective_end: null,
    effective_start: new Date(),
    employment_type: '',
    end_date: null,
    id: 0,
    job_scope: '',
    job_title: '',
    notice_period_days: 0,
    payroll_program: 'riseworks_eor_us',
    restricted_period_days: 0,
    signing_bonus_amount: '',
    start_date: new Date('2024-04-01'),
    stipends: [].slice(),
    time_off: '',
    variable_compensations: [].slice(),
    work_hours_per_week: 0,
  } as const,
]

export const mockUsers = [
  {
    nanoid: 'user-1' as UserNanoid,
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    avatar: 'avatar.jpg',
    rise_account: 'user_rise_account' as UserRiseAccount,
    type: 'user',
    created_at: new Date(),
    account_status: 'activated',
    advanced_security: false,
    alias: '',
    country: '',
    discord: '',
    dob: null,
    last_modified_by: null,
    linkedin: '',
    middle_name: '',
    occupation: '',
    phone: '',
    recovery_email: '',
    state: '',
    updated_at: new Date(),
    v1_id: '',
    website: '',
    x: '',
    parent_riseid: 'user_rise_id' as UserRiseid,
    riseid: 'user_rise_id' as UserRiseid,
  } as const,
]

export const mockPaymentsByCategory: PaymentsByCategory = {
  grosspay: { amount_cents: 10000, currency: 'USD' },
  netpay: { amount_cents: 8000, currency: 'USD' },
  salaries: { amount_cents: 8000, currency: 'USD' },
  extras: {
    signing_bonus: { amount_cents: 0, currency: 'USD' },
    stipends: { amount_cents: 0, currency: 'USD' },
    one_off_bonuses: { amount_cents: 0, currency: 'USD' },
    variable_compensations: {
      bonus: { amount_cents: 0, currency: 'USD' },
      commission: { amount_cents: 0, currency: 'USD' },
      reimbursement: { amount_cents: 0, currency: 'USD' },
    },
  },
  healthcare: {
    employee: { amount_cents: 0, currency: 'USD' },
    employer: { amount_cents: 0, currency: 'USD' },
  },
  retirement: {
    employee: { amount_cents: 0, currency: 'USD' },
    employer: { amount_cents: 0, currency: 'USD' },
  },
  taxes: {
    employee: [],
    employer: [],
  },
}

export const expectedPayslipResult: Awaited<
  ReturnType<typeof getTeamPayslipsForPeriod>
> = [
  {
    employee: {
      nanoid: 'user-1' as UserNanoid,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      avatar: 'avatar.jpg',
      start_date: new Date('2024-04-01'),
      payroll_program: 'riseworks_eor_us',
      payroll_program_country: 'US',
    },
    payslip: expect.any(Object),
  },
]
