import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import { getPayCycle, getPreviousPayCycle } from 'utils/src/common/payCycles.js'

export const generatePayCyclesForPage = (
  page_size: number,
  page_offset: number,
): PayCycle[] => {
  const startCycle = getPayCycleFromOffset(page_offset, page_size)

  return Array.from({ length: page_size }).map((_, i) =>
    Array.from({ length: i }).reduce<PayCycle>(
      (cycle) => getPreviousPayCycle(cycle),
      startCycle,
    ),
  )
}

const getPayCycleFromOffset = (
  page_offset: number,
  page_size: number,
): PayCycle => {
  const totalBackSteps = page_offset * page_size
  const startCycle = getPayCycle()

  return Array.from({ length: totalBackSteps }).reduce<PayCycle>(
    (cycle) => getPreviousPayCycle(cycle),
    startCycle,
  )
}
