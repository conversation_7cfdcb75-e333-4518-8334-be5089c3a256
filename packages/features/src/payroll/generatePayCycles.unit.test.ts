import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import * as PayCycles from 'utils/src/common/payCycles.js'
import { beforeEach, describe, expect, test, vi } from 'vitest'
import { generatePayCyclesForPage } from './generatePayCycles.js'

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))

describe('generatePayCyclesForPage', () => {
  beforeEach(() => {
    vi.restoreAllMocks()
    vi.spyOn(PayCycles, 'getPayCycle').mockReturnValue({
      year: 2025,
      month: 3,
      period: 1,
    })
  })

  test('returns N sequential payCycles from current cycle', () => {
    const result = generatePayCyclesForPage(3, 0)

    expect(result).toEqual([
      { year: 2025, month: 3, period: 1 },
      { year: 2025, month: 2, period: 2 },
      { year: 2025, month: 2, period: 1 },
    ])
  })

  test('respects pageOffset', () => {
    const result = generatePayCyclesForPage(2, 1)

    expect(result).toEqual([
      { year: 2025, month: 2, period: 1 },
      { year: 2025, month: 1, period: 2 },
    ])
  })

  test('handles year change correctly', () => {
    vi.spyOn(PayCycles, 'getPayCycle').mockReturnValue({
      year: 2025,
      month: 1,
      period: 1,
    })

    const result = generatePayCyclesForPage(3, 0)

    expect(result).toEqual([
      { year: 2025, month: 1, period: 1 },
      { year: 2024, month: 12, period: 2 },
      { year: 2024, month: 12, period: 1 },
    ])
  })

  test('handles large offset crossing multiple years', () => {
    const result = generatePayCyclesForPage(2, 12)
    const payCycle = result[0] as PayCycle

    expect(result).toHaveLength(2)
    expect(payCycle.year).toBe(2024)
    expect(payCycle.month).toBeLessThanOrEqual(3)
  })
})
