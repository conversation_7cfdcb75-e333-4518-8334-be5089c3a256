import type {
  TeamNanoid,
  TeamRiseAccount,
} from '@riseworks/contracts/src/brands.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import type { ValidNetworks } from '@riseworks/contracts/src/smartContractTypes.js'
import { teamPayrollCashRequirementPaymentId } from 'repositories/src/payroll.js'
import { groupPaymentsRead } from 'repositories/src/riseAccounts.js'

export const getCompletedCashRequirementsForCycle = async (
  riseAccount: TeamRiseAccount,
  teamNanoid: TeamNanoid,
  payCycle: PayCycle,
) => {
  return await groupPaymentsRead(
    riseAccount,
    teamPayrollCashRequirementPaymentId(teamNanoid, payCycle),
    ['Complete'],
    'arbitrum' as ValidNetworks,
  )
}

export const getAllCashRequirementsForCycle = async (
  riseAccount: TeamRiseAccount,
  teamNanoid: TeamNanoid,
  payCycle: PayCycle,
) => {
  return await groupPaymentsRead(
    riseAccount,
    teamPayrollCashRequirementPaymentId(teamNanoid, payCycle),
    ['Complete', 'Scheduled', 'Intent'],
    'arbitrum' as ValidNetworks,
  )
}
