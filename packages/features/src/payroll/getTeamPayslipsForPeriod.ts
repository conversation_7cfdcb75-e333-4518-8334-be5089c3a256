import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import { getEmployeePayrollSettingsByTeam } from 'repositories/src/employeePayrollSettings.js'
import { getEmployeePaymentsByCategory } from 'repositories/src/payroll.js'
import { getUsersByNanoids } from 'repositories/src/users.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getContext } from 'utils/src/common/requestContext.js'

type GetTeamPayslipsForPeriodInput = {
  teamNanoid: TeamNanoid
  payCycle: { year: number; month: number; period: number }
}

export const getTeamPayslipsForPeriod = async (
  input: GetTeamPayslipsForPeriodInput,
) => {
  const { teamNanoid, payCycle } = input
  const ctx = getContext()
  const { logger } = ctx

  const employeePayrollSettings = await getEmployeePayrollSettingsByTeam({
    team_nanoid: teamNanoid,
    pay_cycle: payCycle,
  })

  if (employeePayrollSettings.length === 0) {
    return []
  }

  const userIds = employeePayrollSettings.map((e) => e.user_nanoid)
  const users = await getUsersByNanoids(userIds)

  const epsWithUser = employeePayrollSettings.map((e) => ({
    employeePayrollSettings: e,
    user: users.find((u) => u.nanoid === e.user_nanoid),
  }))
  logger.info(`EPS User: ${JSON.stringify(epsWithUser)}`)
  const employeesPayslips = await mapAsync(
    epsWithUser,
    async ({ employeePayrollSettings, user }) => {
      assert(user, 'User not found', '404')

      const payslip = await getEmployeePaymentsByCategory(
        employeePayrollSettings,
        payCycle,
      )

      return {
        employee: {
          nanoid: user.nanoid,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          avatar: user.avatar,
          start_date: employeePayrollSettings.start_date,
          payroll_program: employeePayrollSettings.payroll_program,
          payroll_program_country: employeePayrollSettings.country_code,
        },
        payslip,
      }
    },
  )

  return employeesPayslips
}
