import { describe, expect, test, vi, beforeEach } from 'vitest'
import { getTeamPayslipsForPeriod } from './getTeamPayslipsForPeriod.js'
import { getEmployeePayrollSettingsByTeam } from 'repositories/src/employeePayrollSettings.js'
import { getUsersByNanoids } from 'repositories/src/users.js'
import { getEmployeePaymentsByCategory } from 'repositories/src/payroll.js'
import {
  mockTeamNanoid,
  mockPayCycle,
  mockEmployeePayrollSettings,
  mockUsers,
  mockPaymentsByCategory,
  expectedPayslipResult,
} from '../__fixtures__/getTeamPayslipsForPeriod.fixture.js'

vi.mock('utils/src/google/getSecrets.js', () => ({
  getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
    return Object.fromEntries(
      secrets.map((s) => {
        if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
        if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
        return [s, `mocked-${s}`]
      }),
    )
  }),
}))

vi.mock('repositories/src/employeePayrollSettings')
vi.mock('repositories/src/users')
vi.mock('repositories/src/payroll')

describe('getTeamPayslipsForPeriod', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('should return empty array if no employee payroll settings are found', async () => {
    vi.mocked(getEmployeePayrollSettingsByTeam).mockResolvedValue([])

    const result = await getTeamPayslipsForPeriod({
      teamNanoid: mockTeamNanoid,
      payCycle: mockPayCycle,
    })

    expect(result).toEqual([])
    expect(getEmployeePayrollSettingsByTeam).toHaveBeenCalledWith({
      team_nanoid: mockTeamNanoid,
      pay_cycle: mockPayCycle,
    })
  })

  test('should return payslips for each employee', async () => {
    vi.mocked(getEmployeePayrollSettingsByTeam).mockResolvedValue(
      mockEmployeePayrollSettings,
    )
    vi.mocked(getUsersByNanoids).mockResolvedValue(mockUsers)
    vi.mocked(getEmployeePaymentsByCategory).mockResolvedValue(
      mockPaymentsByCategory,
    )

    const result = await getTeamPayslipsForPeriod({
      teamNanoid: mockTeamNanoid,
      payCycle: mockPayCycle,
    })

    expect(result).toEqual(expectedPayslipResult)
  })

  test('should throw 404 if user not found', async () => {
    vi.mocked(getEmployeePayrollSettingsByTeam).mockResolvedValue(
      mockEmployeePayrollSettings,
    )
    vi.mocked(getUsersByNanoids).mockResolvedValue([])

    await expect(
      getTeamPayslipsForPeriod({
        teamNanoid: mockTeamNanoid,
        payCycle: mockPayCycle,
      }),
    ).rejects.toThrow('User not found')
  })
})
