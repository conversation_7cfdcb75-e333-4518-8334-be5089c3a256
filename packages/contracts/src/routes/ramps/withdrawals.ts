import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { withdrawAccountNanoid } from '../../brands.js'

export const schema = {
  '/ramps/withdrawals/:account_nanoid/wait_time': {
    get: {
      tags: ['Ramps'],
      security: [{ bearerAuth: [] }],
      summary:
        'Provides the estimated processing time based on the withdraw account details',
      params: z.object({
        account_nanoid: withdrawAccountNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            wait_time: z.string(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
