import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { companyNanoid } from '../../brands.js'
import { recaptcha } from '../../formats.js'

const getCompaniesResponse = defaultResponse.extend({
  data: z.object({
    companies: z.array(
      z.object({
        nanoid: companyNanoid.describe('Unique identifier for the company.'),
        avatar: z.string().describe("URL of the company's avatar image."),
        name: z.string().describe('Name of the company.'),
      }),
    ),
  }),
})

const putAddressBody = z.object({
  line_1: z.string().optional().describe('The first line of the address.'),
  line_2: z
    .string()
    .optional()
    .describe('The second line of the address, optional.'),
  city: z.string().optional().describe('The city of the address.'),
  state: z.string().optional().describe('The state of the address.'),
  country: z.string().optional().describe('The country of the address.'),
  zip_code: z
    .string()
    .optional()
    .describe('The ZIP or postal code of the address.'),
  timezone: z
    .string()
    .optional()
    .describe('The timezone associated with the address.'),
})

const getTeamsResponse = defaultResponse.extend({
  data: z.object({
    teams: z.array(
      z.object({
        nanoid: z.string().describe('Unique identifier for the team.'),
        name: z.string().min(1).describe('Name of the team.'),
        avatar: z.string().describe("URL of the team's avatar image."),
        balance: z.string().describe('Balance associated with the team.'),
        payees_amount: z.number().describe('Number of payees in the team.'),
        administrators_amount: z
          .number()
          .describe('Number of administrators in the team.'),
      }),
    ),
  }),
})

export const schema = {
  '/v2/user/organizations': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: `Get user organization's`,
      description:
        'Retrieves a list of organizations associated with the logged-in user.',
      response: {
        200: getCompaniesResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/user/teams': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: `Get user team's`,
      description:
        'Retrieves a list of teams associated with the logged-in user.',
      response: {
        200: getTeamsResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/user/address': {
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Update user address details',
      body: putAddressBody,
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/user/avatar/update': {
    post: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update user avatar',
      headers: recaptcha,
      body: z.object({
        file_name: z.string().min(1).trim(),
      }),
      response: {
        201: defaultResponse.extend({
          data: z.literal('Your avatar was updated'),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
