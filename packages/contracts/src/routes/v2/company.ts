import { defaultErrorResponses, defaultResponse } from '../../response.js'
import { z } from 'zod'
import type { Schema } from '../../types.js'
import { companyNanoid, companyOwnerNanoid, userNanoid } from '../../brands.js'
import { companyDocumentTypes, recaptcha } from '../../formats.js'
import {
  selectable_private_data,
  updateable_private_data,
} from '../../codegen/zod/rise_private/private_data.js'
import {
  selectable_companies_data,
  updateable_companies_data,
} from '../../codegen/zod/rise/companies_data.js'

const putAddressBody = z.object({
  line_1: z.string().optional().describe('The first line of the address.'),
  line_2: z
    .string()
    .optional()
    .describe('The second line of the address, optional.'),
  city: z.string().optional().describe('The city of the address.'),
  state: z.string().optional().describe('The state of the address.'),
  country: z.string().optional().describe('The country of the address.'),
  zip_code: z
    .string()
    .optional()
    .describe('The ZIP or postal code of the address.'),
  timezone: z
    .string()
    .optional()
    .describe('The timezone associated with the address.'),
})

const getOrganizationDetailsResponse = defaultResponse.extend({
  data: selectable_companies_data
    .pick({
      name: true,
      doing_business_as: true,
      size: true,
      incorporation_country: true,
      incorporation_type: true,
      website: true,
      phone: true,
    })
    .extend({
      private_data: selectable_private_data.pick({
        us_based: true,
        tax_id: true,
        us_work: true,
      }),
    }),
})

const putOrganizationDetailsBody = updateable_companies_data
  .pick({
    name: true,
    doing_business_as: true,
    size: true,
    incorporation_country: true,
    incorporation_type: true,
    website: true,
    phone: true,
  })
  .extend({
    private_data: updateable_private_data.pick({
      us_based: true,
      tax_id: true,
      us_work: true,
    }),
  })

const getOrganizationContactsResponse = defaultResponse.extend({
  data: z.object({
    admin: z.object({
      fullname: z.string().min(1).describe('Full name of the admin contact.'),
      phone: z.string().describe('Phone number of the admin contact.'),
      email: z.string().describe('Email address of the admin contact.'),
    }),
    document_signing: z
      .object({
        fullname: z
          .string()
          .min(1)
          .describe('Full name of the document signing contact.'),
        phone: z
          .string()
          .describe('Phone number of the document signing contact.'),
        email: z
          .string()
          .describe('Email address of the document signing contact.'),
      })
      .optional(),
    support: z
      .object({
        fullname: z
          .string()
          .min(1)
          .describe('Full name of the support contact.'),
        phone: z.string().describe('Phone number of the support contact.'),
        email: z.string().describe('Email address of the support contact.'),
      })
      .optional(),
    finance: z
      .object({
        fullname: z
          .string()
          .min(1)
          .describe('Full name of the finance contact.'),
        phone: z.string().describe('Phone number of the finance contact.'),
        email: z.string().describe('Email address of the finance contact.'),
      })
      .optional(),
  }),
})

const putOrganizationContactsBody = z.object({
  admin: z.object({
    fullname: z.string().min(1).describe('Full name of the admin contact.'),
    phone: z.string().describe('Phone number of the admin contact.'),
    email: z.string().describe('Email address of the admin contact.'),
  }),
  document_signing: z
    .object({
      fullname: z
        .string()
        .min(1)
        .describe('Full name of the document signing contact.'),
      phone: z
        .string()
        .describe('Phone number of the document signing contact.'),
      email: z
        .string()
        .describe('Email address of the document signing contact.'),
    })
    .optional(),
  support: z
    .object({
      fullname: z.string().min(1).describe('Full name of the support contact.'),
      phone: z.string().describe('Phone number of the support contact.'),
      email: z.string().describe('Email address of the support contact.'),
    })
    .optional(),
  finance: z
    .object({
      fullname: z.string().min(1).describe('Full name of the finance contact.'),
      phone: z.string().describe('Phone number of the finance contact.'),
      email: z.string().describe('Email address of the finance contact.'),
    })
    .optional(),
})

export const getOrganizationOwnershipResponse = defaultResponse.extend({
  data: z.object({
    is_dao: z
      .boolean()
      .describe(
        'Indicates if the organization is a DAO (Decentralized Autonomous Organization).',
      ),
    owners: z
      .array(
        z.object({
          nanoid: companyOwnerNanoid.describe(
            'Unique identifier of the company owner.',
          ),
          percentage: z
            .number()
            .min(0)
            .max(100)
            .describe('Ownership percentage of the owner.'),
          first_name: z.string().min(1).describe('First name of the owner.'),
          last_name: z.string().min(1).describe('Last name of the owner.'),
          dob: z.string().datetime().describe('Date of birth of the owner.'),
          document_id: z.string().describe('Document ID of the owner.'),
        }),
      )
      .nonempty(),
  }),
})

const putOrganizationOwnershipBody = z.object({
  is_dao: z
    .boolean()
    .describe(
      'Indicates if the organization is a DAO (Decentralized Autonomous Organization).',
    ),
  owners: z
    .array(
      z.object({
        nanoid: companyOwnerNanoid.describe(
          'Unique identifier of the company owner.',
        ),
        percentage: z
          .number()
          .min(0)
          .max(100)
          .describe('Ownership percentage of the owner.'),
        first_name: z.string().min(1).describe('First name of the owner.'),
        last_name: z.string().min(1).describe('Last name of the owner.'),
        dob: z.string().datetime().describe('Date of birth of the owner.'),
      }),
    )
    .nonempty(),
})

export const schema = {
  '/v2/company/:nanoid/users': {
    get: {
      tags: ['V2'],
      params: z.object({
        nanoid: companyNanoid.describe('Unique identifier of the company.'),
      }),
      security: [{ bearerAuth: [] }],
      summary: 'Get company users',
      description:
        'Retrieves a list of users associated with the specified company identified by its nanoid.',
      response: {
        200: defaultResponse.extend({
          data: z.object({
            users: z.array(
              z.object({
                nanoid: userNanoid.describe('Unique identifier for the user.'),
                first_name: z.string().describe("User's first name."),
                last_name: z.string().describe("User's last name."),
                email: z.string().describe("User's email address."),
              }),
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/address': {
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      params: z.object({
        nanoid: companyNanoid.describe('Unique identifier of the company.'),
      }),
      headers: recaptcha,
      summary: 'Update company address details',
      body: putAddressBody,
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/details': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get organization details',
      params: z.object({
        nanoid: companyNanoid,
      }),
      response: {
        200: getOrganizationDetailsResponse,
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update organization details',
      headers: recaptcha,
      params: z.object({
        nanoid: companyNanoid,
      }),
      body: putOrganizationDetailsBody,
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/settings': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get company settings',
      params: z.object({
        nanoid: companyNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            payment_delay_in_minutes: z
              .number()
              .describe(
                'Delay in minutes for processing payments, affects when funds are available for use.',
              ),
            invoicing_enabled: z
              .boolean()
              .describe(
                'Indicates whether invoicing is enabled for the company, allowing for billing and payment tracking.',
              ),
            anonymous_users: z
              .boolean()
              .describe(
                'Indicates if anonymous users are allowed, affecting user privacy and data collection.',
              ),
            default_signer: userNanoid.describe(
              'The default user who will sign documents on behalf of the company.',
            ),
            enabled_document_types: z
              .array(companyDocumentTypes)
              .describe(
                'List of document types that are enabled for use within the company.',
              ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update company settings',
      headers: recaptcha,
      params: z.object({
        nanoid: companyNanoid,
      }),
      body: z.object({
        payment_delay_in_minutes: z
          .number()
          .describe(
            'Delay in minutes for processing payments, affects when funds are available for use.',
          ),
        default_signer: userNanoid.describe(
          'The default user who will sign documents on behalf of the company.',
        ),
        anonymous_users: z
          .boolean()
          .describe(
            'Indicates if anonymous users are allowed, affecting user privacy and data collection.',
          ),
        invoicing_enabled: z
          .boolean()
          .describe(
            'Indicates whether invoicing is enabled for the company, allowing for billing and payment tracking.',
          ),
        enabled_document_types: z
          .array(companyDocumentTypes)
          .describe(
            'List of document types that are enabled for use within the company.',
          ),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/members/:user_nanoid/settings': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get company role settings',
      params: z.object({
        nanoid: companyNanoid,
        user_nanoid: userNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            payment_threshold_per_day: z
              .string()
              .describe(
                'The maximum payment amount allowed per day for the user, used for transaction limits.',
              ),
            payment_threshold_per_transaction: z
              .string()
              .describe(
                'The maximum payment amount allowed per transaction for the user, used for transaction limits.',
              ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update company role settings',
      headers: recaptcha,
      params: z.object({
        nanoid: companyNanoid,
        user_nanoid: userNanoid,
      }),
      body: z.object({
        payment_threshold_per_day: z
          .string()
          .describe(
            'The maximum payment amount allowed per day for the user, used for transaction limits.',
          ),
        payment_threshold_per_transaction: z
          .string()
          .describe(
            'The maximum payment amount allowed per transaction for the user, used for transaction limits.',
          ),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/contacts': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'List organization contacts',
      params: z.object({
        nanoid: companyNanoid,
      }),
      response: {
        200: getOrganizationContactsResponse,
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update organization contacts',
      headers: recaptcha,
      params: z.object({
        nanoid: companyNanoid,
      }),
      body: putOrganizationContactsBody,
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/company/:nanoid/ownership': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get organization ownership',
      params: z.object({
        nanoid: companyNanoid,
      }),
      response: {
        200: getOrganizationOwnershipResponse,
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update organization owner data',
      headers: recaptcha,
      params: z.object({
        nanoid: companyNanoid,
      }),
      body: putOrganizationOwnershipBody,
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
