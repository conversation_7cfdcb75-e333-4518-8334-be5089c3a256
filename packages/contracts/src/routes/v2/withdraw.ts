import { z } from 'zod'
import {
  companyNanoid,
  teamNanoid,
  transactionNanoid,
  userNanoid,
  withdrawAccountNanoid,
} from '../../brands.js'
import { rise_entities } from '../../codegen/zod/rise/rise_entities.js'
import {
  blockchainAddress,
  blockchainSignature,
  recaptcha,
  tokenPermitTypedData,
  tokenPermitTypes,
} from '../../formats.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

const withdrawPayload = z.object({
  from: userNanoid
    .or(companyNanoid)
    .describe('Identifier of the user or company initiating the withdrawal.'),
  amount_cents: z.number().int().describe('Amount to withdraw in cents.'),
  workspace_nanoid: teamNanoid.optional(),
})

export const withdrawFeeResponse = z.object({
  fees: z
    .discriminatedUnion('type', [
      z.object({
        type: z.literal('flat').describe('Flat fee type.'),
        amount_cents: z.number().describe('Flat fee amount in cents.'),
        currency: z.string().describe('Currency of the flat fee.'),
      }),
      z.object({
        type: z.literal('percent').describe('Percentage fee type.'),
        amount_percent: z.number().describe('Percentage amount of the fee.'),
        currency: z.string().describe('Currency of the percentage fee.'),
      }),
      z.object({
        type: z.literal('conversion').describe('Conversion fee type.'),
        rate: z.number().describe('Conversion rate.'),
        source_currency: z.string().describe('Currency being converted from.'),
        destination_currency: z
          .string()
          .describe('Currency being converted to.'),
      }),
    ])
    .array(),
  fee_coverage: z.object({
    enabled: z.boolean().describe('Indicates if fee coverage is enabled.'),
    client: z
      .object({
        type: rise_entities.shape.type.describe('Type of the client.'),
        name: z.string().describe('Name of the client.'),
        avatar: z.string().describe('Avatar URL of the client.'),
      })
      .optional(),
  }),
})

export const schema = {
  '/v2/withdraw/:account_nanoid': {
    post: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Return sign data for withdraw',
      params: z.object({
        account_nanoid: withdrawAccountNanoid.describe(
          'Unique identifier of the withdraw account.',
        ),
      }),
      body: withdrawPayload,
      response: {
        200: defaultResponse.extend({
          data: z.object({
            domain: z.object({
              name: z.string().describe('Name of the domain.'),
              version: z.string().describe('Version of the domain.'),
              chainId: z.number().describe('Chain ID of the blockchain.'),
              verifyingContract: blockchainAddress.describe(
                'Address of the verifying contract.',
              ),
            }),
            types: tokenPermitTypes,
            typed_data: tokenPermitTypedData,
            primary_type: z
              .enum(['Permit'])
              .describe('Primary type of the typed data.'),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Execute a withdraw',
      params: z.object({
        account_nanoid: withdrawAccountNanoid.describe(
          'Unique identifier of the withdraw account.',
        ),
      }),
      headers: recaptcha,
      body: withdrawPayload.extend({
        signer: blockchainAddress.describe('Address of the signer.'),
        typed_data: tokenPermitTypedData,
        signature: blockchainSignature.describe(
          'Signature of the transaction.',
        ),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            transaction: transactionNanoid.describe(
              'Unique identifier of the transaction.',
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/withdraw/:account_nanoid/fee': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Return the fees for a withdraw account',
      params: z.object({
        account_nanoid: withdrawAccountNanoid.describe(
          'Unique identifier of the withdraw account.',
        ),
      }),
      response: {
        200: defaultResponse.extend({
          data: withdrawFeeResponse,
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
