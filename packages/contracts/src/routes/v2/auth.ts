import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
export const schema = {
  '/v2/auth/siwe': {
    get: {
      tags: ['V2'],
      summary: 'Get SIWE',
      description:
        'Retrieves a SIWE (Sign-In with Ethereum) message that can be used for authentication. This message is signed by the user to prove their identity.',
      querystring: z.object({
        wallet: z.string().describe('Wallet address of the user.'),
        riseid: z.string().describe('RiseID of the user.'),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            siwe: z
              .string()
              .describe('The SIWE message that the user needs to sign.'),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/auth/verify': {
    post: {
      tags: ['V2'],
      summary: 'Verify a SIWE message and return a JWT for API authentication',
      body: z.object({
        message: z
          .string()
          .describe('The original SIWE message that was signed by the user.'),
        sig: z
          .string()
          .describe(
            'The signature generated by the user when signing the SIWE message.',
          ),
        nonce: z.string().describe('A unique nonce to prevent replay attacks.'),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            jwt: z
              .string()
              .describe(
                'The JSON Web Token (JWT) for authenticated API requests.',
              ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
