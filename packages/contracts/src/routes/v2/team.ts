import { z } from 'zod'
import {
  certificationNanoid,
  companyNanoid,
  payScheduleNanoid,
  teamNanoid,
  userNanoid,
} from '../../brands.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { boolLike, dateLike, recaptcha } from '../../formats.js'
import type { RiseEntitiesType } from '../../codegen/zod/rise/rise_entities.js'
import { selectable_companies_data } from '../../codegen/zod/rise/companies_data.js'
import { selectable_users_onboarding } from '../../codegen/zod/rise/users_onboarding.js'

const adminRoles = z.enum(['team_admin', 'team_finance_admin', 'team_viewer'])

const getTeamResponse = defaultResponse.extend({
  data: z.object({
    nanoid: z.string().describe('Unique identifier for the team.'),
    name: z.string().min(1).describe('Name of the team.'),
    avatar: z.string().describe('URL of the team avatar.'),
    balance: z.string().describe('Current balance of the team.'),
    administrators: z
      .object({
        relationship_nanoid: z
          .string()
          .describe(
            'Unique identifier for the relationship of the administrator.',
          ),
        user_nanoid: z.string().describe('Unique identifier for the user.'),
        avatar: z.string().describe("URL of the administrator's avatar."),
        first_name: z.string().describe('First name of the administrator.'),
        last_name: z.string().describe('Last name of the administrator.'),
        email: z.string().describe('Email address of the administrator.'),
        type: adminRoles.describe(
          'Role type of the administrator within the team.',
        ),
      })
      .array(),
  }),
})

const postTeamResponse = defaultResponse.extend({
  data: z.object({
    name: z.string().min(1).describe('Name of the team.'),
    nanoid: z.string().describe('Unique identifier for the team.'),
    avatar: z.string().describe('URL of the team avatar.'),
  }),
})

const getTeamMemberSummaryResponse = defaultResponse.extend({
  data: z.object({
    user: z.object({
      nanoid: userNanoid.describe('Unique identifier for the user'),
      avatar: z.string().describe("URL of the user's avatar"),
      first_name: z.string().describe("User's first name"),
      middle_name: z.string().describe("User's middle name"),
      last_name: z.string().describe("User's last name"),
      email: z.string().describe("User's email address"),
      blockchain_address: z
        .string()
        .describe("User's blockchain wallet address"),
      phone: z.string().describe("User's phone number"),
      dob: z.string().nullable().describe("User's date of birth, nullable"),
      address: z.object({
        line_1: z.string().describe('Primary address line'),
        line_2: z.string().describe('Secondary address line'),
        city: z.string().describe('City of residence'),
        state: z.string().describe('State or region'),
        country: z.string().describe('Country of residence'),
        zip_code: z.string().describe('Postal code'),
        timezone: z.string().describe("User's timezone"),
      }),
      social: z.object({
        linkedin: z.string().describe("User's LinkedIn profile URL"),
        discord: z.string().describe("User's Discord handle"),
        website: z.string().describe("User's personal website"),
        x: z.string().describe("User's handle on platform X"),
      }),
      certifications: z
        .object({
          nanoid: certificationNanoid.describe(
            'Unique identifier for the certification',
          ),
          title: z.string().describe('Title of the certification'),
          website: z.string().describe('Website URL for certification details'),
          year: z.number().describe('Year the certification was obtained'),
          file: z
            .string()
            .describe('URL of the file related to the certification'),
        })
        .array()
        .describe("List of user's certifications"),
      onboarding: z.object({
        onboarded: boolLike.describe('Indicates whether the user is onboarded'),
        step: selectable_users_onboarding.shape.step
          .nullable()
          .describe('Current onboarding step, nullable'),
        role: selectable_users_onboarding.shape.role
          .nullable()
          .describe("User's role during onboarding, nullable"),
        moderation_status: selectable_users_onboarding.shape.moderation_status
          .nullable()
          .describe('Moderation status during onboarding, nullable'),
      }),
    }),
    relationship_type: z
      .enum(['team_employee', 'contractor'])
      .describe('Type of relationship with the team'),
    company: z
      .object({
        nanoid: companyNanoid.describe('Unique identifier for the company'),
        avatar: z.string().describe("URL of the company's avatar"),
        name: z.string().describe("Company's name"),
        website: z.string().describe("Company's website URL"),
        blockchain_address: z
          .string()
          .describe("Company's blockchain wallet address"),
        address: z.object({
          line_1: z.string().describe('Primary address line'),
          line_2: z.string().describe('Secondary address line'),
          city: z.string().describe('City of the company'),
          state: z.string().describe('State or region'),
          country: z.string().describe('Country of the company'),
          zip_code: z.string().describe('Postal code'),
          timezone: z.string().describe("Company's timezone"),
        }),
        admin_contact: z
          .object({
            fullname: z.string().describe("Administrator's full name"),
            phone: z.string().describe("Administrator's phone number"),
            email: z.string().describe("Administrator's email address"),
          })
          .nullable()
          .describe('Contact information for the company administrator'),
        incorporation_country:
          selectable_companies_data.shape.incorporation_country.describe(
            'Country of incorporation',
          ),
        incorporation_type:
          selectable_companies_data.shape.incorporation_type.describe(
            'Type of incorporation',
          ),
        private_data: z.object({
          tax_id: z.string().describe("Company's tax identification number"),
        }),
      })
      .optional()
      .describe('Details of the associated company, optional'),
    pay_schedules: z
      .object({
        nanoid: payScheduleNanoid.describe(
          'Unique identifier for the pay schedule',
        ),
        type: z
          .enum(['milestone', 'recurring'])
          .describe('Type of pay schedule'),
        amount: z.string().describe('Payment amount'),
        payments_amount: z.number().describe('Total number of payments'),
        start_date: dateLike.describe('Start date of the pay schedule'),
        end_date: dateLike.describe('End date of the pay schedule'),
      })
      .array()
      .describe('List of pay schedules associated with the user'),
  }),
})

export const schema = {
  '/v2/team/:nanoid/users': {
    get: {
      tags: ['V2'],
      params: z.object({
        nanoid: teamNanoid.describe(
          'Unique identifier of the team for which to retrieve users.',
        ),
      }),
      security: [{ bearerAuth: [] }],
      summary: 'Get team users',
      description:
        'Retrieves a list of users associated with the specified team identified by its nanoid. This endpoint is useful for obtaining user details for a specific team, including their names and email addresses.',
      response: {
        200: defaultResponse.extend({
          data: z.object({
            users: z.array(
              z.object({
                nanoid: userNanoid.describe('Unique identifier for the user.'),
                first_name: z.string().describe("User's first name."),
                last_name: z.string().describe("User's last name."),
                email: z.string().describe("User's email address."),
              }),
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/team/:nanoid/settings': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team settings',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            payment_delay_in_minutes: z.number(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Update team settings',
      params: z.object({
        nanoid: teamNanoid,
      }),
      headers: recaptcha,
      body: z.object({
        payment_delay_in_minutes: z.number(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/team/:nanoid': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team by nanoid',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: getTeamResponse,
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      params: z.object({
        nanoid: teamNanoid,
      }),
      summary: 'Update team by nanoid',
      body: z.object({
        name: z.string().min(1).trim(),
      }),
      response: {
        200: postTeamResponse,
        ...defaultErrorResponses,
      },
    },
    delete: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Delete team by nanoid',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/team': {
    post: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Create a team',
      body: z.object({
        name: z
          .string()
          .min(1)
          .trim()
          .describe(
            'Name of the team, must be at least 1 character long and trimmed',
          ),
        company_nanoid: companyNanoid.describe(
          'Unique identifier for the company in nanoid format',
        ),
        admins: z.array(
          z.object({
            nanoid: userNanoid.describe(
              'Unique identifier for the admin user in nanoid format',
            ),
            type: z
              .enum(['team_admin', 'team_finance_admin', 'team_viewer'])
              .and(z.custom<RiseEntitiesType>())
              .describe(
                'Role type of the admin within the team, can be team_admin, team_finance_admin, or team_viewer',
              ),
          }),
        ),
        avatar_file_name: z
          .string()
          .optional()
          .describe('Optional filename for the team avatar'),
        withdraw_fee_coverage: z
          .boolean()
          .describe('Indicates whether the team covers withdrawal fees'),
      }),
      response: {
        200: postTeamResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/team/:team_nanoid/member/:user_nanoid/settings': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team member settings',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            withdraw_fee_coverage: z.boolean(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Update team member settings',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      body: z.object({
        withdraw_fee_coverage: z.boolean(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/team/:team_nanoid/member/:user_nanoid/summary': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team member summary',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      response: {
        200: getTeamMemberSummaryResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
