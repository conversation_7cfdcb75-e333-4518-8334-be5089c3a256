import { z } from 'zod'
import { teamNanoid, userNanoid } from '../../brands.js'
import { selectable_team_payroll } from '../../codegen/zod/rise/team_payroll.js'
import {
  blockchainAddress,
  dateLike,
  paymentsByCategory,
  recaptcha,
} from '../../formats.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

export const schema = {
  '/v2/payroll/team/:team_nanoid': {
    post: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Enables the payroll for the team',
      params: z.object({
        team_nanoid: teamNanoid.describe(
          'Unique identifier for the team in nanoid format',
        ),
      }),
      body: z.object({
        healthcare_employee_percentage: z
          .number()
          .min(0)
          .max(100)
          .describe('Percentage of health coverage for employees (0-100%)'),
        healthcare_dependent_percentage: z
          .number()
          .min(0)
          .max(100)
          .describe('Percentage of health coverage for dependents (0-100%)'),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            rise_account: blockchainAddress.describe(
              'Blockchain address associated with the team',
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Returns team payroll configuration',
      params: z.object({
        team_nanoid: teamNanoid.describe(
          'Unique identifier for the team in nanoid format',
        ),
      }),
      response: {
        200: defaultResponse.extend({
          data: selectable_team_payroll.omit({
            created_at: true,
            updated_at: true,
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/payroll/team/:team_nanoid/period': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Returns employee payroll table',
      params: z.object({
        team_nanoid: teamNanoid.describe(
          'Unique identifier for the team in nanoid format',
        ),
      }),
      response: {
        200: defaultResponse.extend({
          data: z
            .object({
              avatar: z.string().describe("URL of the employee's avatar image"),
              name: z.string().describe('Full name of the employee'),
              email: z.string().describe('Email address of the employee'),
              nanoid: userNanoid.describe(
                'Unique identifier for the user in nanoid format',
              ),
              payment_categories: paymentsByCategory,
            })
            .array(),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/v2/payroll/team/:team_nanoid/period/card': {
    get: {
      tags: ['V2'],
      security: [{ bearerAuth: [] }],
      summary: 'Returns employee payroll card details',
      params: z.object({
        team_nanoid: teamNanoid.describe(
          'Unique identifier for the team in nanoid format',
        ),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            cash_requirements: z.object({
              currency: z
                .string()
                .describe('Currency of the cash requirements amount'),
              amount_cents: z
                .number()
                .describe('Total cash requirements amount in cents'),
              state: z
                .string()
                .describe('State of the cash requirements amount'),
            }),
            required_deposit: z.object({
              currency: z
                .string()
                .describe('Currency of the required deposit amount'),
              amount_cents: z
                .number()
                .describe('Total required deposit amount in cents'),
            }),
            previous_cycle_balance: z.object({
              currency: z
                .string()
                .describe('Currency of the previous cycle balance'),
              amount_cents: z
                .number()
                .describe('Total previous cycle balance in cents'),
            }),
            current_balance: z.object({
              currency: z.string().describe('Currency of the current balance'),
              amount_cents: z
                .number()
                .describe('Total current balance in cents'),
            }),
            pay_cycle: z.object({
              start_date: dateLike.describe('Start date of the pay cycle'),
              end_date: dateLike.describe('End date of the pay cycle'),
            }),
            last_day_to_update: dateLike.describe(
              'Last day to update payroll information',
            ),
            pay_date: dateLike.describe('Date when the payment is processed'),
            due_date: dateLike.describe(
              'Due date for any outstanding payments',
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
