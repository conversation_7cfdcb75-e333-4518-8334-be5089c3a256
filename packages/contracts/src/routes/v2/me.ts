import { z } from 'zod'
import { companyNanoid, userNanoid } from '../../brands.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

const getMeResponse = defaultResponse.extend({
  data: z.object({
    user: z.object({
      nanoid: userNanoid,
      avatar: z.string(),
      first_name: z.string(),
      middle_name: z.string(),
      last_name: z.string(),
    }),
    company: z
      .object({
        nanoid: companyNanoid,
        avatar: z.string(),
        name: z.string(),
      })
      .optional(),
  }),
})
// example schema
export const schema = {
  '/v2/me': {
    get: {
      tags: ['V2'],
      security: [{ v1Auth: [] }],
      summary: 'Get logged user data',
      response: {
        200: getMeResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
