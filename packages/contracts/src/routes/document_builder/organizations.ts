import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { companyNanoid } from '../../brands.js'

const getOrganizationTeamsResponse = defaultResponse.extend({
  data: z
    .object({
      nanoid: z.string(),
      name: z.string().min(1),
      avatar: z.string(),
    })
    .array(),
})

export const schema = {
  '/document_builder/organizations/:company_nanoid/teams': {
    get: {
      tags: ['Document Builder'],
      security: [{ bearerAuth: [] }],
      summary: 'List organization teams for current user',
      params: z.object({
        company_nanoid: companyNanoid,
      }),
      response: {
        200: getOrganizationTeamsResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
