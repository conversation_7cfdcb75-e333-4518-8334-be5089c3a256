import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

export const schema = {
  '/admin/test_user': {
    post: {
      tags: ['Admin'],
      body: z.object({
        first_name: z.string(),
        middle_name: z.string().optional().nullable(),
        last_name: z.string(),
        phone: z.string().optional().nullable(),
        email_address: z.string(),
        role: z.enum(['payer', 'payee']),
        register_business: z.boolean().optional(),
        address: z.string(),
        city: z.string(),
        state: z.string(),
        country: z.string(),
        company_name: z.string().optional().nullable(),
        tax_id: z.string().optional(),
      }),
      security: [{ bearerAuth: [] }],
      summary: 'Create user',
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
