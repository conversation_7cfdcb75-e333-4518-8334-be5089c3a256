import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

const migrationInfo = z.object({
  company_name: z.string().nullable(),
  migration_status: z.enum(['in_progress', 'completed', 'failed']).nullable(),
  migrated_completed: z.boolean().nullable(),
  failure_reason: z.string().nullable(),
  failure_step: z.string().nullable(),
  riseid: z.string().nullable(),
  rise_account: z.string().nullable(),
  entity_type: z.enum(['company', 'team', 'user']).nullable(),
  rise_id_created: z.boolean(),
  rise_account_created: z.boolean(),
  owner_id: z.number().nullable(),
  owner_type: z.enum(['company', 'user']).nullable(),
  company_relationship_id: z.number().nullable(),
  company_relationship_type: z.enum(['company', 'team']).nullable(),
  inngest_event_id: z.string().nullable(),
})

const getUsersResponse = defaultResponse.extend({
  data: z.object({
    page_index: z.number(),
    page_size: z.number(),
    total_records: z.number(),
    data: z
      .object({
        user_id: z.number(),
        full_name: z.string().nullable(),
        email: z.string(),
      })
      .merge(migrationInfo)
      .array(),
  }),
})

export const schema = {
  '/admin/migration/user': {
    post: {
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      summary: 'Trigger migration for user',
      body: z.object({
        user_id: z.number(),
        company_relationship_id: z.number().optional(),
        company_relationship_type: z.enum(['team', 'company']).optional(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/admin/migration/users': {
    get: {
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      querystring: z.object({
        page_index: z.string(),
        page_size: z.string(),
        search: z.string().optional(),
        sort_by: z.string().optional(),
        sort_dir: z.string().optional(),
      }),
      summary: 'Get list of users for migration',
      response: {
        200: getUsersResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
