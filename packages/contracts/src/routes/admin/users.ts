import { z } from 'zod'
import { recaptcha } from '../../formats.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { userNanoid } from '../../brands.js'
import { selectable_users_rsk } from '../../codegen/zod/rise/users_rsk.js'

export const schema = {
  '/admin/users/:user_nanoid/rsk-reset': {
    post: {
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Reset users RSK',
      params: z.object({
        user_nanoid: userNanoid,
      }),
      body: z.object({
        reset_status: selectable_users_rsk.shape.reset_status.optional(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
