import { z } from 'zod'
import { companyNanoid } from '../../brands.js'
import type { Schema } from '../../types.js'

// example schema
export const schema = {
  '/admin/mastertax': {
    post: {
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      summary: 'create a new mastertax PTS report',
      consumes: ['application/json'], // Set the request Content-Type
      produces: ['text/plain'], // Set the response Content-Type
      body: z.object({
        company_nanoid: companyNanoid,
        check_date: z.string().datetime(),
        payroll_description: z.string().max(40).optional(),
        naic: z.string().max(6).optional(),
      }),
      response: {
        200: z.instanceof(Buffer),
      },
    },
  },
} as const satisfies Schema
