import type { Schema } from '../../types.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import { z } from 'zod'
import { withdrawNanoid } from '../../brands.js'
import { blockchainAddress, blockchainSignature } from '../../formats.js'

export const schema = {
  '/admin/withdraw/retry': {
    post: {
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      summary: 'Retry a transfer',
      body: z.object({
        message: z.string().min(10).max(1000),
        signature: blockchainSignature,
        signer_address: blockchainAddress,
        withdraw_id: withdrawNanoid,
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
