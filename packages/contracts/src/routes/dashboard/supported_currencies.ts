import { z } from 'zod'
import { selectable_supported_currencies } from '../../codegen/zod/rise/supported_currencies.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

export const schema = {
  '/dashboard/supported_currencies': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'List Supported currencies',
      response: {
        200: defaultResponse.extend({
          data: z.array(selectable_supported_currencies.omit({ active: true })),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
