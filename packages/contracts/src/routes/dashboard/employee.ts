import { z } from 'zod'
import { teamNanoid, userNanoid } from '../../brands.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { dateLike } from '../../formats.js'
import { getContractorTransactionsMetricsResponse } from './contractors.js'

const getEmployeeTransactionsMetricsResponse =
  getContractorTransactionsMetricsResponse

const employeeTransactionsMetricsQuery = z.object({
  start_date: dateLike,
  end_date: dateLike,
})

export type EmployeeTransactionsMetricsQuery = z.infer<
  typeof employeeTransactionsMetricsQuery
>

export const schema = {
  '/dashboard/employee/transactions/metrics/:team_nanoid': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get data for dashboard employee widget',
      params: z.object({
        team_nanoid: teamNanoid,
      }),
      querystring: employeeTransactionsMetricsQuery,
      response: {
        200: getEmployeeTransactionsMetricsResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/employee/:nanoid/monthly/transactions': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get employee monthy transactions',
      params: z.object({
        nanoid: userNanoid,
      }),
      querystring: z.object({
        team_nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            start_date: dateLike,
            end_date: dateLike,
            total_payment_amount: z.number(),
            total_withdraw_amount: z.number(),
            monthly_payments: z.array(
              z.object({
                year: z.string(),
                month: z.string(),
                payment_amount: z.number(),
                withdraw_amount: z.number(),
              }),
            ),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
