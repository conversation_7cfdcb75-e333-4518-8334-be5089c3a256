import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import {
  DeliveryHistoryQuery,
  DeliveryHistoryResponse,
  GetWebhookResponse,
  ListWebhooksQuery,
  ListWebhooksResponse,
  RegisterWebhookInput,
  RegisterWebhookResponse,
  RetryDeliveryResponse,
  TestWebhookInput,
  TestWebhookResponse,
  UpdateWebhookInput,
  UpdateWebhookResponse,
  WebhookEndpointParams,
  WebhookDeliveryParams,
} from '../b2b/webhook.js'

// --- Route Definitions for the 'schema' export ---
export const schema = {
  '/dashboard/webhooks/register': {
    post: {
      tags: ['B2B Webhooks'],
      summary: 'Register Webhook',
      body: RegisterWebhookInput,
      response: {
        201: RegisterWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/webhooks/:webhook_nanoid': {
    put: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Update Webhook',
      body: UpdateWebhookInput,
      response: {
        200: UpdateWebhookResponse,
        ...defaultErrorResponses,
      },
    },
    delete: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Delete Webhook',
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
    get: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Get Webhook',
      response: {
        200: GetWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/webhooks': {
    get: {
      tags: ['B2B Webhooks'],
      summary: 'List Webhooks',
      querystring: ListWebhooksQuery,
      response: {
        200: ListWebhooksResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/webhooks/test/:webhook_nanoid': {
    post: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Test Webhook',
      body: TestWebhookInput,
      response: {
        202: TestWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/webhooks/retry/:delivery_nanoid': {
    post: {
      tags: ['B2B Webhooks'],
      params: WebhookDeliveryParams,
      summary: 'Retry Delivery',
      response: {
        202: RetryDeliveryResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/webhooks/:webhook_nanoid/deliveries': {
    get: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Delivery History',
      querystring: DeliveryHistoryQuery,
      response: {
        200: DeliveryHistoryResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
