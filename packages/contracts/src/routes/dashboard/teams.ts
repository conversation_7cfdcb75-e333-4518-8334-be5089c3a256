import { z } from 'zod'
import {
  certificationNanoid,
  companyNanoid,
  inviteNanoid,
  payScheduleNanoid,
  teamNanoid,
  teamRoleNanoid,
  transactionNanoid,
  userNanoid,
} from '../../brands.js'
import { selectable_companies_data } from '../../codegen/zod/rise/companies_data.js'
import { selectable_invites } from '../../codegen/zod/rise/invites.js'
import { selectable_pay_schedules } from '../../codegen/zod/rise/pay_schedules.js'
import type { RiseEntitiesType } from '../../codegen/zod/rise/rise_entities.js'
import { selectable_users_onboarding } from '../../codegen/zod/rise/users_onboarding.js'
import {
  blockchainAddress,
  blockchainSignature,
  boolLike,
  dateLike,
  recaptcha,
} from '../../formats.js'
import {
  riseIDForwarderSetRolesTypedDataResponse,
  riseIDForwarderSetRolesValuesSchema,
} from '../../forwardersTypedDataSchemas.js'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import { inviteTeamManagerRoles, prefill } from './invites.js'

const adminRoles = z.enum(['team_admin', 'team_finance_admin', 'team_viewer'])

const getTeamResponse = defaultResponse.extend({
  data: z.object({
    nanoid: z.string(),
    name: z.string().min(1),
    avatar: z.string(),
    balance: z.string(),
    administrators: z
      .object({
        relationship_nanoid: z.string(),
        user_nanoid: z.string(),
        avatar: z.string(),
        first_name: z.string(),
        last_name: z.string(),
        email: z.string(),
        type: adminRoles,
      })
      .array(),
  }),
})

const postTeamResponse = defaultResponse.extend({
  data: z.object({
    name: z.string().min(1),
    nanoid: z.string(),
    avatar: z.string(),
  }),
})

const getTeamMemberSummaryResponse = defaultResponse.extend({
  data: z.object({
    user: z.object({
      nanoid: userNanoid,
      avatar: z.string(),
      first_name: z.string(),
      middle_name: z.string(),
      last_name: z.string(),
      email: z.string(),
      blockchain_address: z.string(),
      phone: z.string(),
      dob: z.string().nullable(),
      address: z.object({
        line_1: z.string(),
        line_2: z.string(),
        city: z.string(),
        state: z.string(),
        country: z.string(),
        zip_code: z.string(),
        timezone: z.string(),
      }),
      social: z.object({
        linkedin: z.string(),
        discord: z.string(),
        website: z.string(),
        x: z.string(),
      }),
      certifications: z
        .object({
          nanoid: certificationNanoid,
          title: z.string(),
          website: z.string(),
          year: z.number(),
          file: z.string(),
        })
        .array(),
      onboarding: z.object({
        onboarded: boolLike,
        step: selectable_users_onboarding.shape.step.nullable(),
        role: selectable_users_onboarding.shape.role.nullable(),
        moderation_status:
          selectable_users_onboarding.shape.moderation_status.nullable(),
      }),
    }),
    relationship_type: z.enum(['team_employee', 'contractor']),
    company: z
      .object({
        nanoid: companyNanoid,
        avatar: z.string(),
        name: z.string(),
        website: z.string(),
        blockchain_address: z.string(),
        address: z.object({
          line_1: z.string(),
          line_2: z.string(),
          city: z.string(),
          state: z.string(),
          country: z.string(),
          zip_code: z.string(),
          timezone: z.string(),
        }),
        admin_contact: z
          .object({
            fullname: z.string(),
            phone: z.string(),
            email: z.string(),
          })
          .nullable(),
        incorporation_country:
          selectable_companies_data.shape.incorporation_country,
        incorporation_type: selectable_companies_data.shape.incorporation_type,
        private_data: z.object({
          tax_id: z.string(),
        }),
      })
      .optional(),
    pay_schedules: z
      .object({
        nanoid: payScheduleNanoid,
        type: z.enum(['milestone', 'recurring']),
        amount: z.string(),
        payments_amount: z.number(),
        start_date: dateLike,
        end_date: dateLike,
      })
      .array(),
  }),
})

const getTeamInvitesResponse = defaultResponse.extend({
  data: z.array(selectable_invites.extend({ prefill: prefill.partial() })),
})

const getTeamMembersResponse = defaultResponse.extend({
  data: z
    .object({
      nanoid: userNanoid.optional().nullable(),
      relationship_nanoid: teamRoleNanoid.optional(),
      relationship_type: z.enum(['team_employee', 'contractor', 'team_admin']),
      settings: z
        .object({
          withdraw_fee_coverage: boolLike,
          status: z.enum(['active', 'inactive']),
          is_on_payroll: boolLike,
        })
        .optional(),
      user: z.object({
        invite_nanoid: inviteNanoid.optional().nullable(),
        nanoid: userNanoid.optional(),
        avatar: z.string().optional(),
        first_name: z.string().optional().nullable(),
        last_name: z.string().optional().nullable(),
        email: z.string(),
        blockchain_address: z.string().optional(),
        is_activated: z.boolean().optional().nullable(),
        address: z
          .object({
            line_1: z.string().optional(),
            line_2: z.string().optional(),
            city: z.string().optional(),
            state: z.string().optional(),
            country: z.string().optional(),
            zip_code: z.string().optional(),
            timezone: z.string().optional(),
          })
          .optional(),
        onboarding: z
          .object({
            onboarded: boolLike,
            step: selectable_users_onboarding.shape.step.nullable(),
            role: selectable_users_onboarding.shape.role.nullable(),
            moderation_status:
              selectable_users_onboarding.shape.moderation_status.nullable(),
            register_business:
              selectable_users_onboarding.shape.register_business.nullable(),
          })
          .optional(),
        country: z.string(),
      }),
      company: z
        .object({
          nanoid: companyNanoid,
          avatar: z.string(),
          name: z.string(),
          admin_contact: z
            .object({
              fullname: z.string(),
              phone: z.string(),
              email: z.string(),
            })
            .nullable(),
        })
        .nullish(),
      pay_schedules: z
        .object({
          nanoid: payScheduleNanoid,
          type: z.enum(['milestone', 'recurring']),
          amount: z.string(),
          payments_amount: z.number(),
          start_date: dateLike,
          end_date: dateLike,
        })
        .array()
        .optional(),
    })
    .array(),
})

const getTeamInvoicesMetricsResponse = defaultResponse.extend({
  data: z.object({
    year: z.number(),
    total_invoices_paid: z.number(),
    total_volume_cents: z.number(),
    monthly_volumes: z.array(
      z.object({
        from: dateLike,
        to: dateLike,
        amount_paid_cents: z.number(),
        invoices_paid: z.number(),
      }),
    ),
  }),
})

const getTeamTransactionsMetricsResponse = defaultResponse.extend({
  data: z.object({
    start_date: dateLike,
    end_date: dateLike,
    total_payments_amount: z.number(),
    monthly_payments: z.array(
      z.object({
        from: dateLike,
        to: dateLike,
        amount: z.number(),
      }),
    ),
  }),
})

const teamTransactionsMetricsQuery = z.object({
  start_date: dateLike,
  end_date: dateLike,
})
export type TeamTransactionsMetricsQuery = z.infer<
  typeof teamTransactionsMetricsQuery
>

const getMonthlyTeamPaymentsResponse = defaultResponse.extend({
  data: z.object({
    start_date: dateLike,
    end_date: dateLike,
    total_amount: z.number(),
    total_contractor_amount: z.number(),
    total_employee_amount: z.number(),
    monthly_payments: z.array(
      z.object({
        year: z.string(),
        month: z.string(),
        contractor_amount: z.number(),
        employee_amount: z.number(),
        total_amount: z.number(),
      }),
    ),
  }),
})

export const schema = {
  '/dashboard/teams/:nanoid': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team by nanoid',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: getTeamResponse,
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      params: z.object({
        nanoid: teamNanoid,
      }),
      summary: 'Update team by nanoid',
      body: z.object({
        name: z.string().min(1).trim(),
      }),
      response: {
        200: postTeamResponse,
        ...defaultErrorResponses,
      },
    },
    delete: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Delete team by nanoid',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams': {
    post: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Create a team',
      body: z.object({
        name: z.string().min(1).trim(),
        company_nanoid: companyNanoid,
        admins: z.array(
          z.object({
            nanoid: userNanoid,
            type: z
              .enum(['team_admin', 'team_finance_admin', 'team_viewer'])
              .and(z.custom<RiseEntitiesType>()),
          }),
        ),
        avatar_file_name: z.string().optional(),
        withdraw_fee_coverage: z.boolean(),
      }),
      response: {
        200: postTeamResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/avatar/sign-url': {
    post: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Get team avatar sign url',
      body: z.object({
        content_type: z.enum(['image/jpeg', 'image/jpg', 'image/png']),
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            file_name: z.string().min(1),
            url: z.string().min(1),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/avatar/update': {
    post: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Update team avatar',
      params: z.object({
        nanoid: teamNanoid,
      }),
      body: z.object({
        file_name: z.string().min(1).trim(),
      }),
      response: {
        201: defaultResponse.extend({
          data: z.literal('Team avatar updated'),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:team_nanoid/members/:user_nanoid/summary': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team member summary',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      response: {
        200: getTeamMemberSummaryResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:team_nanoid/members/:user_nanoid/settings': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team member settings',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            withdraw_fee_coverage: z.boolean(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      headers: recaptcha,
      summary: 'Update team member settings',
      params: z.object({
        team_nanoid: teamNanoid,
        user_nanoid: userNanoid,
      }),
      body: z.object({
        withdraw_fee_coverage: z.boolean(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/members': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'List team members',
      params: z.object({
        nanoid: teamNanoid,
      }),
      querystring: z.object({
        filter: z
          .object({
            worker_type: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                z.enum(['team_employee', 'contractor', 'team_admin']).array(),
              )
              .optional(),
            onboarding_step: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                selectable_users_onboarding.shape.step.array(),
              )
              .optional(),
            fee_coverage: z.boolean().optional(),
            country: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                z.string().array(),
              )
              .optional(),
            pay_schedule: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                selectable_pay_schedules.shape.type.array(),
              )
              .optional(),
            onboarding_status: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                z.enum(['invited', 'approved', 'rejected', 'pending']).array(),
              )
              .optional(),
            user_team_status: z
              .preprocess(
                (query) => (Array.isArray(query) ? query : [query]),
                z.enum(['active', 'inactive']).array(),
              )
              .optional(),
            search: z.string().min(1).optional(),
          })
          .optional(),
      }),
      response: {
        200: getTeamMembersResponse,
        ...defaultErrorResponses,
      },
    },
    delete: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Remove multiple members from a team',
      params: z.object({
        nanoid: teamNanoid,
      }),
      body: z.object({
        user_nanoids: z.array(userNanoid),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/invites': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'List team invites',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: getTeamInvitesResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/settings': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team settings',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.object({
            payment_delay_in_minutes: z.number(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
    put: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Update team settings',
      params: z.object({
        nanoid: teamNanoid,
      }),
      headers: recaptcha,
      body: z.object({
        payment_delay_in_minutes: z.number(),
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/withdraw-coverage': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get list of talents and their withdraw coverage',
      params: z.object({
        nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          data: z.array(
            z.object({
              nanoid: userNanoid,
              name: z.string(),
              email: z.string(),
              avatar: z.string(),
              withdraw_coverage: z.boolean(),
            }),
          ),
        }),
        ...defaultErrorResponses,
      },
    },
    post: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Set withdraw coverage option for talents',
      params: z.object({
        nanoid: teamNanoid,
      }),
      headers: recaptcha,
      body: z
        .array(
          z.object({
            nanoid: userNanoid,
            withdraw_coverage: z.boolean(),
          }),
        )
        .min(1),
      response: {
        200: defaultResponse.extend({
          data: z.array(transactionNanoid),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:team_nanoid/members/roles': {
    post: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Update team member roles',
      headers: recaptcha,
      params: z.object({
        team_nanoid: teamNanoid,
      }),
      body: z.object({
        members: userNanoid.array(),
        role: inviteTeamManagerRoles.or(z.literal('none')),
      }),
      response: {
        200: defaultResponse.extend({
          data: riseIDForwarderSetRolesTypedDataResponse,
        }),
        ...defaultErrorResponses,
      },
      ...defaultErrorResponses,
    },
    put: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Update team member roles',
      headers: recaptcha,
      params: z.object({
        team_nanoid: teamNanoid,
      }),
      body: z.object({
        members: userNanoid.array(),
        role: inviteTeamManagerRoles.or(z.literal('none')),
        signer: blockchainAddress,
        typed_data: riseIDForwarderSetRolesValuesSchema,
        signature: blockchainSignature,
      }),
      response: {
        200: z.object({
          success: z.boolean(),
          data: z.object({
            transaction: transactionNanoid,
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/invoices/metrics': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team invoices metrics',
      params: z.object({
        nanoid: teamNanoid,
      }),
      querystring: z.object({
        year: z
          .string()
          .regex(/^\d{4}$/)
          .transform((v) => Number.parseInt(v))
          .optional(),
      }),
      response: {
        200: getTeamInvoicesMetricsResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:nanoid/transactions/metrics': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get team transactions metrics by nanoid',
      params: z.object({
        nanoid: teamNanoid,
      }),
      querystring: teamTransactionsMetricsQuery,
      response: {
        200: getTeamTransactionsMetricsResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:team_nanoid/summary': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get summarized team data',
      params: z.object({
        team_nanoid: teamNanoid,
      }),
      response: {
        200: defaultResponse.extend({
          success: z.boolean(),
          data: z.object({
            n_contractors: z.number(),
            n_employees: z.number(),
            n_contractors_countries: z.number(),
            total_open_invoices_cents: z.number(),
          }),
        }),
        ...defaultErrorResponses,
      },
    },
  },
  '/dashboard/teams/:team_nanoid/monthly/payments': {
    get: {
      tags: ['Dashboard'],
      security: [{ bearerAuth: [] }],
      summary: 'Get monthly team payments of contractors and employees',
      params: z.object({
        team_nanoid: teamNanoid,
      }),
      response: {
        200: getMonthlyTeamPaymentsResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
