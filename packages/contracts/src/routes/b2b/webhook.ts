import * as z from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'
import {
  companyNanoid,
  webhookEventTypes,
  webhookDeliveryNanoid,
  webhookEndpointNanoid,
  webhookEventNanoid,
  teamNanoid,
} from '../../brands.js'

// Parameter schemas for reuse
export const WebhookEndpointParams = z.object({
  webhook_nanoid: webhookEndpointNanoid.describe(
    'Unique identifier of the Webhook Endpoint.',
  ),
})
export type WebhookTestParams = z.infer<typeof WebhookEndpointParams>

export const WebhookDeliveryParams = z.object({
  delivery_nanoid: webhookDeliveryNanoid.describe(
    'Unique identifier of the Webhook Delivery.',
  ),
})
export type WebhookDeliveryParams = z.infer<typeof WebhookDeliveryParams>

// 1. Register Webhook — POST /webhooks/register
export const RegisterWebhookInput = z.object({
  company_nanoid: companyNanoid.describe('Unique identifier of the company.'),
  team_nanoid: teamNanoid.optional().describe('Unique identifier of the team.'),
  url: z.string().url(),
  events: z.array(z.string()).nonempty(),
  secret: z.string().min(16),
  is_active: z.boolean().optional(),
})
export type RegisterWebhookInput = z.infer<typeof RegisterWebhookInput>

export const RegisterWebhookResponse = defaultResponse.extend({
  data: z.object({
    webhook_nanoid: webhookEndpointNanoid.describe(
      'Unique identifier of the Webhook.',
    ),
    company_nanoid: companyNanoid.describe('Unique identifier of the company.'),
    url: z.string().url(),
    events: z.array(z.string()),
    is_active: z.boolean(),
  }),
})
export type RegisterWebhookResponse = z.infer<typeof RegisterWebhookResponse>

// 2. Update Webhook — PUT /webhooks/:webhook_nanoid
export const UpdateWebhookInput = z.object({
  url: z.string().url().optional(),
  events: z.array(z.string()).min(1).optional(),
  secret: z.string().min(16).optional(),
  is_active: z.boolean().optional(),
})
export type UpdateWebhookInput = z.infer<typeof UpdateWebhookInput>

export const UpdateWebhookResponse = defaultResponse.extend({
  data: z.object({
    webhook_nanoid: webhookEndpointNanoid.describe(
      'Unique identifier of the Webhook.',
    ),
    url: z.string().url(),
    events: z.array(z.string()),
    is_active: z.boolean(),
  }),
})
export type UpdateWebhookResponse = z.infer<typeof UpdateWebhookResponse>

// 3. Delete Webhook - No body, 204 response (already handled by defaultErrorResponses or by specific Zod for 204)

// 4. Get Webhook — GET /webhooks/:webhook_nanoid
export const GetWebhookResponse = defaultResponse.extend({
  data: z.object({
    webhook_nanoid: webhookEndpointNanoid.describe(
      'Unique identifier of the Webhook.',
    ),
    company_nanoid: companyNanoid.describe('Unique identifier of the company.'),
    url: z.string().url(),
    events: z.array(z.string()),
    is_active: z.boolean(),
  }),
})
export type GetWebhookResponse = z.infer<typeof GetWebhookResponse>

// 5. List Webhooks — GET /webhooks
export const ListWebhooksQuery = z.object({
  company_nanoid: companyNanoid.describe('Unique identifier of the company.'),
  cursor: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).optional(),
})
export type ListWebhooksQuery = z.infer<typeof ListWebhooksQuery>

const WebhookListItemSchema = z.object({
  webhook_nanoid: webhookEndpointNanoid.describe(
    'Unique identifier of the Webhook.',
  ),
  url: z.string().url(),
  events: z.array(z.string()),
  is_active: z.boolean(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})
// Not exporting WebhookListItemSchema directly as a type unless needed,
// as it's primarily used within ListWebhooksResponse.
// If you need it: export type WebhookListItem = z.infer<typeof WebhookListItemSchema>;

export const ListWebhooksResponse = defaultResponse.extend({
  data: z.object({
    webhooks: z.array(WebhookListItemSchema),
    next_cursor: z.string().nullable(),
  }),
})

export type ListWebhooksResponse = z.infer<typeof ListWebhooksResponse>

// 6. Test Webhook — POST /webhooks/test/:webhook_nanoid
export const TestWebhookInput = z.object({
  event_type: z.enum([
    webhookEventTypes.ACCOUNT_DUPLICATED_DETECTED,
    webhookEventTypes.DEPOSIT_RECEIVED,
    webhookEventTypes.PAYMENT_SENT,
    webhookEventTypes.PAY_SCHEDULE_CREATED,
    webhookEventTypes.INVITE_ACCEPTED,
    webhookEventTypes.RISEID_ADDRESS_UPDATED,
  ]),
})
export type TestWebhookInput = z.infer<typeof TestWebhookInput>

export const TestWebhookResponse = defaultResponse.extend({
  data: z.object({
    delivery_nanoid: webhookDeliveryNanoid.describe(
      'Unique identifier of the Webhook Delivery.',
    ),
    message: z.string(),
  }),
})

export type TestWebhookResponse = z.infer<typeof TestWebhookResponse>

// 7. Retry Delivery — POST /webhooks/retry/:delivery_nanoid
// No request body
export const RetryDeliveryResponse = defaultResponse.extend({
  data: z.object({
    delivery_nanoid: webhookDeliveryNanoid.describe(
      'Unique identifier of the Webhook Delivery.',
    ),
    status: z.string(),
  }),
})
export type RetryDeliveryResponse = z.infer<typeof RetryDeliveryResponse>

// 8. Delivery History — GET /webhooks/:webhook_nanoid/deliveries
export const DeliveryHistoryQuery = z.object({
  cursor: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).optional(),
})
export type DeliveryHistoryQuery = z.infer<typeof DeliveryHistoryQuery>

const DeliveryHistoryItemSchema = z.object({
  delivery_nanoid: webhookDeliveryNanoid.describe(
    'Unique identifier of the Webhook Delivery.',
  ),
  event_nanoid: webhookEventNanoid.describe(
    'Unique identifier of the Webhook Event.',
  ),
  status: z.enum(['queued', 'success', 'failed', 'retrying']),
  response_code: z.number().int().nullable(),
  created_at: z.string().datetime(),
})
// Not exporting DeliveryHistoryItemSchema directly as a type unless needed.
// If you need it: export type DeliveryHistoryItem = z.infer<typeof DeliveryHistoryItemSchema>;

export const DeliveryHistoryResponse = defaultResponse.extend({
  data: z.object({
    deliveries: z.array(DeliveryHistoryItemSchema),
    next_cursor: z.string().nullable(),
  }),
})
export type DeliveryHistoryResponse = z.infer<typeof DeliveryHistoryResponse>

// --- Route Definitions for the 'schema' export ---
export const schema = {
  '/webhooks/register': {
    post: {
      tags: ['B2B Webhooks'],
      summary: 'Register Webhook',
      body: RegisterWebhookInput,
      response: {
        201: RegisterWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/webhooks/:webhook_nanoid': {
    put: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Update Webhook',
      body: UpdateWebhookInput,
      response: {
        200: UpdateWebhookResponse,
        ...defaultErrorResponses,
      },
    },
    delete: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Delete Webhook',
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
    get: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Get Webhook',
      response: {
        200: GetWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/webhooks': {
    get: {
      tags: ['B2B Webhooks'],
      summary: 'List Webhooks',
      querystring: ListWebhooksQuery,
      response: {
        200: ListWebhooksResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/webhooks/test/:webhook_nanoid': {
    post: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Test Webhook',
      body: TestWebhookInput,
      response: {
        202: TestWebhookResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/webhooks/retry/:delivery_nanoid': {
    post: {
      tags: ['B2B Webhooks'],
      params: WebhookDeliveryParams,
      summary: 'Retry Delivery',
      response: {
        202: RetryDeliveryResponse,
        ...defaultErrorResponses,
      },
    },
  },
  '/webhooks/:webhook_nanoid/deliveries': {
    get: {
      tags: ['B2B Webhooks'],
      params: WebhookEndpointParams,
      summary: 'Delivery History',
      querystring: DeliveryHistoryQuery,
      response: {
        200: DeliveryHistoryResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
