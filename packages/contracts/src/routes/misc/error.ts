import { z } from 'zod'
import {
  defaultErrorResponses,
  defaultResponse,
  errorCode,
} from '../../response.js'
import type { Schema } from '../../types.js'

export const schema = {
  '/misc/error': {
    get: {
      tags: ['Misc'],
      security: [{ bearerAuth: [] }],
      summary: 'Log and return an error code',
      querystring: z.object({
        error_code: errorCode,
      }),
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
