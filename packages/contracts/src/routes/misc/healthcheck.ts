import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

// example schema
export const schema = {
  '/healthcheck': {
    get: {
      tags: ['Misc'],
      security: [{ bearerAuth: [] }],
      summary: 'Healthcheck Endpoint',
      response: {
        200: defaultResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
