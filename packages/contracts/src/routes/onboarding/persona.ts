import { z } from 'zod'
import { defaultErrorResponses, defaultResponse } from '../../response.js'
import type { Schema } from '../../types.js'

const createPersonaInquiryResponse = defaultResponse.extend({
  data: z.object({
    inquiry_id: z.string(),
    status: z
      .enum([
        'created',
        'pending',
        'completed',
        'expired',
        'failed',
        'needs_review',
        'approved',
        'declined',
      ])
      .nullable(),
    token: z.string().nullable(),
  }),
})

export const schema = {
  '/onboarding/persona/inquiries': {
    post: {
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      summary: 'Create Persona Inquiry',
      response: {
        200: createPersonaInquiryResponse,
        ...defaultErrorResponses,
      },
    },
  },
} as const satisfies Schema
