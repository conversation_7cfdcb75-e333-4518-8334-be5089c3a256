// @ts-nocheck
// This file is autogenerated, do not edit manually
import { z } from 'zod'

export const validContracts = z.enum([
  'RiseDeployFactoryGovernor_impl',
  'RiseRampDepositCCTP',
  'RiseRampDepositCCTP',
  'RiseRampDepositSwap_impl',
  'RiseRampWithdrawCCTP_impl',
  'RiseAccount_impl',
  'RiseRampWithdrawExchange_impl',
  'RiseRampWithdrawUniSwap_impl',
  'RiseAccountGovernor_impl',
  'RiseAccountSubscriptionUsage_impl',
  'RisePaymentHandler_impl',
  'RiseRampWithdrawUnblock_impl',
  'RiseRampDeposit_impl',
  'RiseRampWithdrawInternationalUSD_impl',
  'RiseAccountForwarder_impl',
  'RisePricesOracle_impl',
  'RiseRampDepositSwap',
  'RiseRampWithdrawUSDUS_impl',
  'RiseRampWithdrawERC20Token_impl',
  'RiseRampWithdrawInternationalUSDManual_impl',
  'RiseRampWithdrawSwap_impl',
  'RiseRampDepositCCIP_impl',
  'RiseID_impl',
  'RiseRampWithdrawCCIP_impl',
  'RisePayToken_impl',
  'RisePricesOracle',
  'RiseForwarder_impl',
  'RisePaymentHandlerForwarder_impl',
  'RiseDepositGovernor_impl',
  'RiseRampWithdrawUniSwap',
  'RiseRouter_impl',
  'RiseRampDeposit',
  'RiseIDForwarder_impl',
  'RiseRampDepositCCIP',
  'RiseAccountSubscriptionUsage',
  'RiseRampWithdrawSwap',
  'RiseRampWithdrawCCTP',
  'RiseRampWithdrawUSDUS',
  'RiseRampWithdrawUnblock',
  'RiseRampWithdrawERC20Token',
  'RiseRampWithdrawExchange',
  'RiseDeployFactoryGovernor_impl',
  'RiseDeployFactory_impl',
  'RiseAccountGovernor',
  'RiseAccessGovernor',
  'RiseAccessGovernor_impl',
  'RiseDepositGovernor',
  'RiseTokenGovernor_impl',
  'PYUSD',
  'DAI',
  'RisePaymentIdentifiers',
  'RiseAccess_impl',
  'TransferHelper',
  'RiseProxy_impl',
  'RiseUSD',
  'RiseEUR',
  'RiseRampWithdrawInternationalUSD',
  'RiseRampWithdrawInternationalUSDManual',
  'BokkyPooBahsDateTimeLibrary',
  'RiseRouter',
  'RiseRampWithdrawCCIP',
  'RisePayToken',
  'RiseDedicatedFund',
  'RiseForwarder',
  'RiseAccess',
  'RisePayRampEURGBP',
  'RisePaySchedules',
  'RisePayRampNGN',
  'RisePay',
  'RisePayRampUSDCMainnet',
  'USDC',
  'EURC',
  'RiseIDBusiness',
  'RisePayRampUniswap',
  'RiseFundFulfillment',
  'RiseIDIndividual',
  'RisePayRampUSDC',
  'RisePlannedPayments',
  'RiseIDDAO',
  'RiseIDFactory',
  'RisePayRampForEx',
  'RisePayRampUSDInternational',
  'RisePayRampUSDUS',
  'RiseGovernor',
  'RiseStorage',
  'RiseDeterministicDeployFactory',
  'RiseDeductionsAndCredits',
  'RisePayTokenV1',
  'RiseFinanceGovernor',
  'MessageTransmitter',
  'MessageTransmitter',
  'MessageTransmitter',
  'MessageTransmitter',
  'MessageTransmitter',
  'MessageTransmitter',
])
export type ValidContracts = z.infer<typeof validContracts>
export const validNetworks = z.enum([
  'ethereum',
  'arbitrum',
  'base',
  'polygon',
  'avalanche',
  'optimism',
])
export type ValidNetworks = z.infer<typeof validNetworks>
