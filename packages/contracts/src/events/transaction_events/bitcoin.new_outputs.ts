import { z } from 'zod'
import {
  bitcoinOutputRefiner,
  blockHeightInt,
  ownerIdRefiner,
} from '../../bitcoin.js'
import { channelIdRegex } from '../../chainflip.js'
import type { EventSchema } from '../../types.js'

export const schema = {
  'transaction_events/bitcoin.new_outputs': {
    data: z.object({
      // Both properties should be present, but making them optional allows easier testing
      outputs: z.array(bitcoinOutputRefiner).optional(),
      highestBlockHeight: blockHeightInt.optional(),
    }),
  },
} as const satisfies EventSchema

export const eventDataSchema = z.object({
  updatedSwaps: z.array(channelIdRegex).optional(),
  pendingNewSwapOwnerIds: z.array(ownerIdRefiner).optional(),
  newSwaps: z.array(channelIdRegex).optional(),
  lastProcessedBlockHeight: blockHeightInt,
  // TODO: Figure out why updated_at is not being set automatically, then remove this
  lastUpdated: z.coerce.date().transform((date) => date.toISOString()),
})
