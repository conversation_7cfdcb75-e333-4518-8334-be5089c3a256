import { z } from 'zod'
import type { EventSchema } from '../../types.js'
import { teamNanoid } from '../../brands.js'
import { payCycle, risePayrollProgram } from '../../formats.js'

export const schema = {
  'dashboard/payroll.healthcheck_team': {
    data: z.object({
      team_nanoid: teamNanoid,
      payroll_program: risePayrollProgram,
      pay_cycle: payCycle,
      run_id: z.string(),
    }),
  },
} as const satisfies EventSchema
