import { z } from 'zod'
import type { EventSchema } from '../../types.js'
import { teamNanoid, userNanoid } from '../../brands.js'
import { payCycle, risePayrollProgram } from '../../formats.js'

export const schema = {
  'dashboard/payroll.healthcheck_employee': {
    data: z.object({
      team_nanoid: teamNanoid,
      payroll_program: risePayrollProgram,
      pay_cycle: payCycle,
      user_nanoid: userNanoid,
      team_payroll_account: z.string(),
      run_id: z.string(),
    }),
  },
} as const satisfies EventSchema
