import { z } from 'zod'
import type { EventSchema } from '../../types.js'

const postUsersDataBody = z.object({
  user_nanoid: z.string(),
  email_address: z.string().email().optional(),
  first_name: z.string().optional(),
  middle_name: z.string().optional(),
  last_name: z.string().optional(),
  phone: z.string().optional(),
  last_modified_by: z.string(),
})

// example schema
export const schema = {
  'dashboard/users.data_change': {
    data: postUsersDataBody,
  },
} as const satisfies EventSchema
