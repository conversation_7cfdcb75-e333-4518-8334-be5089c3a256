import { z } from 'zod'
import { blockchainAddress } from '../../formats.js'
import { validNetworks } from '../../smartContractTypes.js'
import type { EventSchema } from '../../types.js'

export const schema = {
  'dashboard/riseaccounts.team_balance_check': {
    data: z.object({
      account_address: blockchainAddress,
      network: validNetworks,
      team_nanoid: z.string(),
    }),
  },
} as const satisfies EventSchema
