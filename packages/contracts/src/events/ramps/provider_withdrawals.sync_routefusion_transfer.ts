import { z } from 'zod'
import { selectable_provider_withdrawals } from '../../codegen/zod/rise/provider_withdrawals.js'
import { selectable_users_withdraw_account } from '../../codegen/zod/rise_private/users_withdraw_account.js'
import type { EventSchema } from '../../types.js'

// example schema
export const schema = {
  'ramps/provider_withdrawals.sync_routefusion_transfer': {
    data: z.object({
      id: selectable_provider_withdrawals.shape.id.unwrap(),
      ramp: selectable_users_withdraw_account.shape.ramp,
      reference: selectable_provider_withdrawals.shape.reference.unwrap(),
    }),
  },
} as const satisfies EventSchema
