[{"name": "Admin<PERSON><PERSON>ed", "type": "event", "inputs": [{"name": "previousAdmin", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newAdmin", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"name": "BeaconUpgraded", "type": "event", "inputs": [{"name": "beacon", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "Upgraded", "type": "event", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "initialize", "type": "function", "inputs": [{"name": "_riseAccess", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "paySchedules_PaySchedule_Get", "type": "function", "inputs": [{"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "stateMutability": "view"}, {"name": "paySchedules_PaySchedule_Set", "type": "function", "inputs": [{"name": "psHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "ps", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "paySchedules_PaySchedules", "type": "function", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "paySchedules_RelationshipPayScheduleSet_Add", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "paySchedules_RelationshipPayScheduleSet_All", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "paySchedules_RelationshipPayScheduleSet_At", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "paySchedules_RelationshipPayScheduleSet_Contains", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "paySchedules_RelationshipPayScheduleSet_Len", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "paySchedules_RelationshipPayScheduleSet_Remove", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "paySchedules_UserPayerPayee_Add", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "paySchedules_UserPayerPayee_All", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "paySchedules_UserPayerPayee_At", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "paySchedules_UserPayerPayee_Contains", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "paySchedules_UserPayerPayee_Len", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "paySchedules_UserPayerPayee_Remove", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "plannedPayments_Payment_Get", "type": "function", "inputs": [{"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay"}], "stateMutability": "view"}, {"name": "plannedPayments_Payment_Set", "type": "function", "inputs": [{"name": "payHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "pay", "type": "tuple", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "plannedPayments_Payments", "type": "function", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "plannedPayments_RelationshipPaySet_Add", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "plannedPayments_RelationshipPaySet_All", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "plannedPayments_RelationshipPaySet_At", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "plannedPayments_RelationshipPaySet_Contains", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "plannedPayments_RelationshipPaySet_Len", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "plannedPayments_RelationshipPaySet_Remove", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "plannedPayments_UserPayerPayee_Add", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "plannedPayments_UserPayerPayee_All", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "plannedPayments_UserPayerPayee_At", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "plannedPayments_UserPayerPayee_Contains", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "plannedPayments_UserPayerPayee_Len", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "plannedPayments_UserPayerPayee_Remove", "type": "function", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "proxiableUUID", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "upgradeTo", "type": "function", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "upgradeToAndCall", "type": "function", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}]