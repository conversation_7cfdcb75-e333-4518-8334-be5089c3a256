[{"name": "Rise_Proxy_AlreadyInitialized", "type": "error", "inputs": []}, {"name": "Rise_Proxy_BadBeacon", "type": "error", "inputs": []}, {"name": "Rise_Proxy_BadImplementation", "type": "error", "inputs": []}, {"type": "fallback", "stateMutability": "payable"}, {"name": "initProxy", "type": "function", "inputs": [{"name": "router", "type": "address", "internalType": "address"}, {"name": "implementationRef", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}]