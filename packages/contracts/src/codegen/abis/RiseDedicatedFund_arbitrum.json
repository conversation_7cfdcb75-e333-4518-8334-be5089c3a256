[{"type": "fallback", "stateMutability": "payable"}, {"name": "USDC", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "_USDC", "type": "address", "internalType": "address"}, {"name": "_riseTreasury", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "initData", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"name": "riseTreasury", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "sendTokenToTreasury", "type": "function", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "sendUSDCToTreasury", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]