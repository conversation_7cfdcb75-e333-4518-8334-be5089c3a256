[{"type": "constructor", "inputs": [{"name": "_riseAccess", "type": "address", "internalType": "address"}, {"name": "_risePayToken", "type": "address", "internalType": "address"}, {"name": "_riseStorage", "type": "address", "internalType": "address"}, {"name": "_riseDeductionsCredits", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "PayHash", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "salt", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"name": "PaymentAddExists", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentAddInvalid", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentAddLimit", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentAdded", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentExecuted", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "payHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentExecutedFailed", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentRemoved", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "PaymentsRelationshipCreated", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "ZeroPayPaymentsRemaining", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "fallback", "stateMutability": "payable"}, {"name": "add", "type": "function", "inputs": [{"name": "payAdd", "type": "tuple", "components": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.PayAdd"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "batchAdd", "type": "function", "inputs": [{"name": "payAdd", "type": "tuple[]", "components": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.PayAdd[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "batchPay", "type": "function", "inputs": [{"name": "payExecute", "type": "tuple[]", "components": [{"name": "payerIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct IRiseStorageTypes.PayExecute[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "batchPayLimit", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "batchRemove", "type": "function", "inputs": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payHashes", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "canBePaid", "type": "function", "inputs": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "getPayHash", "type": "function", "inputs": [{"name": "payment", "type": "tuple", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getPaymentAt", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay"}], "stateMutability": "view"}, {"name": "getPayments", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "payDate", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay[]"}], "stateMutability": "view"}, {"name": "getPaymentsHashRange", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "startIndex", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getPaymentsRange", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "startIndex", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "results", "type": "tuple[]", "components": [{"name": "extId", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "date", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseStorageTypes.Pay[]"}, {"name": "totalAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "pay", "type": "function", "inputs": [{"name": "payExecute", "type": "tuple", "components": [{"name": "payerIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct IRiseStorageTypes.PayExecute"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "payItemLimit", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "remove", "type": "function", "inputs": [{"name": "payRemove", "type": "tuple", "components": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "payHash", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct IRiseStorageTypes.PayRemove"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "removeByIndex", "type": "function", "inputs": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "indexes", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseDeductionsAndCredits", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseDeductionsAndCredits"}], "stateMutability": "view"}, {"name": "risePayToken", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRisePayToken"}], "stateMutability": "view"}, {"name": "riseStorage", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseStorage"}], "stateMutability": "view"}, {"name": "updateLimits", "type": "function", "inputs": [{"name": "_payItemLimit", "type": "uint256", "internalType": "uint256"}, {"name": "_batchPayLimit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "upgradeDeductionsAndCedits", "type": "function", "inputs": [{"name": "_riseDeductionsAndCredits", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]