[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "CertifiedDataChanged", "type": "event", "inputs": [{"name": "key", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "value", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"name": "ContractCreated", "type": "event", "inputs": [{"name": "operation", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "contractAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"name": "DataChanged", "type": "event", "inputs": [{"name": "key", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "value", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"name": "Executed", "type": "event", "inputs": [{"name": "operation", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "data", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"name": "OwnershipTransferred", "type": "event", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseIDDelegateAdded", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseIDDelegateRemoved", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "payable"}, {"name": "addDelegate", "type": "function", "inputs": [{"name": "accountIdx", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "addDelegate", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "callRise", "type": "function", "inputs": [{"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "execute", "type": "function", "inputs": [{"name": "_operation", "type": "uint256", "internalType": "uint256"}, {"name": "_to", "type": "uint256", "internalType": "uint256"}, {"name": "_value", "type": "uint256", "internalType": "uint256"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"name": "execute", "type": "function", "inputs": [{"name": "_operation", "type": "uint256", "internalType": "uint256"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_value", "type": "uint256", "internalType": "uint256"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"name": "executeRise", "type": "function", "inputs": [{"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "executeRiseFund", "type": "function", "inputs": [{"name": "tokenIdx", "type": "uint256", "internalType": "uint256"}, {"name": "rampIdx", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "_fromIdx", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "executeRiseFund", "type": "function", "inputs": [{"name": "tokenIdx", "type": "uint256", "internalType": "uint256"}, {"name": "rampIdx", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getCertifiedData", "type": "function", "inputs": [{"name": "keys", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "values", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"name": "getData", "type": "function", "inputs": [{"name": "keys", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "values", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"name": "getDelegates", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getDelegatesLength", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "_newOwner", "type": "address", "internalType": "address"}, {"name": "_riseAccess", "type": "address", "internalType": "address"}, {"name": "_risePayContract", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "initData", "type": "function", "inputs": [{"name": "_newOwner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"name": "isDelegate", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "isDelegate", "type": "function", "inputs": [{"name": "accountIdx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "owner", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "recover", "type": "function", "inputs": [{"name": "owner<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "removeDelegate", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "removeDelegate", "type": "function", "inputs": [{"name": "accountIdx", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "renounceOwnership", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "role", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "setCertifiedData", "type": "function", "inputs": [{"name": "_keys", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "_values", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setData", "type": "function", "inputs": [{"name": "_keys", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "_values", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "supportsInterface", "type": "function", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "transferOwnership", "type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "updateRiseContracts", "type": "function", "inputs": [{"name": "riseAccessContractIdx", "type": "uint256", "internalType": "uint256"}, {"name": "risePayContractIdx", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]