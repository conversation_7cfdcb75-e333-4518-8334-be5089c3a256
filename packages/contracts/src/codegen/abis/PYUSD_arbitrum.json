[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "ReentrancyGuardReentrantCall", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_Unauthorized", "type": "error", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Approval", "type": "event", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"name": "Transfer", "type": "event", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "allowance", "type": "function", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "approve", "type": "function", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "balanceOf", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "burn", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "decimals", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "pure"}, {"name": "decreaseAllowance", "type": "function", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "subtractedValue", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "executeTransfers", "type": "function", "inputs": [{"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getTransfer", "type": "function", "inputs": [{"name": "transferHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "recipient", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "getTransferHash", "type": "function", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "increaseAllowance", "type": "function", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "addedValue", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "init", "type": "function", "inputs": [{"name": "_riseRouter", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "internalTransfer", "type": "function", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "mint", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "name", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"name": "pendingTransferHashes", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "pendingTransferHashesSlice", "type": "function", "inputs": [{"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "_transfers", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "pendingTransfersAll", "type": "function", "inputs": [], "outputs": [{"name": "amount", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "recipient", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "pendingTransfersContains", "type": "function", "inputs": [{"name": "hash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "pendingTransfersCount", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "symbol", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"name": "totalSupply", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "transfer", "type": "function", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"name": "transferFrom", "type": "function", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}]