[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "batchRoleGrantOrRevoke", "type": "function", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "grantRevoke", "type": "bool", "internalType": "bool"}, {"name": "members", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getAllRoles", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "tuple[]", "components": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "adminRole", "type": "bytes32", "internalType": "bytes32"}, {"name": "admin<PERSON>ame", "type": "string", "internalType": "string"}], "internalType": "struct RiseAccessGovernor.Role[]"}], "stateMutability": "view"}, {"name": "getAllRolesCount", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "riseRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "initializeRoleNames", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"name": "multiRoleGrantOrRevoke", "type": "function", "inputs": [{"name": "member", "type": "address", "internalType": "address"}, {"name": "grantRevoke", "type": "bool", "internalType": "bool"}, {"name": "roles", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "setRoleAdmin", "type": "function", "inputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "string", "internalType": "string"}, {"name": "adminRoleName", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setRoleNames", "type": "function", "inputs": [{"name": "name", "type": "string[]", "internalType": "string[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "syncRoleHashes", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}]