[{"name": "addressIdentifier", "type": "function", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "dataIdentifier", "type": "function", "inputs": [{"name": "data", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "groupIdentifier", "type": "function", "inputs": [{"name": "groupID", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "hashDayIdentifier", "type": "function", "inputs": [{"name": "_hash", "type": "bytes32", "internalType": "bytes32"}, {"name": "_identifier", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "hashSubscriptionID", "type": "function", "inputs": [{"name": "monthHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "hashSubscriptionMonth", "type": "function", "inputs": [{"name": "monthHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "pendingIdentifier", "type": "function", "inputs": [{"name": "baseHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}]