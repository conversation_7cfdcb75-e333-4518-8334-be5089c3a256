[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"name": "RiseOwnerAdded", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseOwnerRemoved", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RouteChanged", "type": "event", "inputs": [{"name": "routerReferenceHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "addOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getOwners", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getOwnersLength", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "isOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "removeOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "resolve", "type": "function", "inputs": [{"name": "ref", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "resolveNoFail", "type": "function", "inputs": [{"name": "ref", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "setResolver", "type": "function", "inputs": [{"name": "_ref", "type": "bytes32", "internalType": "bytes32"}, {"name": "_implementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setResolvers", "type": "function", "inputs": [{"name": "_ref", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "_implementation", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}]