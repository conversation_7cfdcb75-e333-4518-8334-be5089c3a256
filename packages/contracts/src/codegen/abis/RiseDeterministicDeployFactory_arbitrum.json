[{"name": "NewInstance", "type": "event", "inputs": [{"name": "instance", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"name": "OwnershipTransferred", "type": "event", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "clone", "type": "function", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "owner", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "predict", "type": "function", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "renounceOwnership", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"name": "transferOwnership", "type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}]