/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RisePayTokenV1_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'allowance(uint256,uint256)'
      | 'allowance(address,address)'
      | 'approve(address,uint256)'
      | 'approve(uint256,uint256)'
      | 'balanceOf(address)'
      | 'balanceOf(uint256)'
      | 'burn(address,uint256)'
      | 'burn(uint256,uint256)'
      | 'decimals'
      | 'decreaseAllowance(uint256,uint256)'
      | 'decreaseAllowance(address,uint256)'
      | 'getHash'
      | 'getPayerAndPayeeHash'
      | 'increaseAllowance(address,uint256)'
      | 'increaseAllowance(uint256,uint256)'
      | 'isPayerAndPayee'
      | 'mint(uint256,uint256)'
      | 'mint(address,uint256)'
      | 'name'
      | 'pay(uint256,uint256,uint256,uint256)'
      | 'pay(address,address,uint256,uint256)'
      | 'payTxs'
      | 'riseAccess'
      | 'setPayerAndPayee'
      | 'symbol'
      | 'totalSupply'
      | 'transfer(uint256,uint256)'
      | 'transfer(address,uint256)'
      | 'transferFrom(address,address,uint256)'
      | 'transferFrom(uint256,uint256,uint256)'
      | 'txSent(bytes32)'
      | 'txSent(address,address,uint256,uint256)',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Approval'
      | 'PayerToPayeeRelationship'
      | 'Transfer',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'allowance(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'allowance(address,address)',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'approve(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'approve(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'balanceOf(address)',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'balanceOf(uint256)',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'burn(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'burn(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'decimals', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'decreaseAllowance(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'decreaseAllowance(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getHash',
    values: [AddressLike, AddressLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayerAndPayeeHash',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'increaseAllowance(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'increaseAllowance(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isPayerAndPayee',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'mint(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'mint(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'name', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'pay(uint256,uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'pay(address,address,uint256,uint256)',
    values: [AddressLike, AddressLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'payTxs', values: [BytesLike]): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setPayerAndPayee',
    values: [AddressLike, AddressLike, boolean],
  ): string
  encodeFunctionData(functionFragment: 'symbol', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'totalSupply',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'transfer(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'transfer(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom(address,address,uint256)',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom(uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'txSent(bytes32)',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'txSent(address,address,uint256,uint256)',
    values: [AddressLike, AddressLike, BigNumberish, BigNumberish],
  ): string

  decodeFunctionResult(
    functionFragment: 'allowance(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'allowance(address,address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'approve(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'approve(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'balanceOf(address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'balanceOf(uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'burn(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'burn(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'decimals', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'decreaseAllowance(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'decreaseAllowance(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getHash', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getPayerAndPayeeHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'increaseAllowance(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'increaseAllowance(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isPayerAndPayee',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'mint(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'mint(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'pay(uint256,uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pay(address,address,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'payTxs', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setPayerAndPayee',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'symbol', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'totalSupply', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'transfer(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transfer(address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom(address,address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom(uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'txSent(bytes32)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'txSent(address,address,uint256,uint256)',
    data: BytesLike,
  ): Result
}

export namespace ApprovalEvent {
  export type InputTuple = [
    owner: AddressLike,
    spender: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [owner: string, spender: string, value: bigint]
  export interface OutputObject {
    owner: string
    spender: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PayerToPayeeRelationshipEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    enabled: boolean,
  ]
  export type OutputTuple = [payer: string, payee: string, enabled: boolean]
  export interface OutputObject {
    payer: string
    payee: string
    enabled: boolean
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace TransferEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [from: string, to: string, value: bigint]
  export interface OutputObject {
    from: string
    to: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePayTokenV1_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePayTokenV1_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePayTokenV1_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  'allowance(uint256,uint256)': TypedContractMethod<
    [ownerIdx: BigNumberish, spenderIdx: BigNumberish],
    [bigint],
    'view'
  >

  'allowance(address,address)': TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >

  'approve(address,uint256)': TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'approve(uint256,uint256)': TypedContractMethod<
    [spenderIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'balanceOf(address)': TypedContractMethod<
    [account: AddressLike],
    [bigint],
    'view'
  >

  'balanceOf(uint256)': TypedContractMethod<
    [accountIdx: BigNumberish],
    [bigint],
    'view'
  >

  'burn(address,uint256)': TypedContractMethod<
    [owner: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  'burn(uint256,uint256)': TypedContractMethod<
    [ownerIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  decimals: TypedContractMethod<[], [bigint], 'view'>

  'decreaseAllowance(uint256,uint256)': TypedContractMethod<
    [spenderIdx: BigNumberish, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'decreaseAllowance(address,uint256)': TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  getHash: TypedContractMethod<
    [
      from: AddressLike,
      to: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [string],
    'view'
  >

  getPayerAndPayeeHash: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [string],
    'view'
  >

  'increaseAllowance(address,uint256)': TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'increaseAllowance(uint256,uint256)': TypedContractMethod<
    [spenderIdx: BigNumberish, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  isPayerAndPayee: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >

  'mint(uint256,uint256)': TypedContractMethod<
    [ownerIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  'mint(address,uint256)': TypedContractMethod<
    [owner: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  name: TypedContractMethod<[], [string], 'view'>

  'pay(uint256,uint256,uint256,uint256)': TypedContractMethod<
    [
      payerIdx: BigNumberish,
      payeeIdx: BigNumberish,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  'pay(address,address,uint256,uint256)': TypedContractMethod<
    [
      payer: AddressLike,
      payee: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  payTxs: TypedContractMethod<[arg0: BytesLike], [boolean], 'view'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  setPayerAndPayee: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, enable: boolean],
    [void],
    'nonpayable'
  >

  symbol: TypedContractMethod<[], [string], 'view'>

  totalSupply: TypedContractMethod<[], [bigint], 'view'>

  'transfer(uint256,uint256)': TypedContractMethod<
    [recipientIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'transfer(address,uint256)': TypedContractMethod<
    [recipient: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'transferFrom(address,address,uint256)': TypedContractMethod<
    [sender: AddressLike, recipient: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'transferFrom(uint256,uint256,uint256)': TypedContractMethod<
    [senderIdx: BigNumberish, recipientIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  'txSent(bytes32)': TypedContractMethod<[txHash: BytesLike], [boolean], 'view'>

  'txSent(address,address,uint256,uint256)': TypedContractMethod<
    [
      from: AddressLike,
      to: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [boolean],
    'view'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'allowance(uint256,uint256)',
  ): TypedContractMethod<
    [ownerIdx: BigNumberish, spenderIdx: BigNumberish],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'allowance(address,address)',
  ): TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'approve(address,uint256)',
  ): TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'approve(uint256,uint256)',
  ): TypedContractMethod<
    [spenderIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'balanceOf(address)',
  ): TypedContractMethod<[account: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'balanceOf(uint256)',
  ): TypedContractMethod<[accountIdx: BigNumberish], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'burn(address,uint256)',
  ): TypedContractMethod<
    [owner: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'burn(uint256,uint256)',
  ): TypedContractMethod<
    [ownerIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'decimals',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'decreaseAllowance(uint256,uint256)',
  ): TypedContractMethod<
    [spenderIdx: BigNumberish, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'decreaseAllowance(address,uint256)',
  ): TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getHash',
  ): TypedContractMethod<
    [
      from: AddressLike,
      to: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPayerAndPayeeHash',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'increaseAllowance(address,uint256)',
  ): TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'increaseAllowance(uint256,uint256)',
  ): TypedContractMethod<
    [spenderIdx: BigNumberish, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isPayerAndPayee',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'mint(uint256,uint256)',
  ): TypedContractMethod<
    [ownerIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'mint(address,uint256)',
  ): TypedContractMethod<
    [owner: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'name',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'pay(uint256,uint256,uint256,uint256)',
  ): TypedContractMethod<
    [
      payerIdx: BigNumberish,
      payeeIdx: BigNumberish,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'pay(address,address,uint256,uint256)',
  ): TypedContractMethod<
    [
      payer: AddressLike,
      payee: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'payTxs',
  ): TypedContractMethod<[arg0: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setPayerAndPayee',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, enable: boolean],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'symbol',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'totalSupply',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'transfer(uint256,uint256)',
  ): TypedContractMethod<
    [recipientIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transfer(address,uint256)',
  ): TypedContractMethod<
    [recipient: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom(address,address,uint256)',
  ): TypedContractMethod<
    [sender: AddressLike, recipient: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom(uint256,uint256,uint256)',
  ): TypedContractMethod<
    [senderIdx: BigNumberish, recipientIdx: BigNumberish, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'txSent(bytes32)',
  ): TypedContractMethod<[txHash: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'txSent(address,address,uint256,uint256)',
  ): TypedContractMethod<
    [
      from: AddressLike,
      to: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
    ],
    [boolean],
    'view'
  >

  getEvent(
    key: 'Approval',
  ): TypedContractEvent<
    ApprovalEvent.InputTuple,
    ApprovalEvent.OutputTuple,
    ApprovalEvent.OutputObject
  >
  getEvent(
    key: 'PayerToPayeeRelationship',
  ): TypedContractEvent<
    PayerToPayeeRelationshipEvent.InputTuple,
    PayerToPayeeRelationshipEvent.OutputTuple,
    PayerToPayeeRelationshipEvent.OutputObject
  >
  getEvent(
    key: 'Transfer',
  ): TypedContractEvent<
    TransferEvent.InputTuple,
    TransferEvent.OutputTuple,
    TransferEvent.OutputObject
  >

  filters: {
    'Approval(address,address,uint256)': TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >
    Approval: TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >

    'PayerToPayeeRelationship(address,address,bool)': TypedContractEvent<
      PayerToPayeeRelationshipEvent.InputTuple,
      PayerToPayeeRelationshipEvent.OutputTuple,
      PayerToPayeeRelationshipEvent.OutputObject
    >
    PayerToPayeeRelationship: TypedContractEvent<
      PayerToPayeeRelationshipEvent.InputTuple,
      PayerToPayeeRelationshipEvent.OutputTuple,
      PayerToPayeeRelationshipEvent.OutputObject
    >

    'Transfer(address,address,uint256)': TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
    Transfer: TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
  }
}
