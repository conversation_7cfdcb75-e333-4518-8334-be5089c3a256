/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseAccountForwarder {
  export type RisePaymentStruct = {
    id: BytesLike
    groupID: BytesLike
    payAtTime: BigNumberish
    validMinutes: BigNumberish
    payType: BigNumberish
    token: AddressLike
    recipient: AddressLike
    amount: BigNumberish
    data: BytesLike
  }

  export type RisePaymentStructOutput = [
    id: string,
    groupID: string,
    payAtTime: bigint,
    validMinutes: bigint,
    payType: bigint,
    token: string,
    recipient: string,
    amount: bigint,
    data: string,
  ] & {
    id: string
    groupID: string
    payAtTime: bigint
    validMinutes: bigint
    payType: bigint
    token: string
    recipient: string
    amount: bigint
    data: string
  }

  export type RisePaymentScheduleRequestStruct = {
    count: BigNumberish
    minuteInterval: BigNumberish
    payment: RiseAccountForwarder.RisePaymentStruct
  }

  export type RisePaymentScheduleRequestStructOutput = [
    count: bigint,
    minuteInterval: bigint,
    payment: RiseAccountForwarder.RisePaymentStructOutput,
  ] & {
    count: bigint
    minuteInterval: bigint
    payment: RiseAccountForwarder.RisePaymentStructOutput
  }

  export type CreatePaymentByScheduleForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RisePaymentScheduleRequestStruct
  }

  export type CreatePaymentByScheduleForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RisePaymentScheduleRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RisePaymentScheduleRequestStructOutput
  }

  export type CreatePaymentForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RisePaymentStruct
  }

  export type CreatePaymentForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RisePaymentStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RisePaymentStructOutput
  }

  export type RisePaymentsRequestStruct = {
    payments: RiseAccountForwarder.RisePaymentStruct[]
  }

  export type RisePaymentsRequestStructOutput = [
    payments: RiseAccountForwarder.RisePaymentStructOutput[],
  ] & { payments: RiseAccountForwarder.RisePaymentStructOutput[] }

  export type CreatePaymentsForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RisePaymentsRequestStruct
  }

  export type CreatePaymentsForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RisePaymentsRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RisePaymentsRequestStructOutput
  }

  export type RiseIntentPaymentsToScheduledRequestStruct = {
    paymentIDs: BytesLike[]
    payAtTime: BigNumberish[]
  }

  export type RiseIntentPaymentsToScheduledRequestStructOutput = [
    paymentIDs: string[],
    payAtTime: bigint[],
  ] & { paymentIDs: string[]; payAtTime: bigint[] }

  export type IntentPaymentToScheduledForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStruct
  }

  export type IntentPaymentToScheduledForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStructOutput
  }

  export type RiseRemovePaymentByGroupRequestStruct = {
    groupID: BytesLike
    idx: BigNumberish
    count: BigNumberish
  }

  export type RiseRemovePaymentByGroupRequestStructOutput = [
    groupID: string,
    idx: bigint,
    count: bigint,
  ] & { groupID: string; idx: bigint; count: bigint }

  export type RemovePaymentsByGroupIDForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseRemovePaymentByGroupRequestStruct
  }

  export type RemovePaymentsByGroupIDForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseRemovePaymentByGroupRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseRemovePaymentByGroupRequestStructOutput
  }

  export type RiseRemovePaymentsRequestStruct = { paymentIDs: BytesLike[] }

  export type RiseRemovePaymentsRequestStructOutput = [paymentIDs: string[]] & {
    paymentIDs: string[]
  }

  export type RemovePaymentsForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseRemovePaymentsRequestStruct
  }

  export type RemovePaymentsForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseRemovePaymentsRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseRemovePaymentsRequestStructOutput
  }

  export type RiseEtherTransferRequestStruct = {
    recipient: AddressLike
    amount: BigNumberish
  }

  export type RiseEtherTransferRequestStructOutput = [
    recipient: string,
    amount: bigint,
  ] & { recipient: string; amount: bigint }

  export type RiseTokenApprovalRequestStruct = {
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type RiseTokenApprovalRequestStructOutput = [
    token: string,
    spender: string,
    amount: bigint,
  ] & { token: string; spender: string; amount: bigint }

  export type RiseTokenTransferRequestStruct = {
    token: AddressLike
    from: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type RiseTokenTransferRequestStructOutput = [
    token: string,
    from: string,
    to: string,
    amount: bigint,
  ] & { token: string; from: string; to: string; amount: bigint }

  export type RiseTransactionLimitRequestStruct = {
    spender: AddressLike
    token: AddressLike
    dailyLimit: BigNumberish
    transactionLimit: BigNumberish
  }

  export type RiseTransactionLimitRequestStructOutput = [
    spender: string,
    token: string,
    dailyLimit: bigint,
    transactionLimit: bigint,
  ] & {
    spender: string
    token: string
    dailyLimit: bigint
    transactionLimit: bigint
  }

  export type RiseUpdateAmountPaymentsRequestStruct = {
    paymentIDs: BytesLike[]
    newAmounts: BigNumberish[]
  }

  export type RiseUpdateAmountPaymentsRequestStructOutput = [
    paymentIDs: string[],
    newAmounts: bigint[],
  ] & { paymentIDs: string[]; newAmounts: bigint[] }

  export type SendEtherForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseEtherTransferRequestStruct
  }

  export type SendEtherForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseEtherTransferRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseEtherTransferRequestStructOutput
  }

  export type SetRoleStruct = { role: BigNumberish; account: AddressLike }

  export type SetRoleStructOutput = [role: bigint, account: string] & {
    role: bigint
    account: string
  }

  export type SetRolesForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.SetRoleStruct[]
  }

  export type SetRolesForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.SetRoleStructOutput[],
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.SetRoleStructOutput[]
  }

  export type SetTokenTransferApprovalForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseTokenApprovalRequestStruct
  }

  export type SetTokenTransferApprovalForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseTokenApprovalRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseTokenApprovalRequestStructOutput
  }

  export type SetTransactionLimitsForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseTransactionLimitRequestStruct
  }

  export type SetTransactionLimitsForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseTransactionLimitRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseTransactionLimitRequestStructOutput
  }

  export type TokenTransferForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseTokenTransferRequestStruct
  }

  export type TokenTransferForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseTokenTransferRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseTokenTransferRequestStructOutput
  }

  export type UpdatePaymentAmountForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStruct
  }

  export type UpdatePaymentAmountForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStructOutput
  }
}

export interface RiseAccountForwarder_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'GET_BYTES32_ARRAY_PACKET_HASH'
      | 'GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH'
      | 'GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH'
      | 'GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH'
      | 'GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH'
      | 'GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH'
      | 'GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH'
      | 'GET_RISEETHERTRANSFERREQUEST_PACKET_HASH'
      | 'GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH'
      | 'GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH'
      | 'GET_RISEPAYMENTSREQUEST_PACKET_HASH'
      | 'GET_RISEPAYMENT_ARRAY_PACKET_HASH'
      | 'GET_RISEPAYMENT_PACKET_HASH'
      | 'GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH'
      | 'GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH'
      | 'GET_RISETOKENAPPROVALREQUEST_PACKET_HASH'
      | 'GET_RISETOKENTRANSFERREQUEST_PACKET_HASH'
      | 'GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH'
      | 'GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH'
      | 'GET_SENDETHERFORWARDREQUEST_PACKET_HASH'
      | 'GET_SETROLESFORWARDREQUEST_PACKET_HASH'
      | 'GET_SETROLE_ARRAY_PACKET_HASH'
      | 'GET_SETROLE_PACKET_HASH'
      | 'GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH'
      | 'GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH'
      | 'GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH'
      | 'GET_UINT128_ARRAY_PACKET_HASH'
      | 'GET_UINT256_ARRAY_PACKET_HASH'
      | 'GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH'
      | 'createPayment'
      | 'createPaymentAndExecute'
      | 'createPaymentBySchedule'
      | 'createPayments'
      | 'createPaymentsAndExecute'
      | 'eip712Domain'
      | 'init'
      | 'intentPaymentToScheduled'
      | 'intentPaymentToScheduledAndExecute'
      | 'recoverToken'
      | 'removePayments'
      | 'removePaymentsByGroupID'
      | 'riseAccess'
      | 'riseRouter'
      | 'sendEther'
      | 'setRoles'
      | 'setRouter'
      | 'setTokenTransferApproval'
      | 'setTransactionLimits'
      | 'tokenTransfer'
      | 'updatePaymentAmount',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'EIP712DomainChanged'
      | 'ExecutedForwardRequest'
      | 'Initialized',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'GET_BYTES32_ARRAY_PACKET_HASH',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.CreatePaymentForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.CreatePaymentsForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RemovePaymentsForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEETHERTRANSFERREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseEtherTransferRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RisePaymentScheduleRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTSREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RisePaymentsRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENT_ARRAY_PACKET_HASH',
    values: [RiseAccountForwarder.RisePaymentStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENT_PACKET_HASH',
    values: [RiseAccountForwarder.RisePaymentStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseRemovePaymentByGroupRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseRemovePaymentsRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISETOKENAPPROVALREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseTokenApprovalRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISETOKENTRANSFERREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseTokenTransferRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseTransactionLimitRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SENDETHERFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.SendEtherForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.SetRolesForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLE_ARRAY_PACKET_HASH',
    values: [RiseAccountForwarder.SetRoleStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLE_PACKET_HASH',
    values: [RiseAccountForwarder.SetRoleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.TokenTransferForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_UINT128_ARRAY_PACKET_HASH',
    values: [BigNumberish[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_UINT256_ARRAY_PACKET_HASH',
    values: [BigNumberish[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH',
    values: [RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'createPayment',
    values: [RiseAccountForwarder.CreatePaymentForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'createPaymentAndExecute',
    values: [RiseAccountForwarder.CreatePaymentForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'createPaymentBySchedule',
    values: [
      RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'createPayments',
    values: [
      RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'createPaymentsAndExecute',
    values: [
      RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'eip712Domain',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, string, string],
  ): string
  encodeFunctionData(
    functionFragment: 'intentPaymentToScheduled',
    values: [
      RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'intentPaymentToScheduledAndExecute',
    values: [
      RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'removePayments',
    values: [
      RiseAccountForwarder.RemovePaymentsForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'removePaymentsByGroupID',
    values: [
      RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'sendEther',
    values: [RiseAccountForwarder.SendEtherForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoles',
    values: [RiseAccountForwarder.SetRolesForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setTokenTransferApproval',
    values: [
      RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'setTransactionLimits',
    values: [
      RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'tokenTransfer',
    values: [RiseAccountForwarder.TokenTransferForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'updatePaymentAmount',
    values: [
      RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct,
      BytesLike,
    ],
  ): string

  decodeFunctionResult(
    functionFragment: 'GET_BYTES32_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEETHERTRANSFERREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTSREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENT_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENT_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISETOKENAPPROVALREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISETOKENTRANSFERREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SENDETHERFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLE_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLE_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_UINT128_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_UINT256_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPayment',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPaymentAndExecute',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPaymentBySchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPaymentsAndExecute',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'eip712Domain',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'intentPaymentToScheduled',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'intentPaymentToScheduledAndExecute',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removePayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removePaymentsByGroupID',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'sendEther', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRoles', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTokenTransferApproval',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setTransactionLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'tokenTransfer',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'updatePaymentAmount',
    data: BytesLike,
  ): Result
}

export namespace EIP712DomainChangedEvent {
  export type InputTuple = []
  export type OutputTuple = []
  export interface OutputObject {}
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ExecutedForwardRequestEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    hash: BytesLike,
    success: boolean,
  ]
  export type OutputTuple = [
    from: string,
    to: string,
    hash: string,
    success: boolean,
  ]
  export interface OutputObject {
    from: string
    to: string
    hash: string
    success: boolean
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccountForwarder_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccountForwarder_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccountForwarder_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  GET_BYTES32_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: BytesLike[]],
    [string],
    'view'
  >

  GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct],
    [string],
    'view'
  >

  GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentForwardRequestStruct],
    [string],
    'view'
  >

  GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct],
    [string],
    'view'
  >

  GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct],
    [string],
    'view'
  >

  GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct],
    [string],
    'view'
  >

  GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RemovePaymentsForwardRequestStruct],
    [string],
    'view'
  >

  GET_RISEETHERTRANSFERREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseEtherTransferRequestStruct],
    [string],
    'view'
  >

  GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStruct],
    [string],
    'view'
  >

  GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentScheduleRequestStruct],
    [string],
    'view'
  >

  GET_RISEPAYMENTSREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentsRequestStruct],
    [string],
    'view'
  >

  GET_RISEPAYMENT_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentStruct[]],
    [string],
    'view'
  >

  GET_RISEPAYMENT_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentStruct],
    [string],
    'view'
  >

  GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseRemovePaymentByGroupRequestStruct],
    [string],
    'view'
  >

  GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseRemovePaymentsRequestStruct],
    [string],
    'view'
  >

  GET_RISETOKENAPPROVALREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTokenApprovalRequestStruct],
    [string],
    'view'
  >

  GET_RISETOKENTRANSFERREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTokenTransferRequestStruct],
    [string],
    'view'
  >

  GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTransactionLimitRequestStruct],
    [string],
    'view'
  >

  GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStruct],
    [string],
    'view'
  >

  GET_SENDETHERFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SendEtherForwardRequestStruct],
    [string],
    'view'
  >

  GET_SETROLESFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SetRolesForwardRequestStruct],
    [string],
    'view'
  >

  GET_SETROLE_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SetRoleStruct[]],
    [string],
    'view'
  >

  GET_SETROLE_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SetRoleStruct],
    [string],
    'view'
  >

  GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct],
    [string],
    'view'
  >

  GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct],
    [string],
    'view'
  >

  GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.TokenTransferForwardRequestStruct],
    [string],
    'view'
  >

  GET_UINT128_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: BigNumberish[]],
    [string],
    'view'
  >

  GET_UINT256_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: BigNumberish[]],
    [string],
    'view'
  >

  GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct],
    [string],
    'view'
  >

  createPayment: TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  createPaymentAndExecute: TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  createPaymentBySchedule: TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  createPayments: TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  createPaymentsAndExecute: TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  eip712Domain: TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >

  init: TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >

  intentPaymentToScheduled: TypedContractMethod<
    [
      _input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  intentPaymentToScheduledAndExecute: TypedContractMethod<
    [
      _input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  removePayments: TypedContractMethod<
    [
      _input: RiseAccountForwarder.RemovePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  removePaymentsByGroupID: TypedContractMethod<
    [
      _input: RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  sendEther: TypedContractMethod<
    [
      _input: RiseAccountForwarder.SendEtherForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  setRoles: TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetRolesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setTokenTransferApproval: TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  setTransactionLimits: TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  tokenTransfer: TypedContractMethod<
    [
      _input: RiseAccountForwarder.TokenTransferForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  updatePaymentAmount: TypedContractMethod<
    [
      _input: RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'GET_BYTES32_ARRAY_PACKET_HASH',
  ): TypedContractMethod<[_input: BytesLike[]], [string], 'view'>
  getFunction(
    nameOrSignature: 'GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RemovePaymentsForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEETHERTRANSFERREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseEtherTransferRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseIntentPaymentsToScheduledRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentScheduleRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTSREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentsRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENT_ARRAY_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentStruct[]],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENT_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RisePaymentStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseRemovePaymentByGroupRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseRemovePaymentsRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISETOKENAPPROVALREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTokenApprovalRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISETOKENTRANSFERREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTokenTransferRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseTransactionLimitRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.RiseUpdateAmountPaymentsRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SENDETHERFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SendEtherForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SetRolesForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLE_ARRAY_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SetRoleStruct[]],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLE_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SetRoleStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.TokenTransferForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_UINT128_ARRAY_PACKET_HASH',
  ): TypedContractMethod<[_input: BigNumberish[]], [string], 'view'>
  getFunction(
    nameOrSignature: 'GET_UINT256_ARRAY_PACKET_HASH',
  ): TypedContractMethod<[_input: BigNumberish[]], [string], 'view'>
  getFunction(
    nameOrSignature: 'GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'createPayment',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPaymentAndExecute',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPaymentBySchedule',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentByScheduleForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPayments',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPaymentsAndExecute',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.CreatePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(nameOrSignature: 'eip712Domain'): TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'intentPaymentToScheduled',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'intentPaymentToScheduledAndExecute',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.IntentPaymentToScheduledForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removePayments',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.RemovePaymentsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'removePaymentsByGroupID',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'sendEther',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.SendEtherForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRoles',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetRolesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setTokenTransferApproval',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetTokenTransferApprovalForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setTransactionLimits',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.SetTransactionLimitsForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'tokenTransfer',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.TokenTransferForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'updatePaymentAmount',
  ): TypedContractMethod<
    [
      _input: RiseAccountForwarder.UpdatePaymentAmountForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getEvent(
    key: 'EIP712DomainChanged',
  ): TypedContractEvent<
    EIP712DomainChangedEvent.InputTuple,
    EIP712DomainChangedEvent.OutputTuple,
    EIP712DomainChangedEvent.OutputObject
  >
  getEvent(
    key: 'ExecutedForwardRequest',
  ): TypedContractEvent<
    ExecutedForwardRequestEvent.InputTuple,
    ExecutedForwardRequestEvent.OutputTuple,
    ExecutedForwardRequestEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'EIP712DomainChanged()': TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >
    EIP712DomainChanged: TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >

    'ExecutedForwardRequest(address,address,bytes32,bool)': TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >
    ExecutedForwardRequest: TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
