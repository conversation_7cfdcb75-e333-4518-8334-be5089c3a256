/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseFinanceGovernorStorage {
  export type CreditDataStruct = { amount: BigNumberish; credited: boolean }

  export type CreditDataStructOutput = [amount: bigint, credited: boolean] & {
    amount: bigint
    credited: boolean
  }

  export type LimitDataStruct = {
    limit: BigNumberish
    usage: BigNumberish
    set: boolean
  }

  export type LimitDataStructOutput = [
    limit: bigint,
    usage: bigint,
    set_: boolean,
  ] & { limit: bigint; usage: bigint; set: boolean }
}

export interface RiseFinanceGovernor_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | '_getDailyLimitKey'
      | 'creditRiseAccount'
      | 'getCredit'
      | 'getDailyLimit'
      | 'getTokenDefaultLimit'
      | 'init'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setDailyLimit'
      | 'setRouter'
      | 'setTokenDefaultLimit',
  ): FunctionFragment

  getEvent(nameOrSignatureOrTopic: 'Initialized'): EventFragment

  encodeFunctionData(
    functionFragment: '_getDailyLimitKey',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'creditRiseAccount',
    values: [AddressLike, AddressLike, BigNumberish, BytesLike],
  ): string
  encodeFunctionData(functionFragment: 'getCredit', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'getDailyLimit',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTokenDefaultLimit',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setDailyLimit',
    values: [AddressLike, AddressLike, BigNumberish, boolean],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setTokenDefaultLimit',
    values: [AddressLike, BigNumberish],
  ): string

  decodeFunctionResult(
    functionFragment: '_getDailyLimitKey',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'creditRiseAccount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getCredit', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getDailyLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTokenDefaultLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setDailyLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTokenDefaultLimit',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseFinanceGovernor_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseFinanceGovernor_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseFinanceGovernor_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  _getDailyLimitKey: TypedContractMethod<
    [riseAccount: AddressLike, token: AddressLike],
    [string],
    'view'
  >

  creditRiseAccount: TypedContractMethod<
    [
      riseAccount: AddressLike,
      token: AddressLike,
      amount: BigNumberish,
      saltOrHashKey: BytesLike,
    ],
    [void],
    'nonpayable'
  >

  getCredit: TypedContractMethod<
    [saltOrHashKey: BytesLike],
    [RiseFinanceGovernorStorage.CreditDataStructOutput],
    'view'
  >

  getDailyLimit: TypedContractMethod<
    [riseAccount: AddressLike, token: AddressLike],
    [RiseFinanceGovernorStorage.LimitDataStructOutput],
    'view'
  >

  getTokenDefaultLimit: TypedContractMethod<
    [token: AddressLike],
    [bigint],
    'view'
  >

  init: TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setDailyLimit: TypedContractMethod<
    [
      riseAccount: AddressLike,
      token: AddressLike,
      limit: BigNumberish,
      set: boolean,
    ],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setTokenDefaultLimit: TypedContractMethod<
    [token: AddressLike, limit: BigNumberish],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: '_getDailyLimitKey',
  ): TypedContractMethod<
    [riseAccount: AddressLike, token: AddressLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'creditRiseAccount',
  ): TypedContractMethod<
    [
      riseAccount: AddressLike,
      token: AddressLike,
      amount: BigNumberish,
      saltOrHashKey: BytesLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getCredit',
  ): TypedContractMethod<
    [saltOrHashKey: BytesLike],
    [RiseFinanceGovernorStorage.CreditDataStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getDailyLimit',
  ): TypedContractMethod<
    [riseAccount: AddressLike, token: AddressLike],
    [RiseFinanceGovernorStorage.LimitDataStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTokenDefaultLimit',
  ): TypedContractMethod<[token: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setDailyLimit',
  ): TypedContractMethod<
    [
      riseAccount: AddressLike,
      token: AddressLike,
      limit: BigNumberish,
      set: boolean,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setTokenDefaultLimit',
  ): TypedContractMethod<
    [token: AddressLike, limit: BigNumberish],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
