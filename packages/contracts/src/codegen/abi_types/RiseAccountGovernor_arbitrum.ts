/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type RisePaymentStruct = {
    id: BytesLike
    groupID: BytesLike
    payAtTime: BigNumberish
    validMinutes: BigNumberish
    payType: BigNumberish
    token: AddressLike
    recipient: AddressLike
    amount: BigNumberish
    data: BytesLike
  }

  export type RisePaymentStructOutput = [
    id: string,
    groupID: string,
    payAtTime: bigint,
    validMinutes: bigint,
    payType: bigint,
    token: string,
    recipient: string,
    amount: bigint,
    data: string,
  ] & {
    id: string
    groupID: string
    payAtTime: bigint
    validMinutes: bigint
    payType: bigint
    token: string
    recipient: string
    amount: bigint
    data: string
  }

  export type RisePaymentsRequestStruct = {
    payments: RiseRequests.RisePaymentStruct[]
  }

  export type RisePaymentsRequestStructOutput = [
    payments: RiseRequests.RisePaymentStructOutput[],
  ] & { payments: RiseRequests.RisePaymentStructOutput[] }
}

export interface RiseAccountGovernor_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'createPayments'
      | 'executePayments'
      | 'getMultiplePaymentSenderTrackingStatsByDay'
      | 'getMultipleTransactionLimits'
      | 'getPaymentSenderTrackingStatsByDay'
      | 'getPaymentSenderTrackingStatsByMonth'
      | 'getPaymentTokenTrackingStatsByDay'
      | 'getPaymentTokenTrackingStatsByMonth'
      | 'getPaymentVolume'
      | 'getTransactionLimits'
      | 'init'
      | 'paymentIsPayable'
      | 'paymentsByAccountsNow'
      | 'paymentsByAddress'
      | 'paymentsByAddressDay'
      | 'paymentsByAddressDays'
      | 'paymentsByDay'
      | 'paymentsByDays'
      | 'paymentsByGroup'
      | 'paymentsByGroupDay'
      | 'recoverToken'
      | 'removePayments'
      | 'riseAccess'
      | 'riseRouter'
      | 'setAccountSponsor'
      | 'setRouter'
      | 'updatePaymentAmount',
  ): FunctionFragment

  getEvent(nameOrSignatureOrTopic: 'Initialized'): EventFragment

  encodeFunctionData(
    functionFragment: 'createPayments',
    values: [AddressLike, RiseRequests.RisePaymentStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'executePayments',
    values: [AddressLike[], BytesLike[][]],
  ): string
  encodeFunctionData(
    functionFragment: 'getMultiplePaymentSenderTrackingStatsByDay',
    values: [AddressLike, AddressLike[], AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getMultipleTransactionLimits',
    values: [AddressLike, AddressLike[], AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentSenderTrackingStatsByDay',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentSenderTrackingStatsByMonth',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentTokenTrackingStatsByDay',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentTokenTrackingStatsByMonth',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentVolume',
    values: [
      AddressLike,
      BigNumberish,
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish[],
      AddressLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getTransactionLimits',
    values: [AddressLike, AddressLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'paymentIsPayable',
    values: [RiseRequests.RisePaymentStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByAccountsNow',
    values: [AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByAddress',
    values: [
      AddressLike,
      BigNumberish,
      AddressLike,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByAddressDay',
    values: [
      AddressLike,
      BigNumberish,
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByAddressDays',
    values: [
      AddressLike,
      BigNumberish,
      AddressLike,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByDay',
    values: [
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByDays',
    values: [AddressLike, BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByGroup',
    values: [AddressLike, BigNumberish, BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsByGroupDay',
    values: [
      AddressLike,
      BigNumberish,
      BytesLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'removePayments',
    values: [AddressLike, BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setAccountSponsor',
    values: [AddressLike, AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'updatePaymentAmount',
    values: [AddressLike, BytesLike[], BigNumberish[]],
  ): string

  decodeFunctionResult(
    functionFragment: 'createPayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executePayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMultiplePaymentSenderTrackingStatsByDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMultipleTransactionLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentSenderTrackingStatsByDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentSenderTrackingStatsByMonth',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentTokenTrackingStatsByDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentTokenTrackingStatsByMonth',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentVolume',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTransactionLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'paymentIsPayable',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByAccountsNow',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByAddress',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByAddressDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByAddressDays',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByDays',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByGroup',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsByGroupDay',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removePayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setAccountSponsor',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'updatePaymentAmount',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccountGovernor_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccountGovernor_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccountGovernor_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  createPayments: TypedContractMethod<
    [account: AddressLike, payments: RiseRequests.RisePaymentStruct[]],
    [void],
    'nonpayable'
  >

  executePayments: TypedContractMethod<
    [accounts: AddressLike[], paymentIDs: BytesLike[][]],
    [void],
    'nonpayable'
  >

  getMultiplePaymentSenderTrackingStatsByDay: TypedContractMethod<
    [
      account: AddressLike,
      senders: AddressLike[],
      token: AddressLike,
      epoch: BigNumberish,
    ],
    [[bigint[], bigint[]]],
    'view'
  >

  getMultipleTransactionLimits: TypedContractMethod<
    [account: AddressLike, senders: AddressLike[], token: AddressLike],
    [[bigint[], bigint[]]],
    'view'
  >

  getPaymentSenderTrackingStatsByDay: TypedContractMethod<
    [account: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >

  getPaymentSenderTrackingStatsByMonth: TypedContractMethod<
    [account: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >

  getPaymentTokenTrackingStatsByDay: TypedContractMethod<
    [account: AddressLike, token: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >

  getPaymentTokenTrackingStatsByMonth: TypedContractMethod<
    [account: AddressLike, token: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; amount: bigint }],
    'view'
  >

  getPaymentVolume: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      token: AddressLike,
      startTime: BigNumberish,
      dayCount: BigNumberish,
      payType: BigNumberish[],
      recipient: AddressLike,
    ],
    [bigint],
    'view'
  >

  getTransactionLimits: TypedContractMethod<
    [account: AddressLike, sender: AddressLike, token: AddressLike],
    [[bigint, bigint]],
    'view'
  >

  init: TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>

  paymentIsPayable: TypedContractMethod<
    [_payment: RiseRequests.RisePaymentStruct],
    [boolean],
    'view'
  >

  paymentsByAccountsNow: TypedContractMethod<
    [accounts: AddressLike[]],
    [RiseRequests.RisePaymentStructOutput[][]],
    'view'
  >

  paymentsByAddress: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  paymentsByAddressDay: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  paymentsByAddressDays: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      startTime: BigNumberish,
      dayCount: BigNumberish,
    ],
    [RiseRequests.RisePaymentsRequestStructOutput[]],
    'view'
  >

  paymentsByDay: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  paymentsByDays: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      startTime: BigNumberish,
      dayCount: BigNumberish,
    ],
    [RiseRequests.RisePaymentsRequestStructOutput[]],
    'view'
  >

  paymentsByGroup: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      groupID: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  paymentsByGroupDay: TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      groupID: BytesLike,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  removePayments: TypedContractMethod<
    [account: AddressLike, paymentIDs: BytesLike[]],
    [void],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setAccountSponsor: TypedContractMethod<
    [sponsorAccount: AddressLike, accounts: AddressLike[]],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  updatePaymentAmount: TypedContractMethod<
    [account: AddressLike, paymentIDs: BytesLike[], newAmounts: BigNumberish[]],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'createPayments',
  ): TypedContractMethod<
    [account: AddressLike, payments: RiseRequests.RisePaymentStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'executePayments',
  ): TypedContractMethod<
    [accounts: AddressLike[], paymentIDs: BytesLike[][]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getMultiplePaymentSenderTrackingStatsByDay',
  ): TypedContractMethod<
    [
      account: AddressLike,
      senders: AddressLike[],
      token: AddressLike,
      epoch: BigNumberish,
    ],
    [[bigint[], bigint[]]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getMultipleTransactionLimits',
  ): TypedContractMethod<
    [account: AddressLike, senders: AddressLike[], token: AddressLike],
    [[bigint[], bigint[]]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentSenderTrackingStatsByDay',
  ): TypedContractMethod<
    [account: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentSenderTrackingStatsByMonth',
  ): TypedContractMethod<
    [account: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentTokenTrackingStatsByDay',
  ): TypedContractMethod<
    [account: AddressLike, token: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentTokenTrackingStatsByMonth',
  ): TypedContractMethod<
    [account: AddressLike, token: AddressLike, epoch: BigNumberish],
    [[bigint, bigint] & { count: bigint; amount: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentVolume',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      token: AddressLike,
      startTime: BigNumberish,
      dayCount: BigNumberish,
      payType: BigNumberish[],
      recipient: AddressLike,
    ],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTransactionLimits',
  ): TypedContractMethod<
    [account: AddressLike, sender: AddressLike, token: AddressLike],
    [[bigint, bigint]],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'paymentIsPayable',
  ): TypedContractMethod<
    [_payment: RiseRequests.RisePaymentStruct],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByAccountsNow',
  ): TypedContractMethod<
    [accounts: AddressLike[]],
    [RiseRequests.RisePaymentStructOutput[][]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByAddress',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByAddressDay',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByAddressDays',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      addr: AddressLike,
      startTime: BigNumberish,
      dayCount: BigNumberish,
    ],
    [RiseRequests.RisePaymentsRequestStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByDay',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByDays',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      startTime: BigNumberish,
      dayCount: BigNumberish,
    ],
    [RiseRequests.RisePaymentsRequestStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByGroup',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      groupID: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsByGroupDay',
  ): TypedContractMethod<
    [
      account: AddressLike,
      storageType: BigNumberish,
      groupID: BytesLike,
      dayTime: BigNumberish,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removePayments',
  ): TypedContractMethod<
    [account: AddressLike, paymentIDs: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setAccountSponsor',
  ): TypedContractMethod<
    [sponsorAccount: AddressLike, accounts: AddressLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'updatePaymentAmount',
  ): TypedContractMethod<
    [account: AddressLike, paymentIDs: BytesLike[], newAmounts: BigNumberish[]],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
