/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseRouter_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addOwner'
      | 'getOwners'
      | 'getOwnersLength'
      | 'init'
      | 'isOwner'
      | 'removeOwner'
      | 'resolve'
      | 'resolveNoFail'
      | 'setResolver'
      | 'setResolvers',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RiseOwnerAdded'
      | 'RiseOwnerRemoved'
      | 'RouteChanged',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'getOwners', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getOwnersLength',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(functionFragment: 'isOwner', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'removeOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'resolve', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'resolveNoFail',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setResolver',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setResolvers',
    values: [BytesLike[], AddressLike[]],
  ): string

  decodeFunctionResult(functionFragment: 'addOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getOwners', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getOwnersLength',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'isOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'removeOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'resolve', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'resolveNoFail',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setResolver', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setResolvers',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerAddedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerRemovedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RouteChangedEvent {
  export type InputTuple = [
    routerReferenceHash: BytesLike,
    implementation: AddressLike,
  ]
  export type OutputTuple = [
    routerReferenceHash: string,
    implementation: string,
  ]
  export interface OutputObject {
    routerReferenceHash: string
    implementation: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseRouter_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseRouter_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseRouter_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  getOwners: TypedContractMethod<[], [string[]], 'view'>

  getOwnersLength: TypedContractMethod<[], [bigint], 'view'>

  init: TypedContractMethod<[newOwner: AddressLike], [void], 'nonpayable'>

  isOwner: TypedContractMethod<[account: AddressLike], [boolean], 'view'>

  removeOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  resolve: TypedContractMethod<[ref: BytesLike], [string], 'view'>

  resolveNoFail: TypedContractMethod<[ref: BytesLike], [string], 'view'>

  setResolver: TypedContractMethod<
    [_ref: BytesLike, _implementation: AddressLike],
    [void],
    'nonpayable'
  >

  setResolvers: TypedContractMethod<
    [_ref: BytesLike[], _implementation: AddressLike[]],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'getOwners',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getOwnersLength',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[newOwner: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'isOwner',
  ): TypedContractMethod<[account: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'removeOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'resolve',
  ): TypedContractMethod<[ref: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'resolveNoFail',
  ): TypedContractMethod<[ref: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'setResolver',
  ): TypedContractMethod<
    [_ref: BytesLike, _implementation: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setResolvers',
  ): TypedContractMethod<
    [_ref: BytesLike[], _implementation: AddressLike[]],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerAdded',
  ): TypedContractEvent<
    RiseOwnerAddedEvent.InputTuple,
    RiseOwnerAddedEvent.OutputTuple,
    RiseOwnerAddedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerRemoved',
  ): TypedContractEvent<
    RiseOwnerRemovedEvent.InputTuple,
    RiseOwnerRemovedEvent.OutputTuple,
    RiseOwnerRemovedEvent.OutputObject
  >
  getEvent(
    key: 'RouteChanged',
  ): TypedContractEvent<
    RouteChangedEvent.InputTuple,
    RouteChangedEvent.OutputTuple,
    RouteChangedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseOwnerAdded(address)': TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >
    RiseOwnerAdded: TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >

    'RiseOwnerRemoved(address)': TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
    RiseOwnerRemoved: TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >

    'RouteChanged(bytes32,address)': TypedContractEvent<
      RouteChangedEvent.InputTuple,
      RouteChangedEvent.OutputTuple,
      RouteChangedEvent.OutputObject
    >
    RouteChanged: TypedContractEvent<
      RouteChangedEvent.InputTuple,
      RouteChangedEvent.OutputTuple,
      RouteChangedEvent.OutputObject
    >
  }
}
