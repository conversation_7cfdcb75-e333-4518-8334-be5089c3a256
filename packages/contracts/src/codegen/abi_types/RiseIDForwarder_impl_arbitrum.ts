/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseIDForwarder {
  export type ApprovalChangeStruct = {
    incOrDec: boolean
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type ApprovalChangeStructOutput = [
    incOrDec: boolean,
    token: string,
    spender: string,
    amount: bigint,
  ] & { incOrDec: boolean; token: string; spender: string; amount: bigint }

  export type ApproveStruct = {
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type ApproveStructOutput = [
    token: string,
    spender: string,
    amount: bigint,
  ] & { token: string; spender: string; amount: bigint }

  export type ApproveForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.ApproveStruct
  }

  export type ApproveForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.ApproveStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.ApproveStructOutput
  }

  export type ExecutionStruct = {
    to: AddressLike
    method: BytesLike
    data: BytesLike
  }

  export type ExecutionStructOutput = [
    to: string,
    method: string,
    data: string,
  ] & { to: string; method: string; data: string }

  export type CallForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.ExecutionStruct
  }

  export type CallForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.ExecutionStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.ExecutionStructOutput
  }

  export type DatasetStruct = { dataKey: BytesLike; dataValue: BytesLike }

  export type DatasetStructOutput = [dataKey: string, dataValue: string] & {
    dataKey: string
    dataValue: string
  }

  export type ExecuteForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.ExecutionStruct
  }

  export type ExecuteForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.ExecutionStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.ExecutionStructOutput
  }

  export type SetDataForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.DatasetStruct
  }

  export type SetDataForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.DatasetStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.DatasetStructOutput
  }

  export type SetRoleStruct = { role: BigNumberish; account: AddressLike }

  export type SetRoleStructOutput = [role: bigint, account: string] & {
    role: bigint
    account: string
  }

  export type SetRolesForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.SetRoleStruct[]
  }

  export type SetRolesForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.SetRoleStructOutput[],
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.SetRoleStructOutput[]
  }

  export type TransferStruct = {
    token: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type TransferStructOutput = [
    token: string,
    to: string,
    amount: bigint,
  ] & { token: string; to: string; amount: bigint }

  export type TransferForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.TransferStruct
  }

  export type TransferForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.TransferStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.TransferStructOutput
  }

  export type TransferFromStruct = {
    token: AddressLike
    from: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type TransferFromStructOutput = [
    token: string,
    from: string,
    to: string,
    amount: bigint,
  ] & { token: string; from: string; to: string; amount: bigint }

  export type TransferFromForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RiseIDForwarder.TransferFromStruct
  }

  export type TransferFromForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RiseIDForwarder.TransferFromStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RiseIDForwarder.TransferFromStructOutput
  }
}

export interface RiseIDForwarder_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'GET_APPROVALCHANGE_PACKET_HASH'
      | 'GET_APPROVEFORWARDREQUEST_PACKET_HASH'
      | 'GET_APPROVE_PACKET_HASH'
      | 'GET_CALLFORWARDREQUEST_PACKET_HASH'
      | 'GET_DATASET_PACKET_HASH'
      | 'GET_EXECUTEFORWARDREQUEST_PACKET_HASH'
      | 'GET_EXECUTION_PACKET_HASH'
      | 'GET_SETDATAFORWARDREQUEST_PACKET_HASH'
      | 'GET_SETROLESFORWARDREQUEST_PACKET_HASH'
      | 'GET_SETROLE_ARRAY_PACKET_HASH'
      | 'GET_SETROLE_PACKET_HASH'
      | 'GET_TRANSFERFORWARDREQUEST_PACKET_HASH'
      | 'GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH'
      | 'GET_TRANSFERFROM_PACKET_HASH'
      | 'GET_TRANSFER_PACKET_HASH'
      | 'approve'
      | 'call'
      | 'eip712Domain'
      | 'execute'
      | 'init'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setData'
      | 'setRoles'
      | 'setRouter'
      | 'transfer'
      | 'transferFrom',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'EIP712DomainChanged'
      | 'ExecutedForwardRequest'
      | 'Initialized',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'GET_APPROVALCHANGE_PACKET_HASH',
    values: [RiseIDForwarder.ApprovalChangeStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_APPROVEFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.ApproveForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_APPROVE_PACKET_HASH',
    values: [RiseIDForwarder.ApproveStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_CALLFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.CallForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_DATASET_PACKET_HASH',
    values: [RiseIDForwarder.DatasetStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_EXECUTEFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.ExecuteForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_EXECUTION_PACKET_HASH',
    values: [RiseIDForwarder.ExecutionStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETDATAFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.SetDataForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.SetRolesForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLE_ARRAY_PACKET_HASH',
    values: [RiseIDForwarder.SetRoleStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETROLE_PACKET_HASH',
    values: [RiseIDForwarder.SetRoleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_TRANSFERFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.TransferForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH',
    values: [RiseIDForwarder.TransferFromForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_TRANSFERFROM_PACKET_HASH',
    values: [RiseIDForwarder.TransferFromStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_TRANSFER_PACKET_HASH',
    values: [RiseIDForwarder.TransferStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'approve',
    values: [RiseIDForwarder.ApproveForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'call',
    values: [RiseIDForwarder.CallForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'eip712Domain',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'execute',
    values: [RiseIDForwarder.ExecuteForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, string, string],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setData',
    values: [RiseIDForwarder.SetDataForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoles',
    values: [RiseIDForwarder.SetRolesForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'transfer',
    values: [RiseIDForwarder.TransferForwardRequestStruct, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom',
    values: [RiseIDForwarder.TransferFromForwardRequestStruct, BytesLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'GET_APPROVALCHANGE_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_APPROVEFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_APPROVE_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_CALLFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_DATASET_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_EXECUTEFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_EXECUTION_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETDATAFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLE_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETROLE_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_TRANSFERFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_TRANSFERFROM_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_TRANSFER_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'approve', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'call', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'eip712Domain',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setData', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRoles', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'transfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom',
    data: BytesLike,
  ): Result
}

export namespace EIP712DomainChangedEvent {
  export type InputTuple = []
  export type OutputTuple = []
  export interface OutputObject {}
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ExecutedForwardRequestEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    hash: BytesLike,
    success: boolean,
  ]
  export type OutputTuple = [
    from: string,
    to: string,
    hash: string,
    success: boolean,
  ]
  export interface OutputObject {
    from: string
    to: string
    hash: string
    success: boolean
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseIDForwarder_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseIDForwarder_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseIDForwarder_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  GET_APPROVALCHANGE_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.ApprovalChangeStruct],
    [string],
    'view'
  >

  GET_APPROVEFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.ApproveForwardRequestStruct],
    [string],
    'view'
  >

  GET_APPROVE_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.ApproveStruct],
    [string],
    'view'
  >

  GET_CALLFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.CallForwardRequestStruct],
    [string],
    'view'
  >

  GET_DATASET_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.DatasetStruct],
    [string],
    'view'
  >

  GET_EXECUTEFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.ExecuteForwardRequestStruct],
    [string],
    'view'
  >

  GET_EXECUTION_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.ExecutionStruct],
    [string],
    'view'
  >

  GET_SETDATAFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.SetDataForwardRequestStruct],
    [string],
    'view'
  >

  GET_SETROLESFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.SetRolesForwardRequestStruct],
    [string],
    'view'
  >

  GET_SETROLE_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.SetRoleStruct[]],
    [string],
    'view'
  >

  GET_SETROLE_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.SetRoleStruct],
    [string],
    'view'
  >

  GET_TRANSFERFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.TransferForwardRequestStruct],
    [string],
    'view'
  >

  GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.TransferFromForwardRequestStruct],
    [string],
    'view'
  >

  GET_TRANSFERFROM_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.TransferFromStruct],
    [string],
    'view'
  >

  GET_TRANSFER_PACKET_HASH: TypedContractMethod<
    [_input: RiseIDForwarder.TransferStruct],
    [string],
    'view'
  >

  approve: TypedContractMethod<
    [_input: RiseIDForwarder.ApproveForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  call: TypedContractMethod<
    [_input: RiseIDForwarder.CallForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  eip712Domain: TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >

  execute: TypedContractMethod<
    [_input: RiseIDForwarder.ExecuteForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  init: TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setData: TypedContractMethod<
    [_input: RiseIDForwarder.SetDataForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  setRoles: TypedContractMethod<
    [
      _input: RiseIDForwarder.SetRolesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  transfer: TypedContractMethod<
    [
      _input: RiseIDForwarder.TransferForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  transferFrom: TypedContractMethod<
    [
      _input: RiseIDForwarder.TransferFromForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'GET_APPROVALCHANGE_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ApprovalChangeStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_APPROVEFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ApproveForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_APPROVE_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ApproveStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_CALLFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.CallForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_DATASET_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.DatasetStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_EXECUTEFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ExecuteForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_EXECUTION_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ExecutionStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETDATAFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.SetDataForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.SetRolesForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLE_ARRAY_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.SetRoleStruct[]],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETROLE_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.SetRoleStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_TRANSFERFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.TransferForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.TransferFromForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_TRANSFERFROM_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.TransferFromStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_TRANSFER_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.TransferStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'approve',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ApproveForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'call',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.CallForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(nameOrSignature: 'eip712Domain'): TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'execute',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.ExecuteForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setData',
  ): TypedContractMethod<
    [_input: RiseIDForwarder.SetDataForwardRequestStruct, signature: BytesLike],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRoles',
  ): TypedContractMethod<
    [
      _input: RiseIDForwarder.SetRolesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'transfer',
  ): TypedContractMethod<
    [
      _input: RiseIDForwarder.TransferForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom',
  ): TypedContractMethod<
    [
      _input: RiseIDForwarder.TransferFromForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getEvent(
    key: 'EIP712DomainChanged',
  ): TypedContractEvent<
    EIP712DomainChangedEvent.InputTuple,
    EIP712DomainChangedEvent.OutputTuple,
    EIP712DomainChangedEvent.OutputObject
  >
  getEvent(
    key: 'ExecutedForwardRequest',
  ): TypedContractEvent<
    ExecutedForwardRequestEvent.InputTuple,
    ExecutedForwardRequestEvent.OutputTuple,
    ExecutedForwardRequestEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'EIP712DomainChanged()': TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >
    EIP712DomainChanged: TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >

    'ExecutedForwardRequest(address,address,bytes32,bool)': TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >
    ExecutedForwardRequest: TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
