/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseIDForwarder_impl_arbitrum,
  RiseIDForwarder_impl_arbitrumInterface,
} from '../RiseIDForwarder_impl_arbitrum.js'

const _abi = [
  {
    name: 'ERC2771ForwarderExpiredRequest',
    type: 'error',
    inputs: [
      {
        name: 'expires',
        type: 'uint48',
        internalType: 'uint48',
      },
    ],
  },
  {
    name: 'ERC2771ForwarderInvalidSigner',
    type: 'error',
    inputs: [
      {
        name: 'from',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'FailedInnerCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_Unauthorized',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'EIP712DomainChanged',
    type: 'event',
    inputs: [],
    anonymous: false,
  },
  {
    name: 'ExecutedForwardRequest',
    type: 'event',
    inputs: [
      {
        name: 'from',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'to',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'hash',
        type: 'bytes32',
        indexed: false,
        internalType: 'bytes32',
      },
      {
        name: 'success',
        type: 'bool',
        indexed: false,
        internalType: 'bool',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: 'GET_APPROVALCHANGE_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'incOrDec',
            type: 'bool',
            internalType: 'bool',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseIDForwarder.ApprovalChange',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_APPROVEFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'spender',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.Approve',
          },
        ],
        internalType: 'struct RiseIDForwarder.ApproveForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_APPROVE_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseIDForwarder.Approve',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_CALLFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'method',
                type: 'bytes',
                internalType: 'bytes',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Execution',
          },
        ],
        internalType: 'struct RiseIDForwarder.CallForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_DATASET_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'dataKey',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'dataValue',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType: 'struct RiseIDForwarder.Dataset',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_EXECUTEFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'method',
                type: 'bytes',
                internalType: 'bytes',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Execution',
          },
        ],
        internalType: 'struct RiseIDForwarder.ExecuteForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_EXECUTION_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'method',
            type: 'bytes',
            internalType: 'bytes',
          },
          {
            name: 'data',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType: 'struct RiseIDForwarder.Execution',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_SETDATAFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'dataKey',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'dataValue',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Dataset',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetDataForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_SETROLESFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple[]',
            components: [
              {
                name: 'role',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'account',
                type: 'address',
                internalType: 'address',
              },
            ],
            internalType: 'struct RiseIDForwarder.SetRole[]',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetRolesForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_SETROLE_ARRAY_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple[]',
        components: [
          {
            name: 'role',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'account',
            type: 'address',
            internalType: 'address',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetRole[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_SETROLE_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'role',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'account',
            type: 'address',
            internalType: 'address',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetRole',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_TRANSFERFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.Transfer',
          },
        ],
        internalType: 'struct RiseIDForwarder.TransferForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_TRANSFERFROMFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'from',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.TransferFrom',
          },
        ],
        internalType: 'struct RiseIDForwarder.TransferFromForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_TRANSFERFROM_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseIDForwarder.TransferFrom',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_TRANSFER_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseIDForwarder.Transfer',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'approve',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'spender',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.Approve',
          },
        ],
        internalType: 'struct RiseIDForwarder.ApproveForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'call',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'method',
                type: 'bytes',
                internalType: 'bytes',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Execution',
          },
        ],
        internalType: 'struct RiseIDForwarder.CallForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'eip712Domain',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: 'fields',
        type: 'bytes1',
        internalType: 'bytes1',
      },
      {
        name: 'name',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'version',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'verifyingContract',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'salt',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'extensions',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'execute',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'method',
                type: 'bytes',
                internalType: 'bytes',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Execution',
          },
        ],
        internalType: 'struct RiseIDForwarder.ExecuteForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_riseRouter',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'name',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'version',
        type: 'string',
        internalType: 'string',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setData',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'dataKey',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'dataValue',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType: 'struct RiseIDForwarder.Dataset',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetDataForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRoles',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple[]',
            components: [
              {
                name: 'role',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'account',
                type: 'address',
                internalType: 'address',
              },
            ],
            internalType: 'struct RiseIDForwarder.SetRole[]',
          },
        ],
        internalType: 'struct RiseIDForwarder.SetRolesForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'transfer',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.Transfer',
          },
        ],
        internalType: 'struct RiseIDForwarder.TransferForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'transferFrom',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'from',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'to',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
            ],
            internalType: 'struct RiseIDForwarder.TransferFrom',
          },
        ],
        internalType: 'struct RiseIDForwarder.TransferFromForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseIDForwarder_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseIDForwarder_impl_arbitrumInterface {
    return new Interface(_abi) as RiseIDForwarder_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseIDForwarder_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseIDForwarder_impl_arbitrum
  }
}
