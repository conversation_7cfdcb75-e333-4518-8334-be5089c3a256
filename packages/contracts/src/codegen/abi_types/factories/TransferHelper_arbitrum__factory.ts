/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  TransferHelper_arbitrum,
  TransferHelper_arbitrumInterface,
} from '../TransferHelper_arbitrum.js'

const _abi = [
  {
    name: 'AddressInsufficientBalance',
    type: 'error',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'FailedInnerCall',
    type: 'error',
    inputs: [],
  },
] as const

export class TransferHelper_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): TransferHelper_arbitrumInterface {
    return new Interface(_abi) as TransferHelper_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): TransferHelper_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as TransferHelper_arbitrum
  }
}
