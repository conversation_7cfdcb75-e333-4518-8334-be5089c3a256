/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePayRampUniswap_arbitrum,
  RisePayRampUniswap_arbitrumInterface,
} from '../RisePayRampUniswap_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseTreasury',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_usdc',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'Fee',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'event',
    inputs: [
      {
        name: 'chargedAccount',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'coveredAccount',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Funded',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'UniswapError',
    type: 'event',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        indexed: false,
        internalType: 'string',
      },
    ],
    anonymous: false,
  },
  {
    name: 'UniswapPanic',
    type: 'event',
    inputs: [
      {
        name: 'errorCode',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'UniswapRevert',
    type: 'event',
    inputs: [
      {
        name: 'lowLevelData',
        type: 'bytes',
        indexed: false,
        internalType: 'bytes',
      },
    ],
    anonymous: false,
  },
  {
    name: 'UniswapSuccess',
    type: 'event',
    inputs: [
      {
        name: 'rate',
        type: 'uint24',
        indexed: true,
        internalType: 'uint24',
      },
      {
        name: 'inToken',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'outToken',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Withdrawal',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        indexed: false,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'USDC',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'fund',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'amountOut',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'name',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'string',
        internalType: 'string',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'nameHash',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'swapRouter02',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IV3SwapRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: 'amountOut',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RisePayRampUniswap_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePayRampUniswap_arbitrumInterface {
    return new Interface(_abi) as RisePayRampUniswap_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePayRampUniswap_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RisePayRampUniswap_arbitrum
  }
}
