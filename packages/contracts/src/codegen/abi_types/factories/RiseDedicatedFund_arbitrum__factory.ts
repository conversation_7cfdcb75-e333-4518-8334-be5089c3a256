/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseDedicatedFund_arbitrum,
  RiseDedicatedFund_arbitrumInterface,
} from '../RiseDedicatedFund_arbitrum.js'

const _abi = [
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'USDC',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_USDC',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseTreasury',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'initData',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseTreasury',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'sendTokenToTreasury',
    type: 'function',
    inputs: [
      {
        name: 'tokenAddress',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'sendUSDCToTreasury',
    type: 'function',
    inputs: [],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RiseDedicatedFund_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseDedicatedFund_arbitrumInterface {
    return new Interface(_abi) as RiseDedicatedFund_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseDedicatedFund_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseDedicatedFund_arbitrum
  }
}
