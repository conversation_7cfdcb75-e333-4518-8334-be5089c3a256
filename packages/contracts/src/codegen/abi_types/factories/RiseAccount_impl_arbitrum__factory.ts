/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseAccount_impl_arbitrum,
  RiseAccount_impl_arbitrumInterface,
} from '../RiseAccount_impl_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'AddressInsufficientBalance',
    type: 'error',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'FailedInnerCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'ReentrancyGuardReentrantCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequestWithReason',
    type: 'error',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        internalType: 'string',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Amount',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Complete',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_DailyLimit',
    type: 'error',
    inputs: [
      {
        name: 'spender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Exists',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_NotPayable',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Recipient',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_SameState',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Token',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_TransactionLimit',
    type: 'error',
    inputs: [
      {
        name: 'spender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_Unauthorized',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    name: 'RisePaymentAmountUpdateEvent',
    type: 'event',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'state',
        type: 'uint8',
        indexed: true,
        internalType: 'enum RiseRequests.RisePaymentState',
      },
      {
        name: 'oldAmount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
      {
        name: 'payment',
        type: 'tuple',
        indexed: false,
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment',
      },
    ],
    anonymous: false,
  },
  {
    name: 'RisePaymentEvent',
    type: 'event',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'newState',
        type: 'uint8',
        indexed: true,
        internalType: 'enum RiseRequests.RisePaymentState',
      },
      {
        name: 'oldState',
        type: 'uint8',
        indexed: true,
        internalType: 'enum RiseRequests.RisePaymentState',
      },
      {
        name: 'payment',
        type: 'tuple',
        indexed: false,
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: 'changeGroupByAdmin',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'newGroupID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'changePayAtTimeByAdmin',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'newPayAtTime',
        type: 'uint128',
        internalType: 'uint128',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'createPayment',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'createPaymentBySchedule',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'count',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'minuteInterval',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'payment',
            type: 'tuple',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentScheduleRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'createPayments',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'payments',
            type: 'tuple[]',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment[]',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentsRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'dailyLimit',
    type: 'function',
    inputs: [
      {
        name: '_hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'executePayment',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'executePayments',
    type: 'function',
    inputs: [
      {
        name: 'paymentIDs',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'getAddressesByMonth',
    type: 'function',
    inputs: [
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'addresses',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getAddressesByMonthCount',
    type: 'function',
    inputs: [
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getCountAndVolumeByHash',
    type: 'function',
    inputs: [
      {
        name: 'hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'volume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getDayPaymentTrackingHashes',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'getMemberRole',
    type: 'function',
    inputs: [
      {
        name: 'member',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint8',
        internalType: 'enum RiseRequests.Role',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMembers',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMembersRoles',
    type: 'function',
    inputs: [
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'member',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'role',
            type: 'uint8',
            internalType: 'enum RiseRequests.Role',
          },
        ],
        internalType: 'struct RiseRequests.MemberRole[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMonthPaymentTrackingHashes',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'getRoleMembers',
    type: 'function',
    inputs: [
      {
        name: 'role',
        type: 'uint8',
        internalType: 'enum RiseRequests.Role',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getSenders',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getSettings',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: 'accountType',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'parentAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sourceOfFunds',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'hiddenRiseTokenTransfers',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'sponsorAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'feeRecipient',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getTokenAndSenderVolumeByHash',
    type: 'function',
    inputs: [
      {
        name: 'tokenHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'senderHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: 'tokenVolume',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'senderVolume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getTokensUsed',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getTransactionLimitHash',
    type: 'function',
    inputs: [
      {
        name: 'spender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_riseRouter',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_parentAccount',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'intentPaymentToScheduled',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'paymentIDs',
            type: 'bytes32[]',
            internalType: 'bytes32[]',
          },
          {
            name: 'payAtTime',
            type: 'uint128[]',
            internalType: 'uint128[]',
          },
        ],
        internalType:
          'struct RiseRequests.RiseIntentPaymentsToScheduledRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'isValidSignature',
    type: 'function',
    inputs: [
      {
        name: 'hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'magicValue',
        type: 'bytes4',
        internalType: 'bytes4',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'payment',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentAndState',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        components: [
          {
            name: 'payment',
            type: 'tuple',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment',
          },
          {
            name: 'state',
            type: 'uint8',
            internalType: 'enum RiseRequests.RisePaymentState',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentAndState',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentState',
    type: 'function',
    inputs: [
      {
        name: 'paymentID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint8',
        internalType: 'enum RiseRequests.RisePaymentState',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'payments',
    type: 'function',
    inputs: [
      {
        name: 'paymentIDs',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'payment',
            type: 'tuple',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment',
          },
          {
            name: 'state',
            type: 'uint8',
            internalType: 'enum RiseRequests.RisePaymentState',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentAndState[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsCount',
    type: 'function',
    inputs: [
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: '_identifier',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsHashSlice',
    type: 'function',
    inputs: [
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: '_identifier',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '_paymentIDs',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsSlice',
    type: 'function',
    inputs: [
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: '_identifier',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '_payments',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'removePayments',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'paymentIDs',
            type: 'bytes32[]',
            internalType: 'bytes32[]',
          },
        ],
        internalType: 'struct RiseRequests.RiseRemovePaymentsRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'removePaymentsByGroupID',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'idx',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'count',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.RiseRemovePaymentByGroupRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'sendEther',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.RiseEtherTransferRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRoles',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple[]',
        components: [
          {
            name: 'role',
            type: 'uint8',
            internalType: 'enum RiseRequests.Role',
          },
          {
            name: 'account',
            type: 'address',
            internalType: 'address',
          },
        ],
        internalType: 'struct RiseRequests.SetRole[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setSettings',
    type: 'function',
    inputs: [
      {
        name: 'accountType',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'parentAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sourceOfFunds',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'hiddenRiseTokenTransfers',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'sponsorAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'feeRecipient',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setTokenTransferApproval',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.RiseTokenApprovalRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setTransactionLimits',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'dailyLimit',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'transactionLimit',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.RiseTransactionLimitRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'tokenTransfer',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.RiseTokenTransferRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'transactionLimit',
    type: 'function',
    inputs: [
      {
        name: '_hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'updatePaymentAmount',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'paymentIDs',
            type: 'bytes32[]',
            internalType: 'bytes32[]',
          },
          {
            name: 'newAmounts',
            type: 'uint256[]',
            internalType: 'uint256[]',
          },
        ],
        internalType: 'struct RiseRequests.RiseUpdateAmountPaymentsRequest',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseAccount_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseAccount_impl_arbitrumInterface {
    return new Interface(_abi) as RiseAccount_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseAccount_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseAccount_impl_arbitrum
  }
}
