/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseID_impl_arbitrum,
  RiseID_impl_arbitrumInterface,
} from '../RiseID_impl_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'AddressEmptyCode',
    type: 'error',
    inputs: [
      {
        name: 'target',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Create2EmptyBytecode',
    type: 'error',
    inputs: [],
  },
  {
    name: 'FailedCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'FailedDeployment',
    type: 'error',
    inputs: [],
  },
  {
    name: 'InsufficientBalance',
    type: 'error',
    inputs: [
      {
        name: 'balance',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'needed',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'ReentrancyGuardReentrantCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequestWithReason',
    type: 'error',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        internalType: 'string',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Address',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Amount',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Payable',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_Unauthorized',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'SafeERC20FailedDecreaseAllowance',
    type: 'error',
    inputs: [
      {
        name: 'spender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'currentAllowance',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'requestedDecrease',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    name: 'SafeERC20FailedOperation',
    type: 'error',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'ContractCreated',
    type: 'event',
    inputs: [
      {
        name: 'operationType',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'contractAddress',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'value',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
      {
        name: 'salt',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
    ],
    anonymous: false,
  },
  {
    name: 'DataChanged',
    type: 'event',
    inputs: [
      {
        name: 'dataKey',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'dataValue',
        type: 'bytes',
        indexed: false,
        internalType: 'bytes',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Executed',
    type: 'event',
    inputs: [
      {
        name: 'operationType',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'target',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'value',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
      {
        name: 'selector',
        type: 'bytes4',
        indexed: true,
        internalType: 'bytes4',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    name: 'approve',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'incOrDec',
            type: 'bool',
            internalType: 'bool',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.ApprovalChange',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'approve',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'spender',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.Approve',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'call',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'method',
            type: 'bytes',
            internalType: 'bytes',
          },
          {
            name: 'data',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType: 'struct RiseRequests.Execution',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'execute',
    type: 'function',
    inputs: [
      {
        name: 'operationType',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'target',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'value',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'data',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'payable',
  },
  {
    name: 'execute',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'method',
            type: 'bytes',
            internalType: 'bytes',
          },
          {
            name: 'data',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType: 'struct RiseRequests.Execution',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'executeBatch',
    type: 'function',
    inputs: [
      {
        name: 'operationsType',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: 'targets',
        type: 'address[]',
        internalType: 'address[]',
      },
      {
        name: 'values',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: 'datas',
        type: 'bytes[]',
        internalType: 'bytes[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes[]',
        internalType: 'bytes[]',
      },
    ],
    stateMutability: 'payable',
  },
  {
    name: 'getData',
    type: 'function',
    inputs: [
      {
        name: 'dataKey',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: 'dataValue',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getDataBatch',
    type: 'function',
    inputs: [
      {
        name: 'dataKeys',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: 'dataValues',
        type: 'bytes[]',
        internalType: 'bytes[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMemberRole',
    type: 'function',
    inputs: [
      {
        name: 'member',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint8',
        internalType: 'enum RiseRequests.Role',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMembers',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMembersRoles',
    type: 'function',
    inputs: [
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'member',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'role',
            type: 'uint8',
            internalType: 'enum RiseRequests.Role',
          },
        ],
        internalType: 'struct RiseRequests.MemberRole[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getRoleMembers',
    type: 'function',
    inputs: [
      {
        name: 'role',
        type: 'uint8',
        internalType: 'enum RiseRequests.Role',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_riseRouter',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'newOwner',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'initData',
    type: 'function',
    inputs: [
      {
        name: 'data',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'isTrustedForwarder',
    type: 'function',
    inputs: [
      {
        name: 'forwarder',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'isValidSignature',
    type: 'function',
    inputs: [
      {
        name: 'hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'magicValue',
        type: 'bytes4',
        internalType: 'bytes4',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'recoverOwnership',
    type: 'function',
    inputs: [
      {
        name: 'newOwner',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setData',
    type: 'function',
    inputs: [
      {
        name: 'dataKey',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'dataValue',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [],
    stateMutability: 'payable',
  },
  {
    name: 'setData',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'dataKey',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'dataValue',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType: 'struct RiseRequests.Dataset',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setDataBatch',
    type: 'function',
    inputs: [
      {
        name: 'dataKeys',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
      {
        name: 'dataValues',
        type: 'bytes[]',
        internalType: 'bytes[]',
      },
    ],
    outputs: [],
    stateMutability: 'payable',
  },
  {
    name: 'setRoles',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple[]',
        components: [
          {
            name: 'role',
            type: 'uint8',
            internalType: 'enum RiseRequests.Role',
          },
          {
            name: 'account',
            type: 'address',
            internalType: 'address',
          },
        ],
        internalType: 'struct RiseRequests.SetRole[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'supportsInterface',
    type: 'function',
    inputs: [
      {
        name: 'interfaceId',
        type: 'bytes4',
        internalType: 'bytes4',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'transfer',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.Transfer',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'transferFrom',
    type: 'function',
    inputs: [
      {
        name: 'req',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct RiseRequests.TransferFrom',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseID_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseID_impl_arbitrumInterface {
    return new Interface(_abi) as RiseID_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseID_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseID_impl_arbitrum
  }
}
