/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseGovernor_arbitrum,
  RiseGovernor_arbitrumInterface,
} from '../RiseGovernor_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseToken',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'addFlatRateCount',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'batchGrant',
    type: 'function',
    inputs: [
      {
        name: 'role',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'riseIdIdxs',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'batchRegister',
    type: 'function',
    inputs: [
      {
        name: 'addresses',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'batchRevoke',
    type: 'function',
    inputs: [
      {
        name: 'role',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'riseIdIdxs',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'isPayerAndPayee',
    type: 'function',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'payees',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool[]',
        internalType: 'bool[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'isTrustedForwarder',
    type: 'function',
    inputs: [
      {
        name: 'forwarder',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'multiLookup',
    type: 'function',
    inputs: [
      {
        name: 'addresses',
        type: 'address[]',
        internalType: 'address[]',
      },
    ],
    outputs: [
      {
        name: 'indexes',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'multiReadBalances',
    type: 'function',
    inputs: [
      {
        name: 'addresses',
        type: 'address[]',
        internalType: 'address[]',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: 'balances',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'multiRoleGrants',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'roles',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'multiRoleRevokes',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'roles',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'multiRolesGet',
    type: 'function',
    inputs: [
      {
        name: 'riseIdAddress',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'roles',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool[]',
        internalType: 'bool[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'risePayToken',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRisePayToken',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setFlatRate',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'value',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setFlatRateCreditCard',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'value',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setMultipleFlatRateCreditCard',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdxs',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: 'value',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setSubscriptionPercent',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'value',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'upgradeDeductionsAndCredits',
    type: 'function',
    inputs: [
      {
        name: 'newDAC',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'contractsIdxs',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'upgradeIds',
    type: 'function',
    inputs: [
      {
        name: 'payIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'riseIdIdxs',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RiseGovernor_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseGovernor_arbitrumInterface {
    return new Interface(_abi) as RiseGovernor_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseGovernor_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseGovernor_arbitrum
  }
}
