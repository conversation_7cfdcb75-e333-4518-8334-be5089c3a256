/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseFinanceGovernor_arbitrum,
  RiseFinanceGovernor_arbitrumInterface,
} from '../RiseFinanceGovernor_arbitrum.js'

const _abi = [
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequestWithReason',
    type: 'error',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        internalType: 'string',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: '_getDailyLimitKey',
    type: 'function',
    inputs: [
      {
        name: 'riseAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'creditRiseAccount',
    type: 'function',
    inputs: [
      {
        name: 'riseAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'saltOrHashKey',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'getCredit',
    type: 'function',
    inputs: [
      {
        name: 'saltOrHashKey',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        components: [
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'credited',
            type: 'bool',
            internalType: 'bool',
          },
        ],
        internalType: 'struct RiseFinanceGovernorStorage.CreditData',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getDailyLimit',
    type: 'function',
    inputs: [
      {
        name: 'riseAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        components: [
          {
            name: 'limit',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'usage',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'set',
            type: 'bool',
            internalType: 'bool',
          },
        ],
        internalType: 'struct RiseFinanceGovernorStorage.LimitData',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getTokenDefaultLimit',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: 'riseRouter',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setDailyLimit',
    type: 'function',
    inputs: [
      {
        name: 'riseAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'limit',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'set',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setTokenDefaultLimit',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'limit',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseFinanceGovernor_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseFinanceGovernor_arbitrumInterface {
    return new Interface(_abi) as RiseFinanceGovernor_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseFinanceGovernor_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseFinanceGovernor_arbitrum
  }
}
