/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePayRampForEx_arbitrum,
  RisePayRampForEx_arbitrumInterface,
} from '../RisePayRampForEx_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseTreasury',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_usdc',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseFeeRecipient',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'Fee',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'FeeChargeBack',
    type: 'event',
    inputs: [
      {
        name: 'chargedAccount',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'coveredAccount',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Funded',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'RiseFeeAmount',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: false,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Withdrawal',
    type: 'event',
    inputs: [
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'feeAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        indexed: false,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'USDC',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'fund',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'getKeyFeeData',
    type: 'function',
    inputs: [
      {
        name: 'key',
        type: 'bytes20',
        internalType: 'bytes20',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        components: [
          {
            name: 'amountFlat',
            type: 'uint96',
            internalType: 'uint96',
          },
          {
            name: 'amountPercent',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'isSet',
            type: 'bool',
            internalType: 'bool',
          },
        ],
        internalType: 'struct RisePayMapFees.FeeData',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getKeyHash',
    type: 'function',
    inputs: [
      {
        name: 'key',
        type: 'bytes20',
        internalType: 'bytes20',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'minimumFeeAmount',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'name',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'string',
        internalType: 'string',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'nameHash',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseFeeRecipient',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setFee',
    type: 'function',
    inputs: [
      {
        name: 'key',
        type: 'bytes20',
        internalType: 'bytes20',
      },
      {
        name: 'amountFlat',
        type: 'uint96',
        internalType: 'uint96',
      },
      {
        name: 'amountPercent',
        type: 'uint16',
        internalType: 'uint16',
      },
      {
        name: 'isSet',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setMinimumFeeAmount',
    type: 'function',
    inputs: [
      {
        name: '_minimumFeeAmount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RisePayRampForEx_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePayRampForEx_arbitrumInterface {
    return new Interface(_abi) as RisePayRampForEx_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePayRampForEx_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RisePayRampForEx_arbitrum
  }
}
