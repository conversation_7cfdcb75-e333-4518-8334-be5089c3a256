/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePaymentIdentifiers_arbitrum,
  RisePaymentIdentifiers_arbitrumInterface,
} from '../RisePaymentIdentifiers_arbitrum.js'

const _abi = [
  {
    name: 'addressIdentifier',
    type: 'function',
    inputs: [
      {
        name: 'addr',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'dataIdentifier',
    type: 'function',
    inputs: [
      {
        name: 'data',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'groupIdentifier',
    type: 'function',
    inputs: [
      {
        name: 'groupID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'hashDayIdentifier',
    type: 'function',
    inputs: [
      {
        name: '_hash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: '_identifier',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'hashSubscriptionID',
    type: 'function',
    inputs: [
      {
        name: 'monthHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'recipient',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'hashSubscriptionMonth',
    type: 'function',
    inputs: [
      {
        name: 'monthHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'pendingIdentifier',
    type: 'function',
    inputs: [
      {
        name: 'baseHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
] as const

export class RisePaymentIdentifiers_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePaymentIdentifiers_arbitrumInterface {
    return new Interface(_abi) as RisePaymentIdentifiers_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePaymentIdentifiers_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RisePaymentIdentifiers_arbitrum
  }
}
