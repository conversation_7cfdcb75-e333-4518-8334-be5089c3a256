/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseIDFactory_arbitrum,
  RiseIDFactory_arbitrumInterface,
} from '../RiseIDFactory_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'NewInstance',
    type: 'event',
    inputs: [
      {
        name: 'instance',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'idx',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
      {
        name: 'role',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
    ],
    anonymous: false,
  },
  {
    name: 'clone',
    type: 'function',
    inputs: [
      {
        name: 'implementationIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'owner',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'isTrustedForwarder',
    type: 'function',
    inputs: [
      {
        name: 'forwarder',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
] as const

export class RiseIDFactory_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseIDFactory_arbitrumInterface {
    return new Interface(_abi) as RiseIDFactory_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseIDFactory_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseIDFactory_arbitrum
  }
}
