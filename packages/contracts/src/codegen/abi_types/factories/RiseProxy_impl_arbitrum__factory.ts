/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseProxy_impl_arbitrum,
  RiseProxy_impl_arbitrumInterface,
} from '../RiseProxy_impl_arbitrum.js'

const _abi = [
  {
    name: 'Rise_Proxy_AlreadyInitialized',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_Proxy_BadBeacon',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_Proxy_BadImplementation',
    type: 'error',
    inputs: [],
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'initProxy',
    type: 'function',
    inputs: [
      {
        name: 'router',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'implementationRef',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseProxy_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseProxy_impl_arbitrumInterface {
    return new Interface(_abi) as RiseProxy_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseProxy_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseProxy_impl_arbitrum
  }
}
