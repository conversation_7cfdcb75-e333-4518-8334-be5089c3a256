/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseAccountGovernor_arbitrum,
  RiseAccountGovernor_arbitrumInterface,
} from '../RiseAccountGovernor_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequestWithReason',
    type: 'error',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        internalType: 'string',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Address',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: 'createPayments',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'payments',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'executePayments',
    type: 'function',
    inputs: [
      {
        name: 'accounts',
        type: 'address[]',
        internalType: 'contract IRiseAccount[]',
      },
      {
        name: 'paymentIDs',
        type: 'bytes32[][]',
        internalType: 'bytes32[][]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'getMultiplePaymentSenderTrackingStatsByDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseTransactionLimits',
      },
      {
        name: 'senders',
        type: 'address[]',
        internalType: 'address[]',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: '',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getMultipleTransactionLimits',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseTransactionLimits',
      },
      {
        name: 'senders',
        type: 'address[]',
        internalType: 'address[]',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: '',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPaymentSenderTrackingStatsByDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRisePaymentTracking',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'volume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPaymentSenderTrackingStatsByMonth',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRisePaymentTracking',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'volume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPaymentTokenTrackingStatsByDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRisePaymentTracking',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'volume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPaymentTokenTrackingStatsByMonth',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRisePaymentTracking',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'epoch',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPaymentVolume',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'startTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dayCount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'payType',
        type: 'uint16[]',
        internalType: 'uint16[]',
      },
      {
        name: 'recipient',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: 'volume',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getTransactionLimits',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseTransactionLimits',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: 'riseRouter',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'paymentIsPayable',
    type: 'function',
    inputs: [
      {
        name: '_payment',
        type: 'tuple',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByAccountsNow',
    type: 'function',
    inputs: [
      {
        name: 'accounts',
        type: 'address[]',
        internalType: 'contract IRiseAccount[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[][]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[][]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByAddress',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'addr',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByAddressDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'addr',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'dayTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByAddressDays',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'addr',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'startTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dayCount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'payments',
            type: 'tuple[]',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment[]',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentsRequest[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'dayTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByDays',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'startTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dayCount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'payments',
            type: 'tuple[]',
            components: [
              {
                name: 'id',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'groupID',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'payAtTime',
                type: 'uint128',
                internalType: 'uint128',
              },
              {
                name: 'validMinutes',
                type: 'uint32',
                internalType: 'uint32',
              },
              {
                name: 'payType',
                type: 'uint16',
                internalType: 'uint16',
              },
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'recipient',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'data',
                type: 'bytes32',
                internalType: 'bytes32',
              },
            ],
            internalType: 'struct RiseRequests.RisePayment[]',
          },
        ],
        internalType: 'struct RiseRequests.RisePaymentsRequest[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByGroup',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'groupID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'paymentsByGroupDay',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'storageType',
        type: 'uint8',
        internalType: 'enum IRisePaymentsBase.RiseStorageTypes',
      },
      {
        name: 'groupID',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'dayTime',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'idx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'count',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'groupID',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'payAtTime',
            type: 'uint128',
            internalType: 'uint128',
          },
          {
            name: 'validMinutes',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'payType',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'data',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseRequests.RisePayment[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'removePayments',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'paymentIDs',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setAccountSponsor',
    type: 'function',
    inputs: [
      {
        name: 'sponsorAccount',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'accounts',
        type: 'address[]',
        internalType: 'contract IRiseAccount[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'updatePaymentAmount',
    type: 'function',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'contract IRiseAccount',
      },
      {
        name: 'paymentIDs',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
      {
        name: 'newAmounts',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseAccountGovernor_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseAccountGovernor_arbitrumInterface {
    return new Interface(_abi) as RiseAccountGovernor_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseAccountGovernor_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseAccountGovernor_arbitrum
  }
}
