/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseDeductionsAndCredits_arbitrum,
  RiseDeductionsAndCredits_arbitrumInterface,
} from '../RiseDeductionsAndCredits_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_risePayToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseFeeRecipient',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseTreasury',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_usdc',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'Credit',
    type: 'event',
    inputs: [
      {
        name: 'eventType',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Deduction',
    type: 'event',
    inputs: [
      {
        name: 'eventType',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Discount',
    type: 'event',
    inputs: [
      {
        name: 'eventType',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'equationAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Fee',
    type: 'event',
    inputs: [
      {
        name: 'eventType',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'account',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'equationAmount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'USDCInstance',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IERC20Simple',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'afterFund',
    type: 'function',
    inputs: [
      {
        name: 'funder',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'funderAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'afterPay',
    type: 'function',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'payerAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
      {
        name: 'payeeAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'afterWithdraw',
    type: 'function',
    inputs: [
      {
        name: 'withdrawer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'withdrawerAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'beforeFund',
    type: 'function',
    inputs: [
      {
        name: 'funder',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'funderAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'beforePay',
    type: 'function',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'payerAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
      {
        name: 'payeeAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'beforeWithdraw',
    type: 'function',
    inputs: [
      {
        name: 'withdrawer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'withdrawerAlteredAmount',
        type: 'int256',
        internalType: 'int256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'chargeFees',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'int256[]',
        internalType: 'int256[]',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseFeeRecipient',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'risePayToken',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRisePayToken',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseTreasury',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RiseDeductionsAndCredits_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseDeductionsAndCredits_arbitrumInterface {
    return new Interface(_abi) as RiseDeductionsAndCredits_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseDeductionsAndCredits_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseDeductionsAndCredits_arbitrum
  }
}
