/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseIDFactory_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature: 'clone' | 'isTrustedForwarder' | 'riseAccess',
  ): FunctionFragment

  getEvent(nameOrSignatureOrTopic: 'NewInstance'): EventFragment

  encodeFunctionData(
    functionFragment: 'clone',
    values: [BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string

  decodeFunctionResult(functionFragment: 'clone', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
}

export namespace NewInstanceEvent {
  export type InputTuple = [
    instance: AddressLike,
    idx: BigNumberish,
    role: BytesLike,
  ]
  export type OutputTuple = [instance: string, idx: bigint, role: string]
  export interface OutputObject {
    instance: string
    idx: bigint
    role: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseIDFactory_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseIDFactory_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseIDFactory_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  clone: TypedContractMethod<
    [implementationIdx: BigNumberish, owner: AddressLike],
    [string],
    'nonpayable'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'clone',
  ): TypedContractMethod<
    [implementationIdx: BigNumberish, owner: AddressLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>

  getEvent(
    key: 'NewInstance',
  ): TypedContractEvent<
    NewInstanceEvent.InputTuple,
    NewInstanceEvent.OutputTuple,
    NewInstanceEvent.OutputObject
  >

  filters: {
    'NewInstance(address,uint256,bytes32)': TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >
    NewInstance: TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >
  }
}
