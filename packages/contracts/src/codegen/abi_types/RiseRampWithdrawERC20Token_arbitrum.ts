/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }
}

export interface RiseRampWithdrawERC20Token_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'execute'
      | 'getMapping'
      | 'getMinimumExecuteAmount'
      | 'init'
      | 'initData'
      | 'isTrustedForwarder'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setMapping'
      | 'setMinimumExecuteAmount'
      | 'setRouter',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RiseRampExecute'
      | 'RiseRampSourceFee'
      | 'RiseRampSourceFeeCovered'
      | 'RiseRampTargetAmount'
      | 'RiseRampTargetFee',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'execute',
    values: [
      AddressLike,
      BigNumberish,
      RiseRequests.RisePaymentHandlerConfigStruct,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getMapping',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getMinimumExecuteAmount',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(functionFragment: 'initData', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setMapping',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setMinimumExecuteAmount',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getMapping', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'initData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setMapping', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampExecuteEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
    references: BytesLike,
    data: BytesLike,
  ]
  export type OutputTuple = [
    token: string,
    amount: bigint,
    destination: string,
    references: string,
    data: string,
  ]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
    references: string
    data: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeCoveredEvent {
  export type InputTuple = [
    amount: BigNumberish,
    sponsor: AddressLike,
    paymentID: BytesLike,
  ]
  export type OutputTuple = [amount: bigint, sponsor: string, paymentID: string]
  export interface OutputObject {
    amount: bigint
    sponsor: string
    paymentID: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetAmountEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
  ]
  export type OutputTuple = [token: string, amount: bigint, destination: string]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseRampWithdrawERC20Token_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseRampWithdrawERC20Token_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseRampWithdrawERC20Token_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  execute: TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >

  getMapping: TypedContractMethod<[source: AddressLike], [string], 'view'>

  getMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike],
    [bigint],
    'view'
  >

  init: TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>

  initData: TypedContractMethod<[data: BytesLike], [string], 'view'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setMapping: TypedContractMethod<
    [source: AddressLike, riseToken: AddressLike],
    [void],
    'nonpayable'
  >

  setMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'execute',
  ): TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getMapping',
  ): TypedContractMethod<[source: AddressLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getMinimumExecuteAmount',
  ): TypedContractMethod<[token: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'initData',
  ): TypedContractMethod<[data: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setMapping',
  ): TypedContractMethod<
    [source: AddressLike, riseToken: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMinimumExecuteAmount',
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampExecute',
  ): TypedContractEvent<
    RiseRampExecuteEvent.InputTuple,
    RiseRampExecuteEvent.OutputTuple,
    RiseRampExecuteEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFee',
  ): TypedContractEvent<
    RiseRampSourceFeeEvent.InputTuple,
    RiseRampSourceFeeEvent.OutputTuple,
    RiseRampSourceFeeEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFeeCovered',
  ): TypedContractEvent<
    RiseRampSourceFeeCoveredEvent.InputTuple,
    RiseRampSourceFeeCoveredEvent.OutputTuple,
    RiseRampSourceFeeCoveredEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetAmount',
  ): TypedContractEvent<
    RiseRampTargetAmountEvent.InputTuple,
    RiseRampTargetAmountEvent.OutputTuple,
    RiseRampTargetAmountEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetFee',
  ): TypedContractEvent<
    RiseRampTargetFeeEvent.InputTuple,
    RiseRampTargetFeeEvent.OutputTuple,
    RiseRampTargetFeeEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseRampExecute(address,uint256,address,bytes32,bytes)': TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >
    RiseRampExecute: TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >

    'RiseRampSourceFee(uint256,address)': TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >
    RiseRampSourceFee: TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >

    'RiseRampSourceFeeCovered(uint256,address,bytes32)': TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >
    RiseRampSourceFeeCovered: TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >

    'RiseRampTargetAmount(address,uint256,address)': TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >
    RiseRampTargetAmount: TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >

    'RiseRampTargetFee(uint256,address)': TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
    RiseRampTargetFee: TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
  }
}
