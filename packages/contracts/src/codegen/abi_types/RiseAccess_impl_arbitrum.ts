/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigN<PERSON>berish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace IRiseAccess {
  export type RiseAccessRoleStruct = { roleHash: BytesLike; roleName: string }

  export type RiseAccessRoleStructOutput = [
    roleHash: string,
    roleName: string,
  ] & { roleHash: string; roleName: string }
}

export interface RiseAccess_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'DEFAULT_ADMIN_ROLE'
      | 'getRoleAdmin'
      | 'getRoleMember'
      | 'getRoleMemberCount'
      | 'getRoleMemberSlice'
      | 'getRoleMembersAll'
      | 'getRoles'
      | 'getTxNullified'
      | 'grantRole'
      | 'hasRole'
      | 'init'
      | 'nullify'
      | 'renounceRole'
      | 'revokeRole'
      | 'setRoleAdmin'
      | 'supportsInterface',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RoleAdminChanged'
      | 'RoleGranted'
      | 'RoleRevoked',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'DEFAULT_ADMIN_ROLE',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleAdmin',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMember',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMemberCount',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMemberSlice',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMembersAll',
    values: [BytesLike],
  ): string
  encodeFunctionData(functionFragment: 'getRoles', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getTxNullified',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'grantRole',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'hasRole',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(functionFragment: 'nullify', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'renounceRole',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'revokeRole',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoleAdmin',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'DEFAULT_ADMIN_ROLE',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleAdmin',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMember',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMemberCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMemberSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMembersAll',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getRoles', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getTxNullified',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'grantRole', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'hasRole', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'nullify', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'renounceRole',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'revokeRole', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setRoleAdmin',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RoleAdminChangedEvent {
  export type InputTuple = [
    role: BytesLike,
    previousAdminRole: BytesLike,
    newAdminRole: BytesLike,
  ]
  export type OutputTuple = [
    role: string,
    previousAdminRole: string,
    newAdminRole: string,
  ]
  export interface OutputObject {
    role: string
    previousAdminRole: string
    newAdminRole: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RoleGrantedEvent {
  export type InputTuple = [
    role: BytesLike,
    account: AddressLike,
    sender: AddressLike,
  ]
  export type OutputTuple = [role: string, account: string, sender: string]
  export interface OutputObject {
    role: string
    account: string
    sender: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RoleRevokedEvent {
  export type InputTuple = [
    role: BytesLike,
    account: AddressLike,
    sender: AddressLike,
  ]
  export type OutputTuple = [role: string, account: string, sender: string]
  export interface OutputObject {
    role: string
    account: string
    sender: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccess_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccess_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccess_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  DEFAULT_ADMIN_ROLE: TypedContractMethod<[], [string], 'view'>

  getRoleAdmin: TypedContractMethod<[role: BytesLike], [string], 'view'>

  getRoleMember: TypedContractMethod<
    [role: BytesLike, index: BigNumberish],
    [string],
    'view'
  >

  getRoleMemberCount: TypedContractMethod<[role: BytesLike], [bigint], 'view'>

  getRoleMemberSlice: TypedContractMethod<
    [role: BytesLike, idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >

  getRoleMembersAll: TypedContractMethod<[role: BytesLike], [string[]], 'view'>

  getRoles: TypedContractMethod<
    [],
    [IRiseAccess.RiseAccessRoleStructOutput[]],
    'view'
  >

  getTxNullified: TypedContractMethod<
    [requestHash: BytesLike],
    [boolean],
    'view'
  >

  grantRole: TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [void],
    'nonpayable'
  >

  hasRole: TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [boolean],
    'view'
  >

  init: TypedContractMethod<[_admin: AddressLike], [void], 'nonpayable'>

  nullify: TypedContractMethod<[requestHash: BytesLike], [void], 'nonpayable'>

  renounceRole: TypedContractMethod<
    [role: BytesLike, callerConfirmation: AddressLike],
    [void],
    'nonpayable'
  >

  revokeRole: TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [void],
    'nonpayable'
  >

  setRoleAdmin: TypedContractMethod<
    [role: BytesLike, adminRole: BytesLike],
    [void],
    'nonpayable'
  >

  supportsInterface: TypedContractMethod<
    [interfaceId: BytesLike],
    [boolean],
    'view'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'DEFAULT_ADMIN_ROLE',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'getRoleAdmin',
  ): TypedContractMethod<[role: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getRoleMember',
  ): TypedContractMethod<
    [role: BytesLike, index: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'getRoleMemberCount',
  ): TypedContractMethod<[role: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getRoleMemberSlice',
  ): TypedContractMethod<
    [role: BytesLike, idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getRoleMembersAll',
  ): TypedContractMethod<[role: BytesLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getRoles',
  ): TypedContractMethod<[], [IRiseAccess.RiseAccessRoleStructOutput[]], 'view'>
  getFunction(
    nameOrSignature: 'getTxNullified',
  ): TypedContractMethod<[requestHash: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'grantRole',
  ): TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'hasRole',
  ): TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_admin: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'nullify',
  ): TypedContractMethod<[requestHash: BytesLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'renounceRole',
  ): TypedContractMethod<
    [role: BytesLike, callerConfirmation: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'revokeRole',
  ): TypedContractMethod<
    [role: BytesLike, account: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRoleAdmin',
  ): TypedContractMethod<
    [role: BytesLike, adminRole: BytesLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'supportsInterface',
  ): TypedContractMethod<[interfaceId: BytesLike], [boolean], 'view'>

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RoleAdminChanged',
  ): TypedContractEvent<
    RoleAdminChangedEvent.InputTuple,
    RoleAdminChangedEvent.OutputTuple,
    RoleAdminChangedEvent.OutputObject
  >
  getEvent(
    key: 'RoleGranted',
  ): TypedContractEvent<
    RoleGrantedEvent.InputTuple,
    RoleGrantedEvent.OutputTuple,
    RoleGrantedEvent.OutputObject
  >
  getEvent(
    key: 'RoleRevoked',
  ): TypedContractEvent<
    RoleRevokedEvent.InputTuple,
    RoleRevokedEvent.OutputTuple,
    RoleRevokedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RoleAdminChanged(bytes32,bytes32,bytes32)': TypedContractEvent<
      RoleAdminChangedEvent.InputTuple,
      RoleAdminChangedEvent.OutputTuple,
      RoleAdminChangedEvent.OutputObject
    >
    RoleAdminChanged: TypedContractEvent<
      RoleAdminChangedEvent.InputTuple,
      RoleAdminChangedEvent.OutputTuple,
      RoleAdminChangedEvent.OutputObject
    >

    'RoleGranted(bytes32,address,address)': TypedContractEvent<
      RoleGrantedEvent.InputTuple,
      RoleGrantedEvent.OutputTuple,
      RoleGrantedEvent.OutputObject
    >
    RoleGranted: TypedContractEvent<
      RoleGrantedEvent.InputTuple,
      RoleGrantedEvent.OutputTuple,
      RoleGrantedEvent.OutputObject
    >

    'RoleRevoked(bytes32,address,address)': TypedContractEvent<
      RoleRevokedEvent.InputTuple,
      RoleRevokedEvent.OutputTuple,
      RoleRevokedEvent.OutputObject
    >
    RoleRevoked: TypedContractEvent<
      RoleRevokedEvent.InputTuple,
      RoleRevokedEvent.OutputTuple,
      RoleRevokedEvent.OutputObject
    >
  }
}
