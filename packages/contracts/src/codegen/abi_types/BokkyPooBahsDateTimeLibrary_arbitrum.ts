/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface BokkyPooBahsDateTimeLibrary_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature: 'getDaySlotHash' | 'getMonthSlotHash' | 'getYearSlotHash',
  ): FunctionFragment

  encodeFunctionData(
    functionFragment: 'getDaySlotHash',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getMonthSlotHash',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getYearSlotHash',
    values: [BigNumberish],
  ): string

  decodeFunctionResult(
    functionFragment: 'getDaySlotHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMonthSlotHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getYearSlotHash',
    data: BytesLike,
  ): Result
}

export interface BokkyPooBahsDateTimeLibrary_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): BokkyPooBahsDateTimeLibrary_arbitrum
  waitForDeployment(): Promise<this>

  interface: BokkyPooBahsDateTimeLibrary_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  getDaySlotHash: TypedContractMethod<
    [timestamp: BigNumberish],
    [string],
    'view'
  >

  getMonthSlotHash: TypedContractMethod<
    [timestamp: BigNumberish],
    [string],
    'view'
  >

  getYearSlotHash: TypedContractMethod<
    [timestamp: BigNumberish],
    [string],
    'view'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'getDaySlotHash',
  ): TypedContractMethod<[timestamp: BigNumberish], [string], 'view'>
  getFunction(
    nameOrSignature: 'getMonthSlotHash',
  ): TypedContractMethod<[timestamp: BigNumberish], [string], 'view'>
  getFunction(
    nameOrSignature: 'getYearSlotHash',
  ): TypedContractMethod<[timestamp: BigNumberish], [string], 'view'>

  filters: {}
}
