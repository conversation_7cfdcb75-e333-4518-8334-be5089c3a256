/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  Contract<PERSON>ethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseDedicatedFund_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'USDC'
      | 'init'
      | 'initData'
      | 'riseTreasury'
      | 'sendTokenToTreasury'
      | 'sendUSDCToTreasury',
  ): FunctionFragment

  encodeFunctionData(functionFragment: 'USDC', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'initData', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'riseTreasury',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'sendTokenToTreasury',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'sendUSDCToTreasury',
    values?: undefined,
  ): string

  decodeFunctionResult(functionFragment: 'USDC', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'initData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'riseTreasury',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'sendTokenToTreasury',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'sendUSDCToTreasury',
    data: BytesLike,
  ): Result
}

export interface RiseDedicatedFund_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseDedicatedFund_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseDedicatedFund_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  USDC: TypedContractMethod<[], [string], 'view'>

  init: TypedContractMethod<
    [_USDC: AddressLike, _riseTreasury: AddressLike],
    [void],
    'nonpayable'
  >

  initData: TypedContractMethod<[], [string], 'view'>

  riseTreasury: TypedContractMethod<[], [string], 'view'>

  sendTokenToTreasury: TypedContractMethod<
    [tokenAddress: AddressLike],
    [void],
    'nonpayable'
  >

  sendUSDCToTreasury: TypedContractMethod<[], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'USDC',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_USDC: AddressLike, _riseTreasury: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'initData',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseTreasury',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'sendTokenToTreasury',
  ): TypedContractMethod<[tokenAddress: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'sendUSDCToTreasury',
  ): TypedContractMethod<[], [void], 'nonpayable'>

  filters: {}
}
