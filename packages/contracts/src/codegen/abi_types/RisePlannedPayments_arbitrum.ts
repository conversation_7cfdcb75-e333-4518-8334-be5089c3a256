/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace IRiseStorageTypes {
  export type PayAddStruct = {
    payeeIdx: BigNumberish
    extId: BigNumberish
    amount: BigNumberish
    date: BigNumberish
  }

  export type PayAddStructOutput = [
    payeeIdx: bigint,
    extId: bigint,
    amount: bigint,
    date: bigint,
  ] & { payeeIdx: bigint; extId: bigint; amount: bigint; date: bigint }

  export type PayExecuteStruct = {
    payerIdx: BigNumberish
    payeeIdx: BigNumberish
    payHash: BytesLike
  }

  export type PayExecuteStructOutput = [
    payerIdx: bigint,
    payeeIdx: bigint,
    payHash: string,
  ] & { payerIdx: bigint; payeeIdx: bigint; payHash: string }

  export type PayStruct = {
    extId: BigNumberish
    amount: BigNumberish
    date: BigNumberish
  }

  export type PayStructOutput = [
    extId: bigint,
    amount: bigint,
    date: bigint,
  ] & { extId: bigint; amount: bigint; date: bigint }

  export type PayRemoveStruct = { payeeIdx: BigNumberish; payHash: BytesLike }

  export type PayRemoveStructOutput = [payeeIdx: bigint, payHash: string] & {
    payeeIdx: bigint
    payHash: string
  }
}

export interface RisePlannedPayments_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'add'
      | 'batchAdd'
      | 'batchPay'
      | 'batchPayLimit'
      | 'batchRemove'
      | 'canBePaid'
      | 'getPayHash'
      | 'getPaymentAt'
      | 'getPayments'
      | 'getPaymentsHashRange'
      | 'getPaymentsRange'
      | 'isTrustedForwarder'
      | 'pay'
      | 'payItemLimit'
      | 'remove'
      | 'removeByIndex'
      | 'riseAccess'
      | 'riseDeductionsAndCredits'
      | 'risePayToken'
      | 'riseStorage'
      | 'updateLimits'
      | 'upgradeDeductionsAndCedits',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'PayHash'
      | 'PaymentAddExists'
      | 'PaymentAddInvalid'
      | 'PaymentAddLimit'
      | 'PaymentAdded'
      | 'PaymentExecuted'
      | 'PaymentExecutedFailed'
      | 'PaymentRemoved'
      | 'PaymentsRelationshipCreated'
      | 'ZeroPayPaymentsRemaining',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'add',
    values: [IRiseStorageTypes.PayAddStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'batchAdd',
    values: [IRiseStorageTypes.PayAddStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'batchPay',
    values: [IRiseStorageTypes.PayExecuteStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'batchPayLimit',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'batchRemove',
    values: [BigNumberish, BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'canBePaid',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayHash',
    values: [IRiseStorageTypes.PayStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentAt',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayments',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentsHashRange',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaymentsRange',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'pay',
    values: [IRiseStorageTypes.PayExecuteStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'payItemLimit',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'remove',
    values: [IRiseStorageTypes.PayRemoveStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'removeByIndex',
    values: [BigNumberish, BigNumberish[]],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'riseDeductionsAndCredits',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'risePayToken',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'riseStorage',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'updateLimits',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeDeductionsAndCedits',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'add', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'batchAdd', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'batchPay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'batchPayLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'batchRemove', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canBePaid', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getPayHash', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentAt',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getPayments', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentsHashRange',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaymentsRange',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'pay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'payItemLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'remove', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'removeByIndex',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'riseDeductionsAndCredits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'risePayToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseStorage', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'updateLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'upgradeDeductionsAndCedits',
    data: BytesLike,
  ): Result
}

export namespace PayHashEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    amount: BigNumberish,
    salt: BigNumberish,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    amount: bigint,
    salt: bigint,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    amount: bigint
    salt: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentAddExistsEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentAddInvalidEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentAddLimitEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentAddedEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentExecutedEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    amount: BigNumberish,
    payHash: BytesLike,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    amount: bigint,
    payHash: string,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    amount: bigint
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentExecutedFailedEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentRemovedEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    payHash: BytesLike,
  ]
  export type OutputTuple = [payer: string, payee: string, payHash: string]
  export interface OutputObject {
    payer: string
    payee: string
    payHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PaymentsRelationshipCreatedEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    relationshipHash: BytesLike,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    relationshipHash: string,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    relationshipHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ZeroPayPaymentsRemainingEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    relationshipHash: BytesLike,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    relationshipHash: string,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    relationshipHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePlannedPayments_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePlannedPayments_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePlannedPayments_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  add: TypedContractMethod<
    [payAdd: IRiseStorageTypes.PayAddStruct],
    [void],
    'nonpayable'
  >

  batchAdd: TypedContractMethod<
    [payAdd: IRiseStorageTypes.PayAddStruct[]],
    [void],
    'nonpayable'
  >

  batchPay: TypedContractMethod<
    [payExecute: IRiseStorageTypes.PayExecuteStruct[]],
    [void],
    'nonpayable'
  >

  batchPayLimit: TypedContractMethod<[], [bigint], 'view'>

  batchRemove: TypedContractMethod<
    [payeeIdx: BigNumberish, payHashes: BytesLike[]],
    [void],
    'nonpayable'
  >

  canBePaid: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >

  getPayHash: TypedContractMethod<
    [payment: IRiseStorageTypes.PayStruct],
    [string],
    'view'
  >

  getPaymentAt: TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [IRiseStorageTypes.PayStructOutput],
    'view'
  >

  getPayments: TypedContractMethod<
    [relationshipHash: BytesLike, payDate: BigNumberish],
    [IRiseStorageTypes.PayStructOutput[]],
    'view'
  >

  getPaymentsHashRange: TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [[string[], bigint]],
    'view'
  >

  getPaymentsRange: TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [
      [IRiseStorageTypes.PayStructOutput[], bigint] & {
        results: IRiseStorageTypes.PayStructOutput[]
        totalAmount: bigint
      },
    ],
    'view'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  pay: TypedContractMethod<
    [payExecute: IRiseStorageTypes.PayExecuteStruct],
    [void],
    'nonpayable'
  >

  payItemLimit: TypedContractMethod<[], [bigint], 'view'>

  remove: TypedContractMethod<
    [payRemove: IRiseStorageTypes.PayRemoveStruct],
    [void],
    'nonpayable'
  >

  removeByIndex: TypedContractMethod<
    [payeeIdx: BigNumberish, indexes: BigNumberish[]],
    [void],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseDeductionsAndCredits: TypedContractMethod<[], [string], 'view'>

  risePayToken: TypedContractMethod<[], [string], 'view'>

  riseStorage: TypedContractMethod<[], [string], 'view'>

  updateLimits: TypedContractMethod<
    [_payItemLimit: BigNumberish, _batchPayLimit: BigNumberish],
    [void],
    'nonpayable'
  >

  upgradeDeductionsAndCedits: TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'add',
  ): TypedContractMethod<
    [payAdd: IRiseStorageTypes.PayAddStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchAdd',
  ): TypedContractMethod<
    [payAdd: IRiseStorageTypes.PayAddStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchPay',
  ): TypedContractMethod<
    [payExecute: IRiseStorageTypes.PayExecuteStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchPayLimit',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'batchRemove',
  ): TypedContractMethod<
    [payeeIdx: BigNumberish, payHashes: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'canBePaid',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPayHash',
  ): TypedContractMethod<
    [payment: IRiseStorageTypes.PayStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentAt',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [IRiseStorageTypes.PayStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPayments',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, payDate: BigNumberish],
    [IRiseStorageTypes.PayStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaymentsHashRange',
  ): TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [[string[], bigint]],
    'view'
  >
  getFunction(nameOrSignature: 'getPaymentsRange'): TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [
      [IRiseStorageTypes.PayStructOutput[], bigint] & {
        results: IRiseStorageTypes.PayStructOutput[]
        totalAmount: bigint
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'pay',
  ): TypedContractMethod<
    [payExecute: IRiseStorageTypes.PayExecuteStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'payItemLimit',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'remove',
  ): TypedContractMethod<
    [payRemove: IRiseStorageTypes.PayRemoveStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'removeByIndex',
  ): TypedContractMethod<
    [payeeIdx: BigNumberish, indexes: BigNumberish[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseDeductionsAndCredits',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'risePayToken',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseStorage',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'updateLimits',
  ): TypedContractMethod<
    [_payItemLimit: BigNumberish, _batchPayLimit: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'upgradeDeductionsAndCedits',
  ): TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'PayHash',
  ): TypedContractEvent<
    PayHashEvent.InputTuple,
    PayHashEvent.OutputTuple,
    PayHashEvent.OutputObject
  >
  getEvent(
    key: 'PaymentAddExists',
  ): TypedContractEvent<
    PaymentAddExistsEvent.InputTuple,
    PaymentAddExistsEvent.OutputTuple,
    PaymentAddExistsEvent.OutputObject
  >
  getEvent(
    key: 'PaymentAddInvalid',
  ): TypedContractEvent<
    PaymentAddInvalidEvent.InputTuple,
    PaymentAddInvalidEvent.OutputTuple,
    PaymentAddInvalidEvent.OutputObject
  >
  getEvent(
    key: 'PaymentAddLimit',
  ): TypedContractEvent<
    PaymentAddLimitEvent.InputTuple,
    PaymentAddLimitEvent.OutputTuple,
    PaymentAddLimitEvent.OutputObject
  >
  getEvent(
    key: 'PaymentAdded',
  ): TypedContractEvent<
    PaymentAddedEvent.InputTuple,
    PaymentAddedEvent.OutputTuple,
    PaymentAddedEvent.OutputObject
  >
  getEvent(
    key: 'PaymentExecuted',
  ): TypedContractEvent<
    PaymentExecutedEvent.InputTuple,
    PaymentExecutedEvent.OutputTuple,
    PaymentExecutedEvent.OutputObject
  >
  getEvent(
    key: 'PaymentExecutedFailed',
  ): TypedContractEvent<
    PaymentExecutedFailedEvent.InputTuple,
    PaymentExecutedFailedEvent.OutputTuple,
    PaymentExecutedFailedEvent.OutputObject
  >
  getEvent(
    key: 'PaymentRemoved',
  ): TypedContractEvent<
    PaymentRemovedEvent.InputTuple,
    PaymentRemovedEvent.OutputTuple,
    PaymentRemovedEvent.OutputObject
  >
  getEvent(
    key: 'PaymentsRelationshipCreated',
  ): TypedContractEvent<
    PaymentsRelationshipCreatedEvent.InputTuple,
    PaymentsRelationshipCreatedEvent.OutputTuple,
    PaymentsRelationshipCreatedEvent.OutputObject
  >
  getEvent(
    key: 'ZeroPayPaymentsRemaining',
  ): TypedContractEvent<
    ZeroPayPaymentsRemainingEvent.InputTuple,
    ZeroPayPaymentsRemainingEvent.OutputTuple,
    ZeroPayPaymentsRemainingEvent.OutputObject
  >

  filters: {
    'PayHash(address,address,uint256,uint256)': TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >
    PayHash: TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >

    'PaymentAddExists(address,address,bytes32)': TypedContractEvent<
      PaymentAddExistsEvent.InputTuple,
      PaymentAddExistsEvent.OutputTuple,
      PaymentAddExistsEvent.OutputObject
    >
    PaymentAddExists: TypedContractEvent<
      PaymentAddExistsEvent.InputTuple,
      PaymentAddExistsEvent.OutputTuple,
      PaymentAddExistsEvent.OutputObject
    >

    'PaymentAddInvalid(address,address,bytes32)': TypedContractEvent<
      PaymentAddInvalidEvent.InputTuple,
      PaymentAddInvalidEvent.OutputTuple,
      PaymentAddInvalidEvent.OutputObject
    >
    PaymentAddInvalid: TypedContractEvent<
      PaymentAddInvalidEvent.InputTuple,
      PaymentAddInvalidEvent.OutputTuple,
      PaymentAddInvalidEvent.OutputObject
    >

    'PaymentAddLimit(address,address,bytes32)': TypedContractEvent<
      PaymentAddLimitEvent.InputTuple,
      PaymentAddLimitEvent.OutputTuple,
      PaymentAddLimitEvent.OutputObject
    >
    PaymentAddLimit: TypedContractEvent<
      PaymentAddLimitEvent.InputTuple,
      PaymentAddLimitEvent.OutputTuple,
      PaymentAddLimitEvent.OutputObject
    >

    'PaymentAdded(address,address,bytes32)': TypedContractEvent<
      PaymentAddedEvent.InputTuple,
      PaymentAddedEvent.OutputTuple,
      PaymentAddedEvent.OutputObject
    >
    PaymentAdded: TypedContractEvent<
      PaymentAddedEvent.InputTuple,
      PaymentAddedEvent.OutputTuple,
      PaymentAddedEvent.OutputObject
    >

    'PaymentExecuted(address,address,uint256,bytes32)': TypedContractEvent<
      PaymentExecutedEvent.InputTuple,
      PaymentExecutedEvent.OutputTuple,
      PaymentExecutedEvent.OutputObject
    >
    PaymentExecuted: TypedContractEvent<
      PaymentExecutedEvent.InputTuple,
      PaymentExecutedEvent.OutputTuple,
      PaymentExecutedEvent.OutputObject
    >

    'PaymentExecutedFailed(address,address,bytes32)': TypedContractEvent<
      PaymentExecutedFailedEvent.InputTuple,
      PaymentExecutedFailedEvent.OutputTuple,
      PaymentExecutedFailedEvent.OutputObject
    >
    PaymentExecutedFailed: TypedContractEvent<
      PaymentExecutedFailedEvent.InputTuple,
      PaymentExecutedFailedEvent.OutputTuple,
      PaymentExecutedFailedEvent.OutputObject
    >

    'PaymentRemoved(address,address,bytes32)': TypedContractEvent<
      PaymentRemovedEvent.InputTuple,
      PaymentRemovedEvent.OutputTuple,
      PaymentRemovedEvent.OutputObject
    >
    PaymentRemoved: TypedContractEvent<
      PaymentRemovedEvent.InputTuple,
      PaymentRemovedEvent.OutputTuple,
      PaymentRemovedEvent.OutputObject
    >

    'PaymentsRelationshipCreated(address,address,bytes32)': TypedContractEvent<
      PaymentsRelationshipCreatedEvent.InputTuple,
      PaymentsRelationshipCreatedEvent.OutputTuple,
      PaymentsRelationshipCreatedEvent.OutputObject
    >
    PaymentsRelationshipCreated: TypedContractEvent<
      PaymentsRelationshipCreatedEvent.InputTuple,
      PaymentsRelationshipCreatedEvent.OutputTuple,
      PaymentsRelationshipCreatedEvent.OutputObject
    >

    'ZeroPayPaymentsRemaining(address,address,bytes32)': TypedContractEvent<
      ZeroPayPaymentsRemainingEvent.InputTuple,
      ZeroPayPaymentsRemainingEvent.OutputTuple,
      ZeroPayPaymentsRemainingEvent.OutputObject
    >
    ZeroPayPaymentsRemaining: TypedContractEvent<
      ZeroPayPaymentsRemainingEvent.InputTuple,
      ZeroPayPaymentsRemainingEvent.OutputTuple,
      ZeroPayPaymentsRemainingEvent.OutputObject
    >
  }
}
