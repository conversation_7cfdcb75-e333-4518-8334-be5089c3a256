/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RisePayRampUniswap_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'USDC'
      | 'fund'
      | 'name'
      | 'nameHash'
      | 'riseAccess'
      | 'swapRouter02'
      | 'withdraw',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Fee'
      | 'FeeChargeBack'
      | 'Funded'
      | 'UniswapError'
      | 'UniswapPanic'
      | 'UniswapRevert'
      | 'UniswapSuccess'
      | 'Withdrawal',
  ): EventFragment

  encodeFunctionData(functionFragment: 'USDC', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'fund',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'name', values?: undefined): string
  encodeFunctionData(functionFragment: 'nameHash', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'swapRouter02',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw',
    values: [AddressLike, AddressLike, BigNumberish, AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'USDC', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'fund', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'nameHash', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'swapRouter02',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'withdraw', data: BytesLike): Result
}

export namespace FeeEvent {
  export type InputTuple = [account: AddressLike, amount: BigNumberish]
  export type OutputTuple = [account: string, amount: bigint]
  export interface OutputObject {
    account: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace FeeChargeBackEvent {
  export type InputTuple = [
    chargedAccount: AddressLike,
    coveredAccount: AddressLike,
    feeAmount: BigNumberish,
  ]
  export type OutputTuple = [
    chargedAccount: string,
    coveredAccount: string,
    feeAmount: bigint,
  ]
  export interface OutputObject {
    chargedAccount: string
    coveredAccount: string
    feeAmount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace FundedEvent {
  export type InputTuple = [
    account: AddressLike,
    amount: BigNumberish,
    feeAmount: BigNumberish,
  ]
  export type OutputTuple = [account: string, amount: bigint, feeAmount: bigint]
  export interface OutputObject {
    account: string
    amount: bigint
    feeAmount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace UniswapErrorEvent {
  export type InputTuple = [reason: string]
  export type OutputTuple = [reason: string]
  export interface OutputObject {
    reason: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace UniswapPanicEvent {
  export type InputTuple = [errorCode: BigNumberish]
  export type OutputTuple = [errorCode: bigint]
  export interface OutputObject {
    errorCode: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace UniswapRevertEvent {
  export type InputTuple = [lowLevelData: BytesLike]
  export type OutputTuple = [lowLevelData: string]
  export interface OutputObject {
    lowLevelData: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace UniswapSuccessEvent {
  export type InputTuple = [
    rate: BigNumberish,
    inToken: AddressLike,
    outToken: AddressLike,
  ]
  export type OutputTuple = [rate: bigint, inToken: string, outToken: string]
  export interface OutputObject {
    rate: bigint
    inToken: string
    outToken: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace WithdrawalEvent {
  export type InputTuple = [
    account: AddressLike,
    amount: BigNumberish,
    feeAmount: BigNumberish,
    dest: AddressLike,
  ]
  export type OutputTuple = [
    account: string,
    amount: bigint,
    feeAmount: bigint,
    dest: string,
  ]
  export interface OutputObject {
    account: string
    amount: bigint
    feeAmount: bigint
    dest: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePayRampUniswap_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePayRampUniswap_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePayRampUniswap_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  USDC: TypedContractMethod<[], [string], 'view'>

  fund: TypedContractMethod<
    [token: AddressLike, account: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >

  name: TypedContractMethod<[], [string], 'view'>

  nameHash: TypedContractMethod<[], [string], 'view'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  swapRouter02: TypedContractMethod<[], [string], 'view'>

  withdraw: TypedContractMethod<
    [
      token: AddressLike,
      account: AddressLike,
      amount: BigNumberish,
      dest: AddressLike,
    ],
    [bigint],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'USDC',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'fund',
  ): TypedContractMethod<
    [token: AddressLike, account: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'name',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'nameHash',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'swapRouter02',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'withdraw',
  ): TypedContractMethod<
    [
      token: AddressLike,
      account: AddressLike,
      amount: BigNumberish,
      dest: AddressLike,
    ],
    [bigint],
    'nonpayable'
  >

  getEvent(
    key: 'Fee',
  ): TypedContractEvent<
    FeeEvent.InputTuple,
    FeeEvent.OutputTuple,
    FeeEvent.OutputObject
  >
  getEvent(
    key: 'FeeChargeBack',
  ): TypedContractEvent<
    FeeChargeBackEvent.InputTuple,
    FeeChargeBackEvent.OutputTuple,
    FeeChargeBackEvent.OutputObject
  >
  getEvent(
    key: 'Funded',
  ): TypedContractEvent<
    FundedEvent.InputTuple,
    FundedEvent.OutputTuple,
    FundedEvent.OutputObject
  >
  getEvent(
    key: 'UniswapError',
  ): TypedContractEvent<
    UniswapErrorEvent.InputTuple,
    UniswapErrorEvent.OutputTuple,
    UniswapErrorEvent.OutputObject
  >
  getEvent(
    key: 'UniswapPanic',
  ): TypedContractEvent<
    UniswapPanicEvent.InputTuple,
    UniswapPanicEvent.OutputTuple,
    UniswapPanicEvent.OutputObject
  >
  getEvent(
    key: 'UniswapRevert',
  ): TypedContractEvent<
    UniswapRevertEvent.InputTuple,
    UniswapRevertEvent.OutputTuple,
    UniswapRevertEvent.OutputObject
  >
  getEvent(
    key: 'UniswapSuccess',
  ): TypedContractEvent<
    UniswapSuccessEvent.InputTuple,
    UniswapSuccessEvent.OutputTuple,
    UniswapSuccessEvent.OutputObject
  >
  getEvent(
    key: 'Withdrawal',
  ): TypedContractEvent<
    WithdrawalEvent.InputTuple,
    WithdrawalEvent.OutputTuple,
    WithdrawalEvent.OutputObject
  >

  filters: {
    'Fee(address,uint256)': TypedContractEvent<
      FeeEvent.InputTuple,
      FeeEvent.OutputTuple,
      FeeEvent.OutputObject
    >
    Fee: TypedContractEvent<
      FeeEvent.InputTuple,
      FeeEvent.OutputTuple,
      FeeEvent.OutputObject
    >

    'FeeChargeBack(address,address,uint256)': TypedContractEvent<
      FeeChargeBackEvent.InputTuple,
      FeeChargeBackEvent.OutputTuple,
      FeeChargeBackEvent.OutputObject
    >
    FeeChargeBack: TypedContractEvent<
      FeeChargeBackEvent.InputTuple,
      FeeChargeBackEvent.OutputTuple,
      FeeChargeBackEvent.OutputObject
    >

    'Funded(address,uint256,uint256)': TypedContractEvent<
      FundedEvent.InputTuple,
      FundedEvent.OutputTuple,
      FundedEvent.OutputObject
    >
    Funded: TypedContractEvent<
      FundedEvent.InputTuple,
      FundedEvent.OutputTuple,
      FundedEvent.OutputObject
    >

    'UniswapError(string)': TypedContractEvent<
      UniswapErrorEvent.InputTuple,
      UniswapErrorEvent.OutputTuple,
      UniswapErrorEvent.OutputObject
    >
    UniswapError: TypedContractEvent<
      UniswapErrorEvent.InputTuple,
      UniswapErrorEvent.OutputTuple,
      UniswapErrorEvent.OutputObject
    >

    'UniswapPanic(uint256)': TypedContractEvent<
      UniswapPanicEvent.InputTuple,
      UniswapPanicEvent.OutputTuple,
      UniswapPanicEvent.OutputObject
    >
    UniswapPanic: TypedContractEvent<
      UniswapPanicEvent.InputTuple,
      UniswapPanicEvent.OutputTuple,
      UniswapPanicEvent.OutputObject
    >

    'UniswapRevert(bytes)': TypedContractEvent<
      UniswapRevertEvent.InputTuple,
      UniswapRevertEvent.OutputTuple,
      UniswapRevertEvent.OutputObject
    >
    UniswapRevert: TypedContractEvent<
      UniswapRevertEvent.InputTuple,
      UniswapRevertEvent.OutputTuple,
      UniswapRevertEvent.OutputObject
    >

    'UniswapSuccess(uint24,address,address)': TypedContractEvent<
      UniswapSuccessEvent.InputTuple,
      UniswapSuccessEvent.OutputTuple,
      UniswapSuccessEvent.OutputObject
    >
    UniswapSuccess: TypedContractEvent<
      UniswapSuccessEvent.InputTuple,
      UniswapSuccessEvent.OutputTuple,
      UniswapSuccessEvent.OutputObject
    >

    'Withdrawal(address,uint256,uint256,address)': TypedContractEvent<
      WithdrawalEvent.InputTuple,
      WithdrawalEvent.OutputTuple,
      WithdrawalEvent.OutputObject
    >
    Withdrawal: TypedContractEvent<
      WithdrawalEvent.InputTuple,
      WithdrawalEvent.OutputTuple,
      WithdrawalEvent.OutputObject
    >
  }
}
