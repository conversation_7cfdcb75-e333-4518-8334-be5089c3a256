/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseDeployFactory_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addOwner'
      | 'clone'
      | 'computeCloneAddress'
      | 'computeDeployAddress'
      | 'deploy'
      | 'getOwners'
      | 'getOwnersLength'
      | 'isOwner'
      | 'removeOwner'
      | 'riseRouter'
      | 'setRouter',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'NewInstance'
      | 'RiseOwnerAdded'
      | 'RiseOwnerRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'clone',
    values: [AddressLike, BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'computeCloneAddress',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'computeDeployAddress',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'deploy',
    values: [BytesLike, BytesLike, BytesLike],
  ): string
  encodeFunctionData(functionFragment: 'getOwners', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getOwnersLength',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'isOwner', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'removeOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'addOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'clone', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'computeCloneAddress',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'computeDeployAddress',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'deploy', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getOwners', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getOwnersLength',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'isOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'removeOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
}

export namespace NewInstanceEvent {
  export type InputTuple = [instance: AddressLike]
  export type OutputTuple = [instance: string]
  export interface OutputObject {
    instance: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerAddedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerRemovedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseDeployFactory_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseDeployFactory_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseDeployFactory_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  clone: TypedContractMethod<
    [implementation: AddressLike, salt: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >

  computeCloneAddress: TypedContractMethod<
    [implementation: AddressLike, salt: BytesLike],
    [string],
    'view'
  >

  computeDeployAddress: TypedContractMethod<
    [salt: BytesLike, bytecodeHash: BytesLike],
    [string],
    'view'
  >

  deploy: TypedContractMethod<
    [salt: BytesLike, bytecode: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >

  getOwners: TypedContractMethod<[], [string[]], 'view'>

  getOwnersLength: TypedContractMethod<[], [bigint], 'view'>

  isOwner: TypedContractMethod<[account: AddressLike], [boolean], 'view'>

  removeOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'clone',
  ): TypedContractMethod<
    [implementation: AddressLike, salt: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'computeCloneAddress',
  ): TypedContractMethod<
    [implementation: AddressLike, salt: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'computeDeployAddress',
  ): TypedContractMethod<
    [salt: BytesLike, bytecodeHash: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'deploy',
  ): TypedContractMethod<
    [salt: BytesLike, bytecode: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getOwners',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getOwnersLength',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'isOwner',
  ): TypedContractMethod<[account: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'removeOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'NewInstance',
  ): TypedContractEvent<
    NewInstanceEvent.InputTuple,
    NewInstanceEvent.OutputTuple,
    NewInstanceEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerAdded',
  ): TypedContractEvent<
    RiseOwnerAddedEvent.InputTuple,
    RiseOwnerAddedEvent.OutputTuple,
    RiseOwnerAddedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerRemoved',
  ): TypedContractEvent<
    RiseOwnerRemovedEvent.InputTuple,
    RiseOwnerRemovedEvent.OutputTuple,
    RiseOwnerRemovedEvent.OutputObject
  >

  filters: {
    'NewInstance(address)': TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >
    NewInstance: TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >

    'RiseOwnerAdded(address)': TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >
    RiseOwnerAdded: TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >

    'RiseOwnerRemoved(address)': TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
    RiseOwnerRemoved: TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
  }
}
