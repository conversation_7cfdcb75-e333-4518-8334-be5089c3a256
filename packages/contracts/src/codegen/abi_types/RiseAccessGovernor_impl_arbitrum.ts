/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseAccessGovernor {
  export type RoleStruct = {
    role: BytesLike
    name: string
    adminRole: BytesLike
    adminName: string
  }

  export type RoleStructOutput = [
    role: string,
    name: string,
    adminRole: string,
    adminName: string,
  ] & { role: string; name: string; adminRole: string; adminName: string }
}

export interface RiseAccessGovernor_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'batchRoleGrantOrRevoke'
      | 'getAllRoles'
      | 'getAllRolesCount'
      | 'init'
      | 'initializeRoleNames'
      | 'multiRoleGrantOrRevoke'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setRoleAdmin'
      | 'setRoleNames'
      | 'setRouter'
      | 'syncRoleHashes',
  ): FunctionFragment

  getEvent(nameOrSignatureOrTopic: 'Initialized'): EventFragment

  encodeFunctionData(
    functionFragment: 'batchRoleGrantOrRevoke',
    values: [string, boolean, AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'getAllRoles',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getAllRolesCount',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'initializeRoleNames',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'multiRoleGrantOrRevoke',
    values: [AddressLike, boolean, BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRoleAdmin',
    values: [string, string],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoleNames',
    values: [string[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'syncRoleHashes',
    values?: undefined,
  ): string

  decodeFunctionResult(
    functionFragment: 'batchRoleGrantOrRevoke',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getAllRoles', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getAllRolesCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'initializeRoleNames',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'multiRoleGrantOrRevoke',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setRoleAdmin',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setRoleNames',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'syncRoleHashes',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccessGovernor_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccessGovernor_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccessGovernor_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  batchRoleGrantOrRevoke: TypedContractMethod<
    [name: string, grantRevoke: boolean, members: AddressLike[]],
    [void],
    'nonpayable'
  >

  getAllRoles: TypedContractMethod<
    [],
    [RiseAccessGovernor.RoleStructOutput[]],
    'view'
  >

  getAllRolesCount: TypedContractMethod<[], [bigint], 'view'>

  init: TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>

  initializeRoleNames: TypedContractMethod<[], [void], 'nonpayable'>

  multiRoleGrantOrRevoke: TypedContractMethod<
    [member: AddressLike, grantRevoke: boolean, roles: BytesLike[]],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRoleAdmin: TypedContractMethod<
    [roleName: string, adminRoleName: string],
    [void],
    'nonpayable'
  >

  setRoleNames: TypedContractMethod<[name: string[]], [void], 'nonpayable'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  syncRoleHashes: TypedContractMethod<[], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'batchRoleGrantOrRevoke',
  ): TypedContractMethod<
    [name: string, grantRevoke: boolean, members: AddressLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getAllRoles',
  ): TypedContractMethod<[], [RiseAccessGovernor.RoleStructOutput[]], 'view'>
  getFunction(
    nameOrSignature: 'getAllRolesCount',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'initializeRoleNames',
  ): TypedContractMethod<[], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'multiRoleGrantOrRevoke',
  ): TypedContractMethod<
    [member: AddressLike, grantRevoke: boolean, roles: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRoleAdmin',
  ): TypedContractMethod<
    [roleName: string, adminRoleName: string],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRoleNames',
  ): TypedContractMethod<[name: string[]], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'syncRoleHashes',
  ): TypedContractMethod<[], [void], 'nonpayable'>

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
