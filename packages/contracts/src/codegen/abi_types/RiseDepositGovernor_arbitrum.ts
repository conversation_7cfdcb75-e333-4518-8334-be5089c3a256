/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseGovernors {
  export type DepositStruct = {
    id: BytesLike
    amount: BigNumberish
    token: AddressLike
    recipient: AddressLike
    references: BytesLike
  }

  export type DepositStructOutput = [
    id: string,
    amount: bigint,
    token: string,
    recipient: string,
    references: string,
  ] & {
    id: string
    amount: bigint
    token: string
    recipient: string
    references: string
  }
}

export interface RiseDepositGovernor_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'depositStatuses'
      | 'deposits'
      | 'init'
      | 'processDeposits'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setRouter',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'DepositProcessed' | 'Initialized',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'depositStatuses',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'deposits',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'processDeposits',
    values: [RiseGovernors.DepositStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'depositStatuses',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'deposits', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'processDeposits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
}

export namespace DepositProcessedEvent {
  export type InputTuple = [
    id: BytesLike,
    newStatus: BigNumberish,
    oldStatus: BigNumberish,
    references: BytesLike,
  ]
  export type OutputTuple = [
    id: string,
    newStatus: bigint,
    oldStatus: bigint,
    references: string,
  ]
  export interface OutputObject {
    id: string
    newStatus: bigint
    oldStatus: bigint
    references: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseDepositGovernor_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseDepositGovernor_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseDepositGovernor_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  depositStatuses: TypedContractMethod<[ids: BytesLike[]], [bigint[]], 'view'>

  deposits: TypedContractMethod<
    [ids: BytesLike[]],
    [RiseGovernors.DepositStructOutput[]],
    'view'
  >

  init: TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>

  processDeposits: TypedContractMethod<
    [_deposits: RiseGovernors.DepositStruct[]],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'depositStatuses',
  ): TypedContractMethod<[ids: BytesLike[]], [bigint[]], 'view'>
  getFunction(
    nameOrSignature: 'deposits',
  ): TypedContractMethod<
    [ids: BytesLike[]],
    [RiseGovernors.DepositStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'processDeposits',
  ): TypedContractMethod<
    [_deposits: RiseGovernors.DepositStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'DepositProcessed',
  ): TypedContractEvent<
    DepositProcessedEvent.InputTuple,
    DepositProcessedEvent.OutputTuple,
    DepositProcessedEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'DepositProcessed(bytes32,uint8,uint8,bytes32)': TypedContractEvent<
      DepositProcessedEvent.InputTuple,
      DepositProcessedEvent.OutputTuple,
      DepositProcessedEvent.OutputObject
    >
    DepositProcessed: TypedContractEvent<
      DepositProcessedEvent.InputTuple,
      DepositProcessedEvent.OutputTuple,
      DepositProcessedEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
