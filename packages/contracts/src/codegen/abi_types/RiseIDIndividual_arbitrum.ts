/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseIDIndividual_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addDelegate(uint256)'
      | 'addDelegate(address)'
      | 'callRise'
      | 'execute(uint256,uint256,uint256,bytes)'
      | 'execute(uint256,address,uint256,bytes)'
      | 'executeRise'
      | 'executeRiseFund(uint256,uint256,uint256,uint256)'
      | 'executeRiseFund(uint256,uint256,uint256)'
      | 'getCertifiedData'
      | 'getData'
      | 'getDelegates'
      | 'getDelegatesLength'
      | 'init'
      | 'initData'
      | 'isDelegate(address)'
      | 'isDelegate(uint256)'
      | 'owner'
      | 'recover'
      | 'removeDelegate(address)'
      | 'removeDelegate(uint256)'
      | 'renounceOwnership'
      | 'riseAccess'
      | 'role'
      | 'setCertifiedData'
      | 'setData'
      | 'supportsInterface'
      | 'transferOwnership'
      | 'updateRiseContracts',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'CertifiedDataChanged'
      | 'ContractCreated'
      | 'DataChanged'
      | 'Executed'
      | 'OwnershipTransferred'
      | 'RiseIDDelegateAdded'
      | 'RiseIDDelegateRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addDelegate(uint256)',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'addDelegate(address)',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'callRise', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'execute(uint256,uint256,uint256,bytes)',
    values: [BigNumberish, BigNumberish, BigNumberish, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'execute(uint256,address,uint256,bytes)',
    values: [BigNumberish, AddressLike, BigNumberish, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'executeRise',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'executeRiseFund(uint256,uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'executeRiseFund(uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getCertifiedData',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'getData', values: [BytesLike[]]): string
  encodeFunctionData(
    functionFragment: 'getDelegates',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getDelegatesLength',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'initData',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'isDelegate(address)',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'isDelegate(uint256)',
    values: [BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string
  encodeFunctionData(functionFragment: 'recover', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'removeDelegate(address)',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'removeDelegate(uint256)',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'renounceOwnership',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'role', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setCertifiedData',
    values: [BytesLike[], BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setData',
    values: [BytesLike[], BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'transferOwnership',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'updateRiseContracts',
    values: [BigNumberish, BigNumberish],
  ): string

  decodeFunctionResult(
    functionFragment: 'addDelegate(uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'addDelegate(address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'callRise', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'execute(uint256,uint256,uint256,bytes)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'execute(uint256,address,uint256,bytes)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'executeRise', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'executeRiseFund(uint256,uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executeRiseFund(uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getCertifiedData',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getDelegates',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getDelegatesLength',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'initData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isDelegate(address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isDelegate(uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'recover', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'removeDelegate(address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removeDelegate(uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'renounceOwnership',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'role', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setCertifiedData',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transferOwnership',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'updateRiseContracts',
    data: BytesLike,
  ): Result
}

export namespace CertifiedDataChangedEvent {
  export type InputTuple = [key: BytesLike, value: BytesLike]
  export type OutputTuple = [key: string, value: string]
  export interface OutputObject {
    key: string
    value: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ContractCreatedEvent {
  export type InputTuple = [
    operation: BigNumberish,
    contractAddress: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [
    operation: bigint,
    contractAddress: string,
    value: bigint,
  ]
  export interface OutputObject {
    operation: bigint
    contractAddress: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace DataChangedEvent {
  export type InputTuple = [key: BytesLike, value: BytesLike]
  export type OutputTuple = [key: string, value: string]
  export interface OutputObject {
    key: string
    value: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ExecutedEvent {
  export type InputTuple = [
    operation: BigNumberish,
    to: AddressLike,
    value: BigNumberish,
    data: BytesLike,
  ]
  export type OutputTuple = [
    operation: bigint,
    to: string,
    value: bigint,
    data: string,
  ]
  export interface OutputObject {
    operation: bigint
    to: string
    value: bigint
    data: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike]
  export type OutputTuple = [previousOwner: string, newOwner: string]
  export interface OutputObject {
    previousOwner: string
    newOwner: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseIDDelegateAddedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseIDDelegateRemovedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseIDIndividual_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseIDIndividual_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseIDIndividual_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  'addDelegate(uint256)': TypedContractMethod<
    [accountIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  'addDelegate(address)': TypedContractMethod<
    [account: AddressLike],
    [void],
    'nonpayable'
  >

  callRise: TypedContractMethod<[_data: BytesLike], [string], 'nonpayable'>

  'execute(uint256,uint256,uint256,bytes)': TypedContractMethod<
    [
      _operation: BigNumberish,
      _to: BigNumberish,
      _value: BigNumberish,
      _data: BytesLike,
    ],
    [string],
    'payable'
  >

  'execute(uint256,address,uint256,bytes)': TypedContractMethod<
    [
      _operation: BigNumberish,
      _to: AddressLike,
      _value: BigNumberish,
      _data: BytesLike,
    ],
    [string],
    'payable'
  >

  executeRise: TypedContractMethod<[_data: BytesLike], [string], 'nonpayable'>

  'executeRiseFund(uint256,uint256,uint256,uint256)': TypedContractMethod<
    [
      tokenIdx: BigNumberish,
      rampIdx: BigNumberish,
      amount: BigNumberish,
      _fromIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  'executeRiseFund(uint256,uint256,uint256)': TypedContractMethod<
    [tokenIdx: BigNumberish, rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  getCertifiedData: TypedContractMethod<[keys: BytesLike[]], [string[]], 'view'>

  getData: TypedContractMethod<[keys: BytesLike[]], [string[]], 'view'>

  getDelegates: TypedContractMethod<[], [string[]], 'view'>

  getDelegatesLength: TypedContractMethod<[], [bigint], 'view'>

  init: TypedContractMethod<
    [
      _newOwner: AddressLike,
      _riseAccess: AddressLike,
      _risePayContract: AddressLike,
    ],
    [void],
    'nonpayable'
  >

  initData: TypedContractMethod<[_newOwner: AddressLike], [string], 'view'>

  'isDelegate(address)': TypedContractMethod<
    [account: AddressLike],
    [boolean],
    'view'
  >

  'isDelegate(uint256)': TypedContractMethod<
    [accountIdx: BigNumberish],
    [boolean],
    'view'
  >

  owner: TypedContractMethod<[], [string], 'view'>

  recover: TypedContractMethod<
    [ownerAddress: AddressLike],
    [void],
    'nonpayable'
  >

  'removeDelegate(address)': TypedContractMethod<
    [account: AddressLike],
    [void],
    'nonpayable'
  >

  'removeDelegate(uint256)': TypedContractMethod<
    [accountIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  renounceOwnership: TypedContractMethod<[], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  role: TypedContractMethod<[], [string], 'view'>

  setCertifiedData: TypedContractMethod<
    [_keys: BytesLike[], _values: BytesLike[]],
    [void],
    'nonpayable'
  >

  setData: TypedContractMethod<
    [_keys: BytesLike[], _values: BytesLike[]],
    [void],
    'nonpayable'
  >

  supportsInterface: TypedContractMethod<
    [interfaceId: BytesLike],
    [boolean],
    'view'
  >

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    'nonpayable'
  >

  updateRiseContracts: TypedContractMethod<
    [riseAccessContractIdx: BigNumberish, risePayContractIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addDelegate(uint256)',
  ): TypedContractMethod<[accountIdx: BigNumberish], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'addDelegate(address)',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'callRise',
  ): TypedContractMethod<[_data: BytesLike], [string], 'nonpayable'>
  getFunction(
    nameOrSignature: 'execute(uint256,uint256,uint256,bytes)',
  ): TypedContractMethod<
    [
      _operation: BigNumberish,
      _to: BigNumberish,
      _value: BigNumberish,
      _data: BytesLike,
    ],
    [string],
    'payable'
  >
  getFunction(
    nameOrSignature: 'execute(uint256,address,uint256,bytes)',
  ): TypedContractMethod<
    [
      _operation: BigNumberish,
      _to: AddressLike,
      _value: BigNumberish,
      _data: BytesLike,
    ],
    [string],
    'payable'
  >
  getFunction(
    nameOrSignature: 'executeRise',
  ): TypedContractMethod<[_data: BytesLike], [string], 'nonpayable'>
  getFunction(
    nameOrSignature: 'executeRiseFund(uint256,uint256,uint256,uint256)',
  ): TypedContractMethod<
    [
      tokenIdx: BigNumberish,
      rampIdx: BigNumberish,
      amount: BigNumberish,
      _fromIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'executeRiseFund(uint256,uint256,uint256)',
  ): TypedContractMethod<
    [tokenIdx: BigNumberish, rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getCertifiedData',
  ): TypedContractMethod<[keys: BytesLike[]], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getData',
  ): TypedContractMethod<[keys: BytesLike[]], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getDelegates',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getDelegatesLength',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [
      _newOwner: AddressLike,
      _riseAccess: AddressLike,
      _risePayContract: AddressLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'initData',
  ): TypedContractMethod<[_newOwner: AddressLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'isDelegate(address)',
  ): TypedContractMethod<[account: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'isDelegate(uint256)',
  ): TypedContractMethod<[accountIdx: BigNumberish], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'owner',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'recover',
  ): TypedContractMethod<[ownerAddress: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removeDelegate(address)',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removeDelegate(uint256)',
  ): TypedContractMethod<[accountIdx: BigNumberish], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'renounceOwnership',
  ): TypedContractMethod<[], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'role',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setCertifiedData',
  ): TypedContractMethod<
    [_keys: BytesLike[], _values: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setData',
  ): TypedContractMethod<
    [_keys: BytesLike[], _values: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'supportsInterface',
  ): TypedContractMethod<[interfaceId: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'transferOwnership',
  ): TypedContractMethod<[newOwner: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'updateRiseContracts',
  ): TypedContractMethod<
    [riseAccessContractIdx: BigNumberish, risePayContractIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'CertifiedDataChanged',
  ): TypedContractEvent<
    CertifiedDataChangedEvent.InputTuple,
    CertifiedDataChangedEvent.OutputTuple,
    CertifiedDataChangedEvent.OutputObject
  >
  getEvent(
    key: 'ContractCreated',
  ): TypedContractEvent<
    ContractCreatedEvent.InputTuple,
    ContractCreatedEvent.OutputTuple,
    ContractCreatedEvent.OutputObject
  >
  getEvent(
    key: 'DataChanged',
  ): TypedContractEvent<
    DataChangedEvent.InputTuple,
    DataChangedEvent.OutputTuple,
    DataChangedEvent.OutputObject
  >
  getEvent(
    key: 'Executed',
  ): TypedContractEvent<
    ExecutedEvent.InputTuple,
    ExecutedEvent.OutputTuple,
    ExecutedEvent.OutputObject
  >
  getEvent(
    key: 'OwnershipTransferred',
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >
  getEvent(
    key: 'RiseIDDelegateAdded',
  ): TypedContractEvent<
    RiseIDDelegateAddedEvent.InputTuple,
    RiseIDDelegateAddedEvent.OutputTuple,
    RiseIDDelegateAddedEvent.OutputObject
  >
  getEvent(
    key: 'RiseIDDelegateRemoved',
  ): TypedContractEvent<
    RiseIDDelegateRemovedEvent.InputTuple,
    RiseIDDelegateRemovedEvent.OutputTuple,
    RiseIDDelegateRemovedEvent.OutputObject
  >

  filters: {
    'CertifiedDataChanged(bytes32,bytes)': TypedContractEvent<
      CertifiedDataChangedEvent.InputTuple,
      CertifiedDataChangedEvent.OutputTuple,
      CertifiedDataChangedEvent.OutputObject
    >
    CertifiedDataChanged: TypedContractEvent<
      CertifiedDataChangedEvent.InputTuple,
      CertifiedDataChangedEvent.OutputTuple,
      CertifiedDataChangedEvent.OutputObject
    >

    'ContractCreated(uint256,address,uint256)': TypedContractEvent<
      ContractCreatedEvent.InputTuple,
      ContractCreatedEvent.OutputTuple,
      ContractCreatedEvent.OutputObject
    >
    ContractCreated: TypedContractEvent<
      ContractCreatedEvent.InputTuple,
      ContractCreatedEvent.OutputTuple,
      ContractCreatedEvent.OutputObject
    >

    'DataChanged(bytes32,bytes)': TypedContractEvent<
      DataChangedEvent.InputTuple,
      DataChangedEvent.OutputTuple,
      DataChangedEvent.OutputObject
    >
    DataChanged: TypedContractEvent<
      DataChangedEvent.InputTuple,
      DataChangedEvent.OutputTuple,
      DataChangedEvent.OutputObject
    >

    'Executed(uint256,address,uint256,bytes)': TypedContractEvent<
      ExecutedEvent.InputTuple,
      ExecutedEvent.OutputTuple,
      ExecutedEvent.OutputObject
    >
    Executed: TypedContractEvent<
      ExecutedEvent.InputTuple,
      ExecutedEvent.OutputTuple,
      ExecutedEvent.OutputObject
    >

    'OwnershipTransferred(address,address)': TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >

    'RiseIDDelegateAdded(address)': TypedContractEvent<
      RiseIDDelegateAddedEvent.InputTuple,
      RiseIDDelegateAddedEvent.OutputTuple,
      RiseIDDelegateAddedEvent.OutputObject
    >
    RiseIDDelegateAdded: TypedContractEvent<
      RiseIDDelegateAddedEvent.InputTuple,
      RiseIDDelegateAddedEvent.OutputTuple,
      RiseIDDelegateAddedEvent.OutputObject
    >

    'RiseIDDelegateRemoved(address)': TypedContractEvent<
      RiseIDDelegateRemovedEvent.InputTuple,
      RiseIDDelegateRemovedEvent.OutputTuple,
      RiseIDDelegateRemovedEvent.OutputObject
    >
    RiseIDDelegateRemoved: TypedContractEvent<
      RiseIDDelegateRemovedEvent.InputTuple,
      RiseIDDelegateRemovedEvent.OutputTuple,
      RiseIDDelegateRemovedEvent.OutputObject
    >
  }
}
