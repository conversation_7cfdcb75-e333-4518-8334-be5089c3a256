/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  Contract<PERSON>eth<PERSON>,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RisePaymentIdentifiers_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addressIdentifier'
      | 'dataIdentifier'
      | 'groupIdentifier'
      | 'hashDayIdentifier'
      | 'hashSubscriptionID'
      | 'hashSubscriptionMonth'
      | 'pendingIdentifier',
  ): FunctionFragment

  encodeFunctionData(
    functionFragment: 'addressIdentifier',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'dataIdentifier',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'groupIdentifier',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'hashDayIdentifier',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'hashSubscriptionID',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'hashSubscriptionMonth',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'pendingIdentifier',
    values: [BytesLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'addressIdentifier',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'dataIdentifier',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'groupIdentifier',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'hashDayIdentifier',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'hashSubscriptionID',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'hashSubscriptionMonth',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingIdentifier',
    data: BytesLike,
  ): Result
}

export interface RisePaymentIdentifiers_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePaymentIdentifiers_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePaymentIdentifiers_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addressIdentifier: TypedContractMethod<[addr: AddressLike], [string], 'view'>

  dataIdentifier: TypedContractMethod<[data: BytesLike], [string], 'view'>

  groupIdentifier: TypedContractMethod<[groupID: BytesLike], [string], 'view'>

  hashDayIdentifier: TypedContractMethod<
    [_hash: BytesLike, _identifier: BytesLike],
    [string],
    'view'
  >

  hashSubscriptionID: TypedContractMethod<
    [monthHash: BytesLike, recipient: AddressLike],
    [string],
    'view'
  >

  hashSubscriptionMonth: TypedContractMethod<
    [monthHash: BytesLike],
    [string],
    'view'
  >

  pendingIdentifier: TypedContractMethod<
    [baseHash: BytesLike],
    [string],
    'view'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addressIdentifier',
  ): TypedContractMethod<[addr: AddressLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'dataIdentifier',
  ): TypedContractMethod<[data: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'groupIdentifier',
  ): TypedContractMethod<[groupID: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'hashDayIdentifier',
  ): TypedContractMethod<
    [_hash: BytesLike, _identifier: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'hashSubscriptionID',
  ): TypedContractMethod<
    [monthHash: BytesLike, recipient: AddressLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'hashSubscriptionMonth',
  ): TypedContractMethod<[monthHash: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'pendingIdentifier',
  ): TypedContractMethod<[baseHash: BytesLike], [string], 'view'>

  filters: {}
}
