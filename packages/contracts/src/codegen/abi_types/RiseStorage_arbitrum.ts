/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace IRiseStorageTypes {
  export type PayScheduleStruct = {
    payer: AddressLike
    payee: AddressLike
    amount: BigNumberish
    salt: BigNumberish
    startTime: BigNumberish
    interval: BigNumberish
    total: BigNumberish
    count: BigNumberish
    enabled: boolean
  }

  export type PayScheduleStructOutput = [
    payer: string,
    payee: string,
    amount: bigint,
    salt: bigint,
    startTime: bigint,
    interval: bigint,
    total: bigint,
    count: bigint,
    enabled: boolean,
  ] & {
    payer: string
    payee: string
    amount: bigint
    salt: bigint
    startTime: bigint
    interval: bigint
    total: bigint
    count: bigint
    enabled: boolean
  }

  export type PayStruct = {
    extId: BigNumberish
    amount: BigNumberish
    date: BigNumberish
  }

  export type PayStructOutput = [
    extId: bigint,
    amount: bigint,
    date: bigint,
  ] & { extId: bigint; amount: bigint; date: bigint }
}

export interface RiseStorage_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'initialize'
      | 'paySchedules_PaySchedule_Get'
      | 'paySchedules_PaySchedule_Set'
      | 'paySchedules_PaySchedules'
      | 'paySchedules_RelationshipPayScheduleSet_Add'
      | 'paySchedules_RelationshipPayScheduleSet_All'
      | 'paySchedules_RelationshipPayScheduleSet_At'
      | 'paySchedules_RelationshipPayScheduleSet_Contains'
      | 'paySchedules_RelationshipPayScheduleSet_Len'
      | 'paySchedules_RelationshipPayScheduleSet_Remove'
      | 'paySchedules_UserPayerPayee_Add'
      | 'paySchedules_UserPayerPayee_All'
      | 'paySchedules_UserPayerPayee_At'
      | 'paySchedules_UserPayerPayee_Contains'
      | 'paySchedules_UserPayerPayee_Len'
      | 'paySchedules_UserPayerPayee_Remove'
      | 'plannedPayments_Payment_Get'
      | 'plannedPayments_Payment_Set'
      | 'plannedPayments_Payments'
      | 'plannedPayments_RelationshipPaySet_Add'
      | 'plannedPayments_RelationshipPaySet_All'
      | 'plannedPayments_RelationshipPaySet_At'
      | 'plannedPayments_RelationshipPaySet_Contains'
      | 'plannedPayments_RelationshipPaySet_Len'
      | 'plannedPayments_RelationshipPaySet_Remove'
      | 'plannedPayments_UserPayerPayee_Add'
      | 'plannedPayments_UserPayerPayee_All'
      | 'plannedPayments_UserPayerPayee_At'
      | 'plannedPayments_UserPayerPayee_Contains'
      | 'plannedPayments_UserPayerPayee_Len'
      | 'plannedPayments_UserPayerPayee_Remove'
      | 'proxiableUUID'
      | 'riseAccess'
      | 'upgradeTo'
      | 'upgradeToAndCall',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'AdminChanged' | 'BeaconUpgraded' | 'Upgraded',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'initialize',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_PaySchedule_Get',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_PaySchedule_Set',
    values: [BytesLike, IRiseStorageTypes.PayScheduleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_PaySchedules',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Add',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_All',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_At',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Contains',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Len',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Remove',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_Add',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_All',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_At',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_Contains',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_Len',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paySchedules_UserPayerPayee_Remove',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_Payment_Get',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_Payment_Set',
    values: [BytesLike, IRiseStorageTypes.PayStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_Payments',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_Add',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_All',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_At',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_Contains',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_Len',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_RelationshipPaySet_Remove',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_Add',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_All',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_At',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_Contains',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_Len',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'plannedPayments_UserPayerPayee_Remove',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'proxiableUUID',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'upgradeTo',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeToAndCall',
    values: [AddressLike, BytesLike],
  ): string

  decodeFunctionResult(functionFragment: 'initialize', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_PaySchedule_Get',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_PaySchedule_Set',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_PaySchedules',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Add',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_All',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_At',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Contains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Len',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_RelationshipPayScheduleSet_Remove',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_Add',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_All',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_At',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_Contains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_Len',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paySchedules_UserPayerPayee_Remove',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_Payment_Get',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_Payment_Set',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_Payments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_Add',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_All',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_At',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_Contains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_Len',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_RelationshipPaySet_Remove',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_Add',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_All',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_At',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_Contains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_Len',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'plannedPayments_UserPayerPayee_Remove',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'proxiableUUID',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'upgradeTo', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'upgradeToAndCall',
    data: BytesLike,
  ): Result
}

export namespace AdminChangedEvent {
  export type InputTuple = [previousAdmin: AddressLike, newAdmin: AddressLike]
  export type OutputTuple = [previousAdmin: string, newAdmin: string]
  export interface OutputObject {
    previousAdmin: string
    newAdmin: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace BeaconUpgradedEvent {
  export type InputTuple = [beacon: AddressLike]
  export type OutputTuple = [beacon: string]
  export interface OutputObject {
    beacon: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace UpgradedEvent {
  export type InputTuple = [implementation: AddressLike]
  export type OutputTuple = [implementation: string]
  export interface OutputObject {
    implementation: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseStorage_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseStorage_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseStorage_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  initialize: TypedContractMethod<
    [_riseAccess: AddressLike],
    [void],
    'nonpayable'
  >

  paySchedules_PaySchedule_Get: TypedContractMethod<
    [psHash: BytesLike],
    [IRiseStorageTypes.PayScheduleStructOutput],
    'view'
  >

  paySchedules_PaySchedule_Set: TypedContractMethod<
    [psHash: BytesLike, ps: IRiseStorageTypes.PayScheduleStruct],
    [void],
    'nonpayable'
  >

  paySchedules_PaySchedules: TypedContractMethod<
    [arg0: BytesLike],
    [
      [
        string,
        string,
        bigint,
        bigint,
        bigint,
        bigint,
        bigint,
        bigint,
        boolean,
      ] & {
        payer: string
        payee: string
        amount: bigint
        salt: bigint
        startTime: bigint
        interval: bigint
        total: bigint
        count: bigint
        enabled: boolean
      },
    ],
    'view'
  >

  paySchedules_RelationshipPayScheduleSet_Add: TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  paySchedules_RelationshipPayScheduleSet_All: TypedContractMethod<
    [relationshipHash: BytesLike],
    [string[]],
    'view'
  >

  paySchedules_RelationshipPayScheduleSet_At: TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [string],
    'view'
  >

  paySchedules_RelationshipPayScheduleSet_Contains: TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'view'
  >

  paySchedules_RelationshipPayScheduleSet_Len: TypedContractMethod<
    [relationshipHash: BytesLike],
    [bigint],
    'view'
  >

  paySchedules_RelationshipPayScheduleSet_Remove: TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  paySchedules_UserPayerPayee_Add: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  paySchedules_UserPayerPayee_All: TypedContractMethod<
    [user: AddressLike],
    [string[]],
    'view'
  >

  paySchedules_UserPayerPayee_At: TypedContractMethod<
    [user: AddressLike, idx: BigNumberish],
    [string],
    'view'
  >

  paySchedules_UserPayerPayee_Contains: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'view'
  >

  paySchedules_UserPayerPayee_Len: TypedContractMethod<
    [user: AddressLike],
    [bigint],
    'view'
  >

  paySchedules_UserPayerPayee_Remove: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  plannedPayments_Payment_Get: TypedContractMethod<
    [payHash: BytesLike],
    [IRiseStorageTypes.PayStructOutput],
    'view'
  >

  plannedPayments_Payment_Set: TypedContractMethod<
    [payHash: BytesLike, pay: IRiseStorageTypes.PayStruct],
    [void],
    'nonpayable'
  >

  plannedPayments_Payments: TypedContractMethod<
    [arg0: BytesLike],
    [
      [bigint, bigint, bigint] & {
        extId: bigint
        amount: bigint
        date: bigint
      },
    ],
    'view'
  >

  plannedPayments_RelationshipPaySet_Add: TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  plannedPayments_RelationshipPaySet_All: TypedContractMethod<
    [relationshipHash: BytesLike],
    [string[]],
    'view'
  >

  plannedPayments_RelationshipPaySet_At: TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [string],
    'view'
  >

  plannedPayments_RelationshipPaySet_Contains: TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'view'
  >

  plannedPayments_RelationshipPaySet_Len: TypedContractMethod<
    [relationshipHash: BytesLike],
    [bigint],
    'view'
  >

  plannedPayments_RelationshipPaySet_Remove: TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  plannedPayments_UserPayerPayee_Add: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  plannedPayments_UserPayerPayee_All: TypedContractMethod<
    [user: AddressLike],
    [string[]],
    'view'
  >

  plannedPayments_UserPayerPayee_At: TypedContractMethod<
    [user: AddressLike, idx: BigNumberish],
    [string],
    'view'
  >

  plannedPayments_UserPayerPayee_Contains: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'view'
  >

  plannedPayments_UserPayerPayee_Len: TypedContractMethod<
    [user: AddressLike],
    [bigint],
    'view'
  >

  plannedPayments_UserPayerPayee_Remove: TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >

  proxiableUUID: TypedContractMethod<[], [string], 'view'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  upgradeTo: TypedContractMethod<
    [newImplementation: AddressLike],
    [void],
    'nonpayable'
  >

  upgradeToAndCall: TypedContractMethod<
    [newImplementation: AddressLike, data: BytesLike],
    [void],
    'payable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'initialize',
  ): TypedContractMethod<[_riseAccess: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'paySchedules_PaySchedule_Get',
  ): TypedContractMethod<
    [psHash: BytesLike],
    [IRiseStorageTypes.PayScheduleStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_PaySchedule_Set',
  ): TypedContractMethod<
    [psHash: BytesLike, ps: IRiseStorageTypes.PayScheduleStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'paySchedules_PaySchedules',
  ): TypedContractMethod<
    [arg0: BytesLike],
    [
      [
        string,
        string,
        bigint,
        bigint,
        bigint,
        bigint,
        bigint,
        bigint,
        boolean,
      ] & {
        payer: string
        payee: string
        amount: bigint
        salt: bigint
        startTime: bigint
        interval: bigint
        total: bigint
        count: bigint
        enabled: boolean
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_Add',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_All',
  ): TypedContractMethod<[relationshipHash: BytesLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_At',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_Contains',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_Len',
  ): TypedContractMethod<[relationshipHash: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'paySchedules_RelationshipPayScheduleSet_Remove',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, psHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_Add',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_All',
  ): TypedContractMethod<[user: AddressLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_At',
  ): TypedContractMethod<
    [user: AddressLike, idx: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_Contains',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_Len',
  ): TypedContractMethod<[user: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'paySchedules_UserPayerPayee_Remove',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_Payment_Get',
  ): TypedContractMethod<
    [payHash: BytesLike],
    [IRiseStorageTypes.PayStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_Payment_Set',
  ): TypedContractMethod<
    [payHash: BytesLike, pay: IRiseStorageTypes.PayStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_Payments',
  ): TypedContractMethod<
    [arg0: BytesLike],
    [
      [bigint, bigint, bigint] & {
        extId: bigint
        amount: bigint
        date: bigint
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_Add',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_All',
  ): TypedContractMethod<[relationshipHash: BytesLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_At',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_Contains',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_Len',
  ): TypedContractMethod<[relationshipHash: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'plannedPayments_RelationshipPaySet_Remove',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, payHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_Add',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_All',
  ): TypedContractMethod<[user: AddressLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_At',
  ): TypedContractMethod<
    [user: AddressLike, idx: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_Contains',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_Len',
  ): TypedContractMethod<[user: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'plannedPayments_UserPayerPayee_Remove',
  ): TypedContractMethod<
    [user: AddressLike, relationshipHash: BytesLike],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'proxiableUUID',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'upgradeTo',
  ): TypedContractMethod<[newImplementation: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'upgradeToAndCall',
  ): TypedContractMethod<
    [newImplementation: AddressLike, data: BytesLike],
    [void],
    'payable'
  >

  getEvent(
    key: 'AdminChanged',
  ): TypedContractEvent<
    AdminChangedEvent.InputTuple,
    AdminChangedEvent.OutputTuple,
    AdminChangedEvent.OutputObject
  >
  getEvent(
    key: 'BeaconUpgraded',
  ): TypedContractEvent<
    BeaconUpgradedEvent.InputTuple,
    BeaconUpgradedEvent.OutputTuple,
    BeaconUpgradedEvent.OutputObject
  >
  getEvent(
    key: 'Upgraded',
  ): TypedContractEvent<
    UpgradedEvent.InputTuple,
    UpgradedEvent.OutputTuple,
    UpgradedEvent.OutputObject
  >

  filters: {
    'AdminChanged(address,address)': TypedContractEvent<
      AdminChangedEvent.InputTuple,
      AdminChangedEvent.OutputTuple,
      AdminChangedEvent.OutputObject
    >
    AdminChanged: TypedContractEvent<
      AdminChangedEvent.InputTuple,
      AdminChangedEvent.OutputTuple,
      AdminChangedEvent.OutputObject
    >

    'BeaconUpgraded(address)': TypedContractEvent<
      BeaconUpgradedEvent.InputTuple,
      BeaconUpgradedEvent.OutputTuple,
      BeaconUpgradedEvent.OutputObject
    >
    BeaconUpgraded: TypedContractEvent<
      BeaconUpgradedEvent.InputTuple,
      BeaconUpgradedEvent.OutputTuple,
      BeaconUpgradedEvent.OutputObject
    >

    'Upgraded(address)': TypedContractEvent<
      UpgradedEvent.InputTuple,
      UpgradedEvent.OutputTuple,
      UpgradedEvent.OutputObject
    >
    Upgraded: TypedContractEvent<
      UpgradedEvent.InputTuple,
      UpgradedEvent.OutputTuple,
      UpgradedEvent.OutputObject
    >
  }
}
