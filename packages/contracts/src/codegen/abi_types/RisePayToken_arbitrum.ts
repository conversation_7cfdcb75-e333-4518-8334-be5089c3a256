/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RisePayToken_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'allowance'
      | 'approve'
      | 'balanceOf'
      | 'burn'
      | 'decimals'
      | 'decreaseAllowance'
      | 'eip712Domain'
      | 'executeTransfers'
      | 'getTransfer'
      | 'getTransferHash'
      | 'increaseAllowance'
      | 'init'
      | 'internalTransfer'
      | 'isTrustedForwarder'
      | 'mint'
      | 'name'
      | 'nonces'
      | 'pendingTransferHashes'
      | 'pendingTransferHashesSlice'
      | 'pendingTransfersAll'
      | 'pendingTransfersContains'
      | 'pendingTransfersCount'
      | 'permit'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setRouter'
      | 'symbol'
      | 'totalSupply'
      | 'transfer'
      | 'transferFrom',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Approval'
      | 'EIP712DomainChanged'
      | 'Initialized'
      | 'Transfer',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'allowance',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'approve',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'balanceOf',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'burn',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'decimals', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'decreaseAllowance',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'eip712Domain',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'executeTransfers',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getTransfer',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTransferHash',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'increaseAllowance',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, string, string, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'internalTransfer',
    values: [AddressLike, BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'mint',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'name', values?: undefined): string
  encodeFunctionData(functionFragment: 'nonces', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'pendingTransferHashes',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransferHashesSlice',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersAll',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersContains',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersCount',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'permit',
    values: [
      AddressLike,
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BytesLike,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'symbol', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'totalSupply',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'transfer',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string

  decodeFunctionResult(functionFragment: 'allowance', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'approve', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'balanceOf', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'burn', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'decimals', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'decreaseAllowance',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'eip712Domain',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executeTransfers',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getTransfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getTransferHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'increaseAllowance',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'internalTransfer',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'mint', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'nonces', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransferHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransferHashesSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersAll',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersContains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'permit', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'symbol', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'totalSupply', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'transfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom',
    data: BytesLike,
  ): Result
}

export namespace ApprovalEvent {
  export type InputTuple = [
    owner: AddressLike,
    spender: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [owner: string, spender: string, value: bigint]
  export interface OutputObject {
    owner: string
    spender: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace EIP712DomainChangedEvent {
  export type InputTuple = []
  export type OutputTuple = []
  export interface OutputObject {}
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace TransferEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [from: string, to: string, value: bigint]
  export interface OutputObject {
    from: string
    to: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePayToken_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePayToken_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePayToken_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  allowance: TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >

  approve: TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  balanceOf: TypedContractMethod<[account: AddressLike], [bigint], 'view'>

  burn: TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  decimals: TypedContractMethod<[], [bigint], 'view'>

  decreaseAllowance: TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  eip712Domain: TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >

  executeTransfers: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >

  getTransfer: TypedContractMethod<
    [transferHash: BytesLike],
    [[bigint, string] & { amount: bigint; recipient: string }],
    'view'
  >

  getTransferHash: TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike],
    [string],
    'view'
  >

  increaseAllowance: TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  init: TypedContractMethod<
    [
      _riseRouter: AddressLike,
      name: string,
      symbol: string,
      decimals: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  internalTransfer: TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  mint: TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  name: TypedContractMethod<[], [string], 'view'>

  nonces: TypedContractMethod<[owner: AddressLike], [bigint], 'view'>

  pendingTransferHashes: TypedContractMethod<[], [string[]], 'view'>

  pendingTransferHashesSlice: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >

  pendingTransfersAll: TypedContractMethod<
    [],
    [[bigint[], string[]] & { amount: bigint[]; recipient: string[] }],
    'view'
  >

  pendingTransfersContains: TypedContractMethod<
    [hash: BytesLike],
    [boolean],
    'view'
  >

  pendingTransfersCount: TypedContractMethod<[], [bigint], 'view'>

  permit: TypedContractMethod<
    [
      owner: AddressLike,
      spender: AddressLike,
      value: BigNumberish,
      deadline: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
    ],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  symbol: TypedContractMethod<[], [string], 'view'>

  totalSupply: TypedContractMethod<[], [bigint], 'view'>

  transfer: TypedContractMethod<
    [to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  transferFrom: TypedContractMethod<
    [from: AddressLike, to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'allowance',
  ): TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'approve',
  ): TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'balanceOf',
  ): TypedContractMethod<[account: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'burn',
  ): TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'decimals',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'decreaseAllowance',
  ): TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(nameOrSignature: 'eip712Domain'): TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'executeTransfers',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getTransfer',
  ): TypedContractMethod<
    [transferHash: BytesLike],
    [[bigint, string] & { amount: bigint; recipient: string }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTransferHash',
  ): TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'increaseAllowance',
  ): TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [
      _riseRouter: AddressLike,
      name: string,
      symbol: string,
      decimals: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'internalTransfer',
  ): TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'mint',
  ): TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'name',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'nonces',
  ): TypedContractMethod<[owner: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransferHashes',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransferHashesSlice',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'pendingTransfersAll',
  ): TypedContractMethod<
    [],
    [[bigint[], string[]] & { amount: bigint[]; recipient: string[] }],
    'view'
  >
  getFunction(
    nameOrSignature: 'pendingTransfersContains',
  ): TypedContractMethod<[hash: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransfersCount',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'permit',
  ): TypedContractMethod<
    [
      owner: AddressLike,
      spender: AddressLike,
      value: BigNumberish,
      deadline: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'symbol',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'totalSupply',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'transfer',
  ): TypedContractMethod<
    [to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom',
  ): TypedContractMethod<
    [from: AddressLike, to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  getEvent(
    key: 'Approval',
  ): TypedContractEvent<
    ApprovalEvent.InputTuple,
    ApprovalEvent.OutputTuple,
    ApprovalEvent.OutputObject
  >
  getEvent(
    key: 'EIP712DomainChanged',
  ): TypedContractEvent<
    EIP712DomainChangedEvent.InputTuple,
    EIP712DomainChangedEvent.OutputTuple,
    EIP712DomainChangedEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'Transfer',
  ): TypedContractEvent<
    TransferEvent.InputTuple,
    TransferEvent.OutputTuple,
    TransferEvent.OutputObject
  >

  filters: {
    'Approval(address,address,uint256)': TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >
    Approval: TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >

    'EIP712DomainChanged()': TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >
    EIP712DomainChanged: TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'Transfer(address,address,uint256)': TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
    Transfer: TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
  }
}
