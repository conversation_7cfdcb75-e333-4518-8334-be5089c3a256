/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type RisePaymentStruct = {
    id: BytesLike
    groupID: BytesLike
    payAtTime: BigNumberish
    validMinutes: BigNumberish
    payType: BigNumberish
    token: AddressLike
    recipient: AddressLike
    amount: BigNumberish
    data: BytesLike
  }

  export type RisePaymentStructOutput = [
    id: string,
    groupID: string,
    payAtTime: bigint,
    validMinutes: bigint,
    payType: bigint,
    token: string,
    recipient: string,
    amount: bigint,
    data: string,
  ] & {
    id: string
    groupID: string
    payAtTime: bigint
    validMinutes: bigint
    payType: bigint
    token: string
    recipient: string
    amount: bigint
    data: string
  }

  export type RisePaymentScheduleRequestStruct = {
    count: BigNumberish
    minuteInterval: BigNumberish
    payment: RiseRequests.RisePaymentStruct
  }

  export type RisePaymentScheduleRequestStructOutput = [
    count: bigint,
    minuteInterval: bigint,
    payment: RiseRequests.RisePaymentStructOutput,
  ] & {
    count: bigint
    minuteInterval: bigint
    payment: RiseRequests.RisePaymentStructOutput
  }

  export type RisePaymentsRequestStruct = {
    payments: RiseRequests.RisePaymentStruct[]
  }

  export type RisePaymentsRequestStructOutput = [
    payments: RiseRequests.RisePaymentStructOutput[],
  ] & { payments: RiseRequests.RisePaymentStructOutput[] }

  export type MemberRoleStruct = { member: AddressLike; role: BigNumberish }

  export type MemberRoleStructOutput = [member: string, role: bigint] & {
    member: string
    role: bigint
  }

  export type RiseIntentPaymentsToScheduledRequestStruct = {
    paymentIDs: BytesLike[]
    payAtTime: BigNumberish[]
  }

  export type RiseIntentPaymentsToScheduledRequestStructOutput = [
    paymentIDs: string[],
    payAtTime: bigint[],
  ] & { paymentIDs: string[]; payAtTime: bigint[] }

  export type RisePaymentAndStateStruct = {
    payment: RiseRequests.RisePaymentStruct
    state: BigNumberish
  }

  export type RisePaymentAndStateStructOutput = [
    payment: RiseRequests.RisePaymentStructOutput,
    state: bigint,
  ] & { payment: RiseRequests.RisePaymentStructOutput; state: bigint }

  export type RiseRemovePaymentsRequestStruct = { paymentIDs: BytesLike[] }

  export type RiseRemovePaymentsRequestStructOutput = [paymentIDs: string[]] & {
    paymentIDs: string[]
  }

  export type RiseRemovePaymentByGroupRequestStruct = {
    groupID: BytesLike
    idx: BigNumberish
    count: BigNumberish
  }

  export type RiseRemovePaymentByGroupRequestStructOutput = [
    groupID: string,
    idx: bigint,
    count: bigint,
  ] & { groupID: string; idx: bigint; count: bigint }

  export type RiseEtherTransferRequestStruct = {
    recipient: AddressLike
    amount: BigNumberish
  }

  export type RiseEtherTransferRequestStructOutput = [
    recipient: string,
    amount: bigint,
  ] & { recipient: string; amount: bigint }

  export type SetRoleStruct = { role: BigNumberish; account: AddressLike }

  export type SetRoleStructOutput = [role: bigint, account: string] & {
    role: bigint
    account: string
  }

  export type RiseTokenApprovalRequestStruct = {
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type RiseTokenApprovalRequestStructOutput = [
    token: string,
    spender: string,
    amount: bigint,
  ] & { token: string; spender: string; amount: bigint }

  export type RiseTransactionLimitRequestStruct = {
    spender: AddressLike
    token: AddressLike
    dailyLimit: BigNumberish
    transactionLimit: BigNumberish
  }

  export type RiseTransactionLimitRequestStructOutput = [
    spender: string,
    token: string,
    dailyLimit: bigint,
    transactionLimit: bigint,
  ] & {
    spender: string
    token: string
    dailyLimit: bigint
    transactionLimit: bigint
  }

  export type RiseTokenTransferRequestStruct = {
    token: AddressLike
    from: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type RiseTokenTransferRequestStructOutput = [
    token: string,
    from: string,
    to: string,
    amount: bigint,
  ] & { token: string; from: string; to: string; amount: bigint }

  export type RiseUpdateAmountPaymentsRequestStruct = {
    paymentIDs: BytesLike[]
    newAmounts: BigNumberish[]
  }

  export type RiseUpdateAmountPaymentsRequestStructOutput = [
    paymentIDs: string[],
    newAmounts: bigint[],
  ] & { paymentIDs: string[]; newAmounts: bigint[] }
}

export interface RiseAccount_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'changeGroupByAdmin'
      | 'changePayAtTimeByAdmin'
      | 'createPayment'
      | 'createPaymentBySchedule'
      | 'createPayments'
      | 'dailyLimit'
      | 'executePayment'
      | 'executePayments'
      | 'getAddressesByMonth'
      | 'getAddressesByMonthCount'
      | 'getCountAndVolumeByHash'
      | 'getDayPaymentTrackingHashes'
      | 'getMemberRole'
      | 'getMembers'
      | 'getMembersRoles'
      | 'getMonthPaymentTrackingHashes'
      | 'getRoleMembers'
      | 'getSenders'
      | 'getSettings'
      | 'getTokenAndSenderVolumeByHash'
      | 'getTokensUsed'
      | 'getTransactionLimitHash'
      | 'init'
      | 'intentPaymentToScheduled'
      | 'isValidSignature'
      | 'payment'
      | 'paymentAndState'
      | 'paymentState'
      | 'payments'
      | 'paymentsCount'
      | 'paymentsHashSlice'
      | 'paymentsSlice'
      | 'recoverToken'
      | 'removePayments'
      | 'removePaymentsByGroupID'
      | 'riseAccess'
      | 'riseRouter'
      | 'sendEther'
      | 'setRoles'
      | 'setRouter'
      | 'setSettings'
      | 'setTokenTransferApproval'
      | 'setTransactionLimits'
      | 'tokenTransfer'
      | 'transactionLimit'
      | 'updatePaymentAmount',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RisePaymentAmountUpdateEvent'
      | 'RisePaymentEvent',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'changeGroupByAdmin',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'changePayAtTimeByAdmin',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'createPayment',
    values: [RiseRequests.RisePaymentStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'createPaymentBySchedule',
    values: [RiseRequests.RisePaymentScheduleRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'createPayments',
    values: [RiseRequests.RisePaymentsRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'dailyLimit',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'executePayment',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'executePayments',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'getAddressesByMonth',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getAddressesByMonthCount',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getCountAndVolumeByHash',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getDayPaymentTrackingHashes',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getMemberRole',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'getMembers', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getMembersRoles',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getMonthPaymentTrackingHashes',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMembers',
    values: [BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'getSenders', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getSettings',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getTokenAndSenderVolumeByHash',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTokensUsed',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getTransactionLimitHash',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'intentPaymentToScheduled',
    values: [RiseRequests.RiseIntentPaymentsToScheduledRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'isValidSignature',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(functionFragment: 'payment', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'paymentAndState',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentState',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'payments',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsCount',
    values: [BigNumberish, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsHashSlice',
    values: [BigNumberish, BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'paymentsSlice',
    values: [BigNumberish, BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'removePayments',
    values: [RiseRequests.RiseRemovePaymentsRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'removePaymentsByGroupID',
    values: [RiseRequests.RiseRemovePaymentByGroupRequestStruct],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'sendEther',
    values: [RiseRequests.RiseEtherTransferRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoles',
    values: [RiseRequests.SetRoleStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setSettings',
    values: [
      BytesLike,
      AddressLike,
      AddressLike,
      boolean,
      AddressLike,
      AddressLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'setTokenTransferApproval',
    values: [RiseRequests.RiseTokenApprovalRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'setTransactionLimits',
    values: [RiseRequests.RiseTransactionLimitRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'tokenTransfer',
    values: [RiseRequests.RiseTokenTransferRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'transactionLimit',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'updatePaymentAmount',
    values: [RiseRequests.RiseUpdateAmountPaymentsRequestStruct],
  ): string

  decodeFunctionResult(
    functionFragment: 'changeGroupByAdmin',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'changePayAtTimeByAdmin',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPayment',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPaymentBySchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'createPayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'dailyLimit', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'executePayment',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executePayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAddressesByMonth',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAddressesByMonthCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getCountAndVolumeByHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getDayPaymentTrackingHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMemberRole',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getMembers', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMembersRoles',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMonthPaymentTrackingHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMembers',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getSenders', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getSettings', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getTokenAndSenderVolumeByHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTokensUsed',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTransactionLimitHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'intentPaymentToScheduled',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isValidSignature',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'payment', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'paymentAndState',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentState',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'payments', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'paymentsCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsHashSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'paymentsSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removePayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removePaymentsByGroupID',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'sendEther', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRoles', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setSettings', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTokenTransferApproval',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setTransactionLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'tokenTransfer',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transactionLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'updatePaymentAmount',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RisePaymentAmountUpdateEventEvent {
  export type InputTuple = [
    paymentID: BytesLike,
    state: BigNumberish,
    oldAmount: BigNumberish,
    payment: RiseRequests.RisePaymentStruct,
  ]
  export type OutputTuple = [
    paymentID: string,
    state: bigint,
    oldAmount: bigint,
    payment: RiseRequests.RisePaymentStructOutput,
  ]
  export interface OutputObject {
    paymentID: string
    state: bigint
    oldAmount: bigint
    payment: RiseRequests.RisePaymentStructOutput
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RisePaymentEventEvent {
  export type InputTuple = [
    paymentID: BytesLike,
    newState: BigNumberish,
    oldState: BigNumberish,
    payment: RiseRequests.RisePaymentStruct,
  ]
  export type OutputTuple = [
    paymentID: string,
    newState: bigint,
    oldState: bigint,
    payment: RiseRequests.RisePaymentStructOutput,
  ]
  export interface OutputObject {
    paymentID: string
    newState: bigint
    oldState: bigint
    payment: RiseRequests.RisePaymentStructOutput
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccount_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccount_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccount_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  changeGroupByAdmin: TypedContractMethod<
    [paymentID: BytesLike, newGroupID: BytesLike],
    [void],
    'nonpayable'
  >

  changePayAtTimeByAdmin: TypedContractMethod<
    [paymentID: BytesLike, newPayAtTime: BigNumberish],
    [void],
    'nonpayable'
  >

  createPayment: TypedContractMethod<
    [req: RiseRequests.RisePaymentStruct],
    [string],
    'nonpayable'
  >

  createPaymentBySchedule: TypedContractMethod<
    [req: RiseRequests.RisePaymentScheduleRequestStruct],
    [string[]],
    'nonpayable'
  >

  createPayments: TypedContractMethod<
    [req: RiseRequests.RisePaymentsRequestStruct],
    [string[]],
    'nonpayable'
  >

  dailyLimit: TypedContractMethod<[_hash: BytesLike], [bigint], 'view'>

  executePayment: TypedContractMethod<
    [paymentID: BytesLike],
    [void],
    'nonpayable'
  >

  executePayments: TypedContractMethod<
    [paymentIDs: BytesLike[]],
    [void],
    'nonpayable'
  >

  getAddressesByMonth: TypedContractMethod<
    [epoch: BigNumberish, idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >

  getAddressesByMonthCount: TypedContractMethod<
    [epoch: BigNumberish],
    [bigint],
    'view'
  >

  getCountAndVolumeByHash: TypedContractMethod<
    [hash: BytesLike],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >

  getDayPaymentTrackingHashes: TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >

  getMemberRole: TypedContractMethod<[member: AddressLike], [bigint], 'view'>

  getMembers: TypedContractMethod<[], [string[]], 'view'>

  getMembersRoles: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [RiseRequests.MemberRoleStructOutput[]],
    'view'
  >

  getMonthPaymentTrackingHashes: TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >

  getRoleMembers: TypedContractMethod<[role: BigNumberish], [string[]], 'view'>

  getSenders: TypedContractMethod<[], [string[]], 'view'>

  getSettings: TypedContractMethod<
    [],
    [
      [string, string, string, boolean, string, string] & {
        accountType: string
        parentAccount: string
        sourceOfFunds: string
        hiddenRiseTokenTransfers: boolean
        sponsorAccount: string
        feeRecipient: string
      },
    ],
    'view'
  >

  getTokenAndSenderVolumeByHash: TypedContractMethod<
    [tokenHash: BytesLike, senderHash: BytesLike],
    [[bigint, bigint] & { tokenVolume: bigint; senderVolume: bigint }],
    'view'
  >

  getTokensUsed: TypedContractMethod<[], [string[]], 'view'>

  getTransactionLimitHash: TypedContractMethod<
    [spender: AddressLike, token: AddressLike],
    [string],
    'view'
  >

  init: TypedContractMethod<
    [_riseRouter: AddressLike, _parentAccount: AddressLike],
    [void],
    'nonpayable'
  >

  intentPaymentToScheduled: TypedContractMethod<
    [req: RiseRequests.RiseIntentPaymentsToScheduledRequestStruct],
    [void],
    'nonpayable'
  >

  isValidSignature: TypedContractMethod<
    [hash: BytesLike, signature: BytesLike],
    [string],
    'view'
  >

  payment: TypedContractMethod<
    [paymentID: BytesLike],
    [RiseRequests.RisePaymentStructOutput],
    'view'
  >

  paymentAndState: TypedContractMethod<
    [paymentID: BytesLike],
    [RiseRequests.RisePaymentAndStateStructOutput],
    'view'
  >

  paymentState: TypedContractMethod<[paymentID: BytesLike], [bigint], 'view'>

  payments: TypedContractMethod<
    [paymentIDs: BytesLike[]],
    [RiseRequests.RisePaymentAndStateStructOutput[]],
    'view'
  >

  paymentsCount: TypedContractMethod<
    [storageType: BigNumberish, _identifier: BytesLike],
    [bigint],
    'view'
  >

  paymentsHashSlice: TypedContractMethod<
    [
      storageType: BigNumberish,
      _identifier: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [string[]],
    'view'
  >

  paymentsSlice: TypedContractMethod<
    [
      storageType: BigNumberish,
      _identifier: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  removePayments: TypedContractMethod<
    [req: RiseRequests.RiseRemovePaymentsRequestStruct],
    [void],
    'nonpayable'
  >

  removePaymentsByGroupID: TypedContractMethod<
    [req: RiseRequests.RiseRemovePaymentByGroupRequestStruct],
    [void],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  sendEther: TypedContractMethod<
    [req: RiseRequests.RiseEtherTransferRequestStruct],
    [void],
    'nonpayable'
  >

  setRoles: TypedContractMethod<
    [req: RiseRequests.SetRoleStruct[]],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setSettings: TypedContractMethod<
    [
      accountType: BytesLike,
      parentAccount: AddressLike,
      sourceOfFunds: AddressLike,
      hiddenRiseTokenTransfers: boolean,
      sponsorAccount: AddressLike,
      feeRecipient: AddressLike,
    ],
    [void],
    'nonpayable'
  >

  setTokenTransferApproval: TypedContractMethod<
    [req: RiseRequests.RiseTokenApprovalRequestStruct],
    [void],
    'nonpayable'
  >

  setTransactionLimits: TypedContractMethod<
    [req: RiseRequests.RiseTransactionLimitRequestStruct],
    [void],
    'nonpayable'
  >

  tokenTransfer: TypedContractMethod<
    [req: RiseRequests.RiseTokenTransferRequestStruct],
    [void],
    'nonpayable'
  >

  transactionLimit: TypedContractMethod<[_hash: BytesLike], [bigint], 'view'>

  updatePaymentAmount: TypedContractMethod<
    [req: RiseRequests.RiseUpdateAmountPaymentsRequestStruct],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'changeGroupByAdmin',
  ): TypedContractMethod<
    [paymentID: BytesLike, newGroupID: BytesLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'changePayAtTimeByAdmin',
  ): TypedContractMethod<
    [paymentID: BytesLike, newPayAtTime: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPayment',
  ): TypedContractMethod<
    [req: RiseRequests.RisePaymentStruct],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPaymentBySchedule',
  ): TypedContractMethod<
    [req: RiseRequests.RisePaymentScheduleRequestStruct],
    [string[]],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'createPayments',
  ): TypedContractMethod<
    [req: RiseRequests.RisePaymentsRequestStruct],
    [string[]],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'dailyLimit',
  ): TypedContractMethod<[_hash: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'executePayment',
  ): TypedContractMethod<[paymentID: BytesLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'executePayments',
  ): TypedContractMethod<[paymentIDs: BytesLike[]], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'getAddressesByMonth',
  ): TypedContractMethod<
    [epoch: BigNumberish, idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getAddressesByMonthCount',
  ): TypedContractMethod<[epoch: BigNumberish], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getCountAndVolumeByHash',
  ): TypedContractMethod<
    [hash: BytesLike],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getDayPaymentTrackingHashes',
  ): TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getMemberRole',
  ): TypedContractMethod<[member: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getMembers',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getMembersRoles',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [RiseRequests.MemberRoleStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getMonthPaymentTrackingHashes',
  ): TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getRoleMembers',
  ): TypedContractMethod<[role: BigNumberish], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getSenders',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(nameOrSignature: 'getSettings'): TypedContractMethod<
    [],
    [
      [string, string, string, boolean, string, string] & {
        accountType: string
        parentAccount: string
        sourceOfFunds: string
        hiddenRiseTokenTransfers: boolean
        sponsorAccount: string
        feeRecipient: string
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTokenAndSenderVolumeByHash',
  ): TypedContractMethod<
    [tokenHash: BytesLike, senderHash: BytesLike],
    [[bigint, bigint] & { tokenVolume: bigint; senderVolume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTokensUsed',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getTransactionLimitHash',
  ): TypedContractMethod<
    [spender: AddressLike, token: AddressLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, _parentAccount: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'intentPaymentToScheduled',
  ): TypedContractMethod<
    [req: RiseRequests.RiseIntentPaymentsToScheduledRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isValidSignature',
  ): TypedContractMethod<
    [hash: BytesLike, signature: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'payment',
  ): TypedContractMethod<
    [paymentID: BytesLike],
    [RiseRequests.RisePaymentStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentAndState',
  ): TypedContractMethod<
    [paymentID: BytesLike],
    [RiseRequests.RisePaymentAndStateStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentState',
  ): TypedContractMethod<[paymentID: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'payments',
  ): TypedContractMethod<
    [paymentIDs: BytesLike[]],
    [RiseRequests.RisePaymentAndStateStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsCount',
  ): TypedContractMethod<
    [storageType: BigNumberish, _identifier: BytesLike],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsHashSlice',
  ): TypedContractMethod<
    [
      storageType: BigNumberish,
      _identifier: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [string[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'paymentsSlice',
  ): TypedContractMethod<
    [
      storageType: BigNumberish,
      _identifier: BytesLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [RiseRequests.RisePaymentStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removePayments',
  ): TypedContractMethod<
    [req: RiseRequests.RiseRemovePaymentsRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'removePaymentsByGroupID',
  ): TypedContractMethod<
    [req: RiseRequests.RiseRemovePaymentByGroupRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'sendEther',
  ): TypedContractMethod<
    [req: RiseRequests.RiseEtherTransferRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRoles',
  ): TypedContractMethod<
    [req: RiseRequests.SetRoleStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setSettings',
  ): TypedContractMethod<
    [
      accountType: BytesLike,
      parentAccount: AddressLike,
      sourceOfFunds: AddressLike,
      hiddenRiseTokenTransfers: boolean,
      sponsorAccount: AddressLike,
      feeRecipient: AddressLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setTokenTransferApproval',
  ): TypedContractMethod<
    [req: RiseRequests.RiseTokenApprovalRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setTransactionLimits',
  ): TypedContractMethod<
    [req: RiseRequests.RiseTransactionLimitRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'tokenTransfer',
  ): TypedContractMethod<
    [req: RiseRequests.RiseTokenTransferRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transactionLimit',
  ): TypedContractMethod<[_hash: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'updatePaymentAmount',
  ): TypedContractMethod<
    [req: RiseRequests.RiseUpdateAmountPaymentsRequestStruct],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RisePaymentAmountUpdateEvent',
  ): TypedContractEvent<
    RisePaymentAmountUpdateEventEvent.InputTuple,
    RisePaymentAmountUpdateEventEvent.OutputTuple,
    RisePaymentAmountUpdateEventEvent.OutputObject
  >
  getEvent(
    key: 'RisePaymentEvent',
  ): TypedContractEvent<
    RisePaymentEventEvent.InputTuple,
    RisePaymentEventEvent.OutputTuple,
    RisePaymentEventEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RisePaymentAmountUpdateEvent(bytes32,uint8,uint256,tuple)': TypedContractEvent<
      RisePaymentAmountUpdateEventEvent.InputTuple,
      RisePaymentAmountUpdateEventEvent.OutputTuple,
      RisePaymentAmountUpdateEventEvent.OutputObject
    >
    RisePaymentAmountUpdateEvent: TypedContractEvent<
      RisePaymentAmountUpdateEventEvent.InputTuple,
      RisePaymentAmountUpdateEventEvent.OutputTuple,
      RisePaymentAmountUpdateEventEvent.OutputObject
    >

    'RisePaymentEvent(bytes32,uint8,uint8,tuple)': TypedContractEvent<
      RisePaymentEventEvent.InputTuple,
      RisePaymentEventEvent.OutputTuple,
      RisePaymentEventEvent.OutputObject
    >
    RisePaymentEvent: TypedContractEvent<
      RisePaymentEventEvent.InputTuple,
      RisePaymentEventEvent.OutputTuple,
      RisePaymentEventEvent.OutputObject
    >
  }
}
