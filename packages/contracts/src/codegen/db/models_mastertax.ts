// @ts-nocheck
// @ts-ignore
import type { Nanoid, CompanyNanoid } from '../../brands.js'
// @ts-ignore
import type { Selectable, Insertable, Updateable } from 'kysely'
// this file was generated with `pnpm codegen`, do not edit it manually
import { ColumnType, Selectable, Insertable, Updateable } from 'kysely'

// JSON type definitions
export type Json = ColumnType<JsonValue, string, string>

export type JsonArray = JsonValue[]

export type JsonObject = {
  [x: string]: JsonValue | undefined
}

export type JsonPrimitive = boolean | number | string | null

export type JsonValue = JsonArray | JsonObject | JsonPrimitive

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>

export type Decimal = ColumnType<string, number | string, number | string>

export type BigInt = ColumnType<string, number | string, number | string>
// Table Interfaces
// Kysely type definitions for cafeteria_plan_benefits

// This interface defines the structure of the 'cafeteria_plan_benefits' table
export interface CafeteriaPlanBenefits {
  accident_health_insurance_premiums: Generated<
    ('applicable' | 'not_applicable') | null
  >
  agent_client_type: Generated<
    | (
        | 'agent_3504'
        | 'cpeo_3504'
        | 'cpeo_3511a'
        | 'cpeo_3511c'
        | 'cpeo_client_31_3504'
        | 'cpeo_client_3511a'
        | 'cpeo_client_3511c'
        | 'cpeo_mixed'
        | 'none'
        | 'other_third_party'
      )
    | null
  >
  business_expense_reimbursement: Generated<
    ('applicable' | 'not_applicable') | null
  >
  cafeteria_plan_benefits: Generated<('applicable' | 'not_applicable') | null>
  cash_service_level: Generated<('full' | 'variances_only') | null>
  chaplain_housing: Generated<('applicable' | 'not_applicable') | null>
  clergy_housing_poverty_vow: Generated<
    ('applicable' | 'not_applicable') | null
  >
  company_effective_date: Date | null
  company_group_name: string | null
  dependent_care_assistance: Generated<('applicable' | 'not_applicable') | null>
  employee_stock_purchase_plan: Generated<
    ('applicable' | 'not_applicable') | null
  >
  employer_contribution_401k: Generated<
    ('applicable' | 'not_applicable') | null
  >
  employer_contribution_sep_ira: Generated<
    ('applicable' | 'not_applicable') | null
  >
  employer_contribution_simple: Generated<
    ('applicable' | 'not_applicable') | null
  >
  exempt_501c3_organization: Generated<('applicable' | 'not_applicable') | null>
  filer_944: Generated<('no' | 'yes') | null>
  foreign_source_income: Generated<('applicable' | 'not_applicable') | null>
  group_term_life_insurance: Generated<('applicable' | 'not_applicable') | null>
  hsa: Generated<('applicable' | 'not_applicable') | null>
  kind_of_employer: Generated<
    | (
        | 'federal_government'
        | 'none'
        | 'state_local_government'
        | 'state_local_tax_exempt'
        | 'tax_exempt'
      )
    | null
  >
  meals_furnished_in_kind: Generated<('applicable' | 'not_applicable') | null>
  naics_code: Generated<string | null>
  nanoid: CompanyNanoid
  non_taxable_fringe_payments: Generated<
    ('applicable' | 'not_applicable') | null
  >
  nonqualified_deferred_comp: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payments_to_election_workers: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payments_to_family_employees: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payments_to_general_partnership: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payments_to_hospital_interns: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payments_to_hospital_patients: Generated<
    ('applicable' | 'not_applicable') | null
  >
  payroll_run_id: string | null
  public_transportation_non_tax: Generated<
    ('applicable' | 'not_applicable') | null
  >
  qualified_moving_expense: Generated<('applicable' | 'not_applicable') | null>
  quarterly_wage_reporting: Generated<('no' | 'yes') | null>
  sick_pay: Generated<('applicable' | 'not_applicable') | null>
  state_govt_employee_salaries: Generated<
    ('applicable' | 'not_applicable') | null
  >
  student_exempt: Generated<('applicable' | 'not_applicable') | null>
  supplemental_unemployment_benefits: Generated<
    ('applicable' | 'not_applicable') | null
  >
  wage_attachment_flag: Generated<('no' | 'yes') | null>
  wc_housing_employment_condition: Generated<
    ('applicable' | 'not_applicable') | null
  >
  workers_compensation: Generated<('applicable' | 'not_applicable') | null>
  worksite_reporting: Generated<('no' | 'yes') | null>
  year_end_employee_filing: Generated<('no' | 'yes') | null>
}

// Helper types for cafeteria_plan_benefits
export type SelectableCafeteriaPlanBenefits = Selectable<CafeteriaPlanBenefits>
export type InsertableCafeteriaPlanBenefits = Insertable<CafeteriaPlanBenefits>
export type UpdateableCafeteriaPlanBenefits = Updateable<CafeteriaPlanBenefits>

// Kysely type definitions for company_address

// This interface defines the structure of the 'company_address' table
export interface CompanyAddress {
  address_line_1: string | null
  address_line_2: string | null
  area_code: string | null
  check_date: Date
  city: string | null
  company_dba: string | null
  country_code: ('CA' | 'MX' | 'US') | null
  email_address: string | null
  extension: string | null
  fax_area_code: string | null
  fax_number: string | null
  first_name: string | null
  in_care_of: string | null
  last_name: string | null
  middle_initial: string | null
  payroll_code: string
  psd_code: string | null
  record_type: Generated<string>
  state_code: string | null
  sub_type: Generated<string>
  telephone_number: string | null
  zip_code: string | null
}

// Helper types for company_address
export type SelectableCompanyAddress = Selectable<CompanyAddress>
export type InsertableCompanyAddress = Insertable<CompanyAddress>
export type UpdateableCompanyAddress = Updateable<CompanyAddress>

// Kysely type definitions for company_cash_care_banks

// This interface defines the structure of the 'company_cash_care_banks' table
export interface CompanyCashCareBanks {
  cash_care_ach_bank_destination: string | null
  check_date: Date
  payroll_code: string
  payroll_tax_account_number: string | null
  payroll_tax_account_type: ('checking' | 'savings') | null
  payroll_tax_bank_name: string | null
  payroll_tax_draft_days: number | null
  payroll_tax_routing_number: string | null
  record_type: Generated<string>
  sub_type: Generated<string>
  wage_attachment_account_number: string | null
  wage_attachment_account_type: ('checking' | 'savings') | null
  wage_attachment_bank_name: string | null
  wage_attachment_draft_days: number | null
  wage_attachment_routing_number: string | null
}

// Helper types for company_cash_care_banks
export type SelectableCompanyCashCareBanks = Selectable<CompanyCashCareBanks>
export type InsertableCompanyCashCareBanks = Insertable<CompanyCashCareBanks>
export type UpdateableCompanyCashCareBanks = Updateable<CompanyCashCareBanks>

// Kysely type definitions for company_disbursement_banks

// This interface defines the structure of the 'company_disbursement_banks' table
export interface CompanyDisbursementBanks {
  check_date: Date
  payroll_code: string
  payroll_tax_disbursement_account_number: string | null
  payroll_tax_disbursement_ach_point: string | null
  payroll_tax_disbursement_bank_name: string | null
  payroll_tax_disbursement_routing_number: string | null
  record_type: Generated<string>
  sub_type: Generated<string>
  wage_attachment_disbursement_account_number: string | null
  wage_attachment_disbursement_ach_point: string | null
  wage_attachment_disbursement_bank_name: string | null
  wage_attachment_disbursement_routing_number: string | null
}

// Helper types for company_disbursement_banks
export type SelectableCompanyDisbursementBanks =
  Selectable<CompanyDisbursementBanks>
export type InsertableCompanyDisbursementBanks =
  Insertable<CompanyDisbursementBanks>
export type UpdateableCompanyDisbursementBanks =
  Updateable<CompanyDisbursementBanks>

// Kysely type definitions for company_general_ledger

// This interface defines the structure of the 'company_general_ledger' table
export interface CompanyGeneralLedger {
  chart_of_accounts_code: string | null
  chart_of_accounts_description: string | null
  check_date: Date
  company_code: string | null
  gl_payroll_code: string | null
  payroll_code: string
  record_type: Generated<string>
  sub_type: Generated<string>
  variance_payroll_code: string | null
}

// Helper types for company_general_ledger
export type SelectableCompanyGeneralLedger = Selectable<CompanyGeneralLedger>
export type InsertableCompanyGeneralLedger = Insertable<CompanyGeneralLedger>
export type UpdateableCompanyGeneralLedger = Updateable<CompanyGeneralLedger>

// Kysely type definitions for company_mailing_address

// This interface defines the structure of the 'company_mailing_address' table
export interface CompanyMailingAddress {
  address_line_1: string | null
  address_line_2: string | null
  area_code: string | null
  check_date: Date
  city: string | null
  company_dba: string | null
  country_code: ('CA' | 'MX' | 'US') | null
  email_address: string | null
  extension: string | null
  fax_area_code: string | null
  fax_number: string | null
  first_name: string | null
  last_name: string | null
  middle_initial: string | null
  payroll_code: string
  record_type: Generated<string>
  route_code: string | null
  state_code: string | null
  sub_type: Generated<string>
  telephone_number: string | null
  zip_code: string | null
}

// Helper types for company_mailing_address
export type SelectableCompanyMailingAddress = Selectable<CompanyMailingAddress>
export type InsertableCompanyMailingAddress = Insertable<CompanyMailingAddress>
export type UpdateableCompanyMailingAddress = Updateable<CompanyMailingAddress>

// Kysely type definitions for company_tax

// This interface defines the structure of the 'company_tax' table
export interface CompanyTax {
  check_date: Date
  company_tax_service_level: ('balance_only' | 'return_only') | null
  company_tax_status: ('active' | 'inactive') | null
  county_code: string | null
  effective_date: Date | null
  eft_password: string | null
  ein: string | null
  ein_type:
    | (
        | 'applied_for'
        | 'common_pay_child'
        | 'common_pay_parent'
        | 'exempt'
        | 'michigan_501'
        | 'registered'
        | 'reimbursable'
        | 'same_as_fein'
      )
    | null
  expanded_tax_rate: string | null
  final_return_effective_date: Date | null
  mark_all_returns_final: ('no' | 'yes') | null
  payment_frequency: string | null
  payment_method:
    | (
        | 'check'
        | 'eft_credit'
        | 'eft_debit'
        | 'eft_debit_file'
        | 'eft_debit_online'
        | 'eft_debit_return'
        | 'eft_debit_touch_tone'
      )
    | null
  payroll_code: string
  record_type: Generated<string>
  reference_ein: string | null
  sub_type: Generated<string>
  tax_code: string
  tax_rate: string | null
  tax_rate_2: string | null
  work_out_of_state_flag: ('no' | 'yes') | null
}

// Helper types for company_tax
export type SelectableCompanyTax = Selectable<CompanyTax>
export type InsertableCompanyTax = Insertable<CompanyTax>
export type UpdateableCompanyTax = Updateable<CompanyTax>

// Kysely type definitions for company_workers_comp_codes

// This interface defines the structure of the 'company_workers_comp_codes' table
export interface CompanyWorkersCompCodes {
  check_date: Date
  class_code: string | null
  effective_date: Date | null
  payroll_code: string
  rate: string | null
  record_type: Generated<string>
  sub_type: Generated<string>
  tax_code: string
}

// Helper types for company_workers_comp_codes
export type SelectableCompanyWorkersCompCodes =
  Selectable<CompanyWorkersCompCodes>
export type InsertableCompanyWorkersCompCodes =
  Insertable<CompanyWorkersCompCodes>
export type UpdateableCompanyWorkersCompCodes =
  Updateable<CompanyWorkersCompCodes>

// Kysely type definitions for file_header

// This interface defines the structure of the 'file_header' table
export interface FileHeader {
  check_date: Date
  customer_id: Generated<string>
  file_type: Generated<string>
  payroll_code: string
  process_date: Date
  process_time: string
  record_type: Generated<string>
  sub_type: Generated<string>
  version: Generated<string>
}

// Helper types for file_header
export type SelectableFileHeader = Selectable<FileHeader>
export type InsertableFileHeader = Insertable<FileHeader>
export type UpdateableFileHeader = Updateable<FileHeader>

// Kysely type definitions for file_trailer

// This interface defines the structure of the 'file_trailer' table
export interface FileTrailer {
  check_date: Date
  payroll_code: string
  record_count: number | null
  record_type: Generated<string>
  sub_type: Generated<string>
  tax_total: Decimal | null
}

// Helper types for file_trailer
export type SelectableFileTrailer = Selectable<FileTrailer>
export type InsertableFileTrailer = Insertable<FileTrailer>
export type UpdateableFileTrailer = Updateable<FileTrailer>

// Kysely type definitions for payroll_header

// This interface defines the structure of the 'payroll_header' table
export interface PayrollHeader {
  accident_health_insurance_premiums: ('applicable' | 'not_applicable') | null
  agent_client_type:
    | (
        | 'agent_3504'
        | 'cpeo_3504'
        | 'cpeo_3511a'
        | 'cpeo_3511c'
        | 'cpeo_client_31_3504'
        | 'cpeo_client_3511a'
        | 'cpeo_client_3511c'
        | 'cpeo_mixed'
        | 'none'
        | 'other_third_party'
      )
    | null
  bank_account_name: string | null
  bank_account_number: string | null
  bank_account_type: ('checking' | 'savings') | null
  bank_setup: ('no' | 'yes') | null
  business_expense_reimbursement: ('applicable' | 'not_applicable') | null
  cafeteria_plan_benefits: ('applicable' | 'not_applicable') | null
  cash_service_level: ('full' | 'variances_only') | null
  chaplain_housing: ('applicable' | 'not_applicable') | null
  check_date: Date
  clergy_housing_poverty_vow: ('applicable' | 'not_applicable') | null
  company_effective_date: Date | null
  company_group_name: string | null
  company_name: string
  company_setup: 'no' | 'yes'
  company_start_date: Date
  company_status: 'active' | 'inactive'
  dependent_care_assistance: ('applicable' | 'not_applicable') | null
  disbursement_ach_bank_destination: string | null
  disbursement_bank_account_name: string | null
  disbursement_bank_account_number: string | null
  disbursement_bank_routing_number: string | null
  draft_days: number | null
  employee_stock_purchase_plan: ('applicable' | 'not_applicable') | null
  employer_contribution_401k: ('applicable' | 'not_applicable') | null
  employer_contribution_sep_ira: ('applicable' | 'not_applicable') | null
  employer_contribution_simple: ('applicable' | 'not_applicable') | null
  exempt_501c3_organization: ('applicable' | 'not_applicable') | null
  fein: string | null
  fein_type:
    | ('applied_for' | 'common_pay_child' | 'common_pay_parent' | 'registered')
    | null
  filer_944: ('no' | 'yes') | null
  foreign_source_income: ('applicable' | 'not_applicable') | null
  group_term_life_insurance: ('applicable' | 'not_applicable') | null
  hsa: ('applicable' | 'not_applicable') | null
  kind_of_employer:
    | (
        | 'federal_government'
        | 'none'
        | 'state_local_government'
        | 'state_local_tax_exempt'
        | 'tax_exempt'
      )
    | null
  meals_furnished_in_kind: ('applicable' | 'not_applicable') | null
  naics_code: string | null
  name_control: string | null
  next_check_date: Date | null
  non_taxable_fringe_payments: ('applicable' | 'not_applicable') | null
  nonqualified_deferred_comp: ('applicable' | 'not_applicable') | null
  payments_to_election_workers: ('applicable' | 'not_applicable') | null
  payments_to_family_employees: ('applicable' | 'not_applicable') | null
  payments_to_general_partnership: ('applicable' | 'not_applicable') | null
  payments_to_hospital_interns: ('applicable' | 'not_applicable') | null
  payments_to_hospital_patients: ('applicable' | 'not_applicable') | null
  payroll_code: string
  payroll_description: string | null
  payroll_run_id: string | null
  public_transportation_non_tax: ('applicable' | 'not_applicable') | null
  qualified_moving_expense: ('applicable' | 'not_applicable') | null
  quarterly_wage_reporting: ('no' | 'yes') | null
  record_type: Generated<string>
  reporting_payroll_code: string | null
  service_level: 'balance_only' | 'full_service' | 'return_only'
  short_reporting_payroll_code: string | null
  sick_pay: ('applicable' | 'not_applicable') | null
  state_govt_employee_salaries: ('applicable' | 'not_applicable') | null
  student_exempt: ('applicable' | 'not_applicable') | null
  sub_type: Generated<string>
  supplemental_unemployment_benefits: ('applicable' | 'not_applicable') | null
  tax_liabilities: 'no' | 'yes'
  transit_routing_number: string | null
  variance_payroll_code: ('no' | 'yes') | null
  wage_attachment_flag: ('no' | 'yes') | null
  wc_housing_employment_condition: ('applicable' | 'not_applicable') | null
  workers_compensation: ('applicable' | 'not_applicable') | null
  worksite_reporting: ('no' | 'yes') | null
  year_end_employee_filing: ('no' | 'yes') | null
}

// Helper types for payroll_header
export type SelectablePayrollHeader = Selectable<PayrollHeader>
export type InsertablePayrollHeader = Insertable<PayrollHeader>
export type UpdateablePayrollHeader = Updateable<PayrollHeader>

// Kysely type definitions for payroll_tax_detail

// This interface defines the structure of the 'payroll_tax_detail' table
export interface PayrollTaxDetail {
  check_date: Date
  ein: string | null
  employee_count: number | null
  employee_count_sign: ('negative' | 'positive') | null
  exempt_overtime_wages_employee_count: number | null
  exempt_wages: Decimal | null
  expanded_tax_rate: string | null
  gross_wages: Decimal | null
  liability_trace_id: string | null
  payroll_code: string
  payroll_frequency:
    | ('bi_weekly' | 'monthly' | 'semi_monthly' | 'weekly')
    | null
  record_type: Generated<string>
  sub_type: Generated<string>
  tax: Decimal | null
  tax_code: string
  tax_rate: string | null
  taxable_wages: Decimal | null
  wc_class_code: string | null
  work_out_of_state_flag: ('no' | 'yes') | null
}

// Helper types for payroll_tax_detail
export type SelectablePayrollTaxDetail = Selectable<PayrollTaxDetail>
export type InsertablePayrollTaxDetail = Insertable<PayrollTaxDetail>
export type UpdateablePayrollTaxDetail = Updateable<PayrollTaxDetail>

// Kysely type definitions for payroll_tax_user_defined_fields

// This interface defines the structure of the 'payroll_tax_user_defined_fields' table
export interface PayrollTaxUserDefinedFields {
  check_date: Date
  payroll_code: string
  record_type: Generated<string>
  sub_type: Generated<string>
  tax_code: string
  user_field_1: string | null
  user_field_2: string | null
  user_field_3: string | null
  user_field_4: string | null
  user_field_5: string | null
  user_field_6: string | null
  user_field_7: string | null
  user_field_8: string | null
  work_out_of_state_flag: ('no' | 'yes') | null
}

// Helper types for payroll_tax_user_defined_fields
export type SelectablePayrollTaxUserDefinedFields =
  Selectable<PayrollTaxUserDefinedFields>
export type InsertablePayrollTaxUserDefinedFields =
  Insertable<PayrollTaxUserDefinedFields>
export type UpdateablePayrollTaxUserDefinedFields =
  Updateable<PayrollTaxUserDefinedFields>

// Kysely type definitions for payroll_trailer

// This interface defines the structure of the 'payroll_trailer' table
export interface PayrollTrailer {
  check_date: Date
  payroll_code: string
  record_count: number | null
  record_type: Generated<string>
  sub_type: Generated<string>
  tax_total: Decimal | null
}

// Helper types for payroll_trailer
export type SelectablePayrollTrailer = Selectable<PayrollTrailer>
export type InsertablePayrollTrailer = Insertable<PayrollTrailer>
export type UpdateablePayrollTrailer = Updateable<PayrollTrailer>

// Kysely type definitions for secondary_company_groups

// This interface defines the structure of the 'secondary_company_groups' table
export interface SecondaryCompanyGroups {
  check_date: Date
  company_group_name_1: string | null
  company_group_name_2: string | null
  company_group_name_3: string | null
  company_group_name_4: string | null
  company_group_type_1: string | null
  company_group_type_2: string | null
  company_group_type_3: string | null
  company_group_type_4: string | null
  payroll_code: string
  record_type: Generated<string>
  sub_type: Generated<string>
}

// Helper types for secondary_company_groups
export type SelectableSecondaryCompanyGroups =
  Selectable<SecondaryCompanyGroups>
export type InsertableSecondaryCompanyGroups =
  Insertable<SecondaryCompanyGroups>
export type UpdateableSecondaryCompanyGroups =
  Updateable<SecondaryCompanyGroups>

// Database Interface
export interface DB {
  cafeteria_plan_benefits: CafeteriaPlanBenefits
  company_address: CompanyAddress
  company_cash_care_banks: CompanyCashCareBanks
  company_disbursement_banks: CompanyDisbursementBanks
  company_general_ledger: CompanyGeneralLedger
  company_mailing_address: CompanyMailingAddress
  company_tax: CompanyTax
  company_workers_comp_codes: CompanyWorkersCompCodes
  file_header: FileHeader
  file_trailer: FileTrailer
  payroll_header: PayrollHeader
  payroll_tax_detail: PayrollTaxDetail
  payroll_tax_user_defined_fields: PayrollTaxUserDefinedFields
  payroll_trailer: PayrollTrailer
  secondary_company_groups: SecondaryCompanyGroups
}
