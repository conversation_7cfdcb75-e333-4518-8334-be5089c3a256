// @ts-nocheck
// @ts-ignore
import { nanoid } from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const cafeteria_plan_benefits = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish()
    .default('none'),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cafeteria_plan_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cash_service_level: z
    .enum(['full', 'variances_only'])
    .nullish()
    .default('full'),
  chaplain_housing: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  dependent_care_assistance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  exempt_501c3_organization: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  filer_944: z.enum(['no', 'yes']).nullish().default('no'),
  foreign_source_income: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  group_term_life_insurance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  hsa: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish()
    .default('none'),
  meals_furnished_in_kind: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  naics_code: z.string().trim().nullish().default('541214'),
  nanoid: z.string().trim().min(1),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  qualified_moving_expense: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  sick_pay: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  student_exempt: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish().default('no'),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  workers_compensation: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  worksite_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish().default('no'),
})

export const insertable_cafeteria_plan_benefits = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish()
    .default('none'),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cafeteria_plan_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cash_service_level: z
    .enum(['full', 'variances_only'])
    .nullish()
    .default('full'),
  chaplain_housing: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  dependent_care_assistance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  exempt_501c3_organization: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  filer_944: z.enum(['no', 'yes']).nullish().default('no'),
  foreign_source_income: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  group_term_life_insurance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  hsa: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish()
    .default('none'),
  meals_furnished_in_kind: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  naics_code: z.string().trim().nullish().default('541214'),
  nanoid: z.string().trim().min(1),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  qualified_moving_expense: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  sick_pay: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  student_exempt: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish().default('no'),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  workers_compensation: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  worksite_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish().default('no'),
})

export const updateable_cafeteria_plan_benefits = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish()
    .default('none'),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cafeteria_plan_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  cash_service_level: z
    .enum(['full', 'variances_only'])
    .nullish()
    .default('full'),
  chaplain_housing: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  dependent_care_assistance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  exempt_501c3_organization: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  filer_944: z.enum(['no', 'yes']).nullish().default('no'),
  foreign_source_income: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  group_term_life_insurance: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  hsa: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish()
    .default('none'),
  meals_furnished_in_kind: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  naics_code: z.string().trim().nullish().default('541214'),
  nanoid: z.string().trim().min(1).optional(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  qualified_moving_expense: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  sick_pay: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  student_exempt: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish().default('no'),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  workers_compensation: z
    .enum(['applicable', 'not_applicable'])
    .nullish()
    .default('not_applicable'),
  worksite_reporting: z.enum(['no', 'yes']).nullish().default('no'),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish().default('no'),
})

export const selectable_cafeteria_plan_benefits = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullable(),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  cafeteria_plan_benefits: z.enum(['applicable', 'not_applicable']).nullable(),
  cash_service_level: z.enum(['full', 'variances_only']).nullable(),
  chaplain_housing: z.enum(['applicable', 'not_applicable']).nullable(),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullable(),
  company_group_name: z.string().nullable(),
  dependent_care_assistance: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  exempt_501c3_organization: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  filer_944: z.enum(['no', 'yes']).nullable(),
  foreign_source_income: z.enum(['applicable', 'not_applicable']).nullable(),
  group_term_life_insurance: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  hsa: z.enum(['applicable', 'not_applicable']).nullable(),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullable(),
  meals_furnished_in_kind: z.enum(['applicable', 'not_applicable']).nullable(),
  naics_code: z.string().nullable(),
  nanoid: z.string(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payroll_run_id: z.string().nullable(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  qualified_moving_expense: z.enum(['applicable', 'not_applicable']).nullable(),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullable(),
  sick_pay: z.enum(['applicable', 'not_applicable']).nullable(),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  student_exempt: z.enum(['applicable', 'not_applicable']).nullable(),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  wage_attachment_flag: z.enum(['no', 'yes']).nullable(),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  workers_compensation: z.enum(['applicable', 'not_applicable']).nullable(),
  worksite_reporting: z.enum(['no', 'yes']).nullable(),
  year_end_employee_filing: z.enum(['no', 'yes']).nullable(),
})

export type CafeteriaPlanBenefitsType = z.infer<typeof cafeteria_plan_benefits>
export type InsertableCafeteriaPlanBenefitsType = z.infer<
  typeof insertable_cafeteria_plan_benefits
>
export type UpdateableCafeteriaPlanBenefitsType = z.infer<
  typeof updateable_cafeteria_plan_benefits
>
export type SelectableCafeteriaPlanBenefitsType = z.infer<
  typeof selectable_cafeteria_plan_benefits
>
