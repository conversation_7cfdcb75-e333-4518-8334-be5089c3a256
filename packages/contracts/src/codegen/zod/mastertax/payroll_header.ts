// @ts-nocheck

// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const payroll_header = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish(),
  bank_account_name: z.string().trim().nullish(),
  bank_account_number: z.string().trim().nullish(),
  bank_account_type: z.enum(['checking', 'savings']).nullish(),
  bank_setup: z.enum(['no', 'yes']).nullish(),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  cafeteria_plan_benefits: z.enum(['applicable', 'not_applicable']).nullish(),
  cash_service_level: z.enum(['full', 'variances_only']).nullish(),
  chaplain_housing: z.enum(['applicable', 'not_applicable']).nullish(),
  check_date: z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date()),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  company_name: z.string().trim().min(1),
  company_setup: z.enum(['no', 'yes']),
  company_start_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional()
    .default('2025-03-01T00:00:00Z'),
  company_status: z.enum(['active', 'inactive']),
  dependent_care_assistance: z.enum(['applicable', 'not_applicable']).nullish(),
  disbursement_ach_bank_destination: z.string().trim().nullish(),
  disbursement_bank_account_name: z.string().trim().nullish(),
  disbursement_bank_account_number: z.string().trim().nullish(),
  disbursement_bank_routing_number: z.string().trim().nullish(),
  draft_days: z.number().nullish(),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  exempt_501c3_organization: z.enum(['applicable', 'not_applicable']).nullish(),
  fein: z.string().trim().nullish(),
  fein_type: z
    .enum([
      'applied_for',
      'common_pay_child',
      'common_pay_parent',
      'registered',
    ])
    .nullish(),
  filer_944: z.enum(['no', 'yes']).nullish(),
  foreign_source_income: z.enum(['applicable', 'not_applicable']).nullish(),
  group_term_life_insurance: z.enum(['applicable', 'not_applicable']).nullish(),
  hsa: z.enum(['applicable', 'not_applicable']).nullish(),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish(),
  meals_furnished_in_kind: z.enum(['applicable', 'not_applicable']).nullish(),
  naics_code: z.string().trim().nullish(),
  name_control: z.string().trim().nullish(),
  next_check_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payroll_code: z.string().trim().min(1),
  payroll_description: z.string().trim().nullish(),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  qualified_moving_expense: z.enum(['applicable', 'not_applicable']).nullish(),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish(),
  record_type: z.string().trim().optional().default('1'),
  reporting_payroll_code: z.string().trim().nullish(),
  service_level: z.enum(['balance_only', 'full_service', 'return_only']),
  short_reporting_payroll_code: z.string().trim().nullish(),
  sick_pay: z.enum(['applicable', 'not_applicable']).nullish(),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  student_exempt: z.enum(['applicable', 'not_applicable']).nullish(),
  sub_type: z.string().trim().optional().default('00'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  tax_liabilities: z.enum(['no', 'yes']),
  transit_routing_number: z.string().trim().nullish(),
  variance_payroll_code: z.enum(['no', 'yes']).nullish(),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish(),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  workers_compensation: z.enum(['applicable', 'not_applicable']).nullish(),
  worksite_reporting: z.enum(['no', 'yes']).nullish(),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish(),
})

export const insertable_payroll_header = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish(),
  bank_account_name: z.string().trim().nullish(),
  bank_account_number: z.string().trim().nullish(),
  bank_account_type: z.enum(['checking', 'savings']).nullish(),
  bank_setup: z.enum(['no', 'yes']).nullish(),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  cafeteria_plan_benefits: z.enum(['applicable', 'not_applicable']).nullish(),
  cash_service_level: z.enum(['full', 'variances_only']).nullish(),
  chaplain_housing: z.enum(['applicable', 'not_applicable']).nullish(),
  check_date: z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date()),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  company_name: z.string().trim().min(1),
  company_setup: z.enum(['no', 'yes']),
  company_start_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional()
    .default('2025-03-01T00:00:00Z'),
  company_status: z.enum(['active', 'inactive']),
  dependent_care_assistance: z.enum(['applicable', 'not_applicable']).nullish(),
  disbursement_ach_bank_destination: z.string().trim().nullish(),
  disbursement_bank_account_name: z.string().trim().nullish(),
  disbursement_bank_account_number: z.string().trim().nullish(),
  disbursement_bank_routing_number: z.string().trim().nullish(),
  draft_days: z.number().nullish(),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  exempt_501c3_organization: z.enum(['applicable', 'not_applicable']).nullish(),
  fein: z.string().trim().nullish(),
  fein_type: z
    .enum([
      'applied_for',
      'common_pay_child',
      'common_pay_parent',
      'registered',
    ])
    .nullish(),
  filer_944: z.enum(['no', 'yes']).nullish(),
  foreign_source_income: z.enum(['applicable', 'not_applicable']).nullish(),
  group_term_life_insurance: z.enum(['applicable', 'not_applicable']).nullish(),
  hsa: z.enum(['applicable', 'not_applicable']).nullish(),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish(),
  meals_furnished_in_kind: z.enum(['applicable', 'not_applicable']).nullish(),
  naics_code: z.string().trim().nullish(),
  name_control: z.string().trim().nullish(),
  next_check_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payroll_code: z.string().trim().min(1),
  payroll_description: z.string().trim().nullish(),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  qualified_moving_expense: z.enum(['applicable', 'not_applicable']).nullish(),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish(),
  record_type: z.string().trim().optional().default('1'),
  reporting_payroll_code: z.string().trim().nullish(),
  service_level: z.enum(['balance_only', 'full_service', 'return_only']),
  short_reporting_payroll_code: z.string().trim().nullish(),
  sick_pay: z.enum(['applicable', 'not_applicable']).nullish(),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  student_exempt: z.enum(['applicable', 'not_applicable']).nullish(),
  sub_type: z.string().trim().optional().default('00'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  tax_liabilities: z.enum(['no', 'yes']),
  transit_routing_number: z.string().trim().nullish(),
  variance_payroll_code: z.enum(['no', 'yes']).nullish(),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish(),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  workers_compensation: z.enum(['applicable', 'not_applicable']).nullish(),
  worksite_reporting: z.enum(['no', 'yes']).nullish(),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish(),
})

export const updateable_payroll_header = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullish(),
  bank_account_name: z.string().trim().nullish(),
  bank_account_number: z.string().trim().nullish(),
  bank_account_type: z.enum(['checking', 'savings']).nullish(),
  bank_setup: z.enum(['no', 'yes']).nullish(),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  cafeteria_plan_benefits: z.enum(['applicable', 'not_applicable']).nullish(),
  cash_service_level: z.enum(['full', 'variances_only']).nullish(),
  chaplain_housing: z.enum(['applicable', 'not_applicable']).nullish(),
  check_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  company_group_name: z.string().trim().nullish(),
  company_name: z.string().trim().min(1).optional(),
  company_setup: z.enum(['no', 'yes']).optional(),
  company_start_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional()
    .default('2025-03-01T00:00:00Z'),
  company_status: z.enum(['active', 'inactive']).optional(),
  dependent_care_assistance: z.enum(['applicable', 'not_applicable']).nullish(),
  disbursement_ach_bank_destination: z.string().trim().nullish(),
  disbursement_bank_account_name: z.string().trim().nullish(),
  disbursement_bank_account_number: z.string().trim().nullish(),
  disbursement_bank_routing_number: z.string().trim().nullish(),
  draft_days: z.number().nullish(),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  exempt_501c3_organization: z.enum(['applicable', 'not_applicable']).nullish(),
  fein: z.string().trim().nullish(),
  fein_type: z
    .enum([
      'applied_for',
      'common_pay_child',
      'common_pay_parent',
      'registered',
    ])
    .nullish(),
  filer_944: z.enum(['no', 'yes']).nullish(),
  foreign_source_income: z.enum(['applicable', 'not_applicable']).nullish(),
  group_term_life_insurance: z.enum(['applicable', 'not_applicable']).nullish(),
  hsa: z.enum(['applicable', 'not_applicable']).nullish(),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullish(),
  meals_furnished_in_kind: z.enum(['applicable', 'not_applicable']).nullish(),
  naics_code: z.string().trim().nullish(),
  name_control: z.string().trim().nullish(),
  next_check_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  payroll_code: z.string().trim().min(1).optional(),
  payroll_description: z.string().trim().nullish(),
  payroll_run_id: z.string().trim().nullish(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  qualified_moving_expense: z.enum(['applicable', 'not_applicable']).nullish(),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullish(),
  record_type: z.string().trim().optional().default('1'),
  reporting_payroll_code: z.string().trim().nullish(),
  service_level: z
    .enum(['balance_only', 'full_service', 'return_only'])
    .optional(),
  short_reporting_payroll_code: z.string().trim().nullish(),
  sick_pay: z.enum(['applicable', 'not_applicable']).nullish(),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  student_exempt: z.enum(['applicable', 'not_applicable']).nullish(),
  sub_type: z.string().trim().optional().default('00'),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  tax_liabilities: z.enum(['no', 'yes']).optional(),
  transit_routing_number: z.string().trim().nullish(),
  variance_payroll_code: z.enum(['no', 'yes']).nullish(),
  wage_attachment_flag: z.enum(['no', 'yes']).nullish(),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullish(),
  workers_compensation: z.enum(['applicable', 'not_applicable']).nullish(),
  worksite_reporting: z.enum(['no', 'yes']).nullish(),
  year_end_employee_filing: z.enum(['no', 'yes']).nullish(),
})

export const selectable_payroll_header = z.object({
  accident_health_insurance_premiums: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  agent_client_type: z
    .enum([
      'agent_3504',
      'cpeo_3504',
      'cpeo_3511a',
      'cpeo_3511c',
      'cpeo_client_31_3504',
      'cpeo_client_3511a',
      'cpeo_client_3511c',
      'cpeo_mixed',
      'none',
      'other_third_party',
    ])
    .nullable(),
  bank_account_name: z.string().nullable(),
  bank_account_number: z.string().nullable(),
  bank_account_type: z.enum(['checking', 'savings']).nullable(),
  bank_setup: z.enum(['no', 'yes']).nullable(),
  business_expense_reimbursement: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  cafeteria_plan_benefits: z.enum(['applicable', 'not_applicable']).nullable(),
  cash_service_level: z.enum(['full', 'variances_only']).nullable(),
  chaplain_housing: z.enum(['applicable', 'not_applicable']).nullable(),
  check_date: z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date()),
  clergy_housing_poverty_vow: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  company_effective_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullable(),
  company_group_name: z.string().nullable(),
  company_name: z.string(),
  company_setup: z.enum(['no', 'yes']),
  company_start_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date()),
  company_status: z.enum(['active', 'inactive']),
  dependent_care_assistance: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  disbursement_ach_bank_destination: z.string().nullable(),
  disbursement_bank_account_name: z.string().nullable(),
  disbursement_bank_account_number: z.string().nullable(),
  disbursement_bank_routing_number: z.string().nullable(),
  draft_days: z.number().nullable(),
  employee_stock_purchase_plan: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_401k: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_sep_ira: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  employer_contribution_simple: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  exempt_501c3_organization: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  fein: z.string().nullable(),
  fein_type: z
    .enum([
      'applied_for',
      'common_pay_child',
      'common_pay_parent',
      'registered',
    ])
    .nullable(),
  filer_944: z.enum(['no', 'yes']).nullable(),
  foreign_source_income: z.enum(['applicable', 'not_applicable']).nullable(),
  group_term_life_insurance: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  hsa: z.enum(['applicable', 'not_applicable']).nullable(),
  kind_of_employer: z
    .enum([
      'federal_government',
      'none',
      'state_local_government',
      'state_local_tax_exempt',
      'tax_exempt',
    ])
    .nullable(),
  meals_furnished_in_kind: z.enum(['applicable', 'not_applicable']).nullable(),
  naics_code: z.string().nullable(),
  name_control: z.string().nullable(),
  next_check_date: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullable(),
  non_taxable_fringe_payments: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  nonqualified_deferred_comp: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_election_workers: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_family_employees: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_general_partnership: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_hospital_interns: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payments_to_hospital_patients: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  payroll_code: z.string(),
  payroll_description: z.string().nullable(),
  payroll_run_id: z.string().nullable(),
  public_transportation_non_tax: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  qualified_moving_expense: z.enum(['applicable', 'not_applicable']).nullable(),
  quarterly_wage_reporting: z.enum(['no', 'yes']).nullable(),
  record_type: z.string(),
  reporting_payroll_code: z.string().nullable(),
  service_level: z.enum(['balance_only', 'full_service', 'return_only']),
  short_reporting_payroll_code: z.string().nullable(),
  sick_pay: z.enum(['applicable', 'not_applicable']).nullable(),
  state_govt_employee_salaries: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  student_exempt: z.enum(['applicable', 'not_applicable']).nullable(),
  sub_type: z.string(),
  supplemental_unemployment_benefits: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  tax_liabilities: z.enum(['no', 'yes']),
  transit_routing_number: z.string().nullable(),
  variance_payroll_code: z.enum(['no', 'yes']).nullable(),
  wage_attachment_flag: z.enum(['no', 'yes']).nullable(),
  wc_housing_employment_condition: z
    .enum(['applicable', 'not_applicable'])
    .nullable(),
  workers_compensation: z.enum(['applicable', 'not_applicable']).nullable(),
  worksite_reporting: z.enum(['no', 'yes']).nullable(),
  year_end_employee_filing: z.enum(['no', 'yes']).nullable(),
})

export type PayrollHeaderType = z.infer<typeof payroll_header>
export type InsertablePayrollHeaderType = z.infer<
  typeof insertable_payroll_header
>
export type UpdateablePayrollHeaderType = z.infer<
  typeof updateable_payroll_header
>
export type SelectablePayrollHeaderType = z.infer<
  typeof selectable_payroll_header
>
