// @ts-nocheck
// @ts-ignore
import { nanoid, teamNanoid, userNanoid } from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const payroll_healthcheck_logs = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  errors: z.string().trim().min(1),
  id: z.number().optional(),
  pay_cycle_month: z.number(),
  pay_cycle_period: z.number(),
  pay_cycle_year: z.number(),
  payroll_program: z.enum([
    'riseworks_eor_us',
    'riseworks_inc',
    'riseworks_pps_us',
  ]),
  rise_account: z.string().trim().min(1),
  run_id: z.string().trim().min(1),
  success: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  target_account: z.enum(['employee_payroll', 'team_payroll']),
  team_nanoid: teamNanoid,
  user_nanoid: userNanoid.nullish(),
})

export const insertable_payroll_healthcheck_logs = z.object({
  errors: z.string().trim().min(1),
  pay_cycle_month: z.number(),
  pay_cycle_period: z.number(),
  pay_cycle_year: z.number(),
  payroll_program: z.enum([
    'riseworks_eor_us',
    'riseworks_inc',
    'riseworks_pps_us',
  ]),
  rise_account: z.string().trim().min(1),
  run_id: z.string().trim().min(1),
  success: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  target_account: z.enum(['employee_payroll', 'team_payroll']),
  team_nanoid: teamNanoid,
  user_nanoid: userNanoid.nullish(),
})

export const updateable_payroll_healthcheck_logs = z.object({
  errors: z.string().trim().min(1).optional(),
  pay_cycle_month: z.number().optional(),
  pay_cycle_period: z.number().optional(),
  pay_cycle_year: z.number().optional(),
  payroll_program: z
    .enum(['riseworks_eor_us', 'riseworks_inc', 'riseworks_pps_us'])
    .optional(),
  rise_account: z.string().trim().min(1).optional(),
  run_id: z.string().trim().min(1).optional(),
  success: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional(),
  target_account: z.enum(['employee_payroll', 'team_payroll']).optional(),
  team_nanoid: teamNanoid.optional(),
  user_nanoid: userNanoid.nullish(),
})

export const selectable_payroll_healthcheck_logs = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  errors: z.string(),
  id: z.number().optional(),
  pay_cycle_month: z.number(),
  pay_cycle_period: z.number(),
  pay_cycle_year: z.number(),
  payroll_program: z.enum([
    'riseworks_eor_us',
    'riseworks_inc',
    'riseworks_pps_us',
  ]),
  rise_account: z.string(),
  run_id: z.string(),
  success: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  target_account: z.enum(['employee_payroll', 'team_payroll']),
  team_nanoid: teamNanoid,
  user_nanoid: userNanoid.nullish(),
})

export type PayrollHealthcheckLogsType = z.infer<
  typeof payroll_healthcheck_logs
>
export type InsertablePayrollHealthcheckLogsType = z.infer<
  typeof insertable_payroll_healthcheck_logs
>
export type UpdateablePayrollHealthcheckLogsType = z.infer<
  typeof updateable_payroll_healthcheck_logs
>
export type SelectablePayrollHealthcheckLogsType = z.infer<
  typeof selectable_payroll_healthcheck_logs
>
