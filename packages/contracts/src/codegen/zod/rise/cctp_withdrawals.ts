// @ts-nocheck
// @ts-ignore
import { nanoid, withdrawNanoid, transactionNanoid } from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const cctp_withdrawals = z.object({
  attestation: z.string().trim().nullish(),
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  domain: z.string().trim().min(1),
  nanoid: withdrawNanoid,
  status: z
    .enum(['complete', 'failed', 'pending'])
    .optional()
    .default('pending'),
  tx_hash: z.string().trim().nullish(),
  tx_nanoid: transactionNanoid.nullable(),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
})

export const insertable_cctp_withdrawals = z.object({
  attestation: z.string().trim().nullish(),
  domain: z.string().trim().min(1),
  nanoid: withdrawNanoid,
  status: z
    .enum(['complete', 'failed', 'pending'])
    .optional()
    .default('pending'),
  tx_hash: z.string().trim().nullish(),
  tx_nanoid: transactionNanoid.nullable(),
})

export const updateable_cctp_withdrawals = z.object({
  attestation: z.string().trim().nullish(),
  domain: z.string().trim().min(1).optional(),
  nanoid: withdrawNanoid.optional(),
  status: z
    .enum(['complete', 'failed', 'pending'])
    .optional()
    .default('pending'),
  tx_hash: z.string().trim().nullish(),
  tx_nanoid: transactionNanoid.nullable(),
})

export const selectable_cctp_withdrawals = z.object({
  attestation: z.string().nullable(),
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  domain: z.string(),
  nanoid: withdrawNanoid,
  status: z.enum(['complete', 'failed', 'pending']),
  tx_hash: z.string().nullable(),
  tx_nanoid: transactionNanoid.nullable(),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
})

export type CctpWithdrawalsType = z.infer<typeof cctp_withdrawals>
export type InsertableCctpWithdrawalsType = z.infer<
  typeof insertable_cctp_withdrawals
>
export type UpdateableCctpWithdrawalsType = z.infer<
  typeof updateable_cctp_withdrawals
>
export type SelectableCctpWithdrawalsType = z.infer<
  typeof selectable_cctp_withdrawals
>
