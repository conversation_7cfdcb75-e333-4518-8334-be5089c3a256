// @ts-nocheck
// @ts-ignore
import {
  nanoid,
  webhookEndpointNanoid,
  webhookEventNanoid,
  webhookDeliveryNanoid,
} from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const webhook_deliveries = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  error_message: z.string().trim().nullish(),
  event_nanoid: webhookEventNanoid,
  nanoid: webhookDeliveryNanoid,
  response_body: z.record(z.any()).nullish(),
  response_code: z.number().nullish(),
  status: z
    .enum(['failed', 'queued', 'retrying', 'success'])
    .optional()
    .default('queued'),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  webhook_nanoid: webhookEndpointNanoid,
})

export const insertable_webhook_deliveries = z.object({
  error_message: z.string().trim().nullish(),
  event_nanoid: webhookEventNanoid,
  nanoid: webhookDeliveryNanoid,
  response_body: z.record(z.any()).nullish(),
  response_code: z.number().nullish(),
  status: z
    .enum(['failed', 'queued', 'retrying', 'success'])
    .optional()
    .default('queued'),
  webhook_nanoid: webhookEndpointNanoid,
})

export const updateable_webhook_deliveries = z.object({
  error_message: z.string().trim().nullish(),
  event_nanoid: webhookEventNanoid.optional(),
  nanoid: webhookDeliveryNanoid.optional(),
  response_body: z.record(z.any()).nullish(),
  response_code: z.number().nullish(),
  status: z
    .enum(['failed', 'queued', 'retrying', 'success'])
    .optional()
    .default('queued'),
  webhook_nanoid: webhookEndpointNanoid.optional(),
})

export const selectable_webhook_deliveries = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  error_message: z.string().nullable(),
  event_nanoid: webhookEventNanoid,
  nanoid: webhookDeliveryNanoid,
  response_body: z.record(z.any()).nullish(),
  response_code: z.number().nullable(),
  status: z.enum(['failed', 'queued', 'retrying', 'success']),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  webhook_nanoid: webhookEndpointNanoid,
})

export type WebhookDeliveriesType = z.infer<typeof webhook_deliveries>
export type InsertableWebhookDeliveriesType = z.infer<
  typeof insertable_webhook_deliveries
>
export type UpdateableWebhookDeliveriesType = z.infer<
  typeof updateable_webhook_deliveries
>
export type SelectableWebhookDeliveriesType = z.infer<
  typeof selectable_webhook_deliveries
>
