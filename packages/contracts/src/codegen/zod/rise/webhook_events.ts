// @ts-nocheck
// @ts-ignore
import { riseid, nanoid, webhookEventNanoid } from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const webhook_events = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  event_type: z.enum([
    'account_duplicated.detected',
    'deposit.deposit_received',
    'invites.invite_accepted',
    'pay_schedules.pay_schedule_created',
    'payee.riseid_address_updated',
    'payment.payment_sent',
  ]),
  nanoid: webhookEventNanoid,
  payload: z.record(z.any()),
  version: z.string().trim().min(1),
})

export const insertable_webhook_events = z.object({
  event_type: z.enum([
    'account_duplicated.detected',
    'deposit.deposit_received',
    'invites.invite_accepted',
    'pay_schedules.pay_schedule_created',
    'payee.riseid_address_updated',
    'payment.payment_sent',
  ]),
  nanoid: webhookEventNanoid,
  payload: z.record(z.any()),
  version: z.string().trim().min(1),
})

export const updateable_webhook_events = z.object({
  event_type: z
    .enum([
      'account_duplicated.detected',
      'deposit.deposit_received',
      'invites.invite_accepted',
      'pay_schedules.pay_schedule_created',
      'payee.riseid_address_updated',
      'payment.payment_sent',
    ])
    .optional(),
  nanoid: webhookEventNanoid.optional(),
  payload: z.record(z.any()).optional(),
  version: z.string().trim().min(1).optional(),
})

export const selectable_webhook_events = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  event_type: z.enum([
    'account_duplicated.detected',
    'deposit.deposit_received',
    'invites.invite_accepted',
    'pay_schedules.pay_schedule_created',
    'payee.riseid_address_updated',
    'payment.payment_sent',
  ]),
  nanoid: webhookEventNanoid,
  payload: z.record(z.any()),
  version: z.string(),
})

export type WebhookEventsType = z.infer<typeof webhook_events>
export type InsertableWebhookEventsType = z.infer<
  typeof insertable_webhook_events
>
export type UpdateableWebhookEventsType = z.infer<
  typeof updateable_webhook_events
>
export type SelectableWebhookEventsType = z.infer<
  typeof selectable_webhook_events
>
