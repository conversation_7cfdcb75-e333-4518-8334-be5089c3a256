// @ts-nocheck
// @ts-ignore
import { nanoid, userNanoid } from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const users_rsk = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  has_iframe_rsk: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  has_passkey: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: z.string().trim().nullish(),
  nanoid: userNanoid,
  reset_status: z
    .enum(['completed', 'failed', 'pending', 'started'])
    .optional()
    .default('completed'),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  verified: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  verified_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
})

export const insertable_users_rsk = z.object({
  has_iframe_rsk: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  has_passkey: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: z.string().trim().nullish(),
  nanoid: userNanoid,
  reset_status: z
    .enum(['completed', 'failed', 'pending', 'started'])
    .optional()
    .default('completed'),
  verified: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  verified_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
})

export const updateable_users_rsk = z.object({
  has_iframe_rsk: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  has_passkey: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: z.string().trim().nullish(),
  nanoid: userNanoid.optional(),
  reset_status: z
    .enum(['completed', 'failed', 'pending', 'started'])
    .optional()
    .default('completed'),
  verified: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  verified_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullish(),
})

export const selectable_users_rsk = z.object({
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  has_iframe_rsk: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  has_passkey: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  last_modified_by: z.string().nullable(),
  nanoid: userNanoid,
  reset_status: z.enum(['completed', 'failed', 'pending', 'started']),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  verified: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  verified_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .nullable(),
})

export type UsersRskType = z.infer<typeof users_rsk>
export type InsertableUsersRskType = z.infer<typeof insertable_users_rsk>
export type UpdateableUsersRskType = z.infer<typeof updateable_users_rsk>
export type SelectableUsersRskType = z.infer<typeof selectable_users_rsk>
