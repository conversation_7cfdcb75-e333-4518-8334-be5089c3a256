// @ts-nocheck
// @ts-ignore
import {
  nanoid,
  companyNanoid,
  teamNanoid,
  userNanoid,
} from '../../../brands.js'
// this file was generated with `pnpm codegen`, do not edit it manually
import { z } from 'zod'

export const webhook_endpoints = z.object({
  company_nanoid: companyNanoid,
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  created_by: userNanoid,
  events: z.string().array(),
  is_active: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(true),
  is_removed: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: userNanoid.nullish(),
  nanoid: z.string().trim().min(1),
  secret_encrypted: z.string().trim().min(1),
  team_nanoid: teamNanoid.nullish(),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  url: z.string().trim().min(1),
})

export const insertable_webhook_endpoints = z.object({
  company_nanoid: companyNanoid,
  created_by: userNanoid,
  events: z.string().array(),
  is_active: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(true),
  is_removed: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: userNanoid.nullish(),
  nanoid: z.string().trim().min(1),
  secret_encrypted: z.string().trim().min(1),
  team_nanoid: teamNanoid.nullish(),
  url: z.string().trim().min(1),
})

export const updateable_webhook_endpoints = z.object({
  company_nanoid: companyNanoid.optional(),
  created_by: userNanoid.optional(),
  events: z.string().array().optional(),
  is_active: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(true),
  is_removed: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean())
    .optional()
    .default(false),
  last_modified_by: userNanoid.nullish(),
  nanoid: z.string().trim().min(1).optional(),
  secret_encrypted: z.string().trim().min(1).optional(),
  team_nanoid: teamNanoid.nullish(),
  url: z.string().trim().min(1).optional(),
})

export const selectable_webhook_endpoints = z.object({
  company_nanoid: companyNanoid,
  created_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  created_by: userNanoid,
  events: z.string().array(),
  is_active: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  is_removed: z
    .union([z.number(), z.string(), z.boolean()])
    .pipe(z.coerce.boolean()),
  last_modified_by: userNanoid.nullish(),
  nanoid: z.string(),
  secret_encrypted: z.string(),
  team_nanoid: teamNanoid.nullish(),
  updated_at: z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date())
    .optional(),
  url: z.string(),
})

export type WebhookEndpointsType = z.infer<typeof webhook_endpoints>
export type InsertableWebhookEndpointsType = z.infer<
  typeof insertable_webhook_endpoints
>
export type UpdateableWebhookEndpointsType = z.infer<
  typeof updateable_webhook_endpoints
>
export type SelectableWebhookEndpointsType = z.infer<
  typeof selectable_webhook_endpoints
>
