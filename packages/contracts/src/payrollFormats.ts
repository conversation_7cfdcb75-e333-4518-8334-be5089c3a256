import { z } from 'zod'
import {
  employeePayrollRiseAccount,
  teamNanoid,
  teamPayrollRiseAccount,
  userNanoid,
} from './brands.js'
import { healthcare_payroll_settings } from './codegen/zod/rise/healthcare_payroll_settings.js'
import { retirement_payroll_settings } from './codegen/zod/rise/retirement_payroll_settings.js'
import {
  isoDate,
  payCycle,
  risePayrollProgram,
  stipends,
  variableCompensations,
  type StipendType,
} from './formats.js'

export const stipendsMap: Record<
  StipendType,
  { label: string; isTaxable: boolean }
> = {
  accident_health_benefits: {
    label: 'Accident and health benefits',
    isTaxable: false,
  },
  achievement_awards: { label: 'Achievement awards', isTaxable: true },
  adoption_assistance: { label: 'Adoption assistance', isTaxable: false },
  athletic_facilities: { label: 'Athletic facilities', isTaxable: true },
  de_minimis_minimal_benefits: {
    label: 'De minimis (minimal) benefits',
    isTaxable: false,
  },
  dependent_care_assistance: {
    label: 'Dependent care assistance',
    isTaxable: true,
  },
  educational_assistance: { label: 'Educational assistance', isTaxable: true },
  employee_discounts: { label: 'Employee discounts', isTaxable: true },
  employee_stock_options: { label: 'Employee stock options', isTaxable: true },
  employer_provided_cell_phones: {
    label: 'Employer-provided cell phones',
    isTaxable: true,
  },
  group_term_life_insurance_coverage: {
    label: 'Group-term life insurance coverage',
    isTaxable: true,
  },
  hsas: { label: 'HSAs', isTaxable: false },
  lodging_on_your_business_premises: {
    label: 'Lodging on your business premises',
    isTaxable: true,
  },
  meals: { label: 'Meals', isTaxable: true },
  no_additional_cost_services: {
    label: 'No-additional-cost services',
    isTaxable: true,
  },
  retirement_planning_services: {
    label: 'Retirement planning services',
    isTaxable: false,
  },
  transportation_commuting_benefits: {
    label: 'Transportation (commuting) benefits',
    isTaxable: false,
  },
  tuition_reduction: { label: 'Tuition reduction', isTaxable: true },
  working_condition_benefits: {
    label: 'Working condition benefits',
    isTaxable: false,
  },
} as const

export const payrollProcessData = z.object({
  team_nanoid: teamNanoid,
  employee_nanoid: userNanoid,
  pay_cycle: payCycle,
  payroll_program: risePayrollProgram,
  employee_payroll_rise_account: employeePayrollRiseAccount,
  team_payroll_rise_account: teamPayrollRiseAccount,
  payments: z.object({
    gross_wages: z.number(),
    days_worked: z.array(isoDate),
    taxes: z.object({
      values: z.array(
        z.object({
          unique_tax_id: z.string(),
          tax_name: z.string(),
          tax_amount: z.number(),
        }),
      ),
      employee_total: z.number(),
      employer_total: z.number(),
      total: z.number(),
    }),
    healthcare: z.object({
      values: z.array(
        z.object({
          type: healthcare_payroll_settings.shape.type,
          amount: z.number(),
          origin_type: z.enum(['employee', 'employer']),
        }),
      ),
      employee_total: z.number(),
      employer_total: z.number(),
      total: z.number(),
    }),
    retirement: z.object({
      values: z.array(
        z.object({
          retirement_type: retirement_payroll_settings.shape.retirement_type,
          amount: z.number(),
          origin_type: z.enum(['employee', 'employer']),
        }),
      ),
      employee_total: z.number(),
      employer_total: z.number(),
      total: z.number(),
    }),
    extras: z.object({
      signing_bonus: z.number(),
      stipends: stipends,
      variable_compensations: z.array(
        variableCompensations.element.pick({
          name: true,
          type: true,
          amount: true,
        }),
      ),
      one_off_bonuses: z.array(
        z.object({
          id: z.number(),
          type: z.string(),
          amount: z.number(),
          pay_at: isoDate,
        }),
      ),
      total: z.number(),
    }),
  }),
})
export type PayrollProcessData = z.infer<typeof payrollProcessData>
