import { z } from 'zod'
import {
  companyNanoid,
  companyRiseAccount,
  teamNanoid,
  teamRiseAccount,
} from './brands.js'

const BitcoinNetworks = {
  Mainnet: 'mainnet',
  Testnet: 'testnet',
} as const

const BitcoinAddressTypes = {
  NativeSegwit: 'native-segwit', // Rise deposit address type (Segwit P2WPKH)
  Taproot: 'taproot', // Expected Chainflip deposit channel address type (Taproot P2TR)
} as const

const EntityTypeIndices = {
  Company: 0,
  Team: 1,
} as const

const mainnetSegwitRegex = z.string().regex(/^bc1q[a-zA-Z0-9]{38}$/)
const testnetSegwitRegex = z.string().regex(/^tb1q[a-zA-Z0-9]{38}$/)
const mainnetTaprootRegex = z.string().regex(/^bc1p[a-zA-Z0-9]{58}$/)
const testnetTaprootRegex = z.string().regex(/^tb1p[a-zA-Z0-9]{58}$/)
const mainnetRegex = mainnetSegwitRegex.or(mainnetTaprootRegex)
const testnetRegex = testnetSegwitRegex.or(testnetTaprootRegex)
const segwitRegex = mainnetSegwitRegex.or(testnetSegwitRegex)
type SegwitAddress = z.infer<typeof segwitRegex>
const bitcoinAddressRegex = mainnetRegex.or(testnetRegex)
type BitcoinAddress = z.infer<typeof bitcoinAddressRegex>
const bitcoinAddressRefiner = z
  .object({
    address: bitcoinAddressRegex,
    addressType: z.nativeEnum(BitcoinAddressTypes).optional(),
    network: z.nativeEnum(BitcoinNetworks).optional(),
  })
  .superRefine((expected, ctx) => {
    const network = mainnetRegex.safeParse(expected.address).success
      ? BitcoinNetworks.Mainnet
      : BitcoinNetworks.Testnet
    if (expected.network && expected.network !== network) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Expected network (${expected.network}) but got (${network})`,
      })
    }
    const addressType = segwitRegex.safeParse(expected.address).success
      ? BitcoinAddressTypes.NativeSegwit
      : BitcoinAddressTypes.Taproot
    if (expected.addressType && expected.addressType !== addressType) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Expected address type (${expected.addressType}) but got (${addressType})`,
      })
    }
  })
const bitcoinAddressParser = bitcoinAddressRegex.transform((address) => {
  const network = mainnetRegex.safeParse(address).success
    ? BitcoinNetworks.Mainnet
    : BitcoinNetworks.Testnet
  const addressType = segwitRegex.safeParse(address).success
    ? BitcoinAddressTypes.NativeSegwit
    : BitcoinAddressTypes.Taproot
  const refinedAddress = bitcoinAddressRefiner.parse({
    address,
    network,
    addressType,
  })
  return {
    ...refinedAddress,
    address,
    addressType,
    network,
  }
})

const riseAccountRegex = companyRiseAccount.or(teamRiseAccount)
const entityNanoidRegex = companyNanoid.or(teamNanoid)
const entityIndexRefiner = z.coerce.number().int().nonnegative().max(1)
type EntityIndexInt = z.infer<typeof entityIndexRefiner>
const entityIndexTransformer = entityNanoidRegex.transform((nanoid) =>
  companyNanoid.safeParse(nanoid).success
    ? entityIndexRefiner.parse(EntityTypeIndices.Company)
    : entityIndexRefiner.parse(EntityTypeIndices.Team),
)
const depositIndexInt = z.coerce.number().int().nonnegative().max(0)
type DepositIndex = z.infer<typeof depositIndexInt>

// Convert Rise account (EVM address) into max 32-bit integer
const MAX_NON_HARDENED_BIP32 = 2 ** 31 - 1
const riseAccountIndexRefiner = z.coerce
  .number()
  .int()
  .nonnegative()
  .max(MAX_NON_HARDENED_BIP32, {
    message: 'Index exceeds non-hardened BIP32 range (0 to 2^31 - 1)',
  })
const riseAccountIndexTransformer = riseAccountRegex.transform(
  (riseAccount) => {
    const hex = riseAccount.toLowerCase().replace('0x', '')
    const index =
      Number.parseInt(hex.slice(0, 8), 16) % (MAX_NON_HARDENED_BIP32 + 1)
    return riseAccountIndexRefiner.parse(index)
  },
)

const ownerIdRefiner = z
  .string()
  .regex(/^\w+:\w+:\w+:\w+:\d+$/)
  .superRefine((ownerId, ctx) => {
    const [
      expectedDepositAddress,
      expectedChangeAddress,
      expectedEntityIndex,
      expectedRiseAccount,
      expectedDepositIndex,
    ] = ownerId.split(':')
    if (!segwitRegex.safeParse(expectedDepositAddress).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Could not extract valid depositAddress: ${expectedDepositAddress}`,
      })
    }
    if (!segwitRegex.safeParse(expectedChangeAddress).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Could not extract valid changeAddress: ${expectedChangeAddress}`,
      })
    }
    if (!entityIndexRefiner.safeParse(expectedEntityIndex).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Could not extract valid entityIndex: ${expectedEntityIndex}`,
      })
    }
    if (!riseAccountRegex.safeParse(expectedRiseAccount).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Could not extract valid riseAccount: ${expectedRiseAccount}`,
      })
    }
    if (!depositIndexInt.safeParse(expectedDepositIndex).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Could not extract valid depositIndex: ${expectedDepositIndex}`,
      })
    }
  })
type OwnerId = z.infer<typeof ownerIdRefiner>
const ownerSchema = z.object({
  entityIndex: entityIndexRefiner,
  riseAccount: riseAccountRegex,
  riseAccountIndex: riseAccountIndexRefiner.optional(),
  depositIndex: depositIndexInt,
  network: z.nativeEnum(BitcoinNetworks).optional(),
  addressType: z.nativeEnum(BitcoinAddressTypes).optional(),
  depositAddress: segwitRegex.optional(),
  changeAddress: segwitRegex.optional(),
  ownerId: ownerIdRefiner.optional(),
})
type StrippedOwner = z.infer<typeof ownerSchema>
// Technically not stripped, but differentiates it from the TransformedOwner
const transformedOwnerSchema = ownerSchema.extend({
  riseAccountIndex: riseAccountIndexRefiner,
  network: z.nativeEnum(BitcoinNetworks),
  addressType: z.nativeEnum(BitcoinAddressTypes),
})
type TransformedOwner = z.infer<typeof transformedOwnerSchema>
const validatedBitcoinOwnerSchema = transformedOwnerSchema.extend({
  depositAddress: segwitRegex,
  changeAddress: segwitRegex,
  ownerId: ownerIdRefiner,
})
type ValidatedOwner = z.infer<typeof validatedBitcoinOwnerSchema>

const bitcoinTxHashRegex = z.string().regex(/^[0-9a-fA-F]{64}$/)
type BitcoinTxHash = z.infer<typeof bitcoinTxHashRegex>
const voutIndexInt = z.coerce.number().int().nonnegative()
type VoutIndex = z.infer<typeof voutIndexInt>

const valueIntTransformer = z.coerce
  .bigint()
  .nonnegative()
  .refine(
    (num) =>
      num > BigInt(Number.MAX_SAFE_INTEGER) ||
      num < BigInt(Number.MIN_SAFE_INTEGER),
    {
      message: 'Value is out of safe number range',
    },
  )
  .transform((num) => z.coerce.number().nonnegative().int().parse(Number(num)))
  .or(z.coerce.number().nonnegative().int())
  .or(
    z.coerce
      .number()
      .nonnegative()
      .transform((num) => Math.round(num)),
  )
type ValueInt = z.infer<typeof valueIntTransformer>

const blockHeightInt = z.coerce.number().int().nonnegative().or(z.literal(-1))
type BlockHeight = z.infer<typeof blockHeightInt>
const outputTypeSchema = z.coerce.number().int().nonnegative().max(1)
const bitcoinOutputRefiner = z
  .string()
  .regex(/^\w+:\d$/)
  .superRefine((output, ctx) => {
    const [addressString, outputTypeString] = output.split(':')
    if (!bitcoinAddressRegex.safeParse(addressString).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid address: ${addressString}`,
      })
    }
    if (!outputTypeSchema.safeParse(outputTypeString).success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid output type: ${outputTypeString}`,
      })
    }
  })
type BitcoinOutput = z.infer<typeof bitcoinOutputRefiner>
// In the case of a payment input, voutIndex actually refers to the vinIndex, and txHash refers to the tx in which it is spent (i.e. not source tx)
const bitcoinOutputTransformer = bitcoinOutputRefiner.transform((output) => {
  const [addressString, outputTypeString] = output.split(':')
  const address = bitcoinAddressRegex.parse(addressString)
  const outputType =
    outputTypeSchema.parse(outputTypeString) === 0 ? 'deposit' : 'payment'
  return {
    address,
    outputType,
  }
})
type TransformedBitcoinOutput = z.infer<typeof bitcoinOutputTransformer>

const blockHashRegex = bitcoinTxHashRegex // Block hashes are the same format as tx hashes, except they usually start with zeroes indicating the difficulty level
type BlockHash = z.infer<typeof blockHashRegex>
const confirmedBlockDataSchema = z.object({
  confirmed: z.literal(true),
  blockHeight: blockHeightInt,
  blockHash: blockHashRegex,
})
const blockDataRefiner = z
  .object({
    confirmed: z.boolean(),
    blockHeight: blockHeightInt.optional(),
    blockHash: blockHashRegex.optional(),
  })
  .refine(
    ({ confirmed, blockHeight, blockHash }) => {
      if (confirmed) {
        return (
          blockHeight !== undefined &&
          blockHeight > 0 &&
          blockHash !== undefined
        )
      }
      return true
    },
    {
      message: 'Block height and hash are required for confirmed blocks',
    },
  )
  .transform((data) => {
    if (data.confirmed) {
      return confirmedBlockDataSchema.parse(data)
    }
    return {
      confirmed: false,
      blockHeight: undefined,
      blockHash: undefined,
    }
  })

const valueBigInt = z.coerce.bigint().nonnegative()
type ValueBigInt = z.infer<typeof valueBigInt>
const txDataSchema = z.object({
  txHash: bitcoinTxHashRegex.optional(),
  fee: valueBigInt.optional(),
})
const broadcastedTxDataSchema = txDataSchema.extend({
  txHash: bitcoinTxHashRegex, // Not optional for broadcasted transactions
})

const voutSchema = z.object({
  voutIndex: voutIndexInt,
  address: z.string(), // bitcoinAddressRegex is limited to addresses we can receive to (i.e. native segwit) and send to (i.e. taproot chainflip deposit channel)
  value: valueBigInt,
})
type Vout = z.infer<typeof voutSchema>
const utxoSchema = voutSchema.extend({
  address: segwitRegex,
  tx: broadcastedTxDataSchema,
  block: blockDataRefiner,
})
type Utxo = z.infer<typeof utxoSchema> // Utxo is not super self-explanatory, but it's better than broadcasted-output-with-tx-and-block-data
const mempoolAddressUtxosTransformer = z
  .object({
    address: segwitRegex, // not included in response
    utxos: z.array(
      z.object({
        txid: z.string(),
        vout: z.coerce.number(),
        value: z.coerce.number(),
        status: z.object({
          confirmed: z.coerce.boolean(),
          block_height: z.coerce.number().nullish(),
          block_hash: z.coerce.string().nullish(),
        }),
      }),
    ),
  })
  .transform(({ address, utxos }) =>
    utxos.map((utxo) =>
      utxoSchema.parse({
        address: address,
        txHash: utxo.txid,
        voutIndex: utxo.vout,
        value: utxo.value,
        tx: broadcastedTxDataSchema.parse({
          txHash: utxo.txid,
          fee: utxo.value,
        }),
        block: blockDataRefiner.parse({
          confirmed: utxo.status.confirmed,
          blockHeight: utxo.status.block_height,
          blockHash: utxo.status.block_hash,
        }),
      }),
    ),
  )

const blockSummarySchema = confirmedBlockDataSchema.extend({
  txCount: z.coerce.number().int().nonnegative(),
})
type BlockSummary = z.infer<typeof blockSummarySchema>
const mempoolBlockSummariesTransformer = z
  .array(
    z.object({
      id: z.string(),
      height: z.coerce.number().int().nonnegative(),
      tx_count: z.coerce.number().int().nonnegative(),
    }),
  )
  .transform((blockSummaries) =>
    blockSummaries.map((blockSummary) => {
      const blockData = confirmedBlockDataSchema.parse({
        confirmed: true,
        blockHeight: blockSummary.height,
        blockHash: blockSummary.id,
      })
      return blockSummarySchema.parse({
        ...blockData,
        txCount: blockSummary.tx_count,
      })
    }),
  )

const vinSchema = z.object({
  vinIndex: voutIndexInt,
  sourceTxHash: bitcoinTxHashRegex,
  sourceTxVoutIndex: voutIndexInt,
  sourceAddress: z.string(),
  value: valueBigInt,
})
type Vin = z.infer<typeof vinSchema>
const bitcoinTxSchema = broadcastedTxDataSchema.extend({
  fee: valueBigInt,
  vin: z.array(vinSchema), // Empty in case of coinbase
  vout: z.array(voutSchema), // Empty in case of no valid payment outputs
})
type BitcoinTx = z.infer<typeof bitcoinTxSchema>
const blockTxSchema = bitcoinTxSchema.extend({
  blockTxIndex: voutIndexInt, // Block transaction indexes are same format as vout indicies
})
type BlockTx = z.infer<typeof blockTxSchema>
const mempoolBlockTxsTransformer = z
  .object({
    startTxIndex: z.coerce.number().int().nonnegative(), // not included in response
    blockTxs: z.array(
      z.object({
        txid: z.string(),
        vin: z.array(
          z.object({
            txid: z.string(),
            vout: z.coerce.number().int().nonnegative(),
            prevout: z
              .object({
                scriptpubkey_address: z.string().nullish(),
                value: z.coerce.number().int().nonnegative(),
              })
              .nullish(),
          }),
        ),
        vout: z.array(
          z.object({
            scriptpubkey_address: z.string().nullish(),
            value: z.coerce.number().int().nonnegative(),
          }),
        ),
        fee: z.coerce.number().int().nonnegative(),
      }),
    ),
  })
  .transform(({ startTxIndex, blockTxs }) =>
    blockTxs.map((blockTx, index) => {
      const vin = blockTx.vin
        .map((vin, vinIndex) => {
          if (!vin.prevout?.scriptpubkey_address) {
            return undefined
          }
          return vinSchema.parse({
            vinIndex,
            sourceTxHash: vin.txid,
            sourceTxVoutIndex: vin.vout,
            sourceAddress: vin.prevout.scriptpubkey_address,
            value: vin.prevout.value,
          })
        })
        .filter((vin) => vin !== undefined)
      const vout = blockTx.vout
        .map((vout, voutIndex) => {
          if (!vout.scriptpubkey_address) {
            return undefined
          }
          return voutSchema.parse({
            voutIndex,
            address: vout.scriptpubkey_address,
            value: vout.value,
          })
        })
        .filter((vout) => vout !== undefined)
      return blockTxSchema.parse({
        blockTxIndex: startTxIndex + index,
        txHash: blockTx.txid,
        vin,
        vout,
        fee: blockTx.fee,
      })
    }),
  )

const mempoolFeeRateTransformer = z
  .object({
    fastestFee: z.coerce.number().nonnegative(),
    halfHourFee: z.coerce.number().nonnegative(),
    hourFee: z.coerce.number().nonnegative(),
    economyFee: z.coerce.number().nonnegative(),
    minimumFee: z.coerce.number().nonnegative(),
  })
  .transform(({ fastestFee }) => valueIntTransformer.parse(fastestFee))

const psbtHexRefiner = z
  .string()
  .regex(/^[0-9a-f]{200,}$/i)
  .refine((hex) => hex.length % 2 === 0, {
    message: 'Hex string must be even length',
  })
  .refine((hex) => hex.toLowerCase().startsWith('70736274ff'), {
    message: 'Hex string must start with PSBT magic bytes',
  })
const psbtBufferTransformer = z
  .instanceof(Buffer)
  .transform((buffer) => psbtHexRefiner.parse(buffer.toString('hex')))
const psbtBase64Transformer = z
  .string()
  .regex(/^[A-Za-z0-9+/=]{100,}$/)
  .transform((base64) =>
    psbtBufferTransformer.parse(Buffer.from(base64, 'base64')),
  )
const psbtHexParser = psbtBufferTransformer
  .or(psbtBase64Transformer)
  .or(psbtHexRefiner)

export {
  bitcoinAddressParser,
  bitcoinAddressRefiner,
  bitcoinAddressRegex,
  BitcoinAddressTypes,
  BitcoinNetworks,
  bitcoinOutputRefiner,
  bitcoinOutputTransformer,
  bitcoinTxHashRegex,
  bitcoinTxSchema,
  blockHeightInt,
  depositIndexInt,
  type EntityIndexInt,
  entityIndexTransformer,
  EntityTypeIndices,
  mainnetRegex,
  mempoolAddressUtxosTransformer,
  mempoolBlockSummariesTransformer,
  mempoolBlockTxsTransformer,
  mempoolFeeRateTransformer,
  ownerIdRefiner,
  ownerSchema,
  psbtHexParser,
  riseAccountIndexTransformer,
  riseAccountRegex,
  segwitRegex,
  transformedOwnerSchema,
  utxoSchema,
  validatedBitcoinOwnerSchema,
  valueBigInt,
  valueIntTransformer,
  vinSchema,
  voutIndexInt,
  voutSchema,
  type BitcoinAddress,
  type BitcoinOutput,
  type BitcoinTx,
  type BitcoinTxHash,
  type BlockHash,
  type BlockHeight,
  type BlockSummary,
  type BlockTx,
  type DepositIndex,
  type OwnerId,
  type SegwitAddress,
  type StrippedOwner,
  type TransformedBitcoinOutput,
  type TransformedOwner,
  type Utxo,
  type ValidatedOwner,
  type ValueBigInt,
  type ValueInt,
  type Vin,
  type Vout,
  type VoutIndex,
}
