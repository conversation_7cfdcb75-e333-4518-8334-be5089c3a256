import { z } from 'zod'
import {
  bitcoinAddressRegex,
  bitcoinTxHashRegex,
  validatedBitcoinOwnerSchema,
  valueBigInt,
  valueIntTransformer,
} from './bitcoin.js'
import { companyNanoid, teamNanoid } from './brands.js'
import type {
  InsertableChainflipSwaps as InsertableSwap,
  SelectableChainflipSwaps as SelectableSwap,
} from './codegen/db/models_rise.js'
import { evmAddress as evmAddressRegex } from './formats.js'

const Chains = {
  Bitcoin: 'Bitcoin',
  Ethereum: 'Ethereum',
  Polkadot: 'Polkadot',
  Arbitrum: 'Arbitrum',
  Solana: 'Solana',
} as const

const solanaAddressRegex = z.string() // TODO: Add a real regex

const ChainRegexMap = {
  [Chains.Bitcoin]: bitcoinAddressRegex,
  [Chains.Ethereum]: evmAddressRegex,
  [Chains.Polkadot]: evmAddressRegex,
  [Chains.Arbitrum]: evmAddressRegex,
  [Chains.Solana]: solanaAddressRegex,
}

const Assets = {
  FLIP: 'FLIP',
  USDC: 'USDC',
  DOT: 'DOT',
  ETH: 'ETH',
  BTC: 'BTC',
  USDT: 'USDT',
  SOL: 'SOL',
} as const

const AssetDecimalMap = {
  [Assets.BTC]: 8,
  [Assets.DOT]: 10,
  [Assets.ETH]: 18,
  [Assets.FLIP]: 18,
  [Assets.SOL]: 9,
  [Assets.USDC]: 6,
  [Assets.USDT]: 6,
} as const

const InputChainsAndAssetsWithMinimumSwapAmounts = {
  [Chains.Bitcoin]: [[Assets.BTC, 70_000n]],
  [Chains.Polkadot]: [[Assets.DOT, 40_000_000_000n]],
  [Chains.Arbitrum]: [
    [Assets.ETH, 39_000_000_000_000_000n],
    [Assets.USDC, 10_000_000n],
  ],
  [Chains.Ethereum]: [
    [Assets.ETH, 10_000_000_000_000_000n],
    [Assets.FLIP, 40_000_000_000_000_000n],
    [Assets.USDC, 20_000_000n],
    [Assets.USDT, 20_000_000n],
  ],
  [Chains.Solana]: [
    [Assets.SOL, 68_000_000n],
    [Assets.USDC, 10_000_000n],
  ],
} as const
type InputChain = keyof typeof InputChainsAndAssetsWithMinimumSwapAmounts
type InputAsset =
  (typeof InputChainsAndAssetsWithMinimumSwapAmounts)[InputChain][number][0]
type InputChainAndAsset = {
  asset: InputAsset
  chain: InputChain
}

const OutputChainsAndAssets = {
  [Chains.Arbitrum]: [Assets.USDC],
} as const
type OutputChain = keyof typeof OutputChainsAndAssets
type OutputAsset = (typeof OutputChainsAndAssets)[OutputChain][number]
type OutputChainAndAsset = {
  asset: OutputAsset
  chain: OutputChain
}

const regularQuote = z.object({
  recommendedSlippageTolerancePercent: z.coerce.number().nonnegative(), // can be decimal, e.g. 1.5
  estimatedPrice: z.coerce.number().nonnegative(), // typically a string, coerced can have many decimal places
  egressAmount: z.coerce.number().nonnegative().int(), // output asset, expressed in base unit
})

const boostQuote = regularQuote.extend({
  estimatedBoostFeeBps: z.coerce.number().int().nonnegative(),
  maxBoostFeeBps: z.coerce.number().int().nonnegative(),
})

const quoteResponse = z.array(
  regularQuote.extend({
    boostQuote: boostQuote.optional(),
  }),
)

// Need the same logic for forcing to non-negative int
const bpsInt = valueIntTransformer
const roundedPriceInt = valueIntTransformer

const quoteSchema = z.object({
  estimatedOutputValue: valueBigInt,
  estimatedPrice: z.coerce.number().nonnegative(), // decimal
  recommendedSlippageToleranceBps: bpsInt,
  maxBoostFeeBps: bpsInt.optional(),
})

const quoteTransformer = quoteResponse.transform((quotes) =>
  quotes.map((data) => {
    const { boostQuote, ...regularQuote } = data
    const quote = boostQuote ? boostQuote : regularQuote
    const parsed = quoteSchema.parse({
      estimatedOutputValue: valueBigInt.parse(quote.egressAmount),
      estimatedPrice: quote.estimatedPrice,
      recommendedSlippageToleranceBps: bpsInt.parse(
        quote.recommendedSlippageTolerancePercent * 100,
      ),
      maxBoostFeeBps: boostQuote?.maxBoostFeeBps
        ? bpsInt.parse(boostQuote?.maxBoostFeeBps)
        : undefined,
    })
    return {
      ...parsed,
      maxBoostFeeBps: parsed.maxBoostFeeBps ?? undefined,
    }
  }),
)
type TransformedQuote = z.infer<typeof quoteTransformer>[number]

// https://docs.chainflip.io/swapping/integrations/running-a-broker/broker-api#price
const minPriceX128Transformer = z
  .object({
    inputAsset: z.nativeEnum(Assets),
    outputAsset: z.nativeEnum(Assets),
    estimatedPrice: z.coerce.number().nonnegative(),
    slippageToleranceBps: bpsInt,
  })
  .transform(
    ({ inputAsset, outputAsset, estimatedPrice, slippageToleranceBps }) => {
      const slippageToleranceDecimal = slippageToleranceBps / 10_000
      const minPrice = estimatedPrice * (1 - slippageToleranceDecimal)
      const assetDecimalDifference = Math.abs(
        AssetDecimalMap[inputAsset] - AssetDecimalMap[outputAsset],
      )
      const adjustedAssetDecimalDifference = 10 ** assetDecimalDifference
      const adjustedMinPrice = minPrice / adjustedAssetDecimalDifference
      const roundedAdjustedMinPrice = roundedPriceInt.parse(adjustedMinPrice)
      const Q128 = BigInt('0x100000000000000000000000000000000')
      // "the amount of quote [output] asset a single unit of the base asset is valued at."
      return (BigInt(roundedAdjustedMinPrice) * Q128).toString()
    },
  )

const channelIdPattern = new RegExp(
  `^\\d+-(${Object.values(Chains).join('|')})-\\d+$`,
)
const channelIdRegex = z.string().regex(channelIdPattern)
type ChannelId = z.infer<typeof channelIdRegex>
const channelAddressRegex = bitcoinAddressRegex
  .or(evmAddressRegex)
  .or(solanaAddressRegex)

const swapMetadataSchema = z.object({
  entityNanoid: companyNanoid.or(teamNanoid),
  inputAddresses: z.array(bitcoinAddressRegex),
  inputNetworkFee: valueBigInt,
  inputSentAt: z.coerce.date(),
  inputTxHash: bitcoinTxHashRegex, // Add more when we support other chains
  inputValue: valueBigInt,
  outputAddress: evmAddressRegex,
  previousSwapIds: z.array(channelIdRegex),
})
type SwapMetadata = z.infer<typeof swapMetadataSchema>

const pendingSwapStatuses = [
  'WAITING',
  'RECEIVING',
  'SENDING',
  'SENT',
  'SWAPPING',
] as SelectableSwap['status'][]

const swapTransformer = swapMetadataSchema
  .extend({
    validatedOwner: validatedBitcoinOwnerSchema,
    swap: z.object({
      state: z.string(),
      srcAsset: z.string(),
      srcChain: z.string(),
      destAsset: z.string(),
      destChain: z.string(),
      destAddress: z.string(),
      fees: z
        .array(
          z.object({
            chain: z.string(),
            asset: z.string(),
            type: z.literal('BOOST').or(z.string()), // unused
            amount: z.coerce.number().int().nonnegative(),
          }),
        )
        .nullish(),
      depositChannel: z
        .object({
          id: z.string(),
          depositAddress: z.string(),
          estimatedExpiryTime: z.coerce.number().int().nonnegative(), // unix ms
        })
        .nullish(),
      deposit: z
        .object({
          witnessedAt: z.coerce.number().int().nonnegative().nullish(), // unix ms
        })
        .nullish(),
      swapEgress: z
        .object({
          amount: z.coerce.number().int().nonnegative().nullish(),
          scheduledAt: z.coerce.number().int().nonnegative().nullish(), // unix ms
          witnessedAt: z.coerce.number().int().nonnegative().nullish(), // unix ms
          txRef: z.string().nullish(),
        })
        .nullish(),
      refundEgress: z
        .object({
          amount: z.coerce.number().int().nonnegative().nullish(),
          scheduledAt: z.coerce.number().int().nonnegative().nullish(), // unix ms
          txRef: z.string().nullish(),
        })
        .nullish(),
      fillOrKillParams: z
        .object({
          refundAddress: z.string(), // unused
          minPrice: z.coerce.number().nonnegative(), // decimal -- unused
        })
        .nullish(),
      boost: z
        .object({
          maxBoostFeeBps: z.coerce.number().int().nonnegative(),
        })
        .nullish(),
    }),
  })
  .transform(
    ({
      entityNanoid,
      validatedOwner,
      inputAddresses,
      inputNetworkFee,
      inputSentAt,
      inputTxHash,
      inputValue,
      outputAddress,
      previousSwapIds,
      swap,
    }) => {
      if (
        !(swap.depositChannel?.estimatedExpiryTime && inputAddresses.length)
      ) {
        return undefined
      }
      const feeMap = (swap.fees ?? []).reduce(
        (feeMap, fee) => {
          const asset = fee.asset as keyof typeof Assets
          feeMap[asset] += fee.amount
          return feeMap
        },
        {
          [Assets.FLIP]: 0,
          [Assets.USDC]: 0,
          [Assets.DOT]: 0,
          [Assets.ETH]: 0,
          [Assets.BTC]: 0,
          [Assets.USDT]: 0,
          [Assets.SOL]: 0,
        },
      )
      return {
        channel_address: swap.depositChannel.depositAddress,
        channel_expires_at: new Date(swap.depositChannel.estimatedExpiryTime),
        channel_id: swap.depositChannel.id,
        completed_at: swap.swapEgress?.witnessedAt
          ? new Date(swap.swapEgress.witnessedAt)
          : null,
        entity_nanoid: entityNanoid,
        error: null, // TODO: Figure out how SDK gets error reason
        failed_at: null, // TODO: Figure out how SDK gets failedAt
        fee_btc: feeMap[Assets.BTC] > 0 ? feeMap[Assets.BTC] : null,
        fee_dot: feeMap[Assets.DOT] > 0 ? feeMap[Assets.DOT] : null,
        fee_eth: feeMap[Assets.ETH] > 0 ? feeMap[Assets.ETH] : null,
        fee_flip: feeMap[Assets.FLIP] > 0 ? feeMap[Assets.FLIP] : null,
        fee_sol: feeMap[Assets.SOL] > 0 ? feeMap[Assets.SOL] : null,
        fee_usdc: feeMap[Assets.USDC] > 0 ? feeMap[Assets.USDC] : null,
        fee_usdt: feeMap[Assets.USDT] > 0 ? feeMap[Assets.USDT] : null,
        input_addresses: JSON.stringify(inputAddresses),
        input_asset: swap.srcAsset,
        input_chain: swap.srcChain,
        input_confirmed_at: swap.deposit?.witnessedAt
          ? new Date(swap.deposit.witnessedAt)
          : null,
        input_network_fee: valueIntTransformer.parse(inputNetworkFee),
        input_sent_at: inputSentAt,
        input_txid: inputTxHash,
        input_value: valueIntTransformer.parse(inputValue),
        output_address: outputAddress,
        output_asset: swap.destAsset,
        output_chain: swap.destChain,
        output_sent_at: swap.swapEgress?.scheduledAt
          ? new Date(swap.swapEgress.scheduledAt)
          : null,
        output_txid: swap.swapEgress?.txRef ?? null,
        output_value: swap.swapEgress?.amount ?? null,
        previous_swap_ids: JSON.stringify(previousSwapIds),
        quote_type: swap.boost?.maxBoostFeeBps ? 'boosted' : 'regular',
        refund_address: validatedOwner.changeAddress,
        refund_received_at: swap.refundEgress?.scheduledAt
          ? new Date(swap.refundEgress?.scheduledAt)
          : null,
        refund_txid: swap.refundEgress?.txRef ?? null,
        refund_value: swap.refundEgress?.amount ?? null,
        status: swap.state as SelectableSwap['status'],
      } as InsertableSwap
    },
  )

const swapChannelSchema = z.object({
  channelId: channelIdRegex,
  channelAddress: channelAddressRegex,
})
const swapChannelTransformer = z
  .object({
    result: z.object({
      data: z.object({
        json: z.object({
          id: z.string(),
          depositAddress: z.string(),
        }),
      }),
    }),
  })
  .transform((swapChannel) => {
    const { id, depositAddress } = swapChannel.result.data.json
    return swapChannelSchema.parse({
      channelId: id,
      channelAddress: depositAddress,
    })
  })

export {
  AssetDecimalMap,
  ChainRegexMap,
  channelIdRegex,
  InputChainsAndAssetsWithMinimumSwapAmounts,
  minPriceX128Transformer,
  pendingSwapStatuses,
  quoteTransformer,
  swapChannelTransformer,
  swapMetadataSchema,
  swapTransformer,
  type ChannelId,
  type InputAsset,
  type InputChain,
  type InputChainAndAsset,
  type OutputAsset,
  type OutputChain,
  type OutputChainAndAsset,
  type SwapMetadata,
  type TransformedQuote,
}
