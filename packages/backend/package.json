{"name": "backend", "version": "1.0.1", "description": "", "type": "module", "main": "./src/index.js", "types": "./src/index.ts", "scripts": {"lint": "biome lint --no-errors-on-unmatched --write src/**/*.ts --config-path ../../", "typecheck": "tsc -b --emitDeclarationOnly", "typecheck:watch": "tsc -b --emitDeclarationOnly --watch", "build": "swc ./src -s --ignore **/*.js -d .", "clean": "rimraf \"dist\" \"./src/**/*.js\" \"./src/**/*.d.ts\" \"./src/**/*.d.ts.map\" \"./src/**/*.js.map\" -g"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@clerk/fastify": "^2.3.0", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.3", "@fastify/otel": "^0.7.0", "@fastify/request-context": "^6.2.0", "@fastify/swagger": "^9.5.1", "@google-cloud/opentelemetry-cloud-trace-exporter": "^2.4.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.58.1", "@opentelemetry/exporter-trace-otlp-proto": "^0.200.0", "@opentelemetry/resource-detector-gcp": "^0.34.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@riseworks/contracts": "workspace:*", "db": "workspace:*", "fastify": "^5.3.3", "fastify-raw-body": "^5.0.0", "fastify-type-provider-zod": "^4.0.2", "fets": "^0.8.5", "go-go-try": "^6.2.0", "jsonwebtoken": "^9.0.2", "qs": "^6.14.0", "remeda": "^2.23.0", "repositories": "workspace:*", "utils": "workspace:*", "zod": "^3.25.63", "zod-validation-error": "^3.5.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.31", "@types/qs": "^6.14.0", "rimraf": "^6.0.1", "typescript": "^5.8.3"}}