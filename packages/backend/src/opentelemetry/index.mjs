import FastifyOtelInstrumentation from '@fastify/otel'
import { TraceExporter } from '@google-cloud/opentelemetry-cloud-trace-exporter'
import { createNoopMeter, metrics } from '@opentelemetry/api'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto'
import { gcpDetector } from '@opentelemetry/resource-detector-gcp'
import { resourceFromAttributes } from '@opentelemetry/resources'
import {
  ConsoleMetricExporter,
  PeriodicExportingMetricReader,
} from '@opentelemetry/sdk-metrics'
import * as opentelemetry from '@opentelemetry/sdk-node'
import {
  BatchSpanProcessor,
  SimpleSpanProcessor,
} from '@opentelemetry/sdk-trace-base'

const defaultSpanProcessor =
  process.env.NODE_ENV === 'localhost'
    ? new SimpleSpanProcessor(
        new OTLPTraceExporter({
          url: process.env.JAEGER_URL,
        }),
      )
    : new BatchSpanProcessor(new TraceExporter())

// this array enables metrics for all applications in a given environment
// the possible values are 'localhost', 'development', 'staging' and 'production'
const metricsEnabledEnvironments = []

const shouldInstrumentMetrics =
  metricsEnabledEnvironments.includes(process.env.NODE_ENV) ||
  process.env.ENABLE_METRICS === 'true'

// Use noop metric provider if metrics are not allowed
if (!shouldInstrumentMetrics) {
  metrics.setGlobalMeterProvider({ getMeter: () => createNoopMeter() })
}

const oneMinuteInms = 60_000
const fourMinutesInms = 4 * oneMinuteInms
const fiveMintesInms = 5 * oneMinuteInms

const exportIntervalByEnv = {
  localhost: oneMinuteInms,
  development: fiveMintesInms,
  staging: fourMinutesInms,
  productions: oneMinuteInms,
}

const getMetricReader = () => {
  // Preventing metrics from being exported if not necessary
  if (!shouldInstrumentMetrics) {
    return
  }
  const exportIntervalMillis =
    exportIntervalByEnv[process.env.NODE_ENV] || fiveMintesInms

  return new PeriodicExportingMetricReader({
    exportIntervalMillis,
    exporter: new ConsoleMetricExporter(),
  })
}

const serviceName = process.env.K_SERVICE || 'rise'
class ServiceNameAttributeSpanProcessor {
  onStart(span) {
    span.setAttribute(ATTR_SERVICE_NAME, serviceName)
  }

  onEnd(_span) {}

  shutdown() {
    return Promise.resolve()
  }

  forceFlush() {
    return Promise.resolve()
  }
}

const resource = resourceFromAttributes({
  [ATTR_SERVICE_NAME]: serviceName,
  environment: process.env.NODE_ENV,
  revision: process.env?.K_REVISION,
})

const cloudEnvs = ['development', 'staging', 'production']

const sdk = new opentelemetry.NodeSDK({
  instrumentations: [
    getNodeAutoInstrumentations({
      // Disable noisy instrumentations
      '@opentelemetry/instrumentation-dns': { enabled: false },
      '@opentelemetry/instrumentation-fs': { enabled: false },
      '@opentelemetry/instrumentation-mysql2': { enabled: false },
      '@opentelemetry/instrumentation-fastify': { enabled: false },
    }),
    new FastifyOtelInstrumentation({
      registerOnInitialization: true,
      servername: serviceName,
    }),
  ],
  serviceName,
  spanProcessors: [
    new ServiceNameAttributeSpanProcessor(),
    defaultSpanProcessor,
  ],
  resource,
  ...(cloudEnvs.includes(process.env.NODE_ENV) && {
    resourceDetectors: [gcpDetector],
    metricReader: getMetricReader(),
  }),
})

try {
  sdk.start()
  console.log('OpenTelemetry SDK started')
} catch (e) {
  console.error(`Failed to start OpenTelemetry SDK: ${e}`)
}

process.on('SIGTERM', sdk.shutdown)
