// this file was generated with `pnpm codegen`, do not edit it manually
export const tablesWithAutoUpdate = [
  'rise_audit.blockchain_transactions',
  'rise_audit.user_sessions',
  'rise_audit.mirror__orum__transfer',
  'rise_audit.location_codes_cache',
  'rise_audit.location_taxes_cache',
  'rise.rise_entities',
  'rise.entity_permissions',
  'rise.agreements',
  'rise.companies_data',
  'rise.invites',
  'rise.invites_riseid',
  'rise.blockchain_addresses',
  'rise.smart_contracts',
  'rise.teams_data',
  'rise.users_data',
  'rise.users_certifications',
  'rise.rise_agreements',
  'rise.users_onboarding',
  'rise.superadmin_settings',
  'rise.company_role_settings',
  'rise.team_role_settings',
  'rise.company_settings',
  'rise.team_settings',
  'rise.withdraw_options',
  'rise.countries',
  'rise.continents',
  'rise.withdrawals',
  'rise.cctp_withdrawals',
  'rise.deposits',
  'rise.provider_withdrawals',
  'rise.erc20_tokens',
  'rise.documents',
  'rise.document_versions',
  'rise.signatures',
  'rise.templates_signatures',
  'rise.templates',
  'rise.time_entries',
  'rise.pay_intents',
  'rise.payment_groups',
  'rise.payment',
  'rise.payment_invoice_data',
  'rise.pay_schedules',
  'rise.countries_risk',
  'rise.withdraw_options_currency',
  'rise.entity_withdraw_options',
  'rise.entity_deposit_accounts',
  'rise.deposit_payment_handlers',
  'rise.admin_blockchain_transactions',
  'rise.withdraw_account_conflict',
  'rise.blockchain_transactions',
  'rise.activity_history',
  'rise.account_ledger',
  'rise.ethereum_transactions',
  'rise.ethereum_transaction_forwarder_calls',
  'rise.ethereum_transaction_events',
  'rise.blockchain_transaction_seen',
  'rise.action_items',
  'rise.withdraw_downtimes',
  'rise.compliance_provider_data',
  'rise.v1_riseid_payment_handler',
  'rise.user_notifications',
  'rise.v1_v2_migration_entity',
  'rise.team_payroll',
  'rise.rise_payroll_company',
  'rise.one_off_bonuses',
  'rise.employee_payroll_past_wh',
  'rise.employee_payroll_taxes_past_wh',
  'rise.ccip_configurations',
  'rise.bucket_accounts',
  'rise.notification_tokens',
  'rise.integration_conn',
  'rise.integration_transaction_data',
  'rise.transfer_needs_document',
  'rise.event_data',
  'rise.chainflip_swaps',
  'rise.entities_multi_currency_configurations',
  'rise_private.addresses',
  'rise_private.company_owners',
  'rise_private.private_data',
  'rise_private.users_data',
  'rise_private.documents',
  'rise_private.company_contacts',
  'rise_private.fee_payers',
  'rise_private.withdraw_account_bank_data',
  'rise_private.oauth_tokens',
  'rise_private.unblock_users_accounts',
  'rise_private.entity_risk',
  'rise_private.rise_entity_compliance',
  'rise_private.users_withdraw_account',
  'rise_private.users_withdraw_account_verification',
  'rise_private.compliance_data',
  'rise_private.users_deposit_bank_accounts_seen',
  'rise.users_rsk',
]
