import {
  BinaryOperationNode,
  type Kysely<PERSON>lugin,
  OperationNodeTransformer,
  OperatorNode,
  type PluginTransformQueryArgs,
  type PluginTransformResultArgs,
  type QueryResult,
  type RootOperationNode,
  type UnknownRow,
  ValueNode,
} from 'kysely'

class SafeNullComparisonTransformer extends OperationNodeTransformer {
  protected transformBinaryOperation(
    node: BinaryOperationNode,
  ): BinaryOperationNode {
    const { operator, leftOperand, rightOperand } =
      super.transformBinaryOperation(node)

    if (
      !ValueNode.is(rightOperand) ||
      rightOperand.value !== null ||
      !OperatorNode.is(operator)
    ) {
      return node
    }

    const op = operator.operator
    if (op !== '=' && op !== '!=' && op !== '<>') {
      return node
    }

    return BinaryOperationNode.create(
      leftOperand,
      OperatorNode.create(op === '=' ? 'is' : 'is not'),
      rightOperand,
    )
  }
}

/**
 * Plugin that handles NULL comparisons to prevent common SQL mistakes.
 *
 * In SQL, comparing values with NULL using standard comparison operators (=, !=, <>)
 * always yields NULL, which is usually not what developers expect. The correct way
 * to compare with NULL is using IS NULL and IS NOT NULL.
 *
 * When working with nullable variables (e.g. string | null), you need to be careful to
 * manually handle these cases with conditional WHERE clauses. This plugins automatically
 * applies the correct operator based on the value, allowing you to simply write `query.where('name', '=', name)`.
 *
 * The plugin transforms the following operators when comparing with NULL:
 * - `=` becomes `IS`
 * - `!=` becomes `IS NOT`
 * - `<>` becomes `IS NOT`
 */
export class SafeNullComparisonPlugin implements KyselyPlugin {
  readonly #transformer = new SafeNullComparisonTransformer()

  transformQuery(args: PluginTransformQueryArgs): RootOperationNode {
    return this.#transformer.transformNode(args.node)
  }

  transformResult(
    args: PluginTransformResultArgs,
  ): Promise<QueryResult<UnknownRow>> {
    return Promise.resolve(args.result)
  }
}
