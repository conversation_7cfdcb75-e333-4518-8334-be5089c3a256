import {
  AliasNode,
  ColumnNode,
  ColumnUpdateNode,
  IdentifierNode,
  type Kysely<PERSON>lugin,
  OperationNodeTransformer,
  type PluginTransformQueryArgs,
  type PluginTransformResultArgs,
  type QueryResult,
  RawNode,
  type RootOper<PERSON>Node,
  TableNode,
  type UnknownRow,
  type UpdateQueryNode,
} from 'kysely'

export class AutoUpdatePlugin implements KyselyPlugin {
  private transformer: AutoUpdateQueryTransformer

  constructor(
    ...args: ConstructorParameters<typeof AutoUpdateQueryTransformer>
  ) {
    this.transformer = new AutoUpdateQueryTransformer(...args)
  }

  transformQuery({ node }: PluginTransformQueryArgs): RootOperationNode {
    return this.transformer.transformNode(node)
  }

  transformResult({
    result,
  }: PluginTransformResultArgs): Promise<QueryResult<UnknownRow>> {
    return Promise.resolve(result)
  }
}

class AutoUpdateQueryTransformer extends OperationNodeTransformer {
  private updatedAtColumnName: string
  private updateableTables: string[]

  constructor({
    updateableTables,
    updatedAtColumnName,
  }: {
    updatedAtColumnName?: string
    updateableTables: string[]
  }) {
    super()
    this.updatedAtColumnName = updatedAtColumnName || 'updated_at'
    this.updateableTables = updateableTables
  }

  protected override transformUpdateQuery(
    originalNode: UpdateQueryNode,
  ): UpdateQueryNode {
    const node = super.transformUpdateQuery(originalNode)
    return this.setUpdatedAt(node)
  }

  private setUpdatedAt<T extends UpdateQueryNode>(node: T): T {
    if (!node.table) return node

    let tableName: string | undefined
    let tableSchema: string | undefined
    let alias = ''
    const table = node.table
    if (TableNode.is(table)) {
      tableName = table.table.identifier.name
      tableSchema = table.table.schema?.name
    } else if (AliasNode.is(table)) {
      if (IdentifierNode.is(table.alias)) {
        alias = table.alias.name
      } else {
        return node
      }
      alias = table.alias.name
      if (!TableNode.is(table.node)) {
        return node
      }
      tableName = table.node.table.identifier.name
      tableSchema = table.node.table.schema?.name
    } else {
      return node
    }
    if (
      !this.updateableTables.find(
        (table) => table === `${tableSchema}.${tableName}`,
      )
    ) {
      return node
    }
    const updates = node.updates || []
    const updateUpdatedAtColumn = ColumnNode.create(
      `${alias ? `${alias}.` : ''}${this.updatedAtColumnName}`,
    )
    const updatedAtUpdate = ColumnUpdateNode.create(
      updateUpdatedAtColumn,
      RawNode.createWithSql('NOW()'),
    )
    return {
      ...node,
      updates: [...updates, updatedAtUpdate],
    }
  }
}
