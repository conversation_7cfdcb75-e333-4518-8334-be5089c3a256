export default [
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/325/thumb/Tether-logo.png?1598003707',
    chainId: 421614,
    address: '******************************************',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/9956/thumb/4943.png?1636636734',
    chainId: 421614,
    address: '******************************************',
    name: 'Dai Stablecoin',
    symbol: 'DAI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/325/thumb/Tether-logo.png?1598003707',
    chainId: 11155111,
    address: '******************************************',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 420,
    address: '******************************************',
    name: 'USD Coin (Optimism)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 10,
    address: '0x0b2c639c533813f4aa9d7837caf62653d097ff85',
    name: 'USD Coin (Optimism)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 43113,
    address: '0x4E3e92A824A4FC1c890D155bE2D69e7eB1BF42B8',
    name: 'USD Coin (Aval)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 43114,
    address: '0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E',
    name: 'USD Coin (Aval)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 80001,
    address: '0x4E3e92A824A4FC1c890D155bE2D69e7eB1BF42B8',
    name: 'USD Coin (Polygon)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x2791bca1f2de4661ed88a30c99a7a9449aa84174',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 137,
    address: '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359',
    name: 'USD Coin (Polygon)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x2791bca1f2de4661ed88a30c99a7a9449aa84174',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 421614,
    address: '******************************************',
    name: 'Rise USDC (Sepolia)',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xD87Ba7A50B2E7E660f678A895E4B72E7CB4CCd9C',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xD87Ba7A50B2E7E660f678A895E4B72E7CB4CCd9C',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 42161,
    address: '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
    name: 'Native USDC',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 11155111,
    address: '0xD87Ba7A50B2E7E660f678A895E4B72E7CB4CCd9C',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 1,
    name: 'USD Coin',
    address: '******************************************',
    symbol: 'USDC',
    decimals: 6,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png',
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/4454/thumb/0xbtc.png?1561603765',
    chainId: 42161,
    address: '******************************************',
    name: '0xBitcoin Token',
    symbol: '0xBTC',
    decimals: 8,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '******************************************',
    name: 'Agave',
    symbol: 'AGVE',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14719/thumb/sbEW5W8.png?1617939648',
    chainId: 42161,
    address: '0x0e15258734300290a651FdBAe8dEb039a8E7a2FA',
    name: 'Alchemy',
    symbol: 'ALCH',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14379/thumb/uaLoLU8c_400x400_%281%29.png?1627873106',
    chainId: 42161,
    address: '0x9b3fa2A7C3EB36d048A5d38d81E7fAFC6bc47B25',
    name: 'Aluna',
    symbol: 'ALN',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/2165/thumb/Auc_Discord_Avatar1.png?1618983355',
    chainId: 42161,
    address: '0xea986d33eF8a20A96120ecc44dBdD49830192043',
    name: 'Auctus Token',
    symbol: 'AUC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13246/thumb/BAC.png?1613231642',
    chainId: 42161,
    address: '0x6F67043201C903bbCBC129750CB3b328Dd56a0a5',
    name: 'BAC',
    symbol: 'BAC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/badger_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Badger',
    symbol: 'BADGER',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/balancer-bal-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Balancer',
    symbol: 'BAL',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '******************************************',
    name: 'BarkCoin',
    symbol: 'BARK',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/blanktoken_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'GoBlank Token',
    symbol: 'BLANK',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/barnbridge-bond-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'BarnBridge Governance Token',
    symbol: 'BOND',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/boostcoin_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Boost',
    symbol: 'BOOST',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/btu_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'BTU Protocol',
    symbol: 'BTU',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11775/thumb/CAP.png?1594083244',
    chainId: 42161,
    address: '******************************************',
    name: 'Cap',
    symbol: 'CAP',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x43044f861ec040db59a7e324c40507addb673142',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x43044f861ec040db59a7e324c40507addb673142',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/4379/thumb/Celr.png?1554705437',
    chainId: 42161,
    address: '0x3a8B787f78D775AECFEEa15706D4221B40F345AB',
    name: 'CelerToken',
    symbol: 'CELR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/cryptionnetwork_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Cryption Network Token',
    symbol: 'CNT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/10775/thumb/COMP.png?1592625425',
    chainId: 42161,
    address: '******************************************',
    name: 'Compound',
    symbol: 'COMP',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/coti-coti-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'COTI Token',
    symbol: 'COTI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/cream-finance-cream-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Cream',
    symbol: 'CREAM',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/curve-dao-token-crv-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Curve DAO Token',
    symbol: 'CRV',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/9956/thumb/4943.png?1636636734',
    chainId: 42161,
    address: '******************************************',
    name: 'Dai Stablecoin',
    symbol: 'DAI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13691/thumb/thGDKHo.png?1610959947',
    chainId: 42161,
    address: '0xdeBa25AF35e4097146d7629055E0EC3C71706324',
    name: 'DEFI Top 5 Tokens Index',
    symbol: 'DEFI5',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xfa6de2697d59e88ed7fc4dfe5a33dac43565ea41',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xfa6de2697d59e88ed7fc4dfe5a33dac43565ea41',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14143/thumb/alpha_logo.png?1614651244',
    chainId: 42161,
    address: '0xAE6e3540E97b0b9EA8797B157B510e133afb6282',
    name: 'DEGEN Index',
    symbol: 'DEGEN',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x126c121f99e1e211df2e5f8de2d96fa36647c855',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x126c121f99e1e211df2e5f8de2d96fa36647c855',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/9709/thumb/xlGxxIjI_400x400.jpg?1571006794',
    chainId: 42161,
    address: '0xaE6aab43C4f3E0cea4Ab83752C278f8dEbabA689',
    name: 'dForce',
    symbol: 'DF',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dfynnetwork_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'DFYN Token',
    symbol: 'DFYN',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dHedge_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'dHedge DAO Token',
    symbol: 'DHT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dodo_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'DODO bird',
    symbol: 'DODO',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/thedogenft_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'The Doge NFT',
    symbol: 'DOG',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dopexgovernance_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Dopex Governance Token',
    symbol: 'DPX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/17482/thumb/photo_2021-08-03_09-24-16.png?1627953917',
    chainId: 42161,
    address: '******************************************',
    name: 'Digital Standard Unit',
    symbol: 'DSU',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dvf_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'DeversiFi Token',
    symbol: 'DVF',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11148/thumb/dxdao.png?1607999331',
    chainId: 42161,
    address: '******************************************',
    name: 'DXdao',
    symbol: 'DXD',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xa1d65e8fb6e87b60feccbc582f7f97804b725521',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xa1d65e8fb6e87b60feccbc582f7f97804b725521',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/17481/thumb/photo_2021-08-03_03-26-29.png?1627953584',
    chainId: 42161,
    address: '0xCE32aA8d60807182c0003Ef9cc1976Fa10E5d312',
    name: 'Empty Set Share',
    symbol: 'ESS',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dforceeur_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'dForce EUR',
    symbol: 'EUX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11756/thumb/fluxres.png?1593748917',
    chainId: 42161,
    address: '******************************************',
    name: 'Flux',
    symbol: 'FLUX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/zel-flux-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Flux Protocol',
    symbol: 'FLUX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/8242/thumb/for.png?1606195375',
    chainId: 42161,
    address: '******************************************',
    name: 'The Force Token',
    symbol: 'FOR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/futureswap2_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Futureswap Token',
    symbol: 'FST',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/10347/thumb/vUXKHEe.png?1601523640',
    chainId: 42161,
    address: '******************************************',
    name: 'Fuse Token',
    symbol: 'FUSE',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x970b9bb2c0444f5e81e9d0efb84c8ccdcdcaf84d',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x970b9bb2c0444f5e81e9d0efb84c8ccdcdcaf84d',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '0x590020B1005b8b25f1a2C82c5f743c540dcfa24d',
    name: 'GMX',
    symbol: 'GMX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xbc30049adc73de06d7a98a5189203aac66b2c830',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xbc30049adc73de06d7a98a5189203aac66b2c830',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/662/thumb/logo_square_simple_300px.png?1609402668',
    chainId: 42161,
    address: '0xa0b862F60edEf4452F25B4160F177db44DeB6Cf1',
    name: 'Gnosis Token',
    symbol: 'GNO',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x6810e776880c02933d47db1b9fc05908e5386b96',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x6810e776880c02933d47db1b9fc05908e5386b96',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13875/thumb/GOVI.png?1612451531',
    chainId: 42161,
    address: '0x07E49d5dE43DDA6162Fa28D24d5935C151875283',
    name: 'GOVI',
    symbol: 'GOVI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xeeaa40b28a2d1b0b08f6f97bb1dd4b75316c6107',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xeeaa40b28a2d1b0b08f6f97bb1dd4b75316c6107',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13397/thumb/Graph_Token.png?1608145566',
    chainId: 42161,
    address: '0x23A941036Ae778Ac51Ab04CEa08Ed6e2FE103614',
    name: 'Graph Token',
    symbol: 'GRT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/impermax_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Impermax',
    symbol: 'IMX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13177/thumb/kun_logo.png?1605923919',
    chainId: 42161,
    address: '******************************************',
    name: 'QIAN governance token',
    symbol: 'KUN',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/farmland_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Land',
    symbol: 'LAND',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/877/thumb/chainlink-new-logo.png?1547034700',
    chainId: 42161,
    address: '******************************************',
    name: 'ChainLink Token',
    symbol: 'LINK',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/loopring-lrc-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'LoopringCoin V2',
    symbol: 'LRC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/18623/thumb/Magic.png?1635755672',
    chainId: 42161,
    address: '******************************************',
    name: 'MAGIC',
    symbol: 'MAGIC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '0xAA086809EFA469631DD90D8C6cB267bAb107E958',
    name: 'My Alpha Leaderboard',
    symbol: 'MAL',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11335/thumb/2020-05-19-token-200.png?1589940590',
    chainId: 42161,
    address: '0x99F40b01BA9C469193B360f72740E416B17Ac332',
    name: 'MATH Token',
    symbol: 'MATH',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/antimatter_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Antimatter.Finance Governance Token',
    symbol: 'MATTER',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11796/thumb/mcb.png?1594355515',
    chainId: 42161,
    address: '******************************************',
    name: 'MCDEX Token',
    symbol: 'MCB',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/maker-mkr-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Maker',
    symbol: 'MKR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/11846/thumb/mStable.png?1594950533',
    chainId: 42161,
    address: '******************************************',
    name: 'Meta',
    symbol: 'MTA',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0xa3bed4e1c75d00fa6f4e5e6922db7261b5e9acd2',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0xa3bed4e1c75d00fa6f4e5e6922db7261b5e9acd2',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13546/thumb/indexed-light.74bb5471.png?1609712728',
    chainId: 42161,
    address: '0xB965029343D55189c25a7f3e0c9394DC0F5D41b1',
    name: 'Indexed',
    symbol: 'NDX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x86772b1409b61c639eaac9ba0acfbb6e238e5f83',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x86772b1409b61c639eaac9ba0acfbb6e238e5f83',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '0xd67D9F7E018B4e7613b0251BBe3Ba3988Baf7C16',
    name: 'New era',
    symbol: 'NEC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/feistydoge_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Feisty Doge NFT',
    symbol: 'NFD',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12594/thumb/octofi-256x256-radius-22percent.png?1610679969',
    chainId: 42161,
    address: '******************************************',
    name: 'Octo.fi',
    symbol: 'OCTO',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x7240ac91f01233baaf8b064248e80feaa5912ba3',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x7240ac91f01233baaf8b064248e80feaa5912ba3',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14483/thumb/token_OHM_%281%29.png?1628311611',
    chainId: 42161,
    address: '0x6E6a3D8F1AfFAc703B1aEF1F43B8D2321bE40043',
    name: 'Olympus',
    symbol: 'OHM',
    decimals: 9,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x383518188c0c6d7730d91b2c03a03c837814a899',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x383518188c0c6d7730d91b2c03a03c837814a899',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13429/thumb/ovr_logo.png?1608518911',
    chainId: 42161,
    address: '0x55704A0e9E2eb59E176C5b69655DbD3DCDCFc0F0',
    name: 'OVR',
    symbol: 'OVR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x21bfbda47a0b4b5b1248c767ee49f7caa9b23697',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x21bfbda47a0b4b5b1248c767ee49f7caa9b23697',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12381/thumb/60d18e06844a844ad75901a9_mark_only_03.png?1628674771',
    chainId: 42161,
    address: '0x753D224bCf9AAFaCD81558c32341416df61D3DAC',
    name: 'Perpetual',
    symbol: 'PERP',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/pickle_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'PickleToken',
    symbol: 'PICKLE',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '******************************************',
    name: 'Plenny',
    symbol: 'PL2',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13962/thumb/apple-touch-icon.png?1623679482',
    chainId: 42161,
    address: '0x51fC0f6660482Ea73330E414eFd7808811a57Fa2',
    name: 'Premia',
    symbol: 'PREMIA',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/raireflexindex_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Rai Reflex Index',
    symbol: 'RAI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dopexrebate_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Dopex Rebate Token',
    symbol: 'RDPX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/RariGovernanceToken_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Rari Governance Token',
    symbol: 'RGT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13709/thumb/route_token_200x200-19.png?1611057698',
    chainId: 42161,
    address: '******************************************',
    name: 'Route',
    symbol: 'ROUTE',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x16eccfdbb4ee1a85a33f3a9b21175cd7ae753db4',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x16eccfdbb4ee1a85a33f3a9b21175cd7ae753db4',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12428/thumb/sake.png?1599777402',
    chainId: 42161,
    address: '0x552E4e96A0Ce6D36d161b63984848c8dAC471ea2',
    name: 'SakeToken',
    symbol: 'SAKE',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x066798d9ef0833ccc719076dab77199ecbd178b0',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x066798d9ef0833ccc719076dab77199ecbd178b0',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/13724/thumb/stakedao_logo.jpg?1611195011',
    chainId: 42161,
    address: '0x7bA4a00d54A07461D9DB2aEF539e91409943AdC9',
    name: 'Stake DAO Token',
    symbol: 'SDT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x73968b9a57c6e53d41345fd57a6e6ae27d6cdb2f',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x73968b9a57c6e53d41345fd57a6e6ae27d6cdb2f',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '0x5575552988A3A80504bBaeB1311674fCFd40aD4B',
    name: 'Sperax',
    symbol: 'SPA',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/spelltoken_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Spell Token',
    symbol: 'SPELL',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/strips_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Strips Token',
    symbol: 'STRP',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/sumswap_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'SUM',
    symbol: 'SUM',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/sushiswap-sushi-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'SushiToken',
    symbol: 'SUSHI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/18740/thumb/swapr.jpg?1633516501',
    chainId: 42161,
    address: '******************************************',
    name: 'Swapr',
    symbol: 'SWPR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/tkdcoop_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Taekwondo Access Credit',
    symbol: 'TAC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/18271/thumb/tracer_logo.png?1631176676',
    chainId: 42161,
    address: '******************************************',
    name: 'Tracer',
    symbol: 'TCR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://images.prismic.io/tusd-homepage/fb4d581a-95ed-404c-b9de-7ab1365c1386_%E5%9B%BE%E5%B1%82+1.png',
    chainId: 42161,
    address: '0x4D15a3A2286D883AF0AA1B3f21367843FAc63E07',
    name: 'TrueUSD',
    symbol: 'TUSD',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '0x0000000000085d4780b73119b644ae5ecd22b376',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '0x0000000000085d4780b73119b644ae5ecd22b376',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/2707/thumb/UnibrightLogo_colorful_500x500_preview.png?1547036916',
    chainId: 42161,
    address: '0x2aD62674A64E698C24831Faf824973C360430140',
    name: 'UniBright',
    symbol: 'UBT',
    decimals: 8,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14545/thumb/unlock.jpg?1616948136',
    chainId: 42161,
    address: '0xd5d3aA404D7562d09a848F96a8a8d5D65977bF90',
    name: 'Unlock Discount Token',
    symbol: 'UDT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12504/thumb/uniswap-uni.png?1600306604',
    chainId: 42161,
    address: '0xFa7F8980b0f1E64A2062791cc3b0871572f1F7f0',
    name: 'Uniswap',
    symbol: 'UNI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/unitynetwork_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Unity Network',
    symbol: 'UNT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/325/thumb/Tether-logo.png?1598003707',
    chainId: 42161,
    address: '******************************************',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/dforceusd_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'dForce USD',
    symbol: 'USX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://etherscan.io/token/images/validator_32.png',
    chainId: 42161,
    address: '******************************************',
    name: 'Validator',
    symbol: 'VALX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14381/thumb/visor_logo.png?1615782828',
    chainId: 42161,
    address: '******************************************',
    name: 'VISOR',
    symbol: 'VISR',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12880/thumb/BSensIa.png?1603261093',
    chainId: 42161,
    address: '0x2eD14d1788dfB780fD216706096AeD018514ECcd',
    name: 'Vox.Finance',
    symbol: 'VOX',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/7598/thumb/wrapped_bitcoin_wbtc.png?1548822744',
    chainId: 42161,
    address: '******************************************',
    name: 'Wrapped BTC',
    symbol: 'WBTC',
    decimals: 8,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/2091/thumb/xaya_logo-1.png?1547036406',
    chainId: 42161,
    address: '******************************************',
    name: 'Wrapped CHI',
    symbol: 'WCHI',
    decimals: 8,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/12921/thumb/w2UiemF__400x400.jpg?1603670367',
    chainId: 42161,
    address: '0xcAFcD85D8ca7Ad1e1C6F82F651fA15E33AEfD07b',
    name: 'Wootrade Network',
    symbol: 'WOO',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/14089/thumb/xToken.png?1614226407',
    chainId: 42161,
    address: '0xF0A5717Ec0883eE56438932b0fe4A20822735fBa',
    name: 'xToken',
    symbol: 'XTK',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI: 'https://cryptologos.cc/logos/yearn-finance-yfi-logo.png',
    chainId: 42161,
    address: '******************************************',
    name: 'yearn.finance',
    symbol: 'YFI',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/4302/thumb/zippie.jpg?1547039665',
    chainId: 42161,
    address: '******************************************',
    name: 'Zippie',
    symbol: 'ZIPT',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/15500/thumb/ibbtc.png?1621077589',
    chainId: 42161,
    address: '******************************************',
    name: 'Interest-Bearing Bitcoin',
    symbol: 'ibBTC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 42161,
    address: '******************************************',
    name: 'KAKI USDC',
    symbol: 'kUSDC',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/5013/thumb/sUSD.png?1616150765',
    chainId: 42161,
    address: '0xA970AF1a584579B618be4d69aD6F73459D112F95',
    name: 'Synth sUSD',
    symbol: 'sUSD',
    decimals: 18,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
  {
    chainId: 1,
    name: '0xBitcoin',
    address: '******************************************',
    symbol: '0xBTC',
    decimals: 8,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2837.png',
  },
  {
    chainId: 1,
    name: 'Agave',
    address: '******************************************',
    symbol: 'AGVE',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'Alchemy',
    address: '******************************************',
    symbol: 'ALCH',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/14719/thumb/sbEW5W8.png?1617939648',
  },
  {
    chainId: 1,
    name: 'Aluna.Social',
    address: '******************************************',
    symbol: 'ALN',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5544.png',
  },
  {
    chainId: 1,
    name: 'Auctus',
    address: '******************************************',
    symbol: 'AUC',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2653.png',
  },
  {
    chainId: 1,
    name: 'Basis Cash',
    address: '0x3449FC1Cd036255BA1EB19d65fF4BA2b8903A69a',
    symbol: 'BAC',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7813.png',
  },
  {
    chainId: 1,
    name: 'Badger DAO',
    address: '******************************************',
    symbol: 'BADGER',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7859.png',
  },
  {
    chainId: 1,
    name: 'Balancer',
    address: '******************************************',
    symbol: 'BAL',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5728.png',
  },
  {
    chainId: 1,
    name: 'BarkCoin',
    address: '******************************************',
    symbol: 'BARK',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'BlockWallet',
    address: '******************************************',
    symbol: 'BLANK',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8695.png',
  },
  {
    chainId: 1,
    name: 'BarnBridge',
    address: '******************************************',
    symbol: 'BOND',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7440.png',
  },
  {
    chainId: 1,
    name: 'Boost Coin',
    address: '******************************************',
    symbol: 'BOOST',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11216.png',
  },
  {
    chainId: 1,
    name: 'BTU Protocol',
    address: '******************************************',
    symbol: 'BTU',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3737.png',
  },
  {
    chainId: 1,
    name: 'Cap',
    address: '******************************************',
    symbol: 'CAP',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5809.png',
  },
  {
    chainId: 1,
    name: 'Celer Network',
    address: '******************************************',
    symbol: 'CELR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3814.png',
  },
  {
    chainId: 1,
    name: 'Cryption Network',
    address: '******************************************',
    symbol: 'CNT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9747.png',
  },
  {
    chainId: 1,
    name: 'Compound',
    address: '******************************************',
    symbol: 'COMP',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5692.png',
  },
  {
    chainId: 1,
    name: 'COTI',
    address: '0xDDB3422497E61e13543BeA06989C0789117555c5',
    symbol: 'COTI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3992.png',
  },
  {
    chainId: 1,
    name: 'Cream Finance',
    address: '******************************************',
    symbol: 'CREAM',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6193.png',
  },
  {
    chainId: 1,
    name: 'Curve DAO Token',
    address: '0xD533a949740bb3306d119CC777fa900bA034cd52',
    symbol: 'CRV',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6538.png',
  },
  {
    chainId: 1,
    name: 'Dai',
    address: '******************************************',
    symbol: 'DAI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/4943.png',
  },
  {
    chainId: 1,
    name: 'DEFI Top 5 Tokens Index',
    address: '0xfa6de2697d59e88ed7fc4dfe5a33dac43565ea41',
    symbol: 'DEFI5',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8430.png',
  },
  {
    chainId: 1,
    name: 'DEGEN Index',
    address: '0x126c121f99e1e211df2e5f8de2d96fa36647c855',
    symbol: 'DEGEN',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/14143/thumb/alpha_logo.png?1614651244',
  },
  {
    chainId: 1,
    name: 'dForce',
    address: '******************************************',
    symbol: 'DF',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/4758.png',
  },
  {
    chainId: 1,
    name: 'Dfyn Network',
    address: '******************************************',
    symbol: 'DFYN',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9511.png',
  },
  {
    chainId: 1,
    name: 'dHedge DAO',
    address: '******************************************',
    symbol: 'DHT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7094.png',
  },
  {
    chainId: 1,
    name: 'DODO',
    address: '******************************************',
    symbol: 'DODO',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7224.png',
  },
  {
    chainId: 1,
    name: 'The Doge NFT',
    address: '******************************************',
    symbol: 'DOG',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11557.png',
  },
  {
    chainId: 1,
    name: 'Dopex',
    address: '******************************************',
    symbol: 'DPX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11188.png',
  },
  {
    chainId: 1,
    name: 'Digital Standard Unit',
    address: '******************************************',
    symbol: 'DSU',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/17482/thumb/photo_2021-08-03_09-24-16.png?1627953917',
  },
  {
    chainId: 1,
    name: 'DeversiFi',
    address: '******************************************',
    symbol: 'DVF',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/10759.png',
  },
  {
    chainId: 1,
    name: 'DXdao',
    address: '0xa1d65e8fb6e87b60feccbc582f7f97804b725521',
    symbol: 'DXD',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5589.png',
  },
  {
    chainId: 1,
    name: 'Empty Set Share',
    address: '******************************************',
    symbol: 'ESS',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/17481/thumb/photo_2021-08-03_03-26-29.png?1627953584',
  },
  {
    chainId: 1,
    name: 'dForce EUR',
    address: '******************************************',
    symbol: 'EUX',
    decimals: 18,
    logoURI: 'https://etherscan.io/token/images/dforceeur_32.png',
  },
  {
    chainId: 1,
    name: 'Datamine FLUX',
    address: '******************************************',
    symbol: 'FLUX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5876.png',
  },
  {
    chainId: 1,
    name: 'Flux Protocol',
    address: '******************************************',
    symbol: 'FLUX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9837.png',
  },
  {
    chainId: 1,
    name: 'ForTube',
    address: '******************************************',
    symbol: 'FOR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/4118.png',
  },
  {
    chainId: 1,
    name: 'Futureswap',
    address: '******************************************',
    symbol: 'FST',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8961.png',
  },
  {
    chainId: 1,
    name: 'Fuse Network',
    address: '0x970b9bb2c0444f5e81e9d0efb84c8ccdcdcaf84d',
    symbol: 'FUSE',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5634.png',
  },
  {
    chainId: 1,
    name: 'GMX',
    address: '0xbc30049adc73de06d7a98a5189203aac66b2c830',
    symbol: 'GMX',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'Gnosis',
    address: '0x6810e776880c02933d47db1b9fc05908e5386b96',
    symbol: 'GNO',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1659.png',
  },
  {
    chainId: 1,
    name: 'Govi',
    address: '0xeeaa40b28a2d1b0b08f6f97bb1dd4b75316c6107',
    symbol: 'GOVI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8408.png',
  },
  {
    chainId: 1,
    name: 'The Graph',
    address: '******************************************',
    symbol: 'GRT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6719.png',
  },
  {
    chainId: 1,
    name: 'Impermax',
    address: '******************************************',
    symbol: 'IMX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9532.png',
  },
  {
    chainId: 1,
    name: 'Chemix Ecology Governance Token',
    address: '******************************************',
    symbol: 'KUN',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7721.png',
  },
  {
    chainId: 1,
    name: 'Land',
    address: '******************************************',
    symbol: 'LAND',
    decimals: 18,
    logoURI: 'https://etherscan.io/token/images/farmland_32.png',
  },
  {
    chainId: 1,
    name: 'Chainlink',
    address: '******************************************',
    symbol: 'LINK',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1975.png',
  },
  {
    chainId: 1,
    name: 'Loopring',
    address: '******************************************',
    symbol: 'LRC',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1934.png',
  },
  {
    chainId: 1,
    name: 'MAGIC',
    address: '******************************************',
    symbol: 'MAGIC',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/18623/thumb/Magic.png?1635755672',
  },
  {
    chainId: 1,
    name: 'My Alpha Leaderboard',
    address: '******************************************',
    symbol: 'MAL',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'MATH',
    address: '******************************************',
    symbol: 'MATH',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5616.png',
  },
  {
    chainId: 1,
    name: 'AntiMatter Governance Token',
    address: '0x9B99CcA871Be05119B2012fd4474731dd653FEBe',
    symbol: 'MATTER',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8603.png',
  },
  {
    chainId: 1,
    name: 'MCDEX Token',
    address: '******************************************',
    symbol: 'MCB',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5956.png',
  },
  {
    chainId: 1,
    name: 'Maker',
    address: '******************************************',
    symbol: 'MKR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1518.png',
  },
  {
    chainId: 1,
    name: 'Meta',
    address: '0xa3bed4e1c75d00fa6f4e5e6922db7261b5e9acd2',
    symbol: 'MTA',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/11846/thumb/mStable.png?1594950533',
  },
  {
    chainId: 1,
    name: 'Indexed Finance',
    address: '0x86772b1409b61c639eaac9ba0acfbb6e238e5f83',
    symbol: 'NDX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8260.png',
  },
  {
    chainId: 1,
    name: 'New era',
    address: '******************************************',
    symbol: 'NEC',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'Feisty Doge NFT',
    address: '0xDFDb7f72c1F195C5951a234e8DB9806EB0635346',
    symbol: 'NFD',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11368.png',
  },
  {
    chainId: 1,
    name: 'OctoFi',
    address: '0x7240aC91f01233BaAf8b064248E80feaA5912BA3',
    symbol: 'OCTO',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7202.png',
  },
  {
    chainId: 1,
    name: 'Olympus v1',
    address: '0x383518188c0c6d7730d91b2c03a03c837814a899',
    symbol: 'OHM',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/16209.png',
  },
  {
    chainId: 1,
    name: 'OVR',
    address: '0x21bfbda47a0b4b5b1248c767ee49f7caa9b23697',
    symbol: 'OVR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8144.png',
  },
  {
    chainId: 1,
    name: 'Perpetual Protocol',
    address: '******************************************',
    symbol: 'PERP',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6950.png',
  },
  {
    chainId: 1,
    name: 'Pickle Finance',
    address: '0x429881672B9AE42b8EbA0E26cD9C73711b891Ca5',
    symbol: 'PICKLE',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7022.png',
  },
  {
    chainId: 1,
    name: 'Plenny',
    address: '******************************************',
    symbol: 'PL2',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'Premia',
    address: '0x6399C842dD2bE3dE30BF99Bc7D1bBF6Fa3650E70',
    symbol: 'PREMIA',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8476.png',
  },
  {
    chainId: 1,
    name: 'Rai Reflex Index',
    address: '******************************************',
    symbol: 'RAI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8525.png',
  },
  {
    chainId: 1,
    name: 'Dopex Rebate Token',
    address: '******************************************',
    symbol: 'RDPX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/12057.png',
  },
  {
    chainId: 1,
    name: 'Rari Governance Token',
    address: '******************************************',
    symbol: 'RGT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7486.png',
  },
  {
    chainId: 1,
    name: 'Router Protocol',
    address: '0x16eccfdbb4ee1a85a33f3a9b21175cd7ae753db4',
    symbol: 'ROUTE',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8292.png',
  },
  {
    chainId: 1,
    name: 'SakeToken',
    address: '0x066798d9ef0833ccc719076dab77199ecbd178b0',
    symbol: 'SAKE',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6997.png',
  },
  {
    chainId: 1,
    name: 'Stake DAO',
    address: '0x73968b9a57c6e53d41345fd57a6e6ae27d6cdb2f',
    symbol: 'SDT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8299.png',
  },
  {
    chainId: 1,
    name: 'Spell Token',
    address: '******************************************',
    symbol: 'SPELL',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11289.png',
  },
  {
    chainId: 1,
    name: 'Strips Finance',
    address: '0x97872EAfd79940C7b24f7BCc1EADb1457347ADc9',
    symbol: 'STRP',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11848.png',
  },
  {
    chainId: 1,
    name: 'SumSwap',
    address: '******************************************',
    symbol: 'SUM',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11208.png',
  },
  {
    chainId: 1,
    name: 'SushiSwap',
    address: '******************************************',
    symbol: 'SUSHI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6758.png',
  },
  {
    chainId: 1,
    name: 'Swapr',
    address: '******************************************',
    symbol: 'SWPR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/12368.png',
  },
  {
    chainId: 1,
    name: 'Taekwondo Access Credit',
    address: '******************************************',
    symbol: 'TAC',
    decimals: 18,
    logoURI: 'https://etherscan.io/token/images/tkdcoop_32.png',
  },
  {
    chainId: 1,
    name: 'Tracer DAO',
    address: '******************************************',
    symbol: 'TCR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/12341.png',
  },
  {
    chainId: 1,
    name: 'TrueUSD',
    address: '******************************************',
    symbol: 'TUSD',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2563.png',
  },
  {
    chainId: 1,
    name: 'Unibright',
    address: '******************************************',
    symbol: 'UBT',
    decimals: 8,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2758.png',
  },
  {
    chainId: 1,
    name: 'Unlock Protocol',
    address: '******************************************',
    symbol: 'UDT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9364.png',
  },
  {
    chainId: 1,
    name: 'Uniswap',
    address: '******************************************',
    symbol: 'UNI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7083.png',
  },
  {
    chainId: 1,
    name: 'Unity Network',
    address: '******************************************',
    symbol: 'UNT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/14602.png',
  },
  {
    chainId: 1,
    name: 'Tether',
    address: '******************************************',
    symbol: 'USDT',
    decimals: 6,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/825.png',
  },
  {
    chainId: 1,
    name: 'dForce USD',
    address: '******************************************',
    symbol: 'USX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/13080.png',
  },
  {
    chainId: 1,
    name: 'Validator',
    address: '******************************************',
    symbol: 'VALX',
    decimals: 18,
    logoURI: 'https://etherscan.io/token/images/validator_32.png',
  },
  {
    chainId: 1,
    name: 'Visor.Finance',
    address: '******************************************',
    symbol: 'VISR',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9170.png',
  },
  {
    chainId: 1,
    name: 'Vox.Finance',
    address: '******************************************',
    symbol: 'VOX',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7465.png',
  },
  {
    chainId: 1,
    name: 'Wrapped Bitcoin',
    address: '******************************************',
    symbol: 'WBTC',
    decimals: 8,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3717.png',
  },
  {
    chainId: 1,
    name: 'Xaya',
    address: '******************************************',
    symbol: 'CHI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5541.png',
  },
  {
    chainId: 1,
    name: 'Sperax',
    address: '******************************************',
    symbol: 'SPA',
    decimals: 18,
    logoURI: 'https://etherscan.io/token/images/speraxtoken_32.png',
  },
  {
    chainId: 1,
    name: 'WOO Network',
    address: '******************************************',
    symbol: 'WOO',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7501.png',
  },
  {
    chainId: 1,
    name: 'xToken',
    address: '******************************************',
    symbol: 'XTK',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8599.png',
  },
  {
    chainId: 1,
    name: 'yearn.finance',
    address: '******************************************',
    symbol: 'YFI',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5864.png',
  },
  {
    chainId: 1,
    name: 'Zippie',
    address: '******************************************',
    symbol: 'ZIPT',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2724.png',
  },
  {
    chainId: 1,
    name: 'Interest-Bearing Bitcoin',
    address: '******************************************',
    symbol: 'ibBTC',
    decimals: 18,
    logoURI:
      'https://assets.coingecko.com/coins/images/15500/thumb/ibbtc.png?1621077589',
  },
  {
    chainId: 1,
    name: 'KAKI USDC',
    address: '******************************************',
    symbol: 'kUSDC',
    decimals: 18,
  },
  {
    chainId: 1,
    name: 'sUSD',
    address: '******************************************',
    symbol: 'SUSD',
    decimals: 18,
    logoURI: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2927.png',
  },
  {
    logoURI:
      'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png?1547042389',
    chainId: 421614,
    address: '******************************************',
    name: 'Rise USDT (Sepolia)',
    symbol: 'RiseUSDT',
    decimals: 6,
    extensions: {
      bridgeInfo: {
        '1': {
          tokenAddress: '******************************************',
          originBridgeAddress: '******************************************',
          destBridgeAddress: '******************************************',
        },
      },
      l1Address: '******************************************',
      l2GatewayAddress: '******************************************',
      l1GatewayAddress: '******************************************',
    },
  },
]
