export default [
  {
    chain_id: 1,
    network: 'ethereum',
    url: 'https://mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 11155111,
    network: 'ethereum',
    url: 'https://sepolia.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 42161,
    network: 'arbitrum',
    url: 'https://arbitrum-mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 421614,
    network: 'arbitrum',
    url: 'http://10.22.0.4:8547',
    active: true,
  },
  {
    chain_id: 421614,
    network: 'arbitrum',
    url: 'https://icy-capable-grass.arbitrum-sepolia.quiknode.pro/156cbcac0be4105b7600daccd2c5fa0eb85515e9/',
    type: 'archive',
    active: true,
  },
  {
    chain_id: 137,
    network: 'polygon',
    url: 'https://polygon-mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 80002,
    network: 'polygon',
    url: 'https://polygon-amoy.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 10,
    network: 'optimism',
    url: 'https://optimism-mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 11155420,
    network: 'optimism',
    url: 'https://optimism-sepolia.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 43114,
    network: 'avalanche',
    url: 'https://avalanche-mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 43113,
    network: 'avalanche',
    url: 'https://avalanche-fuji.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 8453,
    network: 'base',
    url: 'https://base-mainnet.infura.io/v3/********************************',
    active: true,
  },
  {
    chain_id: 84532,
    network: 'base',
    url: 'https://base-sepolia.infura.io/v3/********************************',
    active: true,
  },
] as const
