import { sql } from 'kysely'
// seed_mastertax.ts
import { dbWithoutTracing as db } from '../index.js'

export const seed = async () => {
  // 1) companies_data
  await sql`
    INSERT INTO rise.companies_data (
      nanoid, name, size, incorporation_country, incorporation_type,
      doing_business_as, website, phone, is_dao, payroll_enabled
    ) VALUES (
      'co-813TCfwunZq3', 'Example Corp A', 'medium', 'US', 'im_not_sure',
      'ExCoA', 'https://example-a.com', '+**********', TRUE, TRUE
    )
    ON DUPLICATE KEY UPDATE
      name                 = VALUES(name),
      size                 = VALUES(size),
      incorporation_country= VALUES(incorporation_country),
      incorporation_type   = VALUES(incorporation_type),
      doing_business_as    = VALUES(doing_business_as),
      website              = VALUES(website),
      phone                = VALUES(phone),
      is_dao               = VALUES(is_dao),
      payroll_enabled      = VALUES(payroll_enabled);
  `.execute(db)

  // 2) rise_entities (initial two)
  await sql`
  INSERT INTO rise.rise_entities (nanoid, riseid, parent_riseid,  type)
  VALUES 
  ('co-813TCfwunZq3', '0xC1dbC51eD49018b6C391465d6eFba26bcF0Cfd3G', '0x6d6664531C0b723D1676fc5aeD92C9C7b3e2F760','company'),
  ('us-14IjGM_idzAo', '0x6d6664531C0b723D1676fc5aeD92C9C7b3e2F760', '0x6d6664531C0b723D1676fc5aeD92C9C7b3e2F760','user'),
  ('team-1', 'rise-team-1', '0xC1dbC51eD49018b6C391465d6eFba26bcF0Cfd3G', 'team'),
  ('te-1', 'rise-te-1', 'rise-team-1', 'team_employee'),
  ('us-emp-1', 'rise-user-1', 'rise-te-1', 'user')
    ON DUPLICATE KEY UPDATE
      type          = VALUES(type);
  `.execute(db)

  // 3) users_data
  await sql`
    INSERT INTO rise.users_data (
      nanoid, rise_account, email, first_name, middle_name, last_name,
      country, state, recovery_email, occupation, phone, alias,
      linkedin, discord, x, website, account_status, last_modified_by,
      v1_id, advanced_security
    ) VALUES (
      'us-14IjGM_idzAo','0xC1dbC51eD49018b6C391465d6eFba26bcF0Cfd3G',
      '<EMAIL>','Alice','J.','Smith','US','NY',
      '<EMAIL>','Engineer','+**********','alice_s',
      'alice-linkedin','alice#1234','@alice_x','https://alice.example.com',
      'created','us-14IjGM_idzAo','v1-user-001',0
    )
    ON DUPLICATE KEY UPDATE
      email             = VALUES(email),
      first_name        = VALUES(first_name),
      middle_name       = VALUES(middle_name),
      last_name         = VALUES(last_name),
      country           = VALUES(country),
      state             = VALUES(state),
      recovery_email    = VALUES(recovery_email),
      occupation        = VALUES(occupation),
      phone             = VALUES(phone),
      alias             = VALUES(alias),
      linkedin          = VALUES(linkedin),
      discord           = VALUES(discord),
      x                 = VALUES(x),
      website           = VALUES(website),
      account_status    = VALUES(account_status),
      last_modified_by  = VALUES(last_modified_by),
      v1_id             = VALUES(v1_id),
      advanced_security = VALUES(advanced_security);
  `.execute(db)

  // 4) rise_private.private_data
  await sql`
    INSERT INTO rise_private.private_data (
      nanoid, us_based, tax_id, us_work
    ) VALUES (
      'co-813TCfwunZq3', TRUE, '12-3456789', FALSE
    )
    ON DUPLICATE KEY UPDATE
      us_based = VALUES(us_based),
      tax_id   = VALUES(tax_id),
      us_work  = VALUES(us_work);
  `.execute(db)

  // 5) rise_private.company_owners
  await sql`
    INSERT INTO rise_private.company_owners (
      nanoid, company_nanoid, first_name, last_name, percentage, dob
    ) VALUES (
      'us-14IjGM_idzAo','co-813TCfwunZq3','Alice','Smith',50,'1985-06-15'
    )
    ON DUPLICATE KEY UPDATE
      first_name = VALUES(first_name),
      last_name  = VALUES(last_name),
      percentage = VALUES(percentage),
      dob        = VALUES(dob);
  `.execute(db)

  // 6) rise_private.documents
  await sql`
    INSERT INTO rise_private.documents (
      nanoid, type, has_signed, prefill, document_nanoid, document_version_nanoid
    ) VALUES (
      'us-14IjGM_idzAo','ownership',1,'{}','doc-14IjGM_idzAo','v1-doc-14IjGM'
    )
    ON DUPLICATE KEY UPDATE
      type                    = VALUES(type),
      has_signed              = VALUES(has_signed),
      prefill                 = VALUES(prefill),
      document_nanoid         = VALUES(document_nanoid),
      document_version_nanoid = VALUES(document_version_nanoid);
  `.execute(db)

  // 7) rise_private.company_contacts
  await sql`
    INSERT INTO rise_private.company_contacts (
      nanoid, type, email, fullname, phone
    ) VALUES (
      'co-813TCfwunZq3','admin','<EMAIL>','Admin A','+1111111111'
    )
    ON DUPLICATE KEY UPDATE
      type     = VALUES(type),
      email    = VALUES(email),
      fullname = VALUES(fullname),
      phone    = VALUES(phone);
  `.execute(db)

  // 8) rise_private.addresses
  await sql`
    INSERT INTO rise_private.addresses (
      nanoid, line_1, line_2, city, state, country, timezone, zip_code
    ) VALUES (
      'co-813TCfwunZq3','123 Alpha St','Suite 100','Metropolis','NY','US','EST','10001'
    )
    ON DUPLICATE KEY UPDATE
      line_1   = VALUES(line_1),
      line_2   = VALUES(line_2),
      city     = VALUES(city),
      state    = VALUES(state),
      country  = VALUES(country),
      timezone = VALUES(timezone),
      zip_code = VALUES(zip_code);
  `.execute(db)

  // 9) rise.symmetry_audit
  await sql`
    INSERT INTO rise.symmetry_audit (
      employee_nanoid, team_nanoid, payroll_program,
      gross_wages, subject_wages, ee_taxes, er_taxes,
      ee_retirement, er_retirement, json_input, json_output,
      pay_cycle, payroll_pay_schedule
    ) VALUES (
      'us-emp-1','te-team-01','riseworks_eor_us',
      500.00,500.00,54.33,38.25,0.00,0.00,
      /* JSON arrays omitted for brevity */
      JSON_ARRAY(JSON_OBJECT('dummy', TRUE)),
      JSON_ARRAY(JSON_OBJECT('dummy', TRUE)),
      '2025-04-P1','bimonthly'
    )
    ON DUPLICATE KEY UPDATE
      gross_wages          = VALUES(gross_wages),
      subject_wages        = VALUES(subject_wages),
      ee_taxes             = VALUES(ee_taxes),
      er_taxes             = VALUES(er_taxes),
      ee_retirement        = VALUES(ee_retirement),
      er_retirement        = VALUES(er_retirement),
      json_input           = VALUES(json_input),
      json_output          = VALUES(json_output),
      pay_cycle            = VALUES(pay_cycle),
      payroll_pay_schedule = VALUES(payroll_pay_schedule);
  `.execute(db)

  console.log('✅ MasterTax seed complete')
}
