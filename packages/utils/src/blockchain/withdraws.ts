import type { UsersWithdrawAccount } from '@riseworks/contracts/src/codegen/db/models_rise_private.js'
import type { ValidContracts } from '@riseworks/contracts/src/smartContractTypes.js'

const rampContractMap = {
  arbitrum_wallet: 'RiseRampWithdrawERC20Token',
  base_wallet: 'RiseRampWithdrawCCTP',
  coinbase: 'RiseRampWithdrawERC20Token',
  coinbase_wallet: 'RiseRampWithdrawERC20Token',
  token_swap: 'RiseRampWithdrawSwap',
  ethereum_wallet: 'RiseRampWithdrawCCTP',
  avalanche_wallet: 'RiseRampWithdrawCCTP',
  optimism_wallet: 'RiseRampWithdrawCCTP',
  polygon_wallet: 'RiseRampWithdrawCCTP',
  domestic_usd: 'RiseRampWithdrawUSDUS',
  europe: 'RiseRampWithdrawUnblock',
  gbp: 'RiseRampWithdrawUnblock',
  ngn: 'RiseRampWithdrawUnblock',
  international_usd: 'RiseRampWithdrawInternationalUSD',
  international_exchange: 'RiseRampWithdrawExchange',
  international_usd_default: 'RiseRampWithdrawInternationalUSDManual',
} satisfies Record<UsersWithdrawAccount['ramp'], ValidContracts>

export function getContractForRamp(ramp: UsersWithdrawAccount['ramp']) {
  return rampContractMap[ramp]
}
