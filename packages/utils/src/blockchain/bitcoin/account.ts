import {
  type BitcoinAddress,
  BitcoinAddressTypes,
  BitcoinNetworks,
  type DepositIndex,
  type EntityIndexInt,
  type SegwitAddress,
  type TransformedOwner,
  type ValidatedOwner,
  type Vin,
  ownerIdRefiner,
  ownerSchema,
  riseAccountIndexTransformer,
  transformedOwnerSchema,
  validatedBitcoinOwnerSchema,
} from '@riseworks/contracts/src/bitcoin.js'
import type { EvmAddress } from '@riseworks/contracts/src/formats.js'
import { z } from 'zod'
import assert from '../../common/assertHTTP.js'
import { isProduction } from '../../common/env.js'
import { getContext } from '../../common/requestContext.js'
import {
  type Psbt,
  type Signer,
  type WitnessScript,
  getKeysFromOwner,
} from './helpers.js'

const getOwnerId = (
  depositAddress: BitcoinAddress,
  changeAddress: BitcoinAddress,
  entityIndex: EntityIndexInt,
  riseAccount: EvmAddress,
  depositIndex: DepositIndex,
) =>
  `${depositAddress}:${changeAddress}:${entityIndex}:${riseAccount}:${depositIndex}`

const ownerTransformer = ownerSchema
  .refine(
    ({ depositAddress, changeAddress }) =>
      (depositAddress && changeAddress) || !(depositAddress || changeAddress),
    {
      message: 'Deposit and change addresses must be provided together',
    },
  )
  .transform(
    ({
      entityIndex,
      riseAccount,
      depositIndex,
      depositAddress,
      changeAddress,
    }) => {
      const network = isProduction
        ? BitcoinNetworks.Mainnet
        : BitcoinNetworks.Testnet
      const addressType = BitcoinAddressTypes.NativeSegwit
      const ownerId =
        depositAddress && changeAddress
          ? getOwnerId(
              depositAddress,
              changeAddress,
              entityIndex,
              riseAccount,
              depositIndex,
            )
          : undefined
      const riseAccountIndex = riseAccountIndexTransformer.parse(riseAccount)
      return transformedOwnerSchema.parse({
        entityIndex,
        riseAccount,
        riseAccountIndex,
        depositIndex,
        network,
        addressType,
        depositAddress,
        changeAddress,
        ownerId,
      })
    },
  )

const ownerIdTransformer = ownerIdRefiner
  .transform((ownerId) => {
    const [
      depositAddress,
      changeAddress,
      entityIndex,
      riseAccount,
      depositIndex,
    ] = ownerId.split(':')
    const owner = ownerTransformer.parse({
      entityIndex,
      riseAccount,
      depositIndex,
      depositAddress,
      changeAddress,
    })
    return {
      ownerId,
      owner,
    }
  })
  .superRefine(({ ownerId, owner }, ctx) => {
    if (owner.ownerId && owner.ownerId !== ownerId) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Owner ID mismatch: expected ${ownerId} but got ${owner.ownerId}`,
      })
    }
  })
  .transform((data) => data.owner)

const bitcoinOwnerTransformer = ownerTransformer.or(ownerIdTransformer)

const validatedBitcoinOwnerTransformer = bitcoinOwnerTransformer.transform(
  async (owner) => {
    using _ = getContext()
    const account = new BitcoinAccount(owner)
    return await account.validateOwner()
  },
)

class BitcoinAccount {
  public transformedOwner: TransformedOwner
  public validatedOwner: ValidatedOwner | undefined
  private wallet:
    | {
        depositWitnessScript: WitnessScript
        depositSigner: Signer
        changeWitnessScript: WitnessScript
        changeSigner: Signer
      }
    | undefined

  constructor(owner: TransformedOwner) {
    this.transformedOwner = owner
  }

  public async validateOwner() {
    using _ = getContext()
    const { depositKeys, changeKeys } = await getKeysFromOwner(
      this.transformedOwner,
    )
    const validatedOwnerId = getOwnerId(
      depositKeys.address,
      changeKeys.address,
      this.transformedOwner.entityIndex,
      this.transformedOwner.riseAccount,
      this.transformedOwner.depositIndex,
    )
    if (this.transformedOwner.ownerId) {
      assert(
        validatedOwnerId === this.transformedOwner.ownerId,
        `Derived owner ID ${validatedOwnerId} does not match validated owner ID ${this.transformedOwner.ownerId} -- check bitcoin-keypair in secrets or BITCOIN_KEYPAIR environment variable`,
      )
    }
    this.wallet = {
      depositWitnessScript: depositKeys.witnessScript,
      depositSigner: depositKeys.signer,
      changeWitnessScript: changeKeys.witnessScript,
      changeSigner: changeKeys.signer,
    }
    const validatedOwner = validatedBitcoinOwnerSchema.parse({
      ...this.transformedOwner,
      ownerId: validatedOwnerId,
      depositAddress: depositKeys.address,
      changeAddress: changeKeys.address,
    })
    this.validatedOwner = validatedOwner
    return validatedOwner
  }

  public getWitnessScript(address: SegwitAddress) {
    assert(
      this.validatedOwner && this.wallet,
      'BitcoinAccount is not validated',
    )
    assert(
      [
        this.validatedOwner.depositAddress,
        this.validatedOwner.changeAddress,
      ].includes(address),
      `Could not get witness script: ${this.validatedOwner.riseAccount} does not have access to ${address}`,
    )
    if (address === this.validatedOwner.depositAddress) {
      return this.wallet.depositWitnessScript
    }
    if (address === this.validatedOwner.changeAddress) {
      return this.wallet.changeWitnessScript
    }
    throw new Error(`Could not get witness script for address ${address}`)
  }

  public signTx(inputs: Vin[], unsignedPsbt: Psbt) {
    assert(
      this.validatedOwner && this.wallet,
      'BitcoinAccount is not validated',
    )
    let partiallySignedPsbt = unsignedPsbt
    for (const vin of inputs) {
      assert(
        [
          this.validatedOwner.depositAddress,
          this.validatedOwner.changeAddress,
        ].includes(vin.sourceAddress),
        `Could not sign input #${vin.vinIndex}: ${this.validatedOwner.riseAccount} does not have access to ${vin.sourceAddress}`,
      )
      const signer: Signer | undefined =
        vin.sourceAddress === this.validatedOwner.depositAddress
          ? this.wallet.depositSigner
          : this.wallet.changeSigner
      assert(signer, `Could not sign input #${vin.vinIndex}: signer not found`)
      partiallySignedPsbt = partiallySignedPsbt.signInput(vin.vinIndex, signer)
    }
    return partiallySignedPsbt.finalizeAllInputs()
  }
}
const bitcoinAccountSchema = z.instanceof(BitcoinAccount)

export {
  bitcoinOwnerTransformer,
  type TransformedOwner,
  validatedBitcoinOwnerTransformer,
  BitcoinAccount,
  bitcoinAccountSchema,
}
