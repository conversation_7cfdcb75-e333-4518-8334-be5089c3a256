import {
  type BitcoinAddress,
  BitcoinAddressTypes,
  type BitcoinTxHash,
  type SegwitAddress,
  type ValueBigInt,
  type Vin,
  type Vout,
  type VoutIndex,
  bitcoinAddressRegex,
  bitcoinTxHashRegex,
  bitcoinTxSchema,
  psbtHexParser,
  valueIntTransformer,
  vinSchema,
  voutIndexInt,
  voutSchema,
} from '@riseworks/contracts/src/bitcoin.js'
import assert from '../../common/assertHTTP.js'
import { is } from '../../common/is.js'
import { getContext } from '../../common/requestContext.js'
import { type BitcoinAccount, bitcoinAccountSchema } from './account.js'
import {
  type Psbt,
  createPsbt,
  getAddressFromWitnessScript,
  getPsbtFromHex,
} from './helpers.js'

class BitcoinTransaction {
  public psbt: Psbt
  protected account: BitcoinAccount | undefined

  constructor(psbtHexOrAccount: unknown) {
    using _ = getContext()
    if (is(psbtHexOrAccount, bitcoinAccountSchema)) {
      this.account = bitcoinAccountSchema.parse(psbtHexOrAccount)
      this.psbt = createPsbt()
    } else {
      const hex = psbtHexParser.parse(psbtHexOrAccount)
      this.psbt = getPsbtFromHex(hex)
    }
  }

  public inputs() {
    using _ = getContext()
    const inputs: Vin[] = []
    for (const [index, input] of Object.entries(this.psbt.data.inputs)) {
      const vinIndex = voutIndexInt.parse(index)
      const witnessScript = input.witnessUtxo?.script
      assert(
        witnessScript,
        `Could not get witness script for input #${vinIndex}`,
      )
      const address = getAddressFromWitnessScript(witnessScript)
      const txInput = this.psbt.txInputs[vinIndex]
      assert(txInput, `Could not get source tx for input #${vinIndex}`)
      const sourceTxHash = txInput.hash.reverse().toString('hex')
      const sourceTxVoutIndex = txInput.index
      const vin = vinSchema.parse({
        vinIndex,
        sourceTxHash,
        sourceTxVoutIndex,
        sourceAddress: address,
        value: input.witnessUtxo?.value,
      })
      inputs.push(vin)
    }
    return inputs.sort((a, b) => a.vinIndex - b.vinIndex)
  }

  public outputs() {
    using _ = getContext()
    const outputs: Vout[] = []
    for (const [voutIndex, output] of Object.entries(this.psbt.txOutputs)) {
      const vout = voutSchema.parse({
        voutIndex,
        address: output.address,
        value: output.value,
      })
      outputs.push(vout)
    }
    return outputs.sort((a, b) => a.voutIndex - b.voutIndex)
  }

  public inputTotal() {
    using _ = getContext()
    return this.inputs().reduce((total, input) => total + input.value, 0n)
  }

  public outputTotal() {
    using _ = getContext()
    return this.outputs().reduce((total, output) => total + output.value, 0n)
  }

  public fee() {
    using _ = getContext()
    const calculatedFee = this.inputTotal() - this.outputTotal()
    if (calculatedFee < 0n) {
      return 0n
    }
    return calculatedFee
  }

  public txHash() {
    using _ = getContext()
    try {
      return bitcoinTxHashRegex.parse(this.psbt.extractTransaction().getId())
    } catch (error) {
      return undefined
    }
  }

  public tx() {
    using _ = getContext()
    const txHash = this.txHash()
    const fee = this.fee()
    const vin = this.inputs()
    const vout = this.outputs()
    return bitcoinTxSchema.parse({ txHash, fee, vin, vout })
  }

  public psbtHex() {
    using _ = getContext()
    try {
      return psbtHexParser.parse(this.psbt.toHex())
    } catch (error) {
      return undefined
    }
  }

  public txHex() {
    using _ = getContext()
    try {
      return this.psbt.extractTransaction().toHex()
    } catch (error) {
      return undefined
    }
  }

  public uniqueInputAddresses() {
    using _ = getContext()
    const uniqueInputAddresses = new Set<string>()
    for (const [vinIndex, input] of Object.entries(this.psbt.data.inputs)) {
      const witnessScript = input.witnessUtxo?.script
      assert(
        witnessScript,
        `Could not get witness script for input #${vinIndex}`,
      )
      const address = getAddressFromWitnessScript(witnessScript)
      uniqueInputAddresses.add(address)
    }
    return Array.from(uniqueInputAddresses)
  }

  public uniqueOutputAddresses() {
    using _ = getContext()
    const uniqueOutputAddresses = new Set<string>()
    for (const [voutIndex, output] of Object.entries(this.psbt.txOutputs)) {
      assert(output.address, `Could not get address for output #${voutIndex}`)
      uniqueOutputAddresses.add(output.address)
    }
    return Array.from(uniqueOutputAddresses)
  }

  public isValid() {
    using ctx = getContext()
    if (this.inputTotal() !== this.outputTotal() + this.fee()) {
      ctx.logger.error(
        `Input total (${this.inputTotal()}) is not equal to output total (${this.outputTotal()}) + fee (${this.fee()})`,
      )
      return false
    }
    const twoPercent = this.inputTotal() / 50n
    const maxFee = twoPercent <= 10_000 ? 10_000 : twoPercent
    if (this.fee() >= maxFee) {
      ctx.logger.error(
        `Fee is too high: expected ${maxFee} or less for input total of ${this.inputTotal()}, got ${this.fee()}`,
      )
      return false
    }
    if (this.uniqueInputAddresses().length > 2) {
      ctx.logger.error(
        `Tx should contain a maximum of two addresses (i.e. deposit + change): got ${this.uniqueInputAddresses().length}`,
      )
      return false
    }
    const ownedAddresses = this.account?.validatedOwner
      ? [
          this.account.validatedOwner.depositAddress,
          this.account.validatedOwner.changeAddress,
        ]
      : []
    if (
      this.account &&
      this.uniqueInputAddresses().some(
        (address) => !ownedAddresses.includes(address),
      )
    ) {
      ctx.logger.error(
        `Tx should only contain inputs from the owner's addresses: expected ${ownedAddresses.join(', ')}, got ${this.uniqueInputAddresses().join(', ')}`,
      )
      return false
    }
    return true
  }

  protected addInput(
    address: SegwitAddress,
    value: ValueBigInt,
    txHash: BitcoinTxHash,
    voutIndex: VoutIndex,
  ) {
    using ctx = getContext()
    assert(this.account, 'Tx is not initialized with an account')
    if (this.uniqueInputAddresses().includes(address)) {
      ctx.logger.warn(
        `Multiple inputs from the same address detected: ${address}`,
      )
    } else {
      assert(
        this.uniqueInputAddresses().length < 2,
        'Tx already contains inputs from two addresses (i.e. deposit + change)',
      )
    }
    const witnessScript = this.account.getWitnessScript(address)
    const safeValue = valueIntTransformer.parse(value)
    this.psbt = this.psbt.addInput({
      hash: txHash,
      index: voutIndex,
      witnessUtxo: { script: witnessScript, value: safeValue },
    })
    return this.psbt
  }

  protected addOutput(address: BitcoinAddress, value: ValueBigInt) {
    using _ = getContext()
    assert(
      !this.uniqueOutputAddresses().includes(address),
      'Tx already contains this address in outputs',
    )
    assert(
      is(address, bitcoinAddressRegex),
      `Expected valid address of type "${Object.values(BitcoinAddressTypes).join('", "')}", got ${address}`,
    )
    const safeValue = valueIntTransformer.parse(value)
    this.psbt = this.psbt.addOutput({ address, value: safeValue })
    return this.psbt
  }

  protected signTx() {
    using _ = getContext()
    assert(this.isValid(), 'Tx is invalid')
    assert(this.account, 'Tx is not initialized with an account')
    this.psbt = this.account.signTx(this.inputs(), this.psbt)
    const signedTxHash = this.txHash()
    const signedTxHex = this.txHex()
    assert(signedTxHash && signedTxHex, 'Tx could not be signed')
    return {
      signedTxHash,
      signedTxHex,
    }
  }
}

export { BitcoinTransaction }
