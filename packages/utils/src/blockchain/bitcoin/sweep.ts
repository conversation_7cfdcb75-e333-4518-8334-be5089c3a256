import {
  type BitcoinAddress,
  BitcoinAddressTypes,
  type Utxo,
  type ValueBigInt,
  type ValueInt,
  bitcoinAddressParser,
} from '@riseworks/contracts/src/bitcoin.js'
import assert from '../../common/assertHTTP.js'
import { getContext } from '../../common/requestContext.js'
import { BitcoinTransaction } from './transaction.js'

class BitcoinSweepTransaction extends BitcoinTransaction {
  public maxOutputValue: ValueBigInt | undefined

  private addInputs(inputs: Utxo[]) {
    using _ = getContext()
    let psbt = this.psbt
    for (const utxo of inputs) {
      psbt = this.addInput(
        utxo.address,
        utxo.value,
        utxo.tx.txHash,
        utxo.voutIndex,
      )
    }
    return psbt
  }

  private setMaxOutputValue = (feeRate: ValueInt) => {
    using _ = getContext()
    const outputCount = 1
    // Fixed overhead: 10.5 vbytes (SegWit)
    const overheadVbytes = 10.5
    // Each P2WPKH input: ~67.25 vbytes
    const inputVbytes = 67.25
    // Each P2WPKH output: 31 vbytes
    const outputVbytes = 31
    // Total vbytes = overhead + (inputs * inputVbytes) + (outputs * outputVbytes)
    const totalVbytes = Math.ceil(
      overheadVbytes +
        this.inputs().length * inputVbytes +
        outputCount * outputVbytes,
    )
    // Fee = fee rate (sat/byte) * total vbytes
    const fee = Math.max(Math.ceil(totalVbytes * feeRate), 500) // 500 sats -- minimum relay fee
    const maxOutputValue = this.inputTotal() - BigInt(fee)
    this.maxOutputValue = maxOutputValue
    return this.maxOutputValue
  }

  constructor(psbtHexOrAccount: unknown, inputs: Utxo[], feeRate: ValueInt) {
    using _ = getContext()
    super(psbtHexOrAccount)
    this.addInputs(inputs)
    this.setMaxOutputValue(feeRate)
  }

  public addSweepOutput(address: BitcoinAddress, value: ValueBigInt) {
    using ctx = getContext()
    assert(
      !this.uniqueOutputAddresses().length,
      'Sweep tx already contains outputs',
    )
    const outputAddressType = bitcoinAddressParser.parse(address).addressType
    if (outputAddressType !== BitcoinAddressTypes.Taproot) {
      ctx.logger.warn(
        `Expected output address type "${BitcoinAddressTypes.Taproot}", got "${outputAddressType}"`,
      )
    }
    return this.addOutput(address, value)
  }

  public isValid() {
    using ctx = getContext()
    if (this.uniqueOutputAddresses().length !== 1) {
      ctx.logger.error(
        `Sweep tx should contain a single output: got ${this.uniqueOutputAddresses().length}`,
      )
      return false
    }
    const outputAddressTypes = this.uniqueOutputAddresses().map(
      (address) => bitcoinAddressParser.parse(address).addressType,
    )
    if (
      outputAddressTypes.some(
        (addressType) => addressType !== BitcoinAddressTypes.Taproot,
      )
    ) {
      ctx.logger.warn(
        `Expected output address type "${BitcoinAddressTypes.Taproot}", got "${outputAddressTypes.join('", "')}")`,
      )
    }
    return super.isValid()
  }

  public signTx() {
    using _ = getContext()
    assert(this.isValid(), 'Sweep tx is invalid')
    return super.signTx()
  }
}

export { BitcoinSweepTransaction }
