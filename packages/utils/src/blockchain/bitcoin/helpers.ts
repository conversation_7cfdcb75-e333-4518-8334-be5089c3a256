import { psbtHex<PERSON>arser, segwitRegex } from '@riseworks/contracts/src/bitcoin.js'
import BIP32Factory from 'bip32'
import * as bitcoin from 'bitcoinjs-lib'
import * as ecc from 'tiny-secp256k1'
import { z } from 'zod'
import assert from '../../common/assertHTTP.js'
import { isProduction } from '../../common/env.js'
import { is } from '../../common/is.js'
import { getContext } from '../../common/requestContext.js'
import { getSecrets } from '../../google/getSecrets.js'
import type { TransformedOwner } from './account.js'

bitcoin.initEccLib(ecc)
const bip32 = BIP32Factory(ecc)

type Psbt = bitcoin.Psbt
type PsbtInput = Psbt['data']['inputs'][number]
type WitnessScript = NonNullable<PsbtInput['witnessUtxo']>['script']
type Signer = bitcoin.Signer

// BIP32 prefixes from https://github.com/satoshilabs/slips/blob/master/slip-0132.md#registered-hd-version-bytes
const mainnet = {
  ...bitcoin.networks.bitcoin,
  bip32: {
    public: 0x04b24746, // zpub BIP84
    private: 0x04b2430c, // zprv BIP84
  },
}
const testnet = {
  ...bitcoin.networks.testnet,
  bip32: {
    public: 0x045f1cf6, // vpub BIP84
    private: 0x045f18bc, // vprv BIP84
  },
}
// TODO: Find better solution for local mainnet testing
const network = isProduction ? mainnet : testnet

const rootPathRegex = z
  .string()
  .regex(/^m\/84'\/(0|1)'\/3500745'$/)
  .refine(() => true, {
    message: 'Invalid rootPath',
  })

const accountIndexInt = z.coerce
  .number()
  .int()
  .nonnegative()
  .max(2 ** 32)
const nodePathRegex = z
  .string()
  .regex(/^([01])\/([01])\/(\d+)\/0$/)
  .refine(
    (path) => {
      const [accountIndexRaw] = path.split('/').slice(2)
      return is(accountIndexRaw, accountIndexInt)
    },
    {
      message: 'Invalid nodePath',
    },
  )

const getAddressFromWitnessScript = (witnessScript: WitnessScript) =>
  bitcoin.address.fromOutputScript(witnessScript, network)

// Don't export under any circumstances.
const getKeysFromPath = async (path: string) => {
  using ctx = getContext()
  const nodePath = nodePathRegex.parse(path)
  const hardenedIndex = Array.from('rise').reduce(
    (acc, char) => ((acc << 5) - acc + char.charCodeAt(0)) & 0x7fffffff,
    0,
  )
  const networkIndex = isProduction ? 0 : 1
  const rootPath = rootPathRegex.parse(
    `m/84'/${networkIndex}'/${hardenedIndex}'`,
  )
  const { 'bitcoin-keypair': bitcoinKeypair } = await getSecrets([
    'bitcoin-keypair',
  ])
  console.log('bitcoinKeypair', bitcoinKeypair)
  const [xpub, xpriv] = bitcoinKeypair.split(':')
  const networkName = isProduction ? 'mainnet' : 'testnet'
  if (!(xpub && xpriv)) {
    ctx.logger.error(
      `Please set bitcoin-keypair in secrets or BITCOIN_KEYPAIR environment variable: should be derived from ${rootPath} (BIP84 Segwit, ${networkName}, 'rise')`,
    )
  }
  assert(
    (isProduction && xpub?.startsWith('zpub') && xpriv?.startsWith('zprv')) ||
      (!isProduction && xpub?.startsWith('vpub') && xpriv?.startsWith('vprv')),
    'Could not get Bitcoin keypair',
  )
  assert(xpub, 'Could not get Bitcoin public key')
  assert(xpriv, 'Could not get Bitcoin private key')
  const publicRoot = bip32.fromBase58(xpub, network)
  const privateRoot = bip32.fromBase58(xpriv, network)
  assert(
    publicRoot.depth === 3,
    `Unexpected public root depth: expected 3, got ${publicRoot.depth}`,
  )
  assert(
    privateRoot.depth === 3,
    `Unexpected private root depth: expected 3, got ${privateRoot.depth}`,
  )
  assert(publicRoot.isNeutered(), 'Public root is not neutered')
  assert(!privateRoot.isNeutered(), 'Private root is neutered')
  assert(
    Buffer.from(privateRoot.fingerprint).toString('hex') ===
      Buffer.from(publicRoot.fingerprint).toString('hex'),
    'Private and public roots have different fingerprints',
  )
  const publicNode = publicRoot.derivePath(nodePath)
  const privateNode = privateRoot.derivePath(nodePath)
  assert(
    publicNode.depth === 7,
    `Unexpected public node depth: expected 7, got ${publicNode.depth}`,
  )
  assert(
    privateNode.depth === 7,
    `Unexpected private node depth: expected 7, got ${privateNode.depth}`,
  )
  assert(publicNode.isNeutered(), 'Public node is not neutered')
  assert(!privateNode.isNeutered(), 'Private node is neutered')
  assert(
    Buffer.from(privateNode.fingerprint).toString('hex') ===
      Buffer.from(publicNode.fingerprint).toString('hex'),
    'Private and public nodes have different fingerprints',
  )
  const { address: rawAddress, output: witnessScript } =
    bitcoin.payments.p2wpkh({
      pubkey: Buffer.from(publicNode.publicKey),
      network,
    })
  assert(witnessScript, 'Could not get witness script')
  const expectedAddress = segwitRegex.parse(rawAddress)
  const address = getAddressFromWitnessScript(witnessScript)
  assert(
    address === expectedAddress,
    `Address mismatch: expected ${expectedAddress}, got ${address}`,
  )
  const signer: Signer = {
    publicKey: Buffer.from(privateNode.publicKey),
    sign: (hash: Buffer) => Buffer.from(privateNode.sign(hash)),
  }
  return {
    address,
    witnessScript,
    signer,
  }
}

// Don't export anywhere except for bitcoin/account!
const getKeysFromOwner = async (owner: TransformedOwner) => {
  const depositNodePath = `0/${owner.entityIndex}/${owner.riseAccountIndex}/${owner.depositIndex}`
  const changeNodePath = `1/${owner.entityIndex}/${owner.riseAccountIndex}/${owner.depositIndex}`
  const depositKeys = await getKeysFromPath(depositNodePath)
  const changeKeys = await getKeysFromPath(changeNodePath)
  return {
    depositKeys,
    changeKeys,
  }
}

const createPsbt = () => {
  return new bitcoin.Psbt({ network })
}

const getPsbtFromHex = (hexString: string) => {
  const hex = psbtHexParser.parse(hexString)
  return bitcoin.Psbt.fromHex(hex)
}

export {
  type Psbt,
  type WitnessScript,
  type Signer,
  getAddressFromWitnessScript,
  getKeysFromOwner,
  createPsbt,
  getPsbtFromHex,
}
