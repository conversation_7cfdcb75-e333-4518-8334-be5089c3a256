import assert from '../common/assertHTTP.js'

export const fixedOrPercent = {
  exactUnitAmount: '0',
  percentOfRequestBalance: '1',
  percentOfLastAmountTransferred: '2',
  percentOfCurrentBalance: '3',
  percentOfSourceBalance: '4',
  exactAmountFromSource: '5',
} as const

const fixedOrPercentInverse = {
  '0': 'exactUnitAmount',
  '1': 'percentOfRequestBalance',
  '2': 'percentOfLastAmountTransferred',
  '3': 'percentOfCurrentBalance',
  '4': 'percentOfSourceBalance',
  '5': 'exactAmountFromSource',
} as const

export const getFixedOrPercent = (fixedOrPercent: string | bigint) => {
  const value = fixedOrPercent.toString()
  assert(
    Object.keys(fixedOrPercentInverse).includes(value),
    'Invalid fixedOrPercent',
  )
  return fixedOrPercentInverse[value as keyof typeof fixedOrPercentInverse]
}
