import { getBytes, keccak256 } from 'ethers'

// NOTE: this is copied and adpated from ethers.js since they don't export it(check the code inside ethers.getAddress function)
export const getChecksumAddress = (address: string) => {
  const lowerCaseAddress = address.toLowerCase()

  const chars = lowerCaseAddress.substring(2).split('')

  const expanded = new Uint8Array(40)

  chars.forEach((char, index) => {
    expanded[index] = char.charCodeAt(0)
  })

  const hashed = getBytes(keccak256(expanded))

  for (let i = 0; i < 40; i += 2) {
    // @ts-ignore possible undefined access
    if (hashed[i >> 1] >> 4 >= 8) {
      chars[i] = chars[i]!.toUpperCase()
    }
    // @ts-ignore possible undefined access
    if ((hashed[i >> 1] & 0x0f) >= 8) {
      chars[i + 1] = chars[i + 1]!.toUpperCase()
    }
  }

  return `0x${chars.join('')}`
}
