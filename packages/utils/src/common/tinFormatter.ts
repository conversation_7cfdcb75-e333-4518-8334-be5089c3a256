import assert from './assertHTTP.js'
import { z } from 'zod'

const ssnSchema = /^\d{3}-\d{2}-\d{4}$/

export const formatSSN = z
  .function()
  .args(z.string())
  .returns(z.string())
  .implement((ssn) => {
    if (ssnSchema.test(ssn)) return ssn

    const digits = validateTIN(ssn)
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5)}`
  })

export const formatTinToDigitsOnly = z
  .function()
  .args(z.string())
  .returns(z.string())
  .implement((tin) => {
    const digits = validateTIN(tin)
    return digits
  })

export const formatEIN = z
  .function()
  .args(z.string())
  .returns(z.string())
  .implement((ein) => {
    const digits = validateTIN(ein)
    return `${digits.slice(0, 2)}-${digits.slice(2)}`
  })

function validateTIN(tin: string): string {
  const digits = tin.replace(/\D/g, '')
  assert(digits.length === 9, 'TIN must have 9 digits')
  return digits
}
