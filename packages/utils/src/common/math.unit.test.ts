import {
  divideWithoutRemainder,
  applyPercentageDiscount,
  deductFrom,
} from './math.js'
import { describe, expect, test } from 'vitest'

describe('divideWithoutRemainder', () => {
  test('should divide without remainder', () => {
    const testCases: { input: [number, number]; expected: number[] }[] = [
      { input: [100, 3], expected: [33.34, 33.33, 33.33] },
      {
        input: [1000, 10],
        expected: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
      },
      {
        input: [5000, 11],
        expected: [
          454.55, 454.55, 454.55, 454.55, 454.55, 454.55, 454.54, 454.54,
          454.54, 454.54, 454.54,
        ],
      },
      { input: [100, 1], expected: [100] },
      { input: [33.33, 2], expected: [16.67, 16.66] },
    ]

    for (const { input, expected } of testCases) {
      const [amount, divisor] = input
      const result = divideWithoutRemainder(amount, divisor)
      expect(result).toEqual(expected)
      expect(result.length).toEqual(divisor)
      expect(result.reduce((acc, curr) => acc + curr, 0)).toEqual(amount)
    }
  })
})

describe('applyPercentageDiscount', () => {
  test('should apply percentage discount correctly', () => {
    const testCases: { input: [number, number]; expected: number }[] = [
      { input: [100, 25], expected: 75 },
      { input: [200, 10], expected: 180 },
      { input: [50, 50], expected: 25 },
      { input: [99.99, 5], expected: 94.99 },
      { input: [1000, 0], expected: 1000 },
      { input: [1000, 100], expected: 0 },
    ]

    for (const { input, expected } of testCases) {
      const [value, discount] = input
      const result = applyPercentageDiscount(value, discount)
      expect(result).toBeCloseTo(expected, 2)
    }
  })
})

describe('deductFrom', () => {
  test('should deduct amount correctly and handle remainders', () => {
    const testCases: { input: [number, number]; expected: [number, number] }[] =
      [
        { input: [100, 50], expected: [50, 0] },
        { input: [50, 100], expected: [0, 50] },
        { input: [100, 100], expected: [0, 0] },
        { input: [100, 0], expected: [100, 0] },
        { input: [0, 50], expected: [0, 50] },
        { input: [0, 0], expected: [0, 0] },
        { input: [50, -10], expected: [50, 0] },
        { input: [75.5, 25.25], expected: [50.25, 0] },
        { input: [25.25, 75.5], expected: [0, 50.25] },
      ]

    for (const { input, expected } of testCases) {
      const [amount, deduction] = input
      const result = deductFrom(amount, deduction)
      expect(result).toEqual(expected)
    }
  })
})
