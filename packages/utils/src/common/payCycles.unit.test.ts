import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import { describe, expect, test, vi } from 'vitest'
import {
  comparePayCycles,
  getDueDate,
  getLastDateToMakeChanges,
  getNextPayCycle,
  getPayCycle,
  getPayCycleDates,
  getPayCycleStartEnd,
  getPayCycleToTakeEffect,
  getPayDate,
  getPayPeriodNumber,
  getPreviousPayCycle,
  isFirstMonthOfEmployment,
  isNextPayCycle,
  isSamePayCycle,
  payCycleToString,
} from './payCycles.js'

describe('Test payCycles functions', () => {
  test('getPayCycle', () => {
    const cases: Record<string, PayCycle> = {
      '2024-02-01': { year: 2024, month: 2, period: 1 },
      '2024-02-15': { year: 2024, month: 2, period: 1 },
      '2024-02-16': { year: 2024, month: 2, period: 2 },
      '2024-02-29': { year: 2024, month: 2, period: 2 },
      '2024-03-01': { year: 2024, month: 3, period: 1 },
      '2024-03-15': { year: 2024, month: 3, period: 1 },
      '2024-03-16': { year: 2024, month: 3, period: 2 },
      '2024-03-31': { year: 2024, month: 3, period: 2 },
      '2024-12-01': { year: 2024, month: 12, period: 1 },
      '2025-02-15': { year: 2025, month: 2, period: 1 },
    }

    for (const [date, expected] of Object.entries(cases)) {
      const actual = getPayCycle(date)
      expect(actual).toEqual(expected)
    }
  })

  test('getNextPayCycle', () => {
    const cases: { input: PayCycle; expected: PayCycle }[] = [
      {
        input: { year: 2022, month: 2, period: 1 },
        expected: { year: 2022, month: 2, period: 2 },
      },
      {
        input: { year: 2022, month: 2, period: 2 },
        expected: { year: 2022, month: 3, period: 1 },
      },
      {
        input: { year: 2022, month: 12, period: 1 },
        expected: { year: 2022, month: 12, period: 2 },
      },
      {
        input: { year: 2022, month: 12, period: 2 },
        expected: { year: 2023, month: 1, period: 1 },
      },
      {
        input: { year: 2025, month: 2, period: 1 },
        expected: { year: 2025, month: 2, period: 2 },
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getNextPayCycle(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getPayDate', () => {
    const cases: { input: PayCycle; expected: string }[] = [
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: '2025-02-14',
      },
      {
        input: { year: 2024, month: 2, period: 2 },
        expected: '2024-02-15',
      },
      {
        input: { year: 2024, month: 3, period: 1 },
        expected: '2024-02-29',
      },
      {
        input: { year: 2024, month: 3, period: 2 },
        expected: '2024-03-15',
      },
      {
        input: { year: 2024, month: 4, period: 1 },
        expected: '2024-03-29',
      },
      {
        input: { year: 2024, month: 12, period: 2 },
        expected: '2024-12-13',
      },
      {
        input: { year: 2025, month: 1, period: 1 },
        expected: '2024-12-31',
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getPayDate(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getDueDate', () => {
    const cases: { input: PayCycle; expected: string }[] = [
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: '2025-02-12',
      },
      {
        input: { year: 2024, month: 2, period: 2 },
        expected: '2024-02-13',
      },
      {
        input: { year: 2024, month: 3, period: 1 },
        expected: '2024-02-27',
      },
      {
        input: { year: 2024, month: 3, period: 2 },
        expected: '2024-03-13',
      },
      {
        input: { year: 2024, month: 4, period: 1 },
        expected: '2024-03-27',
      },
      {
        input: { year: 2024, month: 12, period: 2 },
        expected: '2024-12-11',
      },
      {
        input: { year: 2025, month: 1, period: 1 },
        expected: '2024-12-27',
      },
      {
        input: { year: 2025, month: 4, period: 1 },
        expected: '2025-03-27',
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getDueDate(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getPreviousPayCycle', () => {
    const cases: { input: PayCycle; expected: PayCycle }[] = [
      {
        input: { year: 2022, month: 2, period: 2 },
        expected: { year: 2022, month: 2, period: 1 },
      },
      {
        input: { year: 2022, month: 3, period: 1 },
        expected: { year: 2022, month: 2, period: 2 },
      },
      {
        input: { year: 2022, month: 1, period: 1 },
        expected: { year: 2021, month: 12, period: 2 },
      },
      {
        input: { year: 2023, month: 1, period: 1 },
        expected: { year: 2022, month: 12, period: 2 },
      },
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: { year: 2025, month: 2, period: 1 },
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getPreviousPayCycle(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getLastDateToMakeChanges', () => {
    const cases: { input: PayCycle; expected: string }[] = [
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: '2025-02-10',
      },
      {
        input: { year: 2024, month: 2, period: 2 },
        expected: '2024-02-09',
      },
      {
        input: { year: 2024, month: 3, period: 1 },
        expected: '2024-02-23',
      },
      {
        input: { year: 2024, month: 3, period: 2 },
        expected: '2024-03-11',
      },
      {
        input: { year: 2024, month: 12, period: 2 },
        expected: '2024-12-09',
      },
      {
        input: { year: 2025, month: 1, period: 1 },
        expected: '2024-12-25',
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getLastDateToMakeChanges(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getPayCycleStartEnd', () => {
    const cases: {
      input: PayCycle
      expected: { start: string; end: string }
    }[] = [
      {
        input: { year: 2024, month: 2, period: 1 },
        expected: { start: '2024-02-01', end: '2024-02-15' },
      },
      {
        input: { year: 2024, month: 2, period: 2 },
        expected: { start: '2024-02-16', end: '2024-02-29' },
      },
      {
        input: { year: 2024, month: 4, period: 2 },
        expected: { start: '2024-04-16', end: '2024-04-30' },
      },
      {
        input: { year: 2025, month: 2, period: 1 },
        expected: { start: '2025-02-01', end: '2025-02-15' },
      },
      {
        input: { year: 2025, month: 3, period: 2 },
        expected: { start: '2025-03-16', end: '2025-03-31' },
      },
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: { start: '2025-02-16', end: '2025-02-28' },
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getPayCycleStartEnd(input)
      expect(actual).toEqual(expected)
    }
  })

  test('getPayCycleDates', () => {
    // Test for first period of February 2024 (leap year)
    const feb2024Period1 = { year: 2024, month: 2, period: 1 }
    const feb2024Period1Result = getPayCycleDates(feb2024Period1)

    expect(feb2024Period1Result.pay_date).toEqual('2024-01-31')
    expect(feb2024Period1Result.due_date).toEqual('2024-01-29')
    expect(feb2024Period1Result.last_date_to_make_changes).toEqual('2024-01-25')
    expect(feb2024Period1Result.start).toEqual('2024-02-01')
    expect(feb2024Period1Result.end).toEqual('2024-02-15')
    expect(feb2024Period1Result.days.length).toBeGreaterThan(0)
    expect(
      feb2024Period1Result.days.every((d) => new Date(d).getUTCDate() <= 15),
    ).toBe(true)

    // Test for first period of January 2025
    const jan2025Period1 = { year: 2025, month: 1, period: 1 }
    const jan2025Period1Result = getPayCycleDates(jan2025Period1)

    expect(jan2025Period1Result.pay_date).toEqual('2024-12-31')
    expect(jan2025Period1Result.due_date).toEqual('2024-12-27')
    expect(jan2025Period1Result.last_date_to_make_changes).toEqual('2024-12-25')
    expect(jan2025Period1Result.start).toEqual('2025-01-01')
    expect(jan2025Period1Result.end).toEqual('2025-01-15')
    expect(jan2025Period1Result.days.length).toBeGreaterThan(0)
    expect(
      jan2025Period1Result.days.every((d) => new Date(d).getUTCDate() <= 15),
    ).toBe(true)

    // Test for second period of March 2024
    const mar2024Period2 = { year: 2024, month: 3, period: 2 }
    const mar2024Period2Result = getPayCycleDates(mar2024Period2)

    expect(mar2024Period2Result.pay_date).toEqual('2024-03-15')
    expect(mar2024Period2Result.due_date).toEqual('2024-03-13')
    expect(mar2024Period2Result.last_date_to_make_changes).toEqual('2024-03-11')
    expect(mar2024Period2Result.start).toEqual('2024-03-16')
    expect(mar2024Period2Result.end).toEqual('2024-03-31')
    expect(mar2024Period2Result.days.length).toBeGreaterThan(0)
    expect(
      mar2024Period2Result.days.every((d) => new Date(d).getUTCDate() > 15),
    ).toBe(true)
  })

  test('isSamePayCycle', () => {
    const cases: { pc1: PayCycle; pc2: PayCycle; expected: boolean }[] = [
      {
        pc1: { year: 2025, month: 2, period: 1 },
        pc2: { year: 2025, month: 2, period: 1 },
        expected: true,
      },
      {
        pc1: { year: 2025, month: 2, period: 1 },
        pc2: { year: 2025, month: 2, period: 2 },
        expected: false,
      },
      {
        pc1: { year: 2025, month: 2, period: 1 },
        pc2: { year: 2025, month: 3, period: 1 },
        expected: false,
      },
      {
        pc1: { year: 2025, month: 2, period: 1 },
        pc2: { year: 2024, month: 2, period: 1 },
        expected: false,
      },
      {
        pc1: { year: 2025, month: 12, period: 2 },
        pc2: { year: 2025, month: 12, period: 2 },
        expected: true,
      },
    ]

    for (const { pc1, pc2, expected } of cases) {
      const actual = isSamePayCycle(pc1, pc2)
      expect(actual).toEqual(expected)
    }
  })

  test('comparePayCycles', () => {
    const cases: {
      current: PayCycle
      reference: PayCycle
      expected: -1 | 0 | 1
    }[] = [
      {
        current: { year: 2024, month: 2, period: 1 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: -1,
      },
      {
        current: { year: 2026, month: 2, period: 1 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: 1,
      },
      {
        current: { year: 2025, month: 1, period: 1 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: -1,
      },
      {
        current: { year: 2025, month: 3, period: 1 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: 1,
      },
      {
        current: { year: 2025, month: 2, period: 1 },
        reference: { year: 2025, month: 2, period: 2 },
        expected: -1,
      },
      {
        current: { year: 2025, month: 2, period: 2 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: 1,
      },
      {
        current: { year: 2025, month: 2, period: 1 },
        reference: { year: 2025, month: 2, period: 1 },
        expected: 0,
      },
    ]

    for (const { current, reference, expected } of cases) {
      const actual = comparePayCycles(current, reference)
      expect(actual).toEqual(expected)
    }
  })

  test('payCycleToString', () => {
    const cases: { input: PayCycle; expected: string }[] = [
      {
        input: { year: 2025, month: 2, period: 1 },
        expected: '2025-02-P1',
      },
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: '2025-02-P2',
      },
      {
        input: { year: 2025, month: 12, period: 1 },
        expected: '2025-12-P1',
      },
      {
        input: { year: 2024, month: 1, period: 2 },
        expected: '2024-01-P2',
      },
    ]

    for (const { input, expected } of cases) {
      const actual = payCycleToString(input)
      expect(actual).toEqual(expected)
    }
  })

  test('isNextPayCycle', () => {
    // Mock the current date for consistent testing
    const mockToday = '2025-03-05'
    vi.setSystemTime(new Date(mockToday))

    // Current pay cycle is March 2025 Period 1
    // Next pay cycle is March 2025 Period 2

    expect(isNextPayCycle(new Date('2025-03-16'))).toBe(true) // March P2
    expect(isNextPayCycle(new Date('2025-03-31'))).toBe(true) // March P2
    expect(isNextPayCycle(new Date('2025-04-01'))).toBe(false) // April P1
    expect(isNextPayCycle(new Date('2025-03-10'))).toBe(false) // March P1 (current)

    vi.useRealTimers()
  })

  test('getPayCycleToTakeEffect', () => {
    // Test when we're still before the cutoff for next pay cycle
    vi.setSystemTime(new Date('2025-03-01'))

    // Current cycle: March 2025 P1
    // Next cycle: March 2025 P2
    // If we're before the cutoff, next cycle should take effect
    const result1 = getPayCycleToTakeEffect()
    expect(result1.year).toBe(2025)
    expect(result1.month).toBe(3)
    expect(result1.period).toBe(2)

    // Test when we're after the cutoff for next pay cycle
    vi.setSystemTime(new Date('2025-03-20'))

    // Should return the cycle after next
    const result2 = getPayCycleToTakeEffect()
    expect(result2.year).toBe(2025)
    expect(result2.month).toBe(4)
    expect(result2.period).toBe(1)

    vi.useRealTimers()
  })

  test('isFirstMonthOfEmployment', () => {
    const cases: {
      currentCycle: PayCycle
      employeeStartDate: Date
      expected: boolean
    }[] = [
      {
        currentCycle: { year: 2025, month: 3, period: 1 },
        employeeStartDate: new Date('2025-03-05'),
        expected: true,
      },
      {
        currentCycle: { year: 2025, month: 3, period: 2 },
        employeeStartDate: new Date('2025-03-01'),
        expected: true,
      },
      {
        currentCycle: { year: 2025, month: 4, period: 1 },
        employeeStartDate: new Date('2025-03-15'),
        expected: false,
      },
      {
        currentCycle: { year: 2025, month: 3, period: 1 },
        employeeStartDate: new Date('2025-02-28'),
        expected: false,
      },
      {
        currentCycle: { year: 2025, month: 1, period: 1 },
        employeeStartDate: new Date('2024-12-31'),
        expected: false,
      },
    ]

    for (const { currentCycle, employeeStartDate, expected } of cases) {
      const actual = isFirstMonthOfEmployment({
        currentCycle,
        employeeStartDate,
      })
      expect(actual).toEqual(expected)
    }
  })

  test('getPayPeriodNumber', () => {
    const cases: { input: PayCycle; expected: number }[] = [
      {
        input: { year: 2025, month: 1, period: 1 },
        expected: 1,
      },
      {
        input: { year: 2025, month: 1, period: 2 },
        expected: 2,
      },
      {
        input: { year: 2025, month: 2, period: 1 },
        expected: 3,
      },
      {
        input: { year: 2025, month: 2, period: 2 },
        expected: 4,
      },
      {
        input: { year: 2025, month: 6, period: 1 },
        expected: 11,
      },
      {
        input: { year: 2025, month: 6, period: 2 },
        expected: 12,
      },
      {
        input: { year: 2025, month: 12, period: 1 },
        expected: 23,
      },
      {
        input: { year: 2025, month: 12, period: 2 },
        expected: 24,
      },
    ]

    for (const { input, expected } of cases) {
      const actual = getPayPeriodNumber(input)
      expect(actual).toEqual(expected)
    }
  })
})
