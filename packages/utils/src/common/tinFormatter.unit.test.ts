import { describe, it, expect } from 'vitest'
import { formatEIN, formatSSN, formatTinToDigitsOnly } from './tinFormatter.js'

describe('tinFormatter', () => {
  describe('formatSSN', () => {
    it('formats unformatted SSN', () => {
      const result = formatSSN('123456789')
      expect(result).toBe('***********')
    })

    it('returns already formatted SSN', () => {
      const result = formatSSN('***********')
      expect(result).toBe('***********')
    })

    it('throws error on invalid length when less than 9 digits', () => {
      expect(() => formatSSN('12345')).toThrow('TIN must have 9 digits')
    })

    it('throws error on invalid format for alpha-chars', () => {
      expect(() => formatSSN('abc-def-ghij')).toThrow()
    })

    it('throws error on invalid length when formatted and more than 9 digits', () => {
      expect(() => formatSSN('123-456-7890')).toThrow('TIN must have 9 digits')
    })

    it('throws error on invalid length when unformatted and more than 9 digits', () => {
      expect(() => formatSSN('1234567890')).toThrow('TIN must have 9 digits')
    })
  })
  describe('formatTinToDigitOnly', () => {
    it('strips formatting for formatted SSN', () => {
      const result = formatTinToDigitsOnly('***********')
      expect(result).toBe('123456789')
    })

    it('strips formatting for formatted EIN', () => {
      const result = formatTinToDigitsOnly('12-3456789')
      expect(result).toBe('123456789')
    })

    it('returns already stripped TIN', () => {
      const result = formatTinToDigitsOnly('123456789')
      expect(result).toBe('123456789')
    })

    it('throws error for invalid length', () => {
      expect(() => formatTinToDigitsOnly('1234567890')).toThrow(
        'TIN must have 9 digits',
      )
    })
  })

  describe('formatEIN', () => {
    it('formats unformatted EIN', () => {
      const result = formatEIN('123456789')
      expect(result).toBe('12-3456789')
    })

    it('returns already formatted EIN', () => {
      const result = formatEIN('12-3456789')
      expect(result).toBe('12-3456789')
    })

    it('returns EIN format when passed SSN format', () => {
      const result = formatEIN('***********')
      expect(result).toBe('12-3456789')
    })

    it('throws error for invalid length', () => {
      expect(() => formatEIN('12-34567890A')).toThrow('TIN must have 9 digits')
    })
  })
})
