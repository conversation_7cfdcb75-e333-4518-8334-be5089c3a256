import Error<PERSON>tackParser from 'error-stack-parser'
import { logger } from './requestContext.js'

const FUNCTION_NAME_REGEX_1 = /at\s+([^\s(]+)\s*\(/
const FUNCTION_NAME_REGEX_2 = /at\s+([^\s]+)$/
const FILE_PATH_REGEX = /\(([^:]+):/
const LINE_COL_REGEX = /:([0-9]+):([0-9]+)/

const contextRegex = /at\s+getContext\s+\(.*requestContext\.ts/

export function callSiteCapture() {
  let action = 'action'
  let functionName = 'unknown'
  let callerInfo = 'Unknown caller'

  try {
    const err = new Error('Capturing stack trace for caller info')

    // Try using ErrorStackParser first
    try {
      const frames = ErrorStackParser.parse(err)

      // Find callSiteCapture frame index
      const captureIndex = frames.findIndex(
        (frame) =>
          frame.functionName?.includes('callSiteCapture') ||
          frame.fileName?.includes('callSiteCapture.ts'),
      )

      // Find getContext frame index
      const contextIndex = frames.findIndex(
        (frame, index) =>
          index > captureIndex &&
          frame.functionName?.includes('getContext') &&
          frame.fileName?.includes('requestContext.ts'),
      )

      // Get the caller frame (next after getContext)
      if (contextIndex >= 0 && contextIndex + 1 < frames.length) {
        const callerFrame = frames[contextIndex + 1]

        if (callerFrame?.functionName && callerFrame.fileName) {
          const baseFileName = callerFrame.fileName.split('/').pop() || ''
          const lineNumber = callerFrame.lineNumber || '?'
          const columnNumber = callerFrame.columnNumber || '?'

          action = callerFrame.functionName
          functionName = `${baseFileName}:${lineNumber}`
          callerInfo = `${callerFrame.functionName} (${baseFileName}:${lineNumber}:${columnNumber})`

          return { action, callerInfo, functionName }
        }
      }
    } catch (parserError) {
      // Fall back to regex-based parsing if ErrorStackParser fails
      logger.debug(
        `ErrorStackParser failed, falling back to regex: ${parserError}`,
      )
    }

    // Original regex-based implementation as fallback
    const stackTrace = err.stack || ''
    const stackLines = stackTrace
      .split('\n')
      .filter((line) => line.trim() !== '')

    // Find the line that contains the actual caller
    let callerLine: string | undefined

    // First, find the index of the callSiteCapture line
    const captureCallSiteIndex = stackLines.findIndex(
      (line) =>
        line.includes('callSiteCapture') || line.includes('callSiteCapture.ts'),
    )

    // Then, find the index of the getContext line (or whatever is calling callSiteCapture)
    const getContextIndex =
      captureCallSiteIndex >= 0
        ? stackLines.findIndex(
            (line, index) =>
              index > captureCallSiteIndex && contextRegex.test(line),
          )
        : -1

    // The actual caller should be the next line after getContext
    const callerIndex = getContextIndex >= 0 ? getContextIndex + 1 : -1

    // If we found the caller line, extract the information
    if (callerIndex >= 0 && callerIndex < stackLines.length) {
      callerLine = stackLines[callerIndex]
    } else if (stackLines.length > 3) {
      // If we couldn't find the caller using our smart logic, fall back to a simple approach
      // Just use the 4th line (index 3) as a fallback
      callerLine = stackLines[3]
    }

    if (callerLine) {
      // Extract information from the caller line
      callerInfo = callerLine.trim()

      // Extract file path
      let filePath = ''
      const filePathMatch = callerLine.match(FILE_PATH_REGEX)
      if (filePathMatch?.[1]) {
        filePath = filePathMatch[1].trim()
      }

      // Extract line and column numbers
      let lineNumber = ''
      let columnNumber = ''
      const lineColMatch = callerLine.match(LINE_COL_REGEX)
      if (lineColMatch && lineColMatch.length >= 3) {
        lineNumber = lineColMatch[1]!
        columnNumber = lineColMatch[2]!
      }

      // Extract function name from the stack trace
      let extractedFunctionName = 'anonymous'
      const functionNameMatch =
        callerLine.match(FUNCTION_NAME_REGEX_1) ||
        callerLine.match(FUNCTION_NAME_REGEX_2)

      if (functionNameMatch?.[1]) {
        extractedFunctionName = functionNameMatch[1].trim()
      }

      // Format the function name with file and line information
      const baseFileName = filePath.split('/').pop() || ''
      functionName = `${baseFileName}:${lineNumber}`
      action = extractedFunctionName
      callerInfo = `${extractedFunctionName} (${baseFileName}:${lineNumber}:${columnNumber})`
    }
  } catch (error) {
    logger.warn(`Error capturing call site information: ${error}`)
  }

  return {
    action,
    callerInfo,
    functionName,
  }
}
