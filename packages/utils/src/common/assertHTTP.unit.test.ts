import { describe, expect, it } from 'vitest'
import { HTTPError } from './HTTPError.js'
import assert, { DEFAULT_HTTP_STATUS_CODE } from './assertHTTP.js'
import { ERROR_CODES, getErrorCode } from './errorCodes.js'

describe('assertHTTP', () => {
  describe('positional arguments', () => {
    it('should not throw for a truthy condition', () => {
      const condition = true
      const message = 'This should not throw'
      expect(() => assert(condition, message)).not.toThrow()
    })

    it('should throw for a falsy condition', () => {
      const condition = false
      const message = 'This should throw'
      expect(() => assert(condition, message)).toThrow(message)
    })

    it('should use the errorCode to get the status code and message', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      const { code, message } = getErrorCode(errorCode)
      expect(() =>
        assert(condition, undefined, undefined, undefined, errorCode),
      ).toThrow(HTTPError)
      expect(() =>
        assert(condition, undefined, undefined, undefined, errorCode),
      ).toThrow(
        expect.objectContaining({
          code,
          message,
          errorCode,
        }),
      )
    })

    it('should use the default message when no message is provided', () => {
      const condition = false
      const message = 'Bad Request'
      expect(() => assert(condition)).toThrow(HTTPError)
      expect(() => assert(condition)).toThrow(
        expect.objectContaining({ message }),
      )
    })

    it('should throw with a custom status code', () => {
      const condition = false
      const message = 'Custom error message'
      const code = '404'
      expect(() => assert(condition, message, code)).toThrow(HTTPError)
      expect(() => assert(condition, message, code)).toThrow(
        expect.objectContaining({ code, message }),
      )
    })

    it('should throw with a custom internal message', () => {
      const condition = false
      const message = 'Custom error message'
      const internalMessage = 'Internal error message'
      expect(() =>
        assert(condition, message, undefined, internalMessage),
      ).toThrow(HTTPError)
      expect(() =>
        assert(condition, message, undefined, internalMessage),
      ).toThrow(
        expect.objectContaining({
          internalMessage,
          message,
          code: DEFAULT_HTTP_STATUS_CODE,
        }),
      )
    })

    it('should throw with default status code and message', () => {
      const condition = false
      expect(() => assert(condition)).toThrow(HTTPError)
      expect(() => assert(condition)).toThrow(
        expect.objectContaining({
          code: DEFAULT_HTTP_STATUS_CODE,
          message: 'Bad Request',
        }),
      )
    })

    it('should throw with default status code and custom message', () => {
      const condition = false
      const message = 'Custom error message'
      expect(() => assert(condition, message)).toThrow(HTTPError)
      expect(() => assert(condition, message)).toThrow(
        expect.objectContaining({
          code: DEFAULT_HTTP_STATUS_CODE,
          message,
        }),
      )
    })

    it('should throw with default status code and custom internal message', () => {
      const condition = false
      const internalMessage = 'Internal error message'
      expect(() =>
        assert(condition, undefined, undefined, internalMessage),
      ).toThrow(HTTPError)
      expect(() =>
        assert(condition, undefined, undefined, internalMessage),
      ).toThrow(
        expect.objectContaining({
          code: DEFAULT_HTTP_STATUS_CODE,
          message: 'Bad Request',
          internalMessage,
        }),
      )
    })

    it('should throw with status code and message for errorCode', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      expect(() =>
        assert(condition, undefined, undefined, undefined, errorCode),
      ).toThrow(HTTPError)
      expect(() =>
        assert(condition, undefined, undefined, undefined, errorCode),
      ).toThrow(
        expect.objectContaining({
          code: getErrorCode(errorCode).code,
          message: getErrorCode(errorCode).message,
          errorCode,
        }),
      )
    })

    it('should use the message code the code when only code is supplied', () => {
      const condition = false
      const code = '404'
      expect(() => assert(condition, undefined, code)).toThrow(HTTPError)
      expect(() => assert(condition, undefined, code)).toThrow(
        expect.objectContaining({
          code,
          message: 'Not Found',
        }),
      )
    })

    it('should allow an erorCode as the message', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      expect(() => assert(condition, errorCode)).toThrow(HTTPError)
      expect(() => assert(condition, errorCode)).toThrow(
        expect.objectContaining({
          code: getErrorCode(errorCode).code,
          message: getErrorCode(errorCode).message,
          errorCode,
        }),
      )
    })
  })

  describe('named arguments', () => {
    it('should accept an options object', () => {
      const condition = false
      const options = {
        message: 'Custom error message',
        code: '403',
        internalMessage: 'Internal error message',
      } as const

      expect(() => assert(condition, options)).toThrow(HTTPError)
      expect(() => assert(condition, options)).toThrow(
        expect.objectContaining({
          code: options.code,
          message: options.message,
          internalMessage: options.internalMessage,
        }),
      )
    })

    it('should throw with an errorCode', () => {
      const condition = false
      const options = {
        message: 'Custom error message',
        code: '403',
        internalMessage: 'Internal error message',
        errorCode: ERROR_CODES.USER_NOT_AUTHENTICATED,
      } as const

      expect(() => assert(condition, options)).toThrow(HTTPError)
      expect(() => assert(condition, options)).toThrow(
        expect.objectContaining({
          errorCode: options.errorCode,
          message: options.message,
          code: options.code,
          internalMessage: options.internalMessage,
        }),
      )
    })

    it('should throw with message and status code for the error code', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      const message = 'Custom error message'
      expect(() => assert(condition, { message, errorCode })).toThrow(HTTPError)
      expect(() => assert(condition, { message, errorCode })).toThrow(
        expect.objectContaining({
          code: getErrorCode(ERROR_CODES.USER_NOT_AUTHENTICATED).code,
          message,
          errorCode,
        }),
      )
    })

    it('should throw with message and status code for the error code', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      const message = 'Custom error message'
      expect(() => assert(condition, { message, errorCode })).toThrow(HTTPError)
      expect(() => assert(condition, { message, errorCode })).toThrow(
        expect.objectContaining({
          code: getErrorCode(ERROR_CODES.USER_NOT_AUTHENTICATED).code,
          message,
          errorCode,
        }),
      )
    })

    it('should throw with default status code and message for the error code', () => {
      const condition = false
      const errorCode = ERROR_CODES.USER_NOT_AUTHENTICATED
      expect(() => assert(condition, { errorCode })).toThrow(HTTPError)
      expect(() => assert(condition, { errorCode })).toThrow(
        expect.objectContaining({
          code: getErrorCode(ERROR_CODES.USER_NOT_AUTHENTICATED).code,
          message: getErrorCode(ERROR_CODES.USER_NOT_AUTHENTICATED).message,
          errorCode,
        }),
      )
    })
  })
})
