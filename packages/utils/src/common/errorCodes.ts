import { errorCode } from '@riseworks/contracts/src/response.js'
import type { z } from 'zod'
import type { ALLOWED_HTTP_STATUS_CODES } from './HTTPError.js'

export type ERROR_CODE = z.infer<typeof errorCode>

export function isErrorCode(code: unknown): code is ERROR_CODE {
  return (
    typeof code === 'string' && errorCode.options.includes(code as ERROR_CODE)
  )
}

export const ERROR_CODES = errorCode.Enum

const ERROR_CODE_MAP = {
  // --- Authentication & Authorization ---
  USER_NOT_AUTHENTICATED: {
    code: '401',
    message: 'User not authenticated',
  },
  USER_NOT_AUTHORIZED: {
    code: '403',
    message: 'User not authorized',
  },

  // --- Withdraw Accounts ---
  WITHDRAW_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'Withdraw account not found',
  },
  WITHDRAW_ACCOUNT_MISSING_PAYMENT_HANDLER: {
    code: '404',
    message: 'Withdraw account is missing payment handler',
  },
  WITHDRAW_BANK_DATA_NOT_FOUND: {
    code: '404',
    message: 'Bank data not found for withdraw account',
  },
  WITHDRAW_INVALID_RAMP: {
    code: '400',
    message: 'Invalid ramp',
  },
  WITHDRAW_WALLET_ADDRESS_MISSING: {
    code: '404',
    message: 'Withdraw account is missing wallet address',
  },
  WITHDRAW_NETWORK_MISSING: {
    code: '404',
    message: 'Withdraw account is missing network',
  },
  WITHDRAW_TOKEN_MISSING: {
    code: '404',
    message: 'Token not found for withdraw account',
  },
  WITHDRAW_TOKEN_NOT_AVAILABLE: {
    code: '400',
    message: 'Token not available for network',
  },
  WITHDRAW_TOKEN_NOT_ENABLED: {
    code: '400',
    message: 'Token is not enabled for withdraw',
  },
  WITHDRAW_TRANSACTION_FAILED: {
    code: '500',
    message: 'Failed to send withdraw, please try again later',
  },
  WITHDRAW_USER_COUNTRY_INVALID: {
    code: '403',
    message: 'User country not supported for this withdrawal method',
  },
  WITHDRAW_ACCOUNT_NOT_ACTIVE: {
    code: '400',
    message: 'Withdraw account is not active',
  },
  WITHDRAW_NOT_AVAILABLE_FOR_USER: {
    code: '400',
    message: 'This withdrawal option is not available for this user',
  },
  WITHDRAW_ADDRESS_REGISTRATION_FAILED: {
    code: '400',
    message: 'This address cannot be used for this operation',
  },
  WITHDRAW_ACCOUNT_CREATION_FAILED: {
    code: '500',
    message: 'Failed to create account',
  },
  WITHDRAW_PAYMENT_HANDLER_NOT_FOUND: {
    code: '404',
    message: 'Withdraw payment handler not found',
  },
  WITHDRAW_RAMP_UNDEFINED: {
    code: '400',
    message: 'Unable to determine ramp from network and token',
  },
  WITHDRAW_TOKEN_NOT_FOUND_FOR_SWAP: {
    code: '404',
    message: 'Token not found for swap',
  },
  WITHDRAW_NETWORK_NOT_FOUND_FOR_SWAP: {
    code: '404',
    message: 'Network not found for token swap',
  },
  WITHDRAW_WALLET_ADDRESS_NOT_FOUND: {
    code: '404',
    message: 'Wallet address not found for token swap',
  },
  WITHDRAW_CCIP_CONFIG_NOT_FOUND: {
    code: '404',
    message: 'CCIP configuration not found for network',
  },
  WITHDRAW_USER_DOES_NOT_OWN_ACCOUNT: {
    code: '403',
    message: 'User does not own this withdraw account',
  },
  WITHDRAW_INVALID_USER_TYPE: {
    code: '400',
    message: 'Invalid user type for withdraw operation',
  },
  WITHDRAW_PAYMENT_HANDLER_INVALID_PURPOSE: {
    code: '404',
    message: 'Withdraw payment handler purpose is invalid',
  },
  WITHDRAW_MINIMUM_AMOUNT_REQUIRED: {
    code: '400',
    message: 'Amount should be at least the minimum required',
  },
  WITHDRAW_MAXIMUM_AMOUNT_EXCEDED: {
    code: '400',
    message: 'Amount should be at below the maximum required',
  },
  WITHDRAW_WAIT_TIME_NOT_FOUND: {
    code: '404',
    message: 'Withdraw wait time not found for this account',
  },
  WITHDRAW_CURRENCY_NOT_FOUND: {
    code: '404',
    message: 'Currency not found for this account',
  },

  // --- Rise Accounts ---
  RISE_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'Rise account not found',
  },
  RISE_ACCOUNT_DESTINATION_NOT_FOUND: {
    code: '404',
    message: 'Destination wallet not found',
  },
  RISE_TEAM_ROLE_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'Team role rise account not found',
  },

  // --- Teams ---
  TEAM_NOT_FOUND: {
    code: '404',
    message: 'Team not found',
  },
  TEAM_ROLE_NOT_FOUND: {
    code: '404',
    message: 'Team role not found',
  },
  TEAM_RISE_ACCOUNT_MISSING: {
    code: '404',
    message: 'Team role does not have a rise account',
  },

  // --- Payments ---
  PAYMENT_NOT_FOUND: {
    code: '404',
    message: 'Payment not found',
  },
  PAYMENT_INVALID_SIGNATURE: {
    code: '400',
    message: 'Invalid signature',
  },
  PAYMENT_HANDLER_NOT_FOUND: {
    code: '404',
    message: 'Payment handler not found',
  },
  PAYMENT_HANDLER_INVALID_PURPOSE: {
    code: '404',
    message: 'Payment handler purpose is invalid',
  },

  // --- Providers ---
  PROVIDER_NOT_FOUND: {
    code: '404',
    message: 'Provider not found',
  },
  PROVIDER_UNSUPPORTED: {
    code: '400',
    message: 'Unsupported provider',
  },

  // --- External Services ---
  COINBASE_TOKEN_MISSING: {
    code: '400',
    message: 'Failed to get Coinbase token',
  },
  COINBASE_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'No USDC account found in Coinbase',
  },
  COINBASE_ADDRESS_CREATION_FAILED: {
    code: '500',
    message: 'Failed to create address in Coinbase',
  },
  COINBASE_UNAVAILABLE_FOR_USER: {
    code: '400',
    message: 'Coinbase is not available for this user',
  },
  COINBASE_USDC_ACCOUNT_REQUIRED: {
    code: '400',
    message: 'The user needs to create a USDC account in Coinbase',
  },
  COINBASE_NO_ADDRESSES: {
    code: '400',
    message: 'Coinbase account with no address',
  },
  COINBASE_INVALID_ADDRESS: {
    code: '400',
    message: 'Invalid address not found in coinbase account',
  },

  // --- User Accounts ---
  USER_NOT_FOUND: {
    code: '404',
    message: 'User not found',
  },
  USER_INVALID_TYPE: {
    code: '400',
    message: 'Invalid user type',
  },

  // --- Deposit Accounts ---
  DEPOSIT_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'Deposit account not found',
  },
  DEPOSIT_TOKEN_NOT_FOUND: {
    code: '404',
    message: 'Token not found for deposit',
  },
  DEPOSIT_TOKEN_CURRENCY_MISMATCH: {
    code: '400',
    message: 'Token currency mismatch for deposit',
  },
  DEPOSIT_TRANSACTION_FAILED: {
    code: '500',
    message: 'Failed to process deposit transaction',
  },
  DEPOSIT_AMOUNT_TOO_LARGE: {
    code: '400',
    message: 'Deposit amount exceeds maximum allowed',
  },
  DEPOSIT_INVALID_PROVIDER: {
    code: '400',
    message: 'Invalid deposit provider',
  },
  DEPOSIT_ALREADY_PROCESSED: {
    code: '400',
    message: 'Deposit has already been processed',
  },
  DEPOSIT_DATA_INVALID: {
    code: '400',
    message: 'Invalid deposit data',
  },
  DEPOSIT_VIRTUAL_ACCOUNT_NOT_FOUND: {
    code: '404',
    message: 'Virtual account not found for deposit',
  },
  DEPOSIT_PAYMENT_HANDLER_MISSING: {
    code: '404',
    message: 'Payment handler missing for deposit account',
  },
  DEPOSIT_RECIPIENT_INVALID: {
    code: '400',
    message: 'Invalid recipient for deposit',
  },
  DEPOSIT_TOKEN_METADATA_NOT_FOUND: {
    code: '404',
    message: 'Token metadata not found for deposit',
  },
  DEPOSIT_TRANSACTION_IN_PROGRESS: {
    code: '400',
    message: 'Deposit transaction is already in progress',
  },

  // --- Rise entities ---
  INVALID_ENTITY_PROVIDED: {
    code: '400',
    message: 'Invalid rise entity provided',
  },
} as const satisfies Record<
  ERROR_CODE,
  {
    code: ALLOWED_HTTP_STATUS_CODES
    message: string
  }
>

export function getErrorCode<T extends ERROR_CODE>(
  code: T,
): (typeof ERROR_CODE_MAP)[T]
export function getErrorCode<T extends ERROR_CODE>(
  code: unknown,
): (typeof ERROR_CODE_MAP)[T] | undefined
export function getErrorCode(code: unknown) {
  if (!isErrorCode(code)) {
    return
  }

  return ERROR_CODE_MAP[code]
}
