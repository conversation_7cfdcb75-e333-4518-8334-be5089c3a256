import type { RiseRequests } from '@riseworks/contracts/src/codegen/abi_types/RiseAccount_impl_arbitrum.js'
import {
  type PaymentTypeId,
  PaymentTypeMap,
} from '../blockchain/paymentIdentifiers.js'
import { conciseString } from './string.js'

export type FormattedPayment<AmountType = string | bigint> = {
  id: string
  amount: AmountType
  payType: string
  payAtTime: string
  recipient: string
  groupId: string
}

type BaseOptions = {
  concise?: boolean
}

// Function overloads
export function friendlyFormatPayment(
  payment: RiseRequests.RisePaymentStructOutput,
  options: BaseOptions & { amountFormat: 'dollars_string' },
): FormattedPayment<string>

export function friendlyFormatPayment(
  payment: RiseRequests.RisePaymentStructOutput,
  options: BaseOptions & { amountFormat: 'raw_bigint' },
): FormattedPayment<bigint>

export function friendlyFormatPayment(
  payment: RiseRequests.RisePaymentStructOutput,
  options?: BaseOptions,
): FormattedPayment<string>

// Implementation
export function friendlyFormatPayment(
  payment: RiseRequests.RisePaymentStructOutput,
  options: BaseOptions & {
    amountFormat?: 'raw_bigint' | 'dollars_string'
  } = {
    concise: true,
    amountFormat: 'dollars_string',
  },
): FormattedPayment<string | bigint> {
  const { concise } = options

  return {
    id: concise ? conciseString(payment.id) : payment.id,
    amount:
      options.amountFormat === 'dollars_string'
        ? (Number(payment.amount) / 1e6).toFixed(2)
        : payment.amount,
    payType: PaymentTypeMap[payment.payType.toString() as PaymentTypeId],
    payAtTime: new Date(Number(payment.payAtTime) * 1000).toISOString(),
    recipient: concise ? conciseString(payment.recipient) : payment.recipient,
    groupId: concise ? conciseString(payment.groupID) : payment.groupID,
  }
}
