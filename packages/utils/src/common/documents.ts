import type {
  PayrollCurrencies,
  Stipends,
  VariableCompensations,
} from '@riseworks/contracts/src/formats.js'
import { formatEnumKey } from './string.js'

export const updateContentProviderAddress = (
  data: Record<string, unknown>,
  address: string,
): Record<string, unknown> => {
  if (
    data.type === 'input-node' &&
    data.label === 'Provider Address' &&
    typeof data.value === 'string'
  ) {
    return {
      ...data,
      value: address,
    }
  }

  if (data.children && Array.isArray(data.children)) {
    return {
      ...data,
      children: data.children.map((child) =>
        updateContentProviderAddress(child as Record<string, unknown>, address),
      ),
    }
  }

  return data
}

export const formatEmploymentAgreementBenefits = ({
  variableCompensations,
  stipends,
  currency,
}: {
  variableCompensations: VariableCompensations
  stipends: Stipends
  currency: PayrollCurrencies
}): string => {
  const benefits: string[] = []

  const processVariableComp = (comp: VariableCompensations[number]) => {
    const name = comp.name || ''
    const paymentType = comp.type || ''
    const frequency = comp.frequency ? ` ${comp.frequency}` : ''
    return `${name} (${paymentType}): ${currency} ${comp.amount}${frequency}`
  }

  const processStipend = (stipend: Stipends[number]) => {
    const type = stipend.type || 'General'
    return `${formatEnumKey(type)} Stipend: ${currency} ${stipend.amount}`
  }

  for (const comp of variableCompensations) {
    benefits.push(processVariableComp(comp))
  }

  for (const stipend of stipends) {
    benefits.push(processStipend(stipend))
  }

  return benefits.length > 0 ? benefits.join('\n') : 'No benefits specified'
}
