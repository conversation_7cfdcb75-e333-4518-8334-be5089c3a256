import { vi } from 'vitest'

export const mockAllSecrets = () => {
  vi.mock('utils/src/google/getSecrets.js', () => ({
    getSecrets: vi.fn().mockImplementation(async (secrets: string[]) => {
      return Object.fromEntries(
        secrets.map((s) => {
          if (s.includes('entity')) return [s, 'mocked-entity:mocked-user']
          if (s === 'sumsub-tokens') return [s, 'token;secret;webhook']
          return [s, `mocked-${s}`]
        }),
      )
    }),
  }))
}
