import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { parse } from 'csv-parse/sync'
import { stringify } from 'csv-stringify/sync'
import crc32 from 'crc/crc32'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

const symmetryPath = path.resolve(
  __dirname,
  'data',
  'Q2 2024 ADP Master Tax Code List.csv',
)
const mastertaxPath = path.resolve(__dirname, 'data', 'TC_MAPPING_38152512.CSV')
const outputDir = path.resolve(__dirname, 'data')
fs.mkdirSync(outputDir, { recursive: true })

interface SymmetryRow {
  'Symmetry Code': string
  'Symmetry Tax Description': string
  'Mastertax Code': string
  'Mastertax Description': string
}

interface MastertaxRow {
  TAX_CODE: string
  TAX_DESCRIPTION: string
  PAYEE: string
  PSD_CODE: string | null
  REPORT_TAX_CODE: string
  IMPORT_TAX_CODE1: string | null
  IMPORT_TAX_CODE2: string | null
  [key: string]: string | null | undefined
}

function loadCSV<T>(filePath: string): T[] {
  const content = fs.readFileSync(filePath, 'utf8')
  return parse(content, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
    quote: '"',
    relax_column_count: true,
  }) as T[]
}

export const mergeSymmetryWithMasterTax = () => {
  const symmetryRows = loadCSV<SymmetryRow>(symmetryPath)
  const mastertaxRows = loadCSV<MastertaxRow>(mastertaxPath)
  const unmatchedRows: SymmetryRow[] = []

  const taxCodeMap = new Map<string, number>()
  mastertaxRows.forEach((row, idx) => {
    const key = row.TAX_CODE.trim().toUpperCase()
    taxCodeMap.set(key, idx)
  })

  for (const row of symmetryRows) {
    const rawSymmetryCode = row['Symmetry Code']
    const mastertaxCode = row['Mastertax Code'].trim().toUpperCase()
    const state = mastertaxCode.slice(0, 2).toUpperCase() || 'XX'
    const typePart = (rawSymmetryCode.split('-')[3] || 'GEN')
      .replace(/[^A-Z0-9]/gi, '')
      .slice(0, 6)
      .toUpperCase()
    const hash = (crc32(rawSymmetryCode) >>> 0)
      .toString(16)
      .toUpperCase()
      .slice(0, 6)
    const generatedCode = `${state}-${typePart}-${hash}`.slice(0, 20)

    const matchIndex = taxCodeMap.get(mastertaxCode)
    const mrow =
      matchIndex !== undefined ? mastertaxRows[matchIndex] : undefined
    if (mrow) {
      mrow.REPORT_TAX_CODE = generatedCode
      mrow.IMPORT_TAX_CODE1 = rawSymmetryCode
    } else {
      unmatchedRows.push(row)
    }
  }

  const expectedColumns = [
    'TAX_CODE',
    'TAX_DESCRIPTION',
    'PAYEE',
    'PSD_CODE',
    'REPORT_TAX_CODE',
    'IMPORT_TAX_CODE1',
    'IMPORT_TAX_CODE2',
  ]

  const paddedRows = mastertaxRows.map((row) => {
    const complete: Record<string, string> = {}
    for (const col of expectedColumns) {
      const val = row[col]
      complete[col] = val != null ? String(val) : ''
    }
    return complete
  })

  const mergedCsv = stringify(paddedRows, {
    header: true,
    quoted: true,
    quoted_empty: true,
    columns: expectedColumns,
  })
  fs.writeFileSync(
    path.join(outputDir, 'merged_symmetry_mastertax.csv'),
    mergedCsv,
  )

  const unmatchedCsv = stringify(unmatchedRows, {
    header: true,
    quoted: true,
    quoted_empty: true,
  })
  fs.writeFileSync(
    path.join(outputDir, 'unmatched_symmetry_code.csv'),
    unmatchedCsv,
  )
}

mergeSymmetryWithMasterTax()
