import { db as mainDB } from 'db/src/index.js'
import { goTryRaw } from 'go-go-try'
import assert from 'utils/src/common/assertHTTP.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { createInngestClient } from 'utils/src/inngest/index.js'
import { createLock, waitForExternalLock } from 'utils/src/redis/redis.js'
import { createRiseId, getRiseAddress } from './smartContracts.js'

export const getBucketAccount = (id: string, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('rise.bucket_accounts')
    .selectAll()
    .where('id', '=', id)
    .executeTakeFirst()
}

export const getBucketAccountByAddress = (address: string, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('rise.bucket_accounts')
    .selectAll()
    .where('account', '=', address)
    .executeTakeFirst()
}

export const updateBucketAccount = (id: string, name: string, db = mainDB) => {
  using _ = getContext()
  return db
    .updateTable('rise.bucket_accounts')
    .set({
      name,
    })
    .where('id', '=', id)
    .execute()
}

export const getOrCreateBucketAccount = async ({
  id,
  name,
}: { id: string; name?: string }) => {
  using _ = getContext()
  const release = await createLock(id)
  const [err, bucketAccount] = await goTryRaw(async () => {
    const bucketAccount = await getBucketAccount(id)

    if (bucketAccount) {
      if (name && name !== bucketAccount.name) {
        await updateBucketAccount(id, name)
      }
      return bucketAccount
    }

    const riseAddress = await getRiseAddress('rise_riseid_deposit')
    assert(riseAddress, 'No rise deposit owner found', '404')
    const account = await mainDB.transaction().execute(async (trx) => {
      const account = await createRiseId({
        type: 'rise_account',
        owner_address: riseAddress.address,
        parent_account: null,
        network: 'arbitrum',
        purpose: 'tax_account',
        db: trx,
      })

      await trx
        .insertInto('rise.bucket_accounts')
        .values({
          id,
          name,
          account,
        })
        .execute()
      return account
    })

    const client = createInngestClient('dashboard')
    await client.send({
      name: 'dashboard/riseid.activate',
      data: {
        riseid: account,
        network: 'arbitrum',
      },
    })

    await waitForExternalLock(account)
    await release()

    return {
      id,
      account,
    }
  })

  if (err !== undefined) {
    await release()
    throw err
  }

  await release()

  return {
    id: bucketAccount.id,
    account: bucketAccount.account,
  }
}
