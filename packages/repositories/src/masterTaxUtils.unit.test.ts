import type { CompanyNanoid } from '@riseworks/contracts/src/brands.js'
import { describe, expect, test } from 'vitest'
import type { Justification, MasterTaxField } from './masterTaxSpec.js'
import {
  companyNanoidToPayrollCode,
  formatMasterTaxValue,
  formatter,
  getStateAbbreviation,
  payCycleFromDateRise,
  payCycleFromDateSymmetry,
  processingTimeFromDate,
  referenceEINRules,
  symmetryToMasterTaxCodeMapper,
} from './masterTaxUtils.js'

describe('formatMasterTaxValue', () => {
  test('returns spaces when value is null or undefined', () => {
    const field = {
      field: 'test',
      size: 4,
      justification: 'left',
      type: 'Alphanumeric',
    } as MasterTaxField
    expect(formatMasterTaxValue(null, field)).toBe('    ')
    expect(formatMasterTaxValue(undefined, field)).toBe('    ')
  })

  test('formats Date to YYYYMMDD', () => {
    const field = {
      field: 'd',
      size: 10,
      justification: 'left',
      type: 'Alphanumeric',
    } as MasterTaxField
    const dt = new Date(Date.UTC(2025, 0, 2))
    expect(formatMasterTaxValue(dt, field)).toBe('20250102')
  })

  test('formats Amount values correctly', () => {
    const field = {
      field: 'amt',
      size: 6,
      justification: 'right',
      type: 'Amount',
    } as MasterTaxField
    expect(formatMasterTaxValue('123.45', field)).toBe('12345+')
  })

  test('formats Numeric values correctly', () => {
    const field = {
      field: 'num',
      size: 6,
      justification: 'right',
      type: 'Numeric',
    } as MasterTaxField
    // with decimal
    expect(formatMasterTaxValue('7.89', field)).toBe('078900')
    // without decimal
    expect(formatMasterTaxValue('42', field)).toBe('000042')
  })

  test('pads and truncates strings based on justification and size', () => {
    const rightField = {
      field: 's',
      size: 5,
      justification: 'right',
      type: 'Alphanumeric',
    } as MasterTaxField
    const leftField = { ...rightField, justification: 'left' as Justification }
    expect(formatMasterTaxValue('abc', rightField)).toBe('  abc')
    expect(formatMasterTaxValue('abc', leftField)).toBe('abc  ')
    expect(formatMasterTaxValue('abcdef', leftField)).toBe('abcde')
  })
})

describe('formatter', () => {
  test('concatenates fields into fixed-width string', () => {
    const record = { code: 'X', qty: '5', dt: new Date(Date.UTC(2020, 0, 1)) }
    const spec = [
      {
        field: 'code',
        size: 3,
        justification: 'left',
        type: 'Alphanumeric',
      } as MasterTaxField,
      {
        field: 'qty',
        size: 2,
        justification: 'right',
        type: 'Numeric',
      } as MasterTaxField,
      {
        field: 'dt',
        size: 8,
        justification: 'right',
        type: 'Alphanumeric',
      } as MasterTaxField,
    ]
    const line = formatter(record, spec)
    expect(line).toBe('X  0520200101')
  })
})

describe('companyNanoidToPayrollCode', () => {
  test('extracts slice of nanoid string', () => {
    const fakeCompanyNanoid = 'co-1234567890' as CompanyNanoid
    expect(companyNanoidToPayrollCode(fakeCompanyNanoid)).toBe('1234567890')
  })
})

describe('processingTimeFromDate', () => {
  test('formats time as HHMMSS', () => {
    const dt = new Date(Date.UTC(2020, 0, 1, 5, 3, 9))
    expect(processingTimeFromDate(dt)).toBe('050309')
  })
})

describe('getStateAbbreviation', () => {
  test('returns USPS code for valid state name', () => {
    expect(getStateAbbreviation('California')).toBe('CA')
  })

  test('throws for unknown state', () => {
    // biome-ignore lint/performance/useTopLevelRegex: <explanation>
    expect(() => getStateAbbreviation('NotAState')).toThrow(/State not found/)
  })
})

describe('symmetryToMasterTaxCodeMapper', () => {
  test('generates a 20-char code and is deterministic', () => {
    const code1 = symmetryToMasterTaxCodeMapper('00-000-0000-FIT-000')
    const code2 = symmetryToMasterTaxCodeMapper('00-000-0000-FIT-000')
    // biome-ignore lint/performance/useTopLevelRegex: <explanation>
    expect(code1).toMatch(/^FE-FIT-[A-F0-9]{6}$/)
    expect(code1.length).toBeLessThanOrEqual(20)
    expect(code2).toBe(code1)
  })

  test('throws on invalid prefix', () => {
    expect(() => symmetryToMasterTaxCodeMapper('ABC')).toThrow(
      // biome-ignore lint/performance/useTopLevelRegex: <explanation>
      /Invalid Symmetry tax ID/,
    )
    expect(() => symmetryToMasterTaxCodeMapper('99-000-0000-XYZ-000')).toThrow(
      // biome-ignore lint/performance/useTopLevelRegex: <explanation>
      /Unknown Symmetry state prefix/,
    )
  })
})

describe('referenceEINRules', () => {
  test('returns H for SIT code 06-000...', () => {
    expect(referenceEINRules('06-000-0000-SIT-000', 'any')).toBe('H')
  })
  test('returns schedule codes for CT SIT', () => {
    expect(referenceEINRules('09-000-0000-SIT-000', 'weekly')).toBe('W')
    expect(referenceEINRules('09-000-0000-SIT-000', 'monthly')).toBe('M')
    expect(referenceEINRules('09-000-0000-SIT-000', 'other')).toBe('O')
  })
  test('returns empty string for non-SIT codes', () => {
    expect(referenceEINRules('00-000-0000-FIT-000', 'weekly')).toBe('')
  })
})

describe('payCycleFromDateRise', () => {
  test('returns unpadded YYYY-MM-D string for a UTC date', () => {
    const dt = new Date(Date.UTC(2025, 0, 5)) // Jan 5, 2025
    expect(payCycleFromDateRise(dt)).toBe('2025-01-P1')
  })

  test('handles double-digit month and day', () => {
    const dt = new Date(Date.UTC(2025, 11, 18)) // Dec 18, 2025
    expect(payCycleFromDateRise(dt)).toBe('2025-12-P2')
  })
})

describe('payCycleFromDateSymmetry', () => {
  test('returns zero-padded YYYY-MM-DD string for a UTC date', () => {
    const dt = new Date(Date.UTC(2025, 0, 5)) // Jan 5, 2025
    expect(payCycleFromDateSymmetry(dt)).toBe('2025-01-05')
  })

  test('handles double-digit month and day with padding', () => {
    const dt = new Date(Date.UTC(2025, 11, 15)) // Dec 15, 2025
    expect(payCycleFromDateSymmetry(dt)).toBe('2025-12-15')
  })
})
