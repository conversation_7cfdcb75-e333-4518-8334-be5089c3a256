import type {
  CompanyNanoid,
  TeamNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db as mainDB } from 'db/src/index.js'
import { sql } from 'kysely'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getPayCycle } from 'utils/src/common/payCycles.js'
import { getContext } from 'utils/src/common/requestContext.js'
import type { CafeteriaPlanBenefitsType } from '../../contracts/src/codegen/zod/mastertax/cafeteria_plan_benefits.js'
import type { CompanyAddressType } from '../../contracts/src/codegen/zod/mastertax/company_address.js'
import type { CompanyMailingAddressType } from '../../contracts/src/codegen/zod/mastertax/company_mailing_address.js'
import {
  type CompanyTaxType,
  company_tax,
} from '../../contracts/src/codegen/zod/mastertax/company_tax.js'
import type { FileHeaderType } from '../../contracts/src/codegen/zod/mastertax/file_header.js'
import type { FileTrailerType } from '../../contracts/src/codegen/zod/mastertax/file_trailer.js'
import type { PayrollHeaderType } from '../../contracts/src/codegen/zod/mastertax/payroll_header.js'
import {
  type PayrollTaxDetailType,
  payroll_tax_detail,
} from '../../contracts/src/codegen/zod/mastertax/payroll_tax_detail.js'
import type { PayrollTrailerType } from '../../contracts/src/codegen/zod/mastertax/payroll_trailer.js'
import { getEmployeePayrollSettingsByTeam } from './employeePayrollSettings.js'
import type {
  AgentClientTypeKey,
  BankAccountTypeKey,
  CashServiceLevelKey,
  CompanyStatusKey,
  CompanyTaxServiceLevelKey,
  CompanyTaxStatusKey,
  CountryCodeKey,
  EinTypeKey,
  FeinTypeKey,
  FutaExemptionKey,
  KindOfEmployerKey,
  PaymentMethodKey,
  PayrollFrequencyKey,
  ServiceLevelKey,
  SignKey,
  YesNoKey,
} from './masterTaxSchema.js'
import {
  companyAddressSpec,
  companyCashCareBanksSpec,
  companyDisbursementBanksSpec,
  companyGeneralLedgerSpec,
  companyMailingAddressSpec,
  companyTaxSpec,
  companyWorkersCompCodesSpec,
  fileHeaderSpec,
  fileTrailerSpec,
  payrollHeaderSpec,
  payrollTaxDetailSpec,
  payrollTaxUserDefinedFieldsSpec,
  payrollTrailerSpec,
  secondaryCompanyGroupsSpec,
} from './masterTaxSpec.js'
// import { formatter, normalizeNulls, } from './masterTaxUtils.js'
import { formatter, roundToTwoDecimalPlaces } from './masterTaxUtils.js'
import { getEmployeePaymentsByCategory } from './payroll.js'
import { getTeamsForCompany } from './teams.js'

const startsWithNumberRegex = /^\d/
const CENTS_IN_ONE_DOLLAR = 100

export const upsertFileHeader = async (
  data: Partial<FileHeaderType>,
  db = mainDB,
) => {
  using _ = getContext()

  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date, 'Missing required field: check_date')
  assert(data.process_date, 'Missing required field: process_date')
  assert(data.process_time, 'Missing required field: process_time')

  return await db
    .insertInto('mastertax.file_header')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,
      process_date: data.process_date,
      process_time: data.process_time ?? null,
    })
    .onDuplicateKeyUpdate({
      process_date: sql`VALUES(process_date)`,
      process_time: sql`VALUES(process_time)`,
    })
    .execute()
}

export const upsertPayrollHeader = async (
  data: Partial<PayrollHeaderType>,
  db = mainDB,
) => {
  using _ = getContext()

  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date, 'Missing required field: check_date')
  assert(data.company_start_date, 'Missing required field: company_start_date')
  assert(data.company_name, 'Missing required field: company_name')

  return db
    .insertInto('mastertax.payroll_header')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,

      tax_liabilities: data.tax_liabilities as YesNoKey,
      company_setup: data.company_setup as YesNoKey,

      variance_payroll_code: (data.variance_payroll_code as YesNoKey) ?? null,
      bank_setup: (data.bank_setup as YesNoKey) ?? null,
      payroll_description: data.payroll_description ?? null,

      company_start_date: data.company_start_date,
      company_status: data.company_status as CompanyStatusKey,
      company_name: data.company_name,
      service_level: data.service_level as ServiceLevelKey,

      fein_type: (data.fein_type as FeinTypeKey) ?? null,
      fein: data.fein ?? null,
      bank_account_name: data.bank_account_name ?? null,
      transit_routing_number: data.transit_routing_number ?? null,
      bank_account_number: data.bank_account_number ?? null,
      bank_account_type: (data.bank_account_type as BankAccountTypeKey) ?? null,
      draft_days: data.draft_days ?? null,
      next_check_date: data.next_check_date ?? null,
      name_control: data.name_control ?? null,

      disbursement_ach_bank_destination:
        data.disbursement_ach_bank_destination ?? null,
      disbursement_bank_account_name:
        data.disbursement_bank_account_name ?? null,
      disbursement_bank_routing_number:
        data.disbursement_bank_routing_number ?? null,
      disbursement_bank_account_number:
        data.disbursement_bank_account_number ?? null,

      cafeteria_plan_benefits:
        (data.cafeteria_plan_benefits as FutaExemptionKey) ?? null,
      group_term_life_insurance:
        (data.group_term_life_insurance as FutaExemptionKey) ?? null,
      dependent_care_assistance:
        (data.dependent_care_assistance as FutaExemptionKey) ?? null,
      business_expense_reimbursement:
        (data.business_expense_reimbursement as FutaExemptionKey) ?? null,
      employer_contribution_401k:
        (data.employer_contribution_401k as FutaExemptionKey) ?? null,
      employer_contribution_sep_ira:
        (data.employer_contribution_sep_ira as FutaExemptionKey) ?? null,
      employer_contribution_simple:
        (data.employer_contribution_simple as FutaExemptionKey) ?? null,
      accident_health_insurance_premiums:
        (data.accident_health_insurance_premiums as FutaExemptionKey) ?? null,
      sick_pay: (data.sick_pay as FutaExemptionKey) ?? null,
      workers_compensation:
        (data.workers_compensation as FutaExemptionKey) ?? null,
      payments_to_family_employees:
        (data.payments_to_family_employees as FutaExemptionKey) ?? null,
      payments_to_hospital_interns:
        (data.payments_to_hospital_interns as FutaExemptionKey) ?? null,
      payments_to_hospital_patients:
        (data.payments_to_hospital_patients as FutaExemptionKey) ?? null,
      payments_to_general_partnership:
        (data.payments_to_general_partnership as FutaExemptionKey) ?? null,
      state_govt_employee_salaries:
        (data.state_govt_employee_salaries as FutaExemptionKey) ?? null,
      payments_to_election_workers:
        (data.payments_to_election_workers as FutaExemptionKey) ?? null,
      supplemental_unemployment_benefits:
        (data.supplemental_unemployment_benefits as FutaExemptionKey) ?? null,
      nonqualified_deferred_comp:
        (data.nonqualified_deferred_comp as FutaExemptionKey) ?? null,
      meals_furnished_in_kind:
        (data.meals_furnished_in_kind as FutaExemptionKey) ?? null,
      qualified_moving_expense:
        (data.qualified_moving_expense as FutaExemptionKey) ?? null,
      hsa: (data.hsa as FutaExemptionKey) ?? null,
      exempt_501c3_organization:
        (data.exempt_501c3_organization as FutaExemptionKey) ?? null,
      employee_stock_purchase_plan:
        (data.employee_stock_purchase_plan as FutaExemptionKey) ?? null,
      non_taxable_fringe_payments:
        (data.non_taxable_fringe_payments as FutaExemptionKey) ?? null,
      public_transportation_non_tax:
        (data.public_transportation_non_tax as FutaExemptionKey) ?? null,
      wc_housing_employment_condition:
        (data.wc_housing_employment_condition as FutaExemptionKey) ?? null,
      chaplain_housing: (data.chaplain_housing as FutaExemptionKey) ?? null,
      clergy_housing_poverty_vow:
        (data.clergy_housing_poverty_vow as FutaExemptionKey) ?? null,
      foreign_source_income:
        (data.foreign_source_income as FutaExemptionKey) ?? null,
      student_exempt: (data.student_exempt as FutaExemptionKey) ?? null,

      company_group_name: data.company_group_name ?? null,
      agent_client_type: (data.agent_client_type as AgentClientTypeKey) ?? null,
      filer_944: (data.filer_944 as YesNoKey) ?? null,
      quarterly_wage_reporting:
        (data.quarterly_wage_reporting as YesNoKey) ?? null,
      year_end_employee_filing:
        (data.year_end_employee_filing as YesNoKey) ?? null,
      cash_service_level:
        (data.cash_service_level as CashServiceLevelKey) ?? null,
      payroll_run_id: data.payroll_run_id ?? null,
      worksite_reporting: (data.worksite_reporting as YesNoKey) ?? null,
      wage_attachment_flag: (data.wage_attachment_flag as YesNoKey) ?? null,
      short_reporting_payroll_code: data.short_reporting_payroll_code ?? null,
      company_effective_date: data.company_effective_date ?? null,
      kind_of_employer: (data.kind_of_employer as KindOfEmployerKey) ?? null,
      naics_code: data.naics_code ?? null,
      reporting_payroll_code: data.reporting_payroll_code ?? null,
    })
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(data)
          .filter((k) => k !== 'payroll_code' && k !== 'check_date')
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertCompanyAddress = async (
  data: Partial<CompanyAddressType>,
  db = mainDB,
) => {
  using _ = getContext()

  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date, 'Missing required field: check_date')

  return db
    .insertInto('mastertax.company_address')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,
      record_type: data.record_type ?? '2',
      sub_type: data.sub_type ?? '01',

      company_dba: data.company_dba ?? null,
      address_line_1: data.address_line_1 ?? null,
      address_line_2: data.address_line_2 ?? null,
      city: data.city ?? null,
      state_code: data.state_code ?? null,
      zip_code: data.zip_code ?? null,
      country_code: (data.country_code as CountryCodeKey) ?? null,
      psd_code: data.psd_code ?? null,

      first_name: data.first_name ?? null,
      middle_initial: data.middle_initial ?? null,
      last_name: data.last_name ?? null,
      area_code: data.area_code ?? null,
      telephone_number: data.telephone_number ?? null,
      extension: data.extension ?? null,
      fax_area_code: data.fax_area_code ?? null,
      fax_number: data.fax_number ?? null,
      email_address: data.email_address ?? null,
      in_care_of: data.in_care_of ?? null,
    })
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(data)
          .filter((k) => k !== 'payroll_code')
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertCompanyMailingAddress = async (
  data: Partial<CompanyMailingAddressType>,
  db = mainDB,
) => {
  using _ = getContext()

  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date, 'Missing required field: check_date')

  return db
    .insertInto('mastertax.company_mailing_address')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,
      record_type: data.record_type,
      sub_type: data.sub_type,

      company_dba: data.company_dba ?? null,
      address_line_1: data.address_line_1 ?? null,
      address_line_2: data.address_line_2 ?? null,
      city: data.city ?? null,
      state_code: data.state_code ?? null,
      zip_code: data.zip_code ?? null,
      country_code: (data.country_code as CountryCodeKey) ?? null,
      route_code: data.route_code ?? null,

      first_name: data.first_name ?? null,
      middle_initial: data.middle_initial ?? null,
      last_name: data.last_name ?? null,
      area_code: data.area_code ?? null,
      telephone_number: data.telephone_number ?? null,
      extension: data.extension ?? null,
      fax_area_code: data.fax_area_code ?? null,
      fax_number: data.fax_number ?? null,
      email_address: data.email_address ?? null,
    })
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(data)
          .filter((k) => k !== 'payroll_code')
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertCompanyTax = async (
  data: Array<Partial<CompanyTaxType>>,
  db = mainDB,
) => {
  using _ = getContext()

  if (data.length === 0) return []

  // Ensure required fields are present
  const parsed = company_tax.array().parse(data)

  const sanitized_rows = parsed.map((row) => ({
    payroll_code: row.payroll_code,
    tax_code: row.tax_code,
    check_date: row.check_date,

    record_type: row.record_type,
    sub_type: row.sub_type,

    work_out_of_state_flag: (row.work_out_of_state_flag as YesNoKey) ?? null,
    effective_date: row.effective_date ?? null,
    company_tax_status: (row.company_tax_status as CompanyTaxStatusKey) ?? null,
    ein_type: (row.ein_type as EinTypeKey) ?? null,
    ein: row.ein ?? null,

    tax_rate: row.tax_rate ?? null,
    tax_rate_2: row.tax_rate_2 ?? null,
    expanded_tax_rate: row.expanded_tax_rate ?? null,

    payment_frequency: row.payment_frequency ?? null,
    payment_method: (row.payment_method as PaymentMethodKey) ?? null,
    eft_password: row.eft_password ?? null,
    reference_ein: row.reference_ein ?? null,
    county_code: row.county_code ?? null,

    company_tax_service_level:
      (row.company_tax_service_level as CompanyTaxServiceLevelKey) ?? null,
    mark_all_returns_final: (row.mark_all_returns_final as YesNoKey) ?? null,
    final_return_effective_date: row.final_return_effective_date ?? null,
  }))

  assert(sanitized_rows[0] !== undefined, 'No rows to insert')
  const row = sanitized_rows[0]

  return db
    .insertInto('mastertax.company_tax')
    .values(sanitized_rows)
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(row)
          .filter(
            (key) => !['payroll_code', 'tax_code', 'check_date'].includes(key),
          )
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertPayrollTaxDetails = async (
  data: Array<Partial<PayrollTaxDetailType>>,
  db = mainDB,
) => {
  using _ = getContext()

  if (data.length === 0) return []

  // Ensure required fields are present
  const parsed = payroll_tax_detail.array().parse(data)

  const sanitized_rows = parsed.map((row) => ({
    payroll_code: row.payroll_code,
    tax_code: row.tax_code,
    check_date: row.check_date,

    record_type: row.record_type,
    sub_type: row.sub_type,

    work_out_of_state_flag: (row.work_out_of_state_flag as YesNoKey) ?? null,
    ein: row.ein ?? null,

    tax_rate: row.tax_rate ?? null,
    tax: row.tax ?? null,
    taxable_wages: row.taxable_wages ?? null,
    gross_wages: row.gross_wages ?? null,

    employee_count: row.employee_count ?? null,
    employee_count_sign: (row.employee_count_sign as SignKey) ?? null,
    exempt_overtime_wages_employee_count:
      row.exempt_overtime_wages_employee_count ?? null,
    liability_trace_id: row.liability_trace_id ?? null,

    exempt_wages: row.exempt_wages ?? null,
    wc_class_code: row.wc_class_code ?? null,
    payroll_frequency: (row.payroll_frequency as PayrollFrequencyKey) ?? null,
    expanded_tax_rate: row.expanded_tax_rate ?? null,
  }))

  assert(sanitized_rows[0] !== undefined, 'No rows to insert')
  const row = sanitized_rows[0]

  return db
    .insertInto('mastertax.payroll_tax_detail')
    .values(sanitized_rows)
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(row)
          .filter(
            (key) => !['payroll_code', 'tax_code', 'check_date'].includes(key),
          )
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertPayrollTrailer = async (
  data: Partial<PayrollTrailerType>,
  db = mainDB,
) => {
  using _ = getContext()
  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date !== undefined, 'Missing required field: check_date')

  return db
    .insertInto('mastertax.payroll_trailer')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,

      record_type: data.record_type,
      sub_type: data.sub_type,

      record_count: data.record_count ?? null,
      tax_total: data.tax_total ?? null,
    })
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(data)
          .filter((k) => !['payroll_code', 'check_date'].includes(k))
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const upsertFileTrailer = async (
  data: Partial<FileTrailerType>,
  db = mainDB,
) => {
  using _ = getContext()

  // Ensure required fields are present
  assert(data.payroll_code, 'Missing required field: payroll_code')
  assert(data.check_date !== undefined, 'Missing required field: check_date')

  return db
    .insertInto('mastertax.file_trailer')
    .values({
      payroll_code: data.payroll_code,
      check_date: data.check_date,

      record_count: data.record_count,
      tax_total: data.tax_total,
    })
    .onDuplicateKeyUpdate(
      Object.fromEntries(
        Object.keys(data)
          .filter((k) => !['payroll_code', 'check_date'].includes(k))
          .map((key) => [key, sql`VALUES(${sql.ref(key)})`]),
      ),
    )
    .execute()
}

export const getFullMasterTaxReport = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  // get headers and trailers
  const [
    fileHeader,
    payrollHeader,
    companyDisbursement,
    companyCashCare,
    companyGL,
    secondaryCompanyGroups,
    companyAddress,
    companyMailing,
    payrollTrailer,
    fileTrailer,
  ] = await Promise.all([
    getFormattedFileHeaderRecord(payroll_code, check_date, db),
    getFormattedPayrollHeaderRecord(payroll_code, check_date, db),
    getFormattedCompanyDisbursementBanksRecord(payroll_code, db),
    getFormattedCompanyCashCareBanksRecord(payroll_code, db),
    getFormattedCompanyGeneralLedgerRecord(payroll_code, db),
    getFormattedSecondaryCompanyGroupsRecord(payroll_code, db),
    getFormattedCompanyAddressRecord(payroll_code, db),
    getFormattedCompanyMailingAddressRecord(payroll_code, db),
    getFormattedPayrollTrailerRecord(payroll_code, check_date, db),
    getFormattedFileTrailerRecord(payroll_code, check_date, db),
  ])

  // Tax Records
  const taxCodes = await getCompanyTaxCodes(payroll_code, check_date, db)
  // get all the records for each tax code
  const taxRecords = await mapAsync(taxCodes, (tax_code) => {
    return getFormattedCompanyTaxRecord(payroll_code, tax_code, check_date, db)
  })
  const workersCompCodes = await mapAsync(taxCodes, (tax_code) => {
    return getFormattedCompanyWorkersCompCodesRecord(
      payroll_code,
      check_date,
      tax_code,
      db,
    )
  })
  const taxDetails = await mapAsync(taxCodes, (tax_code) => {
    return getFormattedPayrollTaxDetailRecord(
      payroll_code,
      check_date,
      tax_code,
      db,
    )
  })
  const taxUserDefinedFields = await mapAsync(taxCodes, (tax_code) => {
    return getFormattedPayrollTaxUserDefinedFieldsRecord(
      payroll_code,
      check_date,
      tax_code,
      db,
    )
  })

  // Combine all the records
  return [
    fileHeader,
    payrollHeader,
    companyDisbursement,
    companyCashCare,
    companyGL,
    secondaryCompanyGroups,
    companyAddress,
    companyMailing,
    ...taxRecords,
    ...workersCompCodes,
    ...taxDetails,
    ...taxUserDefinedFields,
    payrollTrailer,
    fileTrailer,
  ]
    .flat()
    .filter(Boolean)
    .join('\n')
}

export const getFormattedFileHeaderRecord = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.file_header')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .executeTakeFirst()

  assert(row, 'File header not found')

  // const parsed = file_header.parse(row)

  return formatter(row, fileHeaderSpec)
}

export const getFormattedPayrollHeaderRecord = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.payroll_header')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .executeTakeFirst()

  assert(row, 'Payroll header not found')

  return formatter(row, payrollHeaderSpec)
}

export const getFormattedCompanyDisbursementBanksRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_disbursement_banks')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyDisbursementBanksSpec)
}

export const getFormattedCompanyCashCareBanksRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_cash_care_banks')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyCashCareBanksSpec)
}

export const getFormattedCompanyGeneralLedgerRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_general_ledger')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyGeneralLedgerSpec)
}

export const getFormattedSecondaryCompanyGroupsRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.secondary_company_groups')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, secondaryCompanyGroupsSpec)
}

export const getFormattedCompanyAddressRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_address')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyAddressSpec)
}

export const getFormattedCompanyMailingAddressRecord = async (
  payroll_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_mailing_address')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyMailingAddressSpec)
}

export const getFormattedCompanyTaxRecord = async (
  payroll_code: string,
  tax_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_tax')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('tax_code', '=', tax_code)
    .where('check_date', '=', check_date)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyTaxSpec)
}

export const getFormattedCompanyWorkersCompCodesRecord = async (
  payroll_code: string,
  check_date: Date,
  tax_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.company_workers_comp_codes')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .where('tax_code', '=', tax_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, companyWorkersCompCodesSpec)
}

export const getFormattedPayrollTaxDetailRecord = async (
  payroll_code: string,
  check_date: Date,
  tax_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.payroll_tax_detail')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .where('tax_code', '=', tax_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, payrollTaxDetailSpec)
}

export const getFormattedPayrollTaxUserDefinedFieldsRecord = async (
  payroll_code: string,
  check_date: Date,
  tax_code: string,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.payroll_tax_user_defined_fields')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .where('tax_code', '=', tax_code)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, payrollTaxUserDefinedFieldsSpec)
}

export const getFormattedPayrollTrailerRecord = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.payroll_trailer')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, payrollTrailerSpec)
}

export const getFormattedFileTrailerRecord = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.file_trailer')
    .selectAll()
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .executeTakeFirst()

  if (row === undefined) return ''

  return formatter(row, fileTrailerSpec)
}

// Helper functions
// Helper to get all tax codes used by this company + check_date
const getCompanyTaxCodes = async (
  payroll_code: string,
  check_date: Date,
  db = mainDB,
): Promise<string[]> => {
  const rows = await db
    .selectFrom('mastertax.company_tax')
    .select('tax_code')
    .where('payroll_code', '=', payroll_code)
    .where('check_date', '=', check_date)
    .distinct()
    .execute()

  return rows.map((r) => r.tax_code)
}

// get the wages and taxes for a set of employees and pay cycle
export type TaxSummary = {
  unique_tax_id: string
  total_gross_wages: number
  total_tax_amount: number
}

export type AuditSummary = {
  employee_count: number
  taxes: TaxSummary[]
}

export const getCompanyTaxData = async (
  company_nanoid: string,
  check_date: Date,
  db = mainDB,
): Promise<AuditSummary> => {
  using _ = getContext()

  const tax_map = new Map<
    string,
    { total_gross_wages: number; total_tax_amount: number }
  >()

  // get taxes and wages
  // get company team nanoids
  const company_teams_nanoids = (
    await getTeamsForCompany(company_nanoid as CompanyNanoid)
  ).map((team) => team.nanoid as TeamNanoid)
  const pay_cycle = getPayCycle(check_date)
  // for each team create, get employee payroll settings
  const employee_payroll_settings = (
    await mapAsync(company_teams_nanoids, async (team_nanoid) => {
      return await getEmployeePayrollSettingsByTeam({
        team_nanoid,
        pay_cycle,
      })
    })
  ).flat()

  // for each employee payroll setting get wages and taxes
  const payments = await mapAsync(
    employee_payroll_settings,
    async (setting) => {
      return await getEmployeePaymentsByCategory(setting, pay_cycle)
    },
  )

  for (const payment of payments) {
    // For each payment, update the tax map
    const tax_data = payment.taxes.employee.concat(payment.taxes.employer)
    for (const data of tax_data) {
      if (!data.tax_id) continue // Skip if no tax_id
      // this is very brittle, but it will do for now, we need to split the id in the
      // bucket_accounts into two parts {payroll_program, tax_id} and make it a composite key. We can revisit this once that is done
      const index = data.tax_id.indexOf('-')
      const secondPart = index >= 0 ? data.tax_id.slice(index + 1) : ''
      const startsWithNumber = startsWithNumberRegex.test(secondPart)
      if (!startsWithNumber) continue // Skip if second part does not start with a number
      const unique_tax_id = secondPart
      if (tax_map.has(unique_tax_id)) {
        const existing = tax_map.get(unique_tax_id)!
        tax_map.set(unique_tax_id, {
          total_gross_wages:
            existing.total_gross_wages +
            roundToTwoDecimalPlaces(
              payment.grosspay.amount_cents / CENTS_IN_ONE_DOLLAR,
            ),
          total_tax_amount:
            existing.total_tax_amount +
            roundToTwoDecimalPlaces(
              data.amount.amount_cents / CENTS_IN_ONE_DOLLAR,
            ),
        })
      } else {
        tax_map.set(unique_tax_id, {
          total_gross_wages: roundToTwoDecimalPlaces(
            payment.grosspay.amount_cents / CENTS_IN_ONE_DOLLAR,
          ),
          total_tax_amount: roundToTwoDecimalPlaces(
            data.amount.amount_cents / CENTS_IN_ONE_DOLLAR,
          ),
        })
      }
    }
  }

  const taxes: TaxSummary[] = Array.from(tax_map.entries()).map(
    ([unique_tax_id, data]) => ({
      unique_tax_id,
      total_gross_wages: data.total_gross_wages,
      total_tax_amount: data.total_tax_amount,
    }),
  )

  return {
    employee_count: employee_payroll_settings.length,
    taxes,
  }
}

export const getCafeteriaPlanBenefits = async (
  company_nanoid: CompanyNanoid,
  db = mainDB,
): Promise<Omit<CafeteriaPlanBenefitsType, 'nanoid'> | undefined> => {
  using _ = getContext()

  const row = await db
    .selectFrom('mastertax.cafeteria_plan_benefits')
    .selectAll()
    .where('nanoid', '=', company_nanoid)
    .executeTakeFirst()

  assert(
    row !== undefined,
    'No cafeteria plans found for the given company nanoid',
  )

  const { nanoid, ...rest } = row

  return rest
}
