import type {
  SelectableSmartContracts,
  SelectableSupportedCurrencies,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type { SupportedCurrenciesSymbols } from '@riseworks/contracts/src/routes/dashboard/payments.js'
import type { ValidNetworks } from '@riseworks/contracts/src/smartContractTypes.js'
import { db as mainDB } from 'db/src/index.js'
import { indexBy } from 'remeda'
import assert from 'utils/src/common/assertHTTP.js'
import { getContext } from 'utils/src/common/requestContext.js'

export const getActiveSupportedCurrencies = (db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('rise.supported_currencies')
    .select([
      'id',
      'name',
      'rise_erc20_token',
      'symbol',
      'type',
      'supports_balance',
      'supports_deposits_or_withdraws',
      'supports_invoices',
    ])
    .where('active', 'is', true)
    .execute()
}

/**
 * Retrieves metadata for specified currency symbols from the database.
 *
 * @template T - Type extending SupportedCurrenciesSymbols
 * @param {Object} options - The options object
 * @param {Array<T>} options.currency_symbols - Array of currency symbols to retrieve metadata for
 * @param {ValidNetworks} [options.network='arbitrum'] - The network to filter results by
 * @param {typeof mainDB} [db=mainDB] - The database connection to use
 * @returns {Promise<Record<T, Pick<SelectableSmartContracts, 'address'> & Pick<SelectableSupportedCurrencies, 'symbol'>>>}
 *          A promise that resolves to an object mapping currency symbols to their metadata
 * @throws {Error} When no currency symbols are provided or metadata cannot be fetched for all symbols
 */
export const getCurrencyMetadataFromSymbols = async <
  T extends SupportedCurrenciesSymbols,
>(
  {
    currency_symbols,
    network = 'arbitrum',
  }: {
    currency_symbols: Array<T>
    network: ValidNetworks
  },
  db = mainDB,
): Promise<
  Record<
    T,
    Pick<SelectableSmartContracts, 'address'> &
      Pick<SelectableSupportedCurrencies, 'symbol'>
  >
> => {
  using _ = getContext()

  assert(currency_symbols.length > 0, 'no currency symbols provided')

  const currenciesMetadata = await db
    .selectFrom('rise.supported_currencies as sc')
    .where('sc.symbol', 'in', currency_symbols)
    .innerJoin('rise.smart_contracts as smt', 'smt.name', 'sc.rise_erc20_token')
    .innerJoin('rise.enodes as en', 'en.chain_id', 'smt.network')
    .select(['smt.address', 'sc.symbol'])
    .distinct()
    .where(
      'smt.environment',
      '=',
      process.env.NODE_ENV === 'localhost'
        ? 'development'
        : process.env.NODE_ENV,
    )
    .where('smt.active', '=', true)
    .where('en.active', '=', true)
    .where('en.network', '=', network)
    .execute()

  assert(
    currenciesMetadata.length === currency_symbols.length,
    'Could not fetch metadata for all provided symbols',
  )

  return indexBy(currenciesMetadata, (currency) => currency.symbol)
}
