// Define types for Slack API responses
interface SlackErrorResponse {
  ok: false
  error: string
}

interface SlackSuccessResponse {
  ok: true
  ts: string
  channel: string
}

type SlackResponse = SlackErrorResponse | SlackSuccessResponse

export async function sendSlackMessage(params: {
  blocks: Record<string, unknown>[]
  text: string // Fallback text for notifications
  botToken: string
  channelId: string
}): Promise<[SlackSuccessResponse, null] | [null, Error]> {
  try {
    const response = await fetch('https://slack.com/api/chat.postMessage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        Authorization: `Bearer ${params.botToken}`,
      },
      body: JSON.stringify({
        channel: params.channelId,
        blocks: params.blocks,
        text: params.text,
      }),
    })

    if (!response.ok) {
      // If the response status is not 2xx, throw an error
      const errorData = (await response.json()) as SlackErrorResponse
      return [
        null,
        new Error(
          `Slack API Error: ${response.status} ${response.statusText} - ${errorData.error || JSON.stringify(errorData)}`,
        ),
      ]
    }

    const responseData = (await response.json()) as SlackResponse

    // Slack API responses include an 'ok' boolean field.
    if (responseData.ok) {
      console.log(
        `Successfully sent message ${responseData.ts} to channel ${params.channelId}.`,
      )
      return [responseData, null]
    }
    // This case might occur if response.ok was true but Slack's own 'ok' field is false
    return [
      null,
      new Error(
        `Error sending message to Slack (API indicated failure): ${responseData.error || JSON.stringify(responseData)}`,
      ),
    ]
  } catch (error) {
    return [null, new Error(`Failed to send message to Slack: ${error}`)]
  }
}
