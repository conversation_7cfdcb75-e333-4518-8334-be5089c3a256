import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import { db as mainDB } from 'db/src/index.js'
import type { Expression, SqlBool } from 'kysely'
import { getContext } from 'utils/src/common/requestContext.js'

type InvoiceDataQuery = {
  startDate?: Date
  endDate?: Date
  userNanoid: UserNanoid
  status?: 'open' | 'closed'
}

export const getInvoiceDataByQuery = async (
  query: InvoiceDataQuery,
  db = mainDB,
) => {
  using _ = getContext()
  return await db
    .selectFrom('rise.payment_invoice_data')
    .selectAll()
    .where((eb) => {
      const filters: Expression<SqlBool>[] = []
      if (query.startDate && query.endDate) {
        filters.push(
          eb('rise.payment_invoice_data.created_at', '>=', query.startDate),
        )
        filters.push(
          eb('rise.payment_invoice_data.created_at', '<=', query.endDate),
        )
      }

      if (query.status) {
        filters.push(
          eb('rise.payment_invoice_data.invoice_status', '<=', query.status),
        )
      }

      filters.push(
        eb('rise.payment_invoice_data.recipient_nanoid', '=', query.userNanoid),
      )

      return eb.and(filters)
    })
    .orderBy('created_at desc')
    .execute()
}

/**
 * Calculates the total sum of all open invoices for a specific team.
 *
 * @param team_nanoid - The team's unique identifier (TeamNanoid)
 * @param db - Optional database connection to use (defaults to mainDB)
 * @returns A promise that resolves to an object containing the sum of all open invoice amounts
 *          in cents as 'total_open_invoices_cents' (may be null if no open invoices exist)
 */
export const getOpenInvoiceTotalAmountByTeamNanoid = (
  team_nanoid: TeamNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.payment_invoice_data')
    .where('invoice_status', '=', 'open')
    .where('payer_nanoid', '=', team_nanoid)
    .select((eb) => eb.fn.sum('amount_cents').as('total_open_invoices_cents'))
    .$narrowType<{ total_open_invoices_cents: string }>()
    .executeTakeFirst()
}
