import type {
  UserNanoid,
  WithdrawAccountNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { RisePaymentHandlerForwarderSetTransferRulesRequest } from '@riseworks/contracts/src/forwardersTypedDataSchemas.js'
import type { withdrawFeeResponse } from '@riseworks/contracts/src/routes/dashboard/withdraw.js'
import { BigNumber } from 'bignumber.js'
import { db as mainDB } from 'db'
import { ethers } from 'ethers'
import { null_address } from 'utils/src/blockchain/helpers.js'
import { fixedOrPercent } from 'utils/src/blockchain/paymentHandler.js'
import assert from 'utils/src/common/assertHTTP.js'
import { ERROR_CODES } from 'utils/src/common/errorCodes.js'
import { is } from 'utils/src/common/is.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { z } from 'zod'
import {
  type ValidNetworks,
  getContract,
  getEntityRiseAccount,
  tokenDecimals,
} from './smartContracts.js'
import { getWithdrawAccountById } from './withdrawAccounts.js'

const crossChainRampSchema = z.enum([
  'avalanche_wallet',
  'base_wallet',
  'ethereum_wallet',
  'optimism_wallet',
  'polygon_wallet',
])

export const crossChainNetworkSchema = z.enum([
  'ethereum',
  'avalanche',
  'base',
  'optimism',
  'polygon',
])

const mapDomain: Record<z.infer<typeof crossChainNetworkSchema>, number> = {
  ethereum: 0,
  base: 6,
  avalanche: 1,
  optimism: 2,
  polygon: 7,
}

export const walletGetWithdrawCCTPAccountConfigurations = async (
  nanoid: WithdrawAccountNanoid,
  network: ValidNetworks = 'arbitrum',
  db = mainDB,
): Promise<RisePaymentHandlerForwarderSetTransferRulesRequest['data'][0]> => {
  const withdrawAccount = await getWithdrawAccountById(nanoid, db)
  assert(withdrawAccount, `Withdraw account not found for ${nanoid}`)
  assert(withdrawAccount.network, 'Withdraw account is missing network', '404')
  assert(is(withdrawAccount.ramp, crossChainRampSchema), {
    errorCode: ERROR_CODES.WITHDRAW_INVALID_RAMP,
    internalMessage: `Expected "${crossChainRampSchema.options}" and recieved "${withdrawAccount.ramp}"`,
  })
  assert(is(withdrawAccount.network, crossChainNetworkSchema), {
    errorCode: ERROR_CODES.WITHDRAW_INVALID_RAMP,
    internalMessage: `Expected "${crossChainRampSchema.options}" and recieved "${withdrawAccount.ramp}"`,
  })

  const tokenAddress = await (
    await getContract('RiseUSD', network)
  ).getAddress()

  const riseAccount = await getEntityRiseAccount(
    withdrawAccount.user_nanoid as UserNanoid,
    db,
  )
  assert(riseAccount, 'User account not found', '404')
  assert(
    withdrawAccount.wallet_address,
    'Withdraw account is missing wallet address',
    '404',
  )
  // assert(is(withdrawAccount.wallet_address, null_address), 'Withdraw account is missing wallet address', '404')

  const ramp = await getContract('RiseRampWithdrawCCTP', network)
  const rampImpl = await getContract('RiseRampWithdrawCCTP_impl', network)

  const ctx = await getContext()
  ctx.logger.info(
    `Ramp contract IMPLEMENTATION: ${await rampImpl.getAddress()}`,
  )
  ctx.logger.info(`Ramp contract ADDRESS: ${await ramp.getAddress()}`)
  ctx.logger.info(`Ramp contract DOMAIN: ${mapDomain[withdrawAccount.network]}`)

  const paramData = await ramp.encodeCCTPParams({
    riseAccount: riseAccount.address,
    destinationDomain: mapDomain[withdrawAccount.network],
    mintRecipient: ethers.zeroPadValue(withdrawAccount.wallet_address, 32),
  })

  return {
    token: tokenAddress,
    configs: [
      {
        ramp: await ramp.getAddress(),
        source: tokenAddress, // not used
        destination: null_address,
        amount: '10000',
        transferType: '0',
        fixedOrPercent: fixedOrPercent.percentOfCurrentBalance,
        data: paramData,
        offChainReference: withdrawAccount.external_reference,
      },
    ],
  }
}

export const walletGetWithdrawCCTPAccountFee = async (
  nanoid: WithdrawAccountNanoid,
  db = mainDB,
): Promise<z.infer<typeof withdrawFeeResponse.shape.fees>> => {
  const network = 'arbitrum'
  const withdrawAccount = await getWithdrawAccountById(nanoid, db)
  assert(withdrawAccount, `Withdraw account not found for ${nanoid}`)
  assert(withdrawAccount.network, 'Withdraw account is missing network', '404')
  assert(is(withdrawAccount.ramp, crossChainRampSchema), {
    errorCode: ERROR_CODES.WITHDRAW_INVALID_RAMP,
    internalMessage: `Expected "${crossChainRampSchema.options}" and recieved "${withdrawAccount.ramp}"`,
  })

  const tokenAddress = await (await getContract('USDC', network)).getAddress()
  const ramp = await getContract('RiseRampWithdrawCCTP', network)
  const fee = await ramp.getFee(
    mapDomain[
      withdrawAccount.network as z.infer<typeof crossChainNetworkSchema>
    ],
    tokenAddress,
  )

  const decimals = await tokenDecimals(tokenAddress as 'RiseUSD', network)
  const _decimals = BigNumber(10).pow(decimals - 2n)

  return [
    {
      type: 'flat',
      amount_cents: Number.parseFloat(BigNumber(fee).div(_decimals).toFixed()),
      currency: 'USD',
    },
  ]
}
