import {
  type CompanyNanoid,
  type InviteNanoid,
  type TeamNanoid,
  type TeamRoleNanoid,
  type UserNanoid,
  type UserRiseAccount,
  type UserRiseid,
  companyNanoid,
  webhookEventTypes,
} from '@riseworks/contracts/src/brands.js'
import type {
  InsertableNotificationTokens,
  InsertableRiseEntities,
  InsertableUsersData,
  JsonValue,
  SelectableInvites,
  SelectableNotificationTokens,
  SelectablePaySchedules,
  SelectableUsersOnboarding,
  UpdateableNotificationTokens,
  UpdateableUsersData,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type {
  InsertablePrivateData,
  InsertableUsersData as InsertableRisePrivateUserData,
  SelectableUsersData,
} from '@riseworks/contracts/src/codegen/db/models_rise_private.js'
import { email } from '@riseworks/contracts/src/formats.js'
import { prefill } from '@riseworks/contracts/src/routes/dashboard/invites.js'
import { lower } from 'db/src/helpers/lower.js'
import { type DBTransaction, db as mainDB } from 'db/src/index.js'
import type { Expression, SqlBool } from 'kysely'
import {
  jsonArrayFrom,
  jsonBuildObject,
  jsonObjectFrom,
} from 'kysely/helpers/mysql'
import { keys } from 'remeda'
import { null_address } from 'utils/src/blockchain/helpers.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { run } from 'utils/src/common/run.js'
import { v4 as uuidv4 } from 'uuid'
import { getAddress, upsertAddress } from './addresses.js'
import {
  _getCompaniesByFounderQuery,
  _getCompanyFullDataQuery,
  getCompaniesByFounder,
  getCompanyByCompanyOrTeam,
  getCompanyFromTeam,
  getFullCompanyData,
} from './companies.js'
import { calculatePagination, createCompanyPSA } from './documentsInternal.js'
import { upsertEntityPermission } from './entityPermissions.js'
import { getInviteRiseId, updateInvite } from './invites.js'
import { getTeamRoleFromCompanyRole } from './permissions.js'
import { upsertPrivateData } from './privateData.js'
import { getSponsorAccount } from './riseAccounts.js'
import {
  addCompanyAdminToTeams,
  checkUserRelationshipExists,
  getEntityByNanoid,
  insertEntity,
  issueContractorOrEmployeeAddressesIfNeeded,
  updateEntity,
} from './riseEntities.js'
import { createRiseId, setAddressesOwnerNanoid } from './smartContracts.js'
import { upsertOnboarding } from './usersOnboarding.js'
import { insertUserRsk } from './usersRsk.js'
import webhookEventDispatch from './webhook/webhookEventDispatch.js'

const _getUserQuery = (db = mainDB) => {
  return db
    .selectFrom('rise.users_data as ud')
    .innerJoin('rise.rise_entities as re', 'ud.nanoid', 're.nanoid')
    .selectAll(['ud'])
    .select(['re.riseid', 're.avatar', 're.type', 're.parent_riseid'])
    .$narrowType<{
      type: 'user'
    }>()
}
export const getUserByNanoid = (nanoid: UserNanoid, db = mainDB) => {
  using _ = getContext()
  return _getUserQuery(db).where('re.nanoid', '=', nanoid).executeTakeFirst()
}
export const getUsersByNanoids = (nanoids: UserNanoid[], db = mainDB) => {
  using _ = getContext()
  return _getUserQuery(db).where('re.nanoid', 'in', nanoids).execute()
}
export const getUsersByPaymentHandlers = (addresses: string[], db = mainDB) => {
  using _ = getContext()
  return _getUserQuery(db)
    .innerJoin(
      'rise.blockchain_addresses as ra',
      'ra.owner_address',
      're.riseid',
    )
    .innerJoin(
      'rise.blockchain_addresses as ph',
      'ph.parent_account',
      'ra.address',
    )
    .select(['ph.address as payment_handler'])
    .where('ph.type', '=', 'pay_handler')
    .where('ph.address', 'in', addresses)
    .execute()
}
export const getUserByEmail = (email: string, db = mainDB) => {
  using _ = getContext()
  return _getUserQuery(db).where('email', '=', email).executeTakeFirst()
}
export const getUsersBySearch = (search: string, db = mainDB) => {
  using _ = getContext()
  return _getUserQuery(db)
    .where((eb) =>
      eb.or([
        eb('email', 'like', `%${search}%`),
        eb('first_name', 'like', `%${search}%`),
        eb('last_name', 'like', `%${search}%`),
      ]),
    )
    .execute()
}

export const updateUserWithPrivateData = async (
  user: UpdateableUsersData,
  data: InsertablePrivateData,
  db: DBTransaction,
) => {
  using _ = getContext()
  if (keys(user).length > 0)
    await updateUserData(data.nanoid as UserNanoid, user, db)
  if (keys(data).length > 0) await upsertPrivateData(data, db)
}
export const updateUserData = (
  nanoid: UserNanoid,
  user: UpdateableUsersData,
  db = mainDB,
) => {
  using _ = getContext()

  if (Object.keys(user).length > 0) {
    return db
      .updateTable('rise.users_data')
      .set(user)
      .where('nanoid', '=', nanoid)
      .execute()
  }
}
export const insertUser = async ({
  user,
  entity,
  app,
  db,
}: {
  user: Omit<InsertableUsersData, 'nanoid' | 'rise_account'>
  entity: {
    avatar: string
  }
  app?: 'pay' | 'documents'
  db: DBTransaction
}) => {
  using _ = getContext()
  assert(is(user.email, email), `Missing/Invalid user email: ${user.email}`)

  const inviteRiseId = (await getInviteRiseId(user.email)) as UserRiseid

  const riseid =
    inviteRiseId ??
    (await createRiseId({
      type: 'user',
      owner_address: null_address,
      parent_account: null,
      last_modified_by: user.last_modified_by ?? undefined,
      db,
    }))
  const riseAccount = (await createRiseId({
    type: 'rise_account',
    owner_address: riseid,
    parent_account: null,
    last_modified_by: user.last_modified_by ?? undefined,
    db,
  })) as unknown as UserRiseAccount
  const nanoid = await insertEntity(
    {
      riseid,
      type: 'user',
      avatar: entity.avatar,
      parent_riseid: riseid,
    },
    db,
  )
  await setAddressesOwnerNanoid([riseid, riseAccount], nanoid, db)
  await db
    .insertInto('rise.users_data')
    .values({ ...user, rise_account: riseAccount, nanoid })
    .execute()

  await db
    .insertInto('rise_private.users_data')
    .values({ nanoid, pk_salt: uuidv4() })
    .execute()

  await upsertEntityPermission(
    {
      nanoid,
      ...(app === 'documents' && {
        document_app: true,
      }),
      ...(app === 'pay' && {
        pay_app: true,
      }),
    },
    db,
  )

  if (app === 'documents') {
    await upsertOnboarding(
      {
        nanoid: nanoid,
        step: 'complete',
        role: 'viewer',
        moderation_status: 'approved',
        moderation_internal_note: '',
        moderation_public_note: '',
        accepted_invites: true,
        register_business: true,
        last_modified_by: nanoid,
      },
      db,
    )
  }

  await insertUserRsk(
    {
      nanoid,
      last_modified_by: nanoid,
    },
    db,
  )

  return getUserByEmail(user.email, db)
}
export const updateAvatar = (
  riseid: UserRiseid,
  avatar: string,
  last_modified_by?: UserNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return updateEntity({
    riseid: riseid,
    data: {
      avatar,
      last_modified_by,
    },
    db,
  })
}

export const getUsersFromCompany = (
  company_nanoid: CompanyNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.rise_entities as re')
    .innerJoin('rise.rise_entities as re2', (join) =>
      join
        .onRef('re2.riseid', '=', 're.parent_riseid')
        .onRef('re2.parent_riseid', '=', 're.parent_riseid'),
    )
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 're2.nanoid')
    .selectAll(['ud'])
    .select(['re.type as type'])
    .where('re.nanoid', '=', company_nanoid)
    .execute()
}

export const getUsersFromTeams = (team_nanoids: TeamNanoid[], db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('rise.rise_entities as re')
    .innerJoin('rise.rise_entities as re2', (join) =>
      join
        .onRef('re2.riseid', '=', 're.riseid')
        .on('re2.type', 'in', [
          'team_admin',
          'team_viewer',
          'team_employee',
          'contractor',
          'team_finance_admin',
        ]),
    )
    .innerJoin('rise.rise_entities as re3', (join) =>
      join
        .onRef('re3.riseid', '=', 're2.parent_riseid')
        .onRef('re3.parent_riseid', '=', 're2.parent_riseid'),
    )
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 're3.nanoid')
    .selectAll(['ud', 're2'])
    .select(['re2.type as type'])
    .where('re.nanoid', 'in', team_nanoids)
    .execute()
}

export const getFounderFromCompany = async (
  company_nanoid: CompanyNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  const result = await db
    .selectFrom('rise.rise_entities as re')
    .innerJoin('rise.rise_entities as re2', (join) =>
      join
        .onRef('re2.riseid', '=', 're.parent_riseid')
        .onRef('re2.parent_riseid', '=', 're.parent_riseid'),
    )
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 're2.nanoid')
    .selectAll(['ud'])
    .where('re.nanoid', '=', company_nanoid)
    .where('re.type', '=', 'company')
    .executeTakeFirst()
  return result
}

export const getUsersFromCompanyAndTeams = async (
  company_nanoid: CompanyNanoid,
  team_nanoids: TeamNanoid[] = [],
  db = mainDB,
) => {
  using _ = getContext()
  const usersFromCompany = await getUsersFromCompany(company_nanoid, db)
  const usersFromTeams = await db
    .selectFrom('rise.rise_entities as re')
    .innerJoin('rise.rise_entities as re2', (join) =>
      join
        .onRef('re2.parent_riseid', '=', 're.riseid')
        .on('re2.type', '=', 'team'),
    )
    .innerJoin('rise.rise_entities as re3', (join) =>
      join
        .onRef('re3.riseid', '=', 're2.riseid')
        .on('re3.type', 'in', [
          'team_admin',
          'team_viewer',
          'team_employee',
          'contractor',
          'team_finance_admin',
        ]),
    )
    .innerJoin('rise.rise_entities as re4', (join) =>
      join
        .onRef('re4.riseid', '=', 're3.parent_riseid')
        .onRef('re4.parent_riseid', '=', 're.parent_riseid'),
    )
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 're4.nanoid')
    .selectAll(['ud'])
    .select(['re4.type'])

    .where('re.nanoid', '=', company_nanoid)
    .$if(team_nanoids.length > 0, (qb) =>
      qb.where('re2.nanoid', 'in', team_nanoids),
    )
    .execute()
  return usersFromCompany.concat(usersFromTeams)
}

export const getUsersFromTeam = (team_nanoid: TeamNanoid, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('rise.rise_entities as re')
    .innerJoin('rise.rise_entities as re2', (join) =>
      join.onRef('re2.riseid', '=', 're.riseid').on('re2.type', '!=', 'team'),
    )
    .innerJoin('rise.rise_entities as re3', (join) =>
      join
        .onRef('re3.riseid', '=', 're2.parent_riseid')
        .onRef('re3.parent_riseid', '=', 're2.parent_riseid'),
    )
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 're3.nanoid')
    .selectAll(['ud'])
    .select(['re2.type as type'])
    .where('re.nanoid', '=', team_nanoid)
    .execute()
}

const _getFullUserQuery = ({
  db = mainDB,
  team_nanoid,
}: {
  db?: typeof mainDB
  team_nanoid?: TeamNanoid
}) => {
  return db
    .selectFrom('rise.users_data as ud')
    .innerJoin('rise.rise_entities as re', 'ud.nanoid', 're.nanoid')
    .innerJoin('rise_private.addresses as ad', 'ad.nanoid', 'ud.nanoid')
    .leftJoin('rise.users_onboarding as uo', 'ud.nanoid', 'uo.nanoid')
    .leftJoin(
      (eb) => {
        let query = eb
          .selectFrom('rise.invites as i1')
          .leftJoin('rise.invites as i2', (join) => {
            let joinBuilder = join
              .onRef('i1.email', '=', 'i2.email')
              .onRef('i1.created_at', '<', 'i2.created_at')

            if (team_nanoid) {
              joinBuilder = joinBuilder.onRef(
                'i1.invited_to',
                '=',
                'i2.invited_to',
              )
            }

            return joinBuilder
          })
          .where('i2.nanoid', 'is', null)
          .select(['i1.nanoid', 'i1.email'])

        if (team_nanoid) {
          query = query.where('i1.invited_to', '=', team_nanoid)
        }

        return query.as('contextual_invite')
      },
      (join) => join.onRef('contextual_invite.email', '=', 'ud.email'),
    )
    .select((eb) => [
      'ud.nanoid',
      'ud.email',
      'ud.first_name',
      'ud.middle_name',
      'ud.last_name',
      'ud.phone',
      're.avatar',
      'ud.last_modified_by',
      're.riseid as blockchain_address',
      're.type',
      'ud.dob',
      'ud.account_status',
      'ud.country',
      'contextual_invite.nanoid as invite_nanoid',
      jsonBuildObject({
        line_1: eb.ref('ad.line_1'),
        line_2: eb.ref('ad.line_2'),
        city: eb.ref('ad.city'),
        state: eb.ref('ad.state'),
        zip_code: eb.ref('ad.zip_code'),
        country: eb.ref('ad.country'),
        timezone: eb.ref('ad.timezone'),
      }).as('address'),
      jsonBuildObject({
        linkedin: eb.ref('ud.linkedin'),
        discord: eb.ref('ud.discord'),
        website: eb.ref('ud.website'),
        x: eb.ref('ud.x'),
      }).as('social'),
      jsonArrayFrom(
        eb
          .selectFrom('rise.users_certifications as uc')
          .whereRef('uc.user_nanoid', '=', 'ud.nanoid')
          .select([
            'uc.nanoid',
            'uc.title',
            'uc.website',
            'uc.year',
            'uc.file',
          ]),
      ).as('certifications'),
      jsonBuildObject({
        onboarded: eb
          .case()
          .when('uo.nanoid', 'is not', null)
          .then(true)
          .else(false)
          .end(),
        step: eb.ref('uo.step'),
        role: eb.ref('uo.role'),
        moderation_status: eb.ref('uo.moderation_status'),
        register_business: eb.ref('uo.register_business'),
      }).as('onboarding'),
    ])
}

export const getFullUserData = (nanoid: UserNanoid, db = mainDB) => {
  using _ = getContext()
  return _getFullUserQuery({ db })
    .where('re.nanoid', '=', nanoid)
    .executeTakeFirst()
}

export const getFullUserDataList = (nanoids: UserNanoid[], db = mainDB) => {
  using _ = getContext()
  return _getFullUserQuery({ db }).where('re.nanoid', 'in', nanoids).execute()
}

export const getUserSummariesByTeam = async (
  team_nanoid: TeamNanoid,
  filter?: {
    worker_type?: Array<'team_employee' | 'contractor' | 'team_admin'>
    onboarding_step?: SelectableUsersOnboarding['step'][]
    fee_coverage?: boolean
    country?: string[]
    pay_schedule?: SelectablePaySchedules['type'][]
    onboarding_status?: Array<'invited' | 'approved' | 'rejected' | 'pending'>
    user_team_status?: Array<'active' | 'inactive'>
    search?: string
  },
  db = mainDB,
) => {
  using _ = getContext()

  const _team_members = await db
    .with('team_members', (qb) =>
      qb
        .selectFrom('rise.rise_entities as re')
        .innerJoin('rise.rise_entities as re2', (join) =>
          join.onRef('re2.riseid', '=', 're.riseid'),
        )
        .innerJoin('rise.rise_entities as re3', (join) =>
          join
            .onRef('re3.riseid', '=', 're2.parent_riseid')
            .onRef('re3.parent_riseid', '=', 're2.parent_riseid'),
        )
        .innerJoin('rise.team_role_settings as trs', (join) =>
          join
            .onRef('trs.team_nanoid', '=', 're.nanoid')
            .onRef('trs.user_nanoid', '=', 're3.nanoid'),
        )
        .innerJoin('rise.users_data as ud', 'ud.nanoid', 're3.nanoid')
        .where('re.nanoid', '=', team_nanoid)
        .select(({ eb, ref }) => [
          're3.nanoid',
          're3.type',
          'trs.status as status',
          're2.nanoid as relationship_nanoid',
          're2.type as relationship_type',
          eb
            .case()
            .when('re2.type', '=', 'team_employee')
            .then(ref('trs.rise_account'))
            .else(ref('ud.rise_account'))
            .end()
            .as('rise_account'),
        ])
        .$if(
          !!filter?.user_team_status && filter.user_team_status.length > 0,
          (qb) => qb.where('trs.status', 'in', filter?.user_team_status!),
        )
        .$if(!!filter?.worker_type && filter.worker_type.length > 0, (qb) =>
          qb.where('re2.type', 'in', filter?.worker_type!),
        )
        .$narrowType<{
          nanoid: UserNanoid
          type: InsertableRiseEntities['type']
          relationship_type: 'team_employee' | 'contractor'
          relationship_nanoid: TeamRoleNanoid
          rise_account: UserRiseAccount
        }>(),
    )
    .with('team_members_data', (qb) =>
      _getFullUserQuery({ db, team_nanoid })
        .where(
          're.nanoid',
          'in',
          qb.selectFrom('team_members').select('nanoid'),
        )
        .$if(
          !!filter?.onboarding_step && filter.onboarding_step.length > 0,
          (qb) => qb.where('uo.step', 'in', filter?.onboarding_step!),
        )
        .$if(
          !!filter?.onboarding_status && filter.onboarding_status.length > 0,
          (qb) => {
            const filterStatuses =
              filter?.onboarding_status?.filter((s) => s !== 'invited') || []
            return filterStatuses.length
              ? qb.where('uo.moderation_status', 'in', filterStatuses!)
              : qb.where('uo.moderation_status', 'not in', [
                  'pending',
                  'submitted',
                  'approved',
                  'rejected',
                ])
          },
        )
        .$if(!!filter?.country && filter.country.length > 0, (qb) =>
          qb.where('ud.country', 'in', filter?.country!),
        ),
    )
    .with('team_members_companies', (qb) =>
      _getCompaniesByFounderQuery(db)
        .where(
          're2.nanoid',
          'in',
          qb.selectFrom('team_members').select('nanoid'),
        )
        .$narrowType<{ nanoid: CompanyNanoid }>(),
    )
    .with('full_company_data', (qb) =>
      _getCompanyFullDataQuery(db).where(
        'cd.nanoid',
        'in',
        qb.selectFrom('team_members_companies').select('nanoid'),
      ),
    )
    .with('pay_schedules', (qb) =>
      qb
        .selectFrom('rise.pay_schedules')
        .selectAll()
        .where('team_nanoid', '=', team_nanoid)
        .where(
          'user_nanoid',
          'in',
          qb.selectFrom('team_members').select('nanoid'),
        ),
    )
    .with('employee_payroll', (qb) =>
      qb
        .selectFrom('rise.employee_payroll_settings')
        .select(['user_nanoid'])
        .where('team_nanoid', '=', team_nanoid)
        .where(
          'user_nanoid',
          'in',
          qb.selectFrom('team_members').select('nanoid'),
        )
        .where((eb) =>
          eb.or([
            eb('effective_end', 'is', null),
            eb('effective_end', '>', new Date()),
          ]),
        ),
    )
    .selectFrom('team_members as tm')
    .innerJoin('team_members_data as tmd', 'tmd.nanoid', 'tm.nanoid')
    .$if(!!filter?.pay_schedule && filter.pay_schedule.length > 0, (qb) =>
      qb.where((eb) =>
        eb.exists(
          eb
            .selectFrom('pay_schedules as ps')
            .selectAll()
            .where('ps.type', 'in', filter?.pay_schedule!),
        ),
      ),
    )
    .select((eb) => [
      'tm.relationship_type',
      'tm.relationship_nanoid',
      'tm.rise_account',
      jsonBuildObject({
        withdraw_fee_coverage: eb.val(false),
        status: eb.ref('tm.status'),
        is_on_payroll: eb.exists(
          eb
            .selectFrom('employee_payroll as ep')
            .select('ep.user_nanoid')
            .where('ep.user_nanoid', '=', eb.ref('tm.nanoid')),
        ),
      }).as('settings'),
      jsonObjectFrom(
        eb
          .selectFrom('team_members_data as tmd')
          .select([
            'tmd.address',
            'tmd.avatar',
            'tmd.blockchain_address',
            'tmd.certifications',
            'tmd.country',
            'tmd.dob',
            'tmd.email',
            'tmd.first_name',
            'tmd.last_modified_by',
            'tmd.last_name',
            'tmd.middle_name',
            'tmd.nanoid',
            'tmd.onboarding',
            'tmd.phone',
            'tmd.social',
            'tmd.type',
            'tmd.invite_nanoid',
          ])
          .whereRef('tmd.nanoid', '=', 'tm.nanoid'),
      )
        .$notNull()
        .as('user'),
      jsonObjectFrom(
        eb
          .selectFrom('full_company_data as fcd')
          .select([
            'fcd.address',
            'fcd.avatar',
            'fcd.blockchain_address',
            'fcd.admin_contact',
            'fcd.doing_business_as',
            'fcd.incorporation_country',
            'fcd.incorporation_type',
            'fcd.is_dao',
            'fcd.name',
            'fcd.nanoid',
            'fcd.ownership',
            'fcd.phone',
          ])
          .whereRef('fcd.user_nanoid', '=', 'tm.nanoid')
          .limit(1),
      ).as('company'),
      jsonArrayFrom(
        eb
          .selectFrom('pay_schedules as ps')
          .select([
            'ps.amount',
            'ps.end_date',
            'ps.nanoid',
            'ps.start_date',
            'ps.team_nanoid',
            'ps.payments_amount',
            'ps.type',
          ])
          .whereRef('ps.user_nanoid', '=', 'tm.nanoid'),
      ).as('pay_schedules'),
    ])
    .$if(!!filter?.search, (qb) =>
      filter?.search
        ? qb.where(({ eb, ref }) => {
            const searchTerm = `%${filter.search!.toLowerCase()}%`
            return eb.or([
              eb(lower(ref('tmd.email')), 'like', searchTerm),
              eb(lower(ref('tmd.first_name')), 'like', searchTerm),
              eb(lower(ref('tmd.last_name')), 'like', searchTerm),
              eb(
                lower(
                  eb.fn('concat', [
                    eb.ref('tmd.first_name'),
                    eb.val(' '),
                    eb.ref('tmd.last_name'),
                  ]),
                ),
                'like',
                searchTerm,
              ),
            ])
          })
        : qb,
    )
    .execute()

  const invited_members = await db
    .selectFrom('rise.invites as i')
    .leftJoin('rise.users_data as ud', 'i.email', 'ud.email')
    .select((eb) => [
      'i.role as relationship_type',
      'i.prefill',
      'ud.nanoid',
      'ud.first_name',
      'ud.middle_name',
      'ud.last_name',
      'ud.account_status',
      jsonBuildObject({
        email: eb.ref('i.email'),
      }).as('user'),
      jsonBuildObject({
        withdraw_fee_coverage: eb.val(false),
        status: eb.val('active'),
        is_on_payroll: eb.val(false),
      }).as('settings'),
    ])
    .where('i.invited_to', '=', team_nanoid)
    .where('i.expires_at', '>', new Date())
    .where('i.status', '=', 'pending')
    .$if(
      !!filter?.onboarding_status && filter.onboarding_status.length > 0,
      (qb) =>
        filter?.onboarding_status?.includes('invited')
          ? qb.where('i.status', '=', 'pending')
          : qb.where('i.status', '<>', 'pending'),
    )
    .$if(!!filter?.worker_type && filter.worker_type.length > 0, (qb) =>
      qb.where('i.role', 'in', filter?.worker_type!),
    )
    .$if(!!filter?.search, (qb) =>
      filter?.search
        ? qb.where(({ eb, ref }) => {
            const searchTerm = `%${filter.search!.toLowerCase()}%`
            return eb.or([eb(lower(ref('i.email')), 'like', searchTerm)])
          })
        : qb,
    )
    .$narrowType<{
      relationship_type: 'team_employee' | 'contractor' | 'team_admin'
    }>()
    .execute()

  const team_members = await mapAsync(
    _team_members,
    async ({ rise_account, settings, ...data }) => {
      return {
        ...data,
        settings: {
          ...settings,
          withdraw_fee_coverage: Boolean(await getSponsorAccount(rise_account)),
        },
      }
    },
    3,
  )

  return [
    ...invited_members.map((i) => {
      const clean_prefill = Object.fromEntries(
        Object.entries(i.prefill || {}).filter(([_, v]) => v !== ''),
      )
      const _prefill = prefill.partial().parse(clean_prefill)
      return {
        relationship_type: i.relationship_type,
        nanoid: i.nanoid,
        settings: {
          withdraw_fee_coverage: false,
          status: 'active' as const,
          is_on_payroll: false,
        },
        user: {
          first_name: _prefill.first_name || i.first_name,
          last_name: _prefill.last_name || i.last_name,
          middle_name: _prefill.middle_name || i.middle_name,
          is_activated: i.account_status === 'activated',
          dob: _prefill.dob,
          address: {
            line_1: _prefill.line_1,
            line_2: _prefill.line_2,
            city: _prefill.city,
            state: _prefill.state,
            country: _prefill.country,
            zip_code: _prefill.zip_code,
            timezone: _prefill.timezone,
          },
          country: _prefill.country || '',
          email: i.user.email,
        },
      }
    }),
    ...team_members,
  ].filter((team_member) => {
    if (typeof filter?.fee_coverage === 'boolean') {
      return team_member.settings.withdraw_fee_coverage === filter.fee_coverage
    }

    return true
  })
}

export const acceptInvites = async ({
  invites,
  user_nanoid,
  last_modified_by,
  ip,
  db,
}: {
  invites: {
    nanoid: InviteNanoid
    invited_to: TeamNanoid | CompanyNanoid
    invited_by: UserNanoid
    prefill: JsonValue
    role: SelectableInvites['role']
  }[]
  user_nanoid: UserNanoid
  ip: string
  last_modified_by: UserNanoid
  db: DBTransaction
}) => {
  using _ = getContext()
  const user = await getUserByNanoid(user_nanoid, db)
  assert(user, `User ${user_nanoid} not found`, '404')
  const address = await getAddress(user.nanoid, db)
  const psaList: Array<{
    userNanoid: UserNanoid
    companyNanoid: CompanyNanoid
    teamNanoid: TeamNanoid
    ip: string
  }> = []
  for (const invite of invites) {
    const entity = await getEntityByNanoid(invite.invited_to, db)
    let inviteCompanyNanoid: CompanyNanoid | null = null
    assert(entity, `Entity ${invite.invited_to} not found`, '404')
    if (
      ['org_admin', 'org_finance_admin', 'org_viewer'].includes(invite.role)
    ) {
      assert(is(invite.invited_to, companyNanoid), 'Invalid invite role', '400')
      inviteCompanyNanoid = invite.invited_to as CompanyNanoid
      await addCompanyAdminToTeams(
        inviteCompanyNanoid,
        user.riseid as UserRiseid,
        getTeamRoleFromCompanyRole(invite.role),
        db,
      )
    } else {
      const company = await getCompanyFromTeam(
        invite.invited_to as TeamNanoid,
        db,
      )
      assert(
        company,
        `Failed to retrieve company for team ${invite.invited_to}`,
      )
      inviteCompanyNanoid = company.nanoid
    }

    const relationshipExists = await checkUserRelationshipExists(
      user.nanoid,
      entity.nanoid as TeamNanoid | CompanyNanoid,
      invite.role,
      db,
    )
    if (!relationshipExists) {
      await insertEntity(
        {
          riseid: entity.riseid,
          type: invite.role,
          parent_riseid: user.riseid,
          last_modified_by,
        },
        db,
      )
      await issueContractorOrEmployeeAddressesIfNeeded(
        user_nanoid,
        invite.invited_to as TeamNanoid,
        'arbitrum',
        db,
      )
    }
    // PSA is only created contractor roles
    if (invite.role === 'contractor') {
      psaList.push({
        userNanoid: user_nanoid,
        companyNanoid: inviteCompanyNanoid,
        teamNanoid: invite.invited_to as TeamNanoid,
        ip,
      })
    }
    if (is(invite.invited_to, companyNanoid)) {
      await webhookEventDispatch({
        event_key: `${webhookEventTypes.INVITE_ACCEPTED}.v1`,
        event_type: webhookEventTypes.INVITE_ACCEPTED,
        version: '1.0',
        company_nanoid: invite.invited_to,
        payload: {
          invite: {
            user_nanoid: user.nanoid,
            nanoid: invite.nanoid,
            invited_to: invite.invited_to,
            invited_by: invite.invited_by,
            role: invite.role,
            email: user.email,
            status: 'accepted',
          },
        },
      })
    } else {
      const teamCompany = await getCompanyByCompanyOrTeam(invite.invited_to, db)
      assert(teamCompany, `Company ${invite.invited_to} not found`, '404')
      await webhookEventDispatch({
        event_key: `${webhookEventTypes.INVITE_ACCEPTED}.v1`,
        event_type: webhookEventTypes.INVITE_ACCEPTED,
        version: '1.0',
        company_nanoid: teamCompany.nanoid,
        team_nanoid: invite.invited_to,
        payload: {
          invite: {
            user_nanoid: user.nanoid,
            nanoid: invite.nanoid,
            invited_to: invite.invited_to,
            invited_by: invite.invited_by,
            role: invite.role,
            email: user.email,
            status: 'accepted',
          },
        },
      })
    }
  }

  if (!address) {
    const addressFromPrefill = invites.find(
      (i) => prefill.safeParse(i.prefill).success,
    )?.prefill
    const addressObject = run(() => {
      if (addressFromPrefill) {
        const obj = prefill.parse(addressFromPrefill)
        return {
          line_1: obj.line_1 ?? '',
          line_2: obj.line_2 ?? '',
          city: obj.city ?? '',
          state: obj.state ?? '',
          country: obj.country ?? '',
          zip_code: obj.zip_code ?? '',
        }
      }
      return {
        line_1: '',
        line_2: '',
        city: '',
        state: '',
        country: '',
        zip_code: '',
      }
    })
    await upsertAddress(
      {
        nanoid: user.nanoid,
        last_modified_by,
        ...addressObject,
      },
      db,
    )
  }
  await mapAsync(invites, (i) =>
    updateInvite(i.nanoid, { last_modified_by, status: 'accepted' }, db),
  )

  await mapAsync(psaList, (i) => createCompanyPSA({ ...i, db }))
}

export const getUsersForDocumentSign = async (
  { offset, limit, name }: { offset?: number; limit?: number; name?: string },
  db = mainDB,
) => {
  using _ = getContext()
  let pagination = {
    total_records: 1,
    total_pages: 1,
    current_page: 1,
    prev_page: 0,
    next_page: 0,
  }
  let query = db
    .selectFrom('rise.users_data as ud')
    .innerJoin('rise.rise_entities as re', 'ud.nanoid', 're.nanoid')
    .select([
      'first_name',
      'last_name',
      'middle_name',
      'email',
      'ud.nanoid',
      're.avatar',
    ])
    .where((eb) => {
      const filters: Expression<SqlBool>[] = []

      if (name) filters.push(eb('ud.first_name', 'like', `%${name}%`))

      return eb.and(filters)
    })
    .orderBy('first_name asc')

  const resultForPagination = await query.execute()

  if (offset && limit) {
    pagination = calculatePagination(resultForPagination.length, offset, limit)
  }

  if (limit) query = query.limit(limit)
  if (offset) query = query.offset(offset)

  return {
    items: await query.execute(),
    pagination,
  }
}

export const getUserCompany = async (nanoid: UserNanoid, db = mainDB) => {
  using _ = getContext()
  const companies = await getCompaniesByFounder(nanoid, db)
  assert(companies.length, `User ${nanoid} has no companies`, '404')
  assert(companies.length === 1, `User ${nanoid} has multiple companies`, '500')
  assert(!!companies[0], `User ${nanoid} company is invalid`, '500')
  const company = await getFullCompanyData(companies[0].nanoid, db)
  assert(company, `Company ${companies[0].nanoid} not found`, '404')

  return company
}

export const getNotificationToken = async (
  user_nanoid: SelectableNotificationTokens['user_nanoid'],
  db = mainDB,
) => {
  using _ = getContext()

  const result = await db
    .selectFrom('rise.notification_tokens as nt')
    .selectAll(['nt'])
    .where('nt.user_nanoid', '=', user_nanoid)
    .where('nt.status', '=', 'active')
    .orderBy('nt.updated_at', 'desc')
    .execute()

  return result.find((nt) => nt.app_type === 'mobile') ?? result?.[0]
}

export const getNotificationTokenByExtUserId = async (
  ext_user_id: SelectableNotificationTokens['ext_user_id'],
  db = mainDB,
) => {
  using _ = getContext()

  const result = await db
    .selectFrom('rise.notification_tokens as nt')
    .selectAll(['nt'])
    .where('nt.ext_user_id', '=', ext_user_id)
    .where('nt.status', '=', 'active')
    .orderBy('nt.updated_at', 'desc')
    .executeTakeFirst()

  return result
}

export const getNotificationTokensByAppType = async (
  user_nanoid: SelectableNotificationTokens['user_nanoid'],
  app_type: SelectableNotificationTokens['app_type'],
  db = mainDB,
) => {
  using _ = getContext()

  const result = await db
    .selectFrom('rise.notification_tokens as nt')
    .selectAll(['nt'])
    .where('nt.user_nanoid', '=', user_nanoid)
    .where('nt.app_type', '=', app_type)
    .execute()

  return result
}

export const insertNotificationToken = async (
  payload: InsertableNotificationTokens,
  db = mainDB,
) => {
  using _ = getContext()

  await db
    .insertInto('rise.notification_tokens')
    .values({
      ...payload,
      updated_at: new Date(),
    })
    .execute()
}

export const updateNotificationToken = async (
  ids: Array<SelectableNotificationTokens['id']>,
  payload: UpdateableNotificationTokens,
  db = mainDB,
) => {
  using _ = getContext()

  await db
    .updateTable('rise.notification_tokens')
    .set({
      ...payload,
      updated_at: new Date(),
    })
    .where('id', 'in', ids)
    .execute()
}

export const getUsersWithNoRisePrivateUserData = async (db = mainDB) => {
  using _ = getContext()
  const result = await db
    .selectFrom('rise.users_data as ud')
    .leftJoin('rise_private.users_data as pd', 'ud.nanoid', 'pd.nanoid')
    .where('pd.nanoid', 'is', null)
    .select(['ud.nanoid'])
    .limit(10)
    .execute()

  return result
}

export const getRisePrivateUsersData = async (
  user_nanoid: SelectableUsersData['nanoid'],
  db = mainDB,
) => {
  using _ = getContext()
  const result = await db
    .selectFrom('rise_private.users_data as pd')
    .where('pd.nanoid', '=', user_nanoid)
    .select(['pd.nanoid', 'pd.pk_salt'])
    .executeTakeFirst()

  return result
}

export const insertRisePrivateUsersData = async (
  payload: InsertableRisePrivateUserData,
  db = mainDB,
) => {
  using _ = getContext()

  await db
    .insertInto('rise_private.users_data')
    .values({
      ...payload,
      pk_salt: uuidv4(),
      updated_at: new Date(),
    })
    .execute()
}

export const getUserEmailsByCompanyOrTeamNanoid = async (
  companyOrTeamNanoid: CompanyNanoid | TeamNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  const result = await db
    .selectFrom('rise.users_data as ud')
    .innerJoin(
      db
        .selectFrom('rise.company_role_settings')
        .select('user_nanoid')
        .where('company_nanoid', '=', companyOrTeamNanoid as CompanyNanoid)
        .union(
          db
            .selectFrom('rise.team_role_settings')
            .select('user_nanoid')
            .where('team_nanoid', '=', companyOrTeamNanoid as TeamNanoid),
        )
        .as('matched_users'),
      'matched_users.user_nanoid',
      'ud.nanoid',
    )
    .select('ud.email')
    .distinct()
    .execute()

  return result.map(({ email }) => email)
}
