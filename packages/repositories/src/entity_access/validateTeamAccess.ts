import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getTeamByNanoid, getTeamNanoidsForUser } from '../teams.js'

export const validateTeamAccess = async (
  user_nanoid: UserNanoid,
  team_nanoid: TeamNanoid,
): Promise<void> => {
  // Validate that the team exists
  const team = await getTeamByNanoid(team_nanoid)
  assert(team, 'Team not found', '404')

  // Validate if the user has access to the team
  const userTeams = await getTeamNanoidsForUser(user_nanoid)
  const hasAccess = userTeams.some((t) => t.nanoid === team_nanoid)
  assert(hasAccess, 'User does not have access to this team', '403')
}
