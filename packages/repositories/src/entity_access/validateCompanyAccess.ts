import type {
  CompanyNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getCompaniesByUser, getCompanyByNanoid } from '../companies.js'

export const validateCompanyAccess = async (
  user_nanoid: UserNanoid,
  company_nanoid: CompanyNanoid,
): Promise<void> => {
  // Validate that the company exists
  const company = await getCompanyByNanoid(company_nanoid)
  assert(company, 'Company not found', '404')

  // Validate if the user has access to the company
  const userCompanies = await getCompaniesByUser(user_nanoid)
  const hasAccess = userCompanies.some((c) => c.nanoid === company_nanoid)
  assert(hasAccess, 'User does not have access to this company', '403')
}
