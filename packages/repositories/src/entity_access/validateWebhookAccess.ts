import type {
  UserNanoid,
  WebhookEndpointNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { WebhookEndpointsType } from '@riseworks/contracts/src/codegen/zod/rise/webhook_endpoints.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getWebhookEndpointById } from '../webhook_endpoints.js'
import { validateCompanyAccess } from './validateCompanyAccess.js'
import { validateTeamAccess } from './validateTeamAccess.js'

export const authorizeAndFetchWebhook = async (
  user_nanoid: UserNanoid,
  webhook_nanoid: WebhookEndpointNanoid,
): Promise<WebhookEndpointsType> => {
  // Validate that the webhook exists
  const webhook = await getWebhookEndpointById(webhook_nanoid)
  assert(webhook, 'Webhook endpoint not found', '404')

  // Validate if the user has access to the webhook's company
  await validateCompanyAccess(user_nanoid, webhook.company_nanoid)

  // If the webhook is associated with a team, validate team access
  if (webhook.team_nanoid) {
    await validateTeamAccess(user_nanoid, webhook.team_nanoid)
  }

  return webhook
}
