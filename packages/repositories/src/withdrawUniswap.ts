import type { WithdrawAccountNanoid } from '@riseworks/contracts/src/brands.js'
import type { RisePaymentHandlerForwarderSetTransferRulesRequest } from '@riseworks/contracts/src/forwardersTypedDataSchemas.js'
import type { withdrawFeeResponse } from '@riseworks/contracts/src/routes/dashboard/withdraw.js'
import {
  type BigintIsh,
  CurrencyAmount,
  Percent,
  Token,
  TradeType,
} from '@uniswap/sdk-core'
import { Pool, Route, type TickDataProvider, Trade } from '@uniswap/v3-sdk'
import { db as mainDB } from 'db/src/index.js'
import { ethers } from 'ethers'
import { goTry } from 'go-go-try'
import { getChainId } from 'utils/src/blockchain/helpers.js'
import { fixedOrPercent } from 'utils/src/blockchain/paymentHandler.js'
import { getChecksumAddress } from 'utils/src/common/address.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isProduction } from 'utils/src/common/env.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { run } from 'utils/src/common/run.js'
import type { z } from 'zod'
import { getTokenForNetwork } from './erc20_tokens.js'
import {
  type ValidNetworks,
  getContract,
  getProvider,
  getProviderURL,
  tokenMetadata,
} from './smartContracts.js'
import { getUniswapQuoteV4, getUniswapV4Config } from './uniswapApi.js'
import { getWithdrawAccountById } from './withdrawAccounts.js'

// Constants
const UNISWAP_V3_FACTORY_ADDRESS = '******************************************'
const UNISWAP_V3_FEE_TIERS = [3000, 10000, 500, 100]

interface IUniswapV3Pool {
  token0(): Promise<string>
  token1(): Promise<string>
  slot0(): Promise<{
    sqrtPriceX96: bigint
    tick: number | string
    observationIndex: number
    observationCardinality: number
    observationCardinalityNext: number
    feeProtocol: number
    unlocked: boolean
  }>
  liquidity(): Promise<bigint | string>
  getPool(tokenA: string, tokenB: string, fee: number): Promise<string>
}

// Uniswap V3 Pool ABI (minimal required functions)
const UNISWAP_V3_POOL_ABI = [
  'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  'function liquidity() external view returns (uint128)',
  'function token0() external view returns (address)',
  'function token1() external view returns (address)',
]

// Uniswap V3 Factory ABI
const UNISWAP_V3_FACTORY_ABI = [
  'function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool)',
]

/**
 * Tick data provider for the Uniswap SDK
 * This is a simplified version that provides necessary tick data
 */
class FullTickDataProvider implements TickDataProvider {
  private ticks: Record<
    number,
    {
      liquidityGross: bigint
      liquidityNet: bigint
      initialized: boolean
    }
  >

  constructor(currentTick: number) {
    // Create mock ticks
    this.ticks = {
      [currentTick]: {
        liquidityGross: BigInt(10000000),
        liquidityNet: BigInt(10000000),
        initialized: true,
      },
    }
  }

  getTick(tick: number): Promise<{ liquidityNet: BigintIsh }> {
    return Promise.resolve({
      liquidityNet: (this.ticks[tick]?.liquidityNet || BigInt(0)).toString(),
    })
  }

  nextInitializedTickWithinOneWord(
    tick: number,
    lte: boolean,
    tickSpacing: number,
  ): Promise<[number, boolean]> {
    const compressed = Math.floor(tick / tickSpacing) * tickSpacing
    const nextTick = lte ? compressed - tickSpacing : compressed + tickSpacing
    return Promise.resolve([nextTick, this.ticks[nextTick]?.initialized] as [
      number,
      boolean,
    ])
  }
}

// Helper function to get pool address from Uniswap factory
async function getPoolAddress(
  provider: ethers.Provider,
  token0Address: string,
  token1Address: string,
  feeTier: number,
): Promise<string> {
  // Sort tokens by address (Uniswap requirement)
  const [tokenA, tokenB] =
    token0Address.toLowerCase() < token1Address.toLowerCase()
      ? [token0Address, token1Address]
      : [token1Address, token0Address]

  console.log(
    `Getting pool for tokens: ${tokenA} and ${tokenB} with fee ${feeTier}`,
  )

  // Use factory contract to get pool
  const factoryContract = new ethers.Contract(
    UNISWAP_V3_FACTORY_ADDRESS,
    UNISWAP_V3_FACTORY_ABI,
    provider,
  ) as unknown as IUniswapV3Pool

  const poolAddress = await factoryContract.getPool(tokenA, tokenB, feeTier)

  if (poolAddress === ethers.ZeroAddress) {
    throw new Error(
      `No Uniswap V3 pool found for tokens ${tokenA} and ${tokenB} with fee ${feeTier}`,
    )
  }

  console.log(`Found pool at address: ${poolAddress}`)
  return poolAddress
}

// Result interface for getUniswapQuote
interface QuoteResult {
  poolAddress: string
  inputAmount: string
  outputAmount: string
  priceImpact: string
  minimumOutput: string
  executionPrice: string
  trade: Trade<Token, Token, TradeType> | null
  inputToken: Token
  outputToken: Token
  quoteRaw?: string
}

/**
 * Get a Uniswap quote for swapping tokens
 *
 * @param inputTokenAddress The address of the input token
 * @param outputTokenAddress The address of the output token
 * @param inputAmount The amount of input tokens to swap
 * @param feeTier The fee tier for the Uniswap pool
 * @returns Quote information with trade details
 */
async function getUniswapQuoteV3(
  inputTokenAddress: string,
  outputTokenAddress: string,
  inputAmountCents: number,
  feeTier: number,
): Promise<QuoteResult> {
  using _ = getContext()

  // Hardcoded configuration values
  const slippageTolerancePercent: number = 0.5

  const provider = await getProvider('arbitrum')
  const chainId = await getChainId('arbitrum')

  //tokenMetadata
  // Token configurations
  const inputTokenMetadata = await tokenMetadata(inputTokenAddress, 'arbitrum')
  const outputTokenMetadata = await tokenMetadata(
    outputTokenAddress,
    'arbitrum',
  )

  // Create token instances
  const inputToken = new Token(
    chainId,
    inputTokenMetadata.token,
    Number(inputTokenMetadata.decimals),
    inputTokenMetadata.symbol,
    inputTokenMetadata.name,
  )

  const outputToken = new Token(
    chainId,
    outputTokenMetadata.token,
    Number(outputTokenMetadata.decimals),
    outputTokenMetadata.symbol,
    outputTokenMetadata.name,
  )

  // Parse the input amount
  const parsedInputAmount = ethers.parseUnits(
    inputAmountCents.toString(),
    inputToken.decimals - 2,
  )

  _.logger.info(`Parsed input amount: ${parsedInputAmount.toString()}`)

  // Default to Uniswap V3 flow
  _.logger.info('Using Uniswap V3 flow')

  // Get the pool address
  const poolAddress = await getPoolAddress(
    provider,
    inputTokenAddress,
    outputTokenAddress,
    feeTier,
  ).catch((error) => {
    if (error instanceof Error) {
      _.logger.warn(`Error getting pool address from factory: ${error.message}`)
    } else {
      _.logger.warn(`Error getting pool address from factory: ${error}`)
    }
    // Fallback to known pool address
    _.logger.info('Using fallback pool address')
    return '******************************************'
  })

  _.logger.info(`Pool address: ${poolAddress}`)

  // Get the pool contract
  const poolContract = new ethers.Contract(
    poolAddress,
    UNISWAP_V3_POOL_ABI,
    provider,
  ) as unknown as IUniswapV3Pool

  // Get actual token ordering in the pool
  const [actualToken0Address, actualToken1Address] = await Promise.all([
    poolContract.token0(),
    poolContract.token1(),
  ])

  _.logger.info(`Pool token0: ${actualToken0Address}`)
  _.logger.info(`Pool token1: ${actualToken1Address}`)

  // Determine which token is which in the pool
  // For our use case, input is always USDC, output is always USDT
  const isUsdcToken0 =
    actualToken0Address.toLowerCase() === inputTokenAddress.toLowerCase()
  _.logger.info(`USDC is ${isUsdcToken0 ? 'token0' : 'token1'} in the pool`)

  // Get pool state
  const [slot0, liquidity] = await Promise.all([
    poolContract.slot0(),
    poolContract.liquidity(),
  ])

  const currentTick = Number.parseInt(slot0.tick.toString())
  _.logger.info(`Current tick: ${currentTick}`)
  _.logger.info(`Liquidity: ${liquidity.toString()}`)

  // Create token objects in the order they appear in the pool
  const token0 = isUsdcToken0 ? inputToken : outputToken
  const token1 = isUsdcToken0 ? outputToken : inputToken

  // Create SDK pool
  const sdkPool = new Pool(
    token0,
    token1,
    feeTier,
    slot0.sqrtPriceX96.toString(),
    liquidity.toString(),
    currentTick,
    new FullTickDataProvider(currentTick),
  )

  // Build trade route
  // Input token is USDC, output token is USDT

  const route = new Route([sdkPool], inputToken, outputToken)
  const currencyAmount = CurrencyAmount.fromRawAmount(
    inputToken,
    parsedInputAmount.toString(),
  )

  // Create trade with price impact calculation
  const trade = await Trade.fromRoute(
    route,
    currencyAmount,
    TradeType.EXACT_INPUT,
  )

  // Calculate slippage tolerance and minimum output
  const slippageTolerance = new Percent(
    Math.floor(slippageTolerancePercent * 100),
    10_000,
  )

  const minimumAmountOut = trade.minimumAmountOut(slippageTolerance)

  return {
    poolAddress,
    inputAmount: `${trade.inputAmount.toExact()} ${inputToken.symbol}`,
    outputAmount: `${trade.outputAmount.toExact()} ${outputToken.symbol}`,
    priceImpact: `${trade.priceImpact.toSignificant(3)}%`,
    minimumOutput: `${ethers.formatUnits(
      minimumAmountOut.quotient.toString(),
      outputToken.decimals,
    )} ${outputToken.symbol}`,
    executionPrice: trade.executionPrice.toSignificant(6),
    trade,
    inputToken,
    outputToken,
  }
}

/**
 * Execute a Uniswap withdraw with swapping
 *
 * @param inputTokenAddress The address of the input token
 * @param outputTokenAddress The address of the output token
 * @param withdrawAmount The amount to withdraw (as a string in human-readable format)
 * @param feeTier The fee tier for the Uniswap pool (default 3000)
 * @returns RiseConfig object for transaction execution
 */
export async function getUniswapV3Config(
  inputTokenAddress: string,
  outputTokenAddress: string,
  withdrawAmountCents: number,
  receiverAddress: string,
  offChainReference: string,
  feeTier = 3000,
) {
  using _ = getContext()

  // Constants
  const SWAP_ROUTER = '******************************************'
  const slippageTolerancePercent = 0.5

  // Get token metadata for input token to get decimals
  const inputTokenMetadata = await tokenMetadata(inputTokenAddress, 'arbitrum')

  // Parse withdraw amount
  const parsedWithdrawAmount = ethers.parseUnits(
    withdrawAmountCents.toString(),
    Number(inputTokenMetadata.decimals) - 2,
  )

  _.logger.info(
    `Withdraw amount: ${withdrawAmountCents} ${inputTokenMetadata.symbol}`,
  )

  // Get a quote for the swap using the updated function
  const quoteResult = await getUniswapQuoteV3(
    inputTokenAddress,
    outputTokenAddress,
    withdrawAmountCents,
    feeTier,
  )

  // Extract info from the quote result
  const trade = quoteResult.trade
  const inputToken = quoteResult.inputToken
  const outputToken = quoteResult.outputToken

  _.logger.info(`Quote details:
    Input: ${quoteResult.inputAmount}
    Expected output: ${quoteResult.outputAmount}
    Price impact: ${quoteResult.priceImpact}
    Minimum output: ${quoteResult.minimumOutput}`)

  // Calculate slippage tolerance
  const slippageTolerance = new Percent(
    Math.floor(slippageTolerancePercent * 100),
    10_000,
  )

  // Get minimum amount out based on slippage
  assert(trade, 'Trade not found')
  const minimumAmountOut = trade.minimumAmountOut(slippageTolerance)
  const finalAmountOutMin = BigInt(minimumAmountOut.quotient.toString())

  // Encode the swap parameters for the router
  const swapInterface = new ethers.Interface([
    'function exactInputSingle((address tokenIn,address tokenOut,uint24 fee,address recipient,uint256 amountIn,uint256 amountOutMinimum,uint160 sqrtPriceLimitX96))',
  ])

  const rampWithdraw = await getContract('RiseRampWithdrawUniSwap', 'arbitrum')

  const swapParams = {
    tokenIn: inputToken.address,
    tokenOut: outputToken.address,
    fee: feeTier,
    recipient: await rampWithdraw.getAddress(),
    amountIn: parsedWithdrawAmount.toString(),
    amountOutMinimum: 0,
    sqrtPriceLimitX96: 0,
  }

  // Uniswap Router call
  const swapCalldata = swapInterface.encodeFunctionData('exactInputSingle', [
    swapParams,
  ])

  // Encode swap parameters
  const swapParamsData = await rampWithdraw.encodeSwapParams(
    SWAP_ROUTER,
    swapCalldata,
    finalAmountOutMin.toString(),
    Math.floor(Date.now() / 1000) + 1000, // 10 minutes
    trade.outputAmount.quotient.toString(),
    500, // 5% tolerance
    receiverAddress,
    outputToken.address,
    0,
  )

  _.logger.info('Used manual encoding for V3 swap params')

  // Create RiseConfig object
  const riseConfig = {
    amount: '10000',
    transferType: '0',
    fixedOrPercent: fixedOrPercent.percentOfCurrentBalance,
    ramp: await rampWithdraw.getAddress(),
    source: inputToken.address,
    destination: outputToken.address,
    offChainReference,
    data: swapParamsData,
  } satisfies RisePaymentHandlerForwarderSetTransferRulesRequest['data'][0]['configs'][0]

  return riseConfig
}

export const getUniswapQuote = async (
  nanoid: WithdrawAccountNanoid,
  amountCents: number,
  db = mainDB,
): Promise<z.infer<typeof withdrawFeeResponse.shape.fees>> => {
  using _ = getContext()

  const withdrawAccount = await getWithdrawAccountById(nanoid, db)
  assert(withdrawAccount, `Withdraw account not found for ${nanoid}`)
  assert(
    withdrawAccount.wallet_address,
    'Withdraw account is missing payment handler',
    '404',
  )
  assert(withdrawAccount.network, 'Withdraw account is missing network', '404')
  assert(withdrawAccount.ramp === 'token_swap', 'Invalid ramp')
  assert(withdrawAccount.network === 'arbitrum', 'Invalid network')

  const network = 'arbitrum'
  const tokenAddress = await (await getContract('USDC', network)).getAddress()

  assert(
    withdrawAccount.wallet_address,
    'Wallet address not found for token swap',
    '404',
  )
  assert(withdrawAccount.token, 'Token not found for token swap', '404')
  assert(withdrawAccount.network, 'Network not found for token swap', '404')
  const swapToken = await getTokenForNetwork(
    withdrawAccount.token,
    withdrawAccount.network,
    db,
  )
  assert(swapToken, 'Token not found for swap')

  // Params
  const inputTokenAddress = tokenAddress
  const outputTokenAddress = swapToken.address
  const inputAmountCents = amountCents

  if (isProduction) {
    const providerUrl = await getProviderURL('arbitrum')
    const rampAddress = await (
      await getContract('RiseRampWithdrawUniSwap', 'arbitrum')
    ).getAddress()
    const result = await getUniswapQuoteV4({
      inputTokenAddress,
      outputTokenAddress,
      inputAmountCents,
      providerUrl,
      rampAddress,
    })

    return [
      {
        type: 'conversion',
        rate: Number.parseFloat(result.executionPrice),
        source_currency: 'USD',
        destination_currency: swapToken.symbol,
      },
    ]
  }

  for (const feeTier of UNISWAP_V3_FEE_TIERS) {
    const result = await getUniswapQuoteV3(
      inputTokenAddress,
      outputTokenAddress,
      inputAmountCents,
      feeTier,
    ).catch((_) => null)
    if (result) {
      return [
        {
          type: 'conversion',
          rate: Number.parseFloat(result.executionPrice),
          source_currency: 'USD',
          destination_currency: swapToken.symbol,
        },
      ]
    }
  }

  assert(false, 'No quote found')
}

export const getUniswapConfig = async (
  nanoid: WithdrawAccountNanoid,
  amountCents: number,
  network: ValidNetworks = 'arbitrum',
  db = mainDB,
): Promise<RisePaymentHandlerForwarderSetTransferRulesRequest['data'][0]> => {
  using _ = getContext()
  const withdrawAccount = await getWithdrawAccountById(nanoid, db)
  assert(withdrawAccount, `Withdraw account not found for ${nanoid}`)
  assert(
    withdrawAccount.wallet_address,
    'Withdraw account is missing payment handler',
    '404',
  )
  assert(withdrawAccount.network, 'Withdraw account is missing network', '404')
  assert(withdrawAccount.ramp === 'token_swap', 'Invalid ramp')

  const tokenAddress = await (
    await getContract('RiseUSD', network)
  ).getAddress()

  const ramp = await getContract('RiseRampWithdrawUniSwap', network)
  const rampAddress = await ramp.getAddress()

  const mappedTokenAddress = await ramp.getMapping(tokenAddress)
  assert(mappedTokenAddress !== ethers.ZeroAddress, {
    message: 'Token not mapped to a token address',
    code: '404',
  })

  // Make sure mapped token is a token
  const [error, metadata] = await goTry(
    tokenMetadata(mappedTokenAddress, network),
  )
  assert(!error, 'Token metadata not found', '404')
  assert(metadata, 'Token metadata not found', '404')

  const walletAddress = getChecksumAddress(withdrawAccount.wallet_address)
  assert(walletAddress, 'Wallet address not found for token swap', '404')
  assert(withdrawAccount.token, 'Token not found for token swap', '404')
  assert(withdrawAccount.network, 'Network not found for token swap', '404')
  const swapToken = await getTokenForNetwork(
    withdrawAccount.token,
    withdrawAccount.network,
    db,
  )
  assert(swapToken, 'Token not found for swap')

  const config = await run(async () => {
    if (isProduction) {
      const providerUrl = await getProviderURL(network)

      return await getUniswapV4Config({
        inputTokenAddress: mappedTokenAddress,
        outputTokenAddress: swapToken.address,
        inputAmountCents: amountCents,
        receiverAddress: walletAddress,
        offChainReference: withdrawAccount.external_reference,
        rampAddress,
        providerUrl,
      })
    }

    for (const feeTier of UNISWAP_V3_FEE_TIERS) {
      const config = await getUniswapV3Config(
        mappedTokenAddress,
        swapToken.address,
        amountCents,
        walletAddress,
        withdrawAccount.external_reference,
        feeTier,
      ).catch((_) => null)
      if (config) return config
    }
    assert(false, 'No config found')
  })

  return {
    token: tokenAddress,
    configs: [config],
  }
}

export const walletGetUniswapWithdrawAccountConfigurations = (
  amountCents: number,
) => {
  return (
    nanoid: WithdrawAccountNanoid,
    network: ValidNetworks,
    db?: typeof mainDB,
  ) => getUniswapConfig(nanoid, amountCents, network, db)
}
