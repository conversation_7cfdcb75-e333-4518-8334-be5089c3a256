import type { DB as DBAll } from '@riseworks/contracts/src/codegen/db/all.js'
import type { Kysely } from 'kysely'
import { getContext } from 'utils/src/common/requestContext.js'

function captureStackTrace(error: unknown): string {
  if (error instanceof Error) {
    return `${error.message}\n${error.stack}`
  }
  return JSON.stringify(error)
}

export const logMigrationFailureFromStep = async ({
  db,
  entityType,
  entityV1Id,
  step,
  error,
}: {
  db: Kysely<DBAll>
  entityType: 'company' | 'user' | 'team'
  entityV1Id: number
  step: string
  error: unknown
}) => {
  const {
    logger,
    application,
    route,
    tracestate,
    inngestRunId,
    inngestEventName,
  } = getContext()

  const stackTrace = captureStackTrace(error)

  logger.error({
    msg: `Migration failed at step: ${step}`,
    error: stackTrace,
    step,
    application,
    route,
    tracestate,
    inngestRunId,
    inngestEventName,
  })

  await db
    .updateTable('rise.v1_v2_migration_entity')
    .set({
      failure_reason: stackTrace,
      failure_step: step,
      migration_status: 'failed',
    })
    .where('entity_v1_id', '=', entityV1Id)
    .where('entity_type', '=', entityType)
    .execute()
}

export const runMigrationStep = async <T>(
  db: Kysely<DBAll>,
  step: string,
  entityType: 'company' | 'user' | 'team',
  entityV1Id: number,
  fn: () => Promise<T>,
): Promise<T> => {
  try {
    return await fn()
  } catch (error) {
    await logMigrationFailureFromStep({
      db,
      entityType,
      entityV1Id,
      step,
      error,
    })
    throw error
  }
}
