import { getContext } from 'utils/src/common/requestContext.js'
import { type ValidNetworks, getContract } from './smartContracts.js'

const getEIP712Domain = async (network: ValidNetworks) => {
  using _ = getContext()
  const risePayToken = await getContract('RiseUSD', network)

  const domain = await risePayToken.eip712Domain()
  return {
    name: domain[1],
    version: domain[2],
    chainId: Number.parseInt(`${domain[3]}`),
    verifyingContract: domain[4],
  }
}

export const PermitType = [
  {
    name: 'owner',
    type: 'address',
  },
  {
    name: 'spender',
    type: 'address',
  },
  {
    name: 'value',
    type: 'uint256',
  },
  {
    name: 'nonce',
    type: 'uint256',
  },
  {
    name: 'deadline',
    type: 'uint256',
  },
]

export type PermitTypedDataRequest = {
  owner: string
  spender: string
  value: string
  deadline: number
  nonce: string
}

export const permitTypedData = async (
  {
    owner,
    spender,
    value,
    deadline,
  }: {
    owner: string
    spender: string
    value: string
    deadline: number
  },
  network: ValidNetworks,
) => {
  using _ = getContext()

  const risePayToken = await getContract('RiseUSD', network)
  const nonce = await risePayToken.nonces(owner)

  return {
    domain: await getEIP712Domain(network),
    types: { Permit: PermitType },
    primary_type: 'Permit',
    typed_data: {
      owner,
      spender,
      value,
      nonce: nonce.toString(),
      deadline,
    },
  } as const
}

export const checkNonce = async (
  {
    owner,
    nonce,
  }: {
    owner: string
    nonce: string | bigint
  },
  network: ValidNetworks,
) => {
  using _ = getContext()

  const risePayToken = await getContract('RiseUSD', network)
  const currentNonce = await risePayToken.nonces(owner)

  return currentNonce.toString() === nonce.toString()
}
