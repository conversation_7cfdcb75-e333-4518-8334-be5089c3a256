import { getContract } from './smartContracts.js'
import globals from 'utils/src/blockchain/riseGlobals.js'

export const isRisePaymentHandler = async (address: string) => {
  const riseAccess = await getContract('RiseAccess', 'arbitrum')
  return await riseAccess.hasRole(globals.RISE_IS_PAYMENT_HANDLER, address)
}

export const isRiseAccount = async (address: string) => {
  const riseAccess = await getContract('RiseAccess', 'arbitrum')
  return await riseAccess.hasRole(globals.RISE_IS_ACCOUNT, address)
}
