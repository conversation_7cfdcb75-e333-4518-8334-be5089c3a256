import { ethers } from 'ethers'
import { getContext } from 'utils/src/common/requestContext.js'
import { getTokenPrice } from './coingecko.js'
import { getContract } from './smartContracts.js'

/**
 * Calculates the transaction price in USDC
 * @param gasLimit Gas limit of the transaction
 * @param gasPrice Gas price in Gwei
 * @returns Promise with the price in USDC
 */
export const calculateTransactionPriceInUSDC = async (
  gasLimit: bigint,
  gasPrice: bigint,
): Promise<bigint> => {
  const ctx = getContext()

  // Fixed ETH price in USD
  const ethPrice = await getTokenPrice('ethereum', 'usd')

  console.log('ethPrice', ethPrice)
  console.log('gasPrice', gasPrice.toString())
  console.log('gasLimit', gasLimit.toString())

  // Calcular custo em ETH (convertendo de wei para ETH)
  const costInWei = gasLimit * gasPrice
  const costInETHStr = ethers.formatUnits(costInWei, 18)
  const costInUSD = Number.parseFloat(costInETHStr) * ethPrice

  // Convert to USDC (6 decimals) maintaining precision
  const costInUSDC = BigInt(Math.round(costInUSD * 1000000))

  ctx.logger.info(`Transaction cost: ${costInETHStr} ETH ($${ethPrice})`)
  ctx.logger.info(
    `Transaction cost in USDC: ${ethers.formatUnits(costInUSDC, 6)} USDC`,
  )

  return costInUSDC
}

/**
 * Configures the fee on Ramp for a specific domain and token
 * @param signer Signer to sign the transaction
 * @param destinationDomain Destination domain ID
 * @param token Token address
 * @param fee Fee in USDC (6 decimals)
 * @returns Promise with the transaction result
 */
export const setRampFee = async (
  destinationDomain: number,
  token: string,
  fee: bigint,
): Promise<ethers.ContractTransactionResponse> => {
  const ctx = getContext()

  const provider = new ethers.JsonRpcProvider(
    'https://arbitrum-sepolia.infura.io/v3/********************************',
  )
  const signer = new ethers.Wallet(
    '0x15c14695ff8e83e0877040ae4f4bb8f459bd418019a698cef87794882d0b4b9b',
    provider,
  )

  const contract = await getContract('RiseRampWithdrawCCTP', 'arbitrum')
  const contractWithSigner = contract.connect(signer)

  // Check if contract is deployed
  const contractAddress = await contract.getAddress()
  const code = await provider.getCode(contractAddress)
  if (code === '0x') {
    throw new Error(
      `Contrato não está implantado no endereço ${contractAddress}`,
    )
  }

  ctx.logger.info('Setting fee for RiseRampWithdrawCCTP contract on Arbitrum')
  ctx.logger.info(`Contract address: ${contractAddress}`)
  ctx.logger.info(`Fee: ${fee.toString()} USDC`)
  ctx.logger.info(
    `Setting fee for domain ${destinationDomain} and token ${token} to ${ethers.formatUnits(fee, 6)} USDC`,
  )

  try {
    // Check if contract has setFee function
    const abi = await contract.interface.format()
    ctx.logger.info('Contract ABI:', abi)

    // Check if signer has permission
    const signerAddress = await signer.getAddress()
    ctx.logger.info(`Signer address: ${signerAddress}`)

    // Check if contract has riseAccess function
    try {
      const riseAccess = await contract.riseAccess()
      ctx.logger.info(`RiseAccess contract: ${riseAccess}`)
    } catch (error) {
      ctx.logger.error('Error getting riseAccess:', error)
    }

    // Try to get current fee to verify if function exists
    try {
      const currentFee = await contract.getFee(destinationDomain, token)
      ctx.logger.info(`Current fee: ${currentFee.toString()}`)
    } catch (error) {
      ctx.logger.error('Error getting current fee:', error)
    }

    // Send transaction directly using contract
    const tx = await contractWithSigner.setFee(destinationDomain, token, fee, {
      gasLimit: BigInt(1000000), // Gas limit fixed high to ensure
    })

    ctx.logger.info('Transaction sent:', tx.hash)

    // Wait for confirmation
    const receipt = await tx.wait()
    if (!receipt) {
      throw new Error('Transaction receipt is null')
    }

    if (receipt.status === 0) {
      throw new Error('Transaction failed')
    }

    ctx.logger.info(`Fee set successfully! Transaction hash: ${receipt.hash}`)

    return tx as unknown as ethers.ContractTransactionResponse
  } catch (error) {
    ctx.logger.error('Error setting fee:', error)
    throw error
  }
}

/**
 * Calculates and configures the fee on Ramp for a specific domain and token
 * @param signer Signer to sign the transaction
 * @param destinationDomain Destination domain ID
 * @param token Token address
 * @param gasLimit Gas limit of the transaction
 * @param gasPrice Gas price in Gwei
 * @returns Promise with the transaction result
 */
export const calculateAndSetRampFee = async (
  destinationDomain: number,
  token: string,
  gasLimit: bigint,
  gasPrice: bigint,
): Promise<ethers.ContractTransactionResponse> => {
  const feeInUSDC = await calculateTransactionPriceInUSDC(gasLimit, gasPrice)

  console.log('feeInUSDC', feeInUSDC.toString())

  const tx = await setRampFee(destinationDomain, token, feeInUSDC)

  return tx
}

export const CCTP_DOMAINS = {
  arbitrum_sepolia: 3,
  base_sepolia: 6,
  ethereum: 0,
  avalanche: 1,
  optimism: 2,
} as const
