import {
  type BitcoinOutput,
  type ValidatedOwner,
  type ValueBigInt,
  bitcoinOutputTransformer,
} from '@riseworks/contracts/src/bitcoin.js'
import {
  ChainRegexMap,
  type ChannelId,
  type InputAsset,
  type InputChain,
  type InputChainAndAsset,
  InputChainsAndAssetsWithMinimumSwapAmounts,
  type OutputAsset,
  type OutputChain,
  type OutputChainAndAsset,
  type SwapMetadata,
  type TransformedQuote,
  minPriceX128Transformer,
  pendingSwapStatuses,
  quoteTransformer,
  swapChannelTransformer,
  swapMetadataSchema,
  swapTransformer,
} from '@riseworks/contracts/src/chainflip.js'
import type { SelectableChainflipSwaps as SelectableSwap } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type { EvmAddress } from '@riseworks/contracts/src/formats.js'
import { type DBTransaction, db as mainDB } from 'db/src/index.js'
import {
  type BitcoinAccount,
  validatedBitcoinOwnerTransformer,
} from 'utils/src/blockchain/bitcoin/account.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isProduction } from 'utils/src/common/env.js'
import { type HTTPUrl, createHTTPClient } from 'utils/src/common/httpClient.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { broadcastSweepTransaction, createSweepTransaction } from './bitcoin.js'
import {
  getBitcoinEntityDepositAccountIds,
  getNanoidAndPaymentHandlerAddressFromAccountId,
} from './entityDepositAccounts.js'

const getPendingSwaps = async (db = mainDB): Promise<SelectableSwap[]> => {
  using _ = getContext()
  return await db
    .selectFrom('rise.chainflip_swaps')
    .selectAll()
    .where('status', 'in', pendingSwapStatuses)
    .execute()
}

const processNewSwapChannelOutputs = async (
  swapChannelOutputs: BitcoinOutput[],
  db = mainDB,
): Promise<{
  expectedUpdatedSwaps: SelectableSwap[]
  unexpectedUpdatedSwaps: SelectableSwap[]
}> => {
  using _ = getContext()
  const bitcoinOwnerIds = await getBitcoinEntityDepositAccountIds()
  assert(bitcoinOwnerIds.length, 'No bitcoin owner ids found')
  const pendingSwaps = await getPendingSwaps()
  const validatedOwnerIds = Array.from(
    new Set(
      pendingSwaps
        .map((swap) =>
          bitcoinOwnerIds.find((id) => id.includes(swap.refund_address)),
        )
        .filter((id) => id !== undefined),
    ),
  )
  const validatedOwners = await Promise.all(
    validatedOwnerIds.map((id) =>
      validatedBitcoinOwnerTransformer.parseAsync(id),
    ),
  )
  const updatedSwaps = await db.transaction().execute(async (trx) => {
    return await Promise.all(
      pendingSwaps.map((swap) => {
        const validatedOwner = validatedOwners.find(
          (owner) => owner.changeAddress === swap.refund_address,
        )
        assert(
          validatedOwner,
          `Validated owner not found for ${swap.channel_id}`,
        )
        return validateAndUpsertSwap(swap.channel_id, validatedOwner, trx)
      }),
    )
  })
  const channelAddresses = Array.from(
    new Set(
      swapChannelOutputs.map(
        (outputString) => bitcoinOutputTransformer.parse(outputString).address,
      ),
    ),
  )
  const expectedUpdatedSwaps = updatedSwaps.filter((swap) =>
    channelAddresses.includes(swap.output_address),
  )
  const unexpectedUpdatedSwaps = updatedSwaps.filter(
    (swap) => !channelAddresses.includes(swap.output_address),
  )
  return {
    expectedUpdatedSwaps,
    unexpectedUpdatedSwaps,
  }
}

const getPendingSwapsByOutputAddress = async (
  outputAddress: EvmAddress,
  trx: DBTransaction,
): Promise<SelectableSwap[]> => {
  return await trx
    .selectFrom('rise.chainflip_swaps')
    .selectAll()
    .where('status', 'in', pendingSwapStatuses)
    .where('output_address', '=', outputAddress)
    .execute()
}

const getSwapFromChannelId = async (
  channelId: ChannelId,
  trx: DBTransaction,
): Promise<SelectableSwap | undefined> => {
  return await trx
    .selectFrom('rise.chainflip_swaps')
    .selectAll()
    .where('channel_id', '=', channelId)
    .executeTakeFirst()
}

const chainflipApiPrefixUrl = (
  isProduction
    ? 'https://chainflip-swap.chainflip.io/'
    : 'https://chainflip-swap-perseverance.chainflip.io/'
) as HTTPUrl

const chainflipApi = createHTTPClient({
  prefixUrl: chainflipApiPrefixUrl,
})

const validateAndUpsertSwap = async (
  channelId: ChannelId,
  validatedOwner: ValidatedOwner,
  trx: DBTransaction,
  newSwapMetadata?: SwapMetadata,
): Promise<SelectableSwap> => {
  using _ = getContext()
  let swapMetadata: SwapMetadata | undefined = newSwapMetadata
  let existingSwap: SelectableSwap | undefined
  if (!newSwapMetadata) {
    existingSwap = await getSwapFromChannelId(channelId, trx)
    assert(existingSwap, `Swap not found for ${channelId}`)
    const [entityNanoid, outputAddress] =
      await getNanoidAndPaymentHandlerAddressFromAccountId(
        validatedOwner.ownerId,
      )
    swapMetadata = swapMetadataSchema.parse({
      entityNanoid,
      inputNetworkFee: existingSwap.input_network_fee,
      inputSentAt: existingSwap.input_sent_at,
      inputTxHash: existingSwap.input_txid,
      inputValue: existingSwap.input_value,
      inputAddresses: existingSwap.input_addresses,
      outputAddress,
      previousSwapIds: existingSwap.previous_swap_ids,
    })
  }
  assert(swapMetadata, `Swap metadata not found for ${channelId}`)
  const swapResponse = await chainflipApi.get(`v2/swaps/${channelId}`).json()
  const insertableSwap = swapTransformer.parse({
    ...swapMetadata,
    validatedOwner,
    swap: swapResponse,
  })
  assert(insertableSwap, `Could not validate swap for ${channelId}`)
  const upsertableSwap = existingSwap
    ? { ...existingSwap, ...insertableSwap }
    : insertableSwap
  await trx
    .insertInto('rise.chainflip_swaps')
    .values(upsertableSwap)
    .onDuplicateKeyUpdate(upsertableSwap)
    .execute()
  const updatedSwap = await getSwapFromChannelId(channelId, trx)
  assert(updatedSwap, `Could not get updated swap for ${channelId}`)
  return updatedSwap
}

const validateAndUpsertPreviousSwaps = async (
  pendingSwaps: SelectableSwap[],
  validatedOwner: ValidatedOwner,
  trx: DBTransaction,
) => {
  using _ = getContext()
  return await Promise.all(
    pendingSwaps.map((swap) =>
      validateAndUpsertSwap(swap.channel_id, validatedOwner, trx),
    ),
  )
}

const openSwapChannel = async (
  outputChainAndAsset: OutputChainAndAsset,
  outputAddress: string,
  inputChainAndAsset: InputChainAndAsset,
  refundAddress: string,
  inputValue: ValueBigInt,
  quote: TransformedQuote,
  maxSlippageToleranceBps = 150,
) => {
  using ctx = getContext()
  const validatedOutputAddress =
    ChainRegexMap[outputChainAndAsset.chain].parse(outputAddress)
  const validatedRefundAddress =
    ChainRegexMap[inputChainAndAsset.chain].parse(refundAddress)
  const minSwapAmount =
    InputChainsAndAssetsWithMinimumSwapAmounts[inputChainAndAsset.chain].find(
      ([asset]) => asset === inputChainAndAsset.asset,
    )?.[1] ?? 0n
  if (isProduction) {
    assert(
      inputValue >= minSwapAmount,
      `Input value must be greater than or equal to ${minSwapAmount}`,
    )
  }
  if (quote.recommendedSlippageToleranceBps >= maxSlippageToleranceBps) {
    ctx.logger.warn(
      `Recommended slippage tolerance is ${quote.recommendedSlippageToleranceBps}bps, which is greater than the max allowed ${maxSlippageToleranceBps}bps`,
    )
  }
  const swapChannelResponse = await chainflipApi
    .post('trpc/openSwapDepositChannel', {
      json: {
        json: {
          srcAsset: inputChainAndAsset.asset,
          srcChain: inputChainAndAsset.chain,
          destAsset: outputChainAndAsset.asset,
          destChain: outputChainAndAsset.chain,
          destAddress: validatedOutputAddress,
          fillOrKillParams: {
            refundAddress: validatedRefundAddress,
            retryDurationBlocks: 50, // 6 seconds * 50 = 5 minutes
            minPriceX128: minPriceX128Transformer.parse({
              inputAsset: inputChainAndAsset.asset,
              outputAsset: outputChainAndAsset.asset,
              estimatedPrice: quote.estimatedPrice,
              slippageToleranceBps: Math.min(
                quote.recommendedSlippageToleranceBps,
                maxSlippageToleranceBps,
              ),
            }),
          },
          amount: inputValue.toString(),
          quote: {
            egressAmount: quote.estimatedOutputValue.toString(),
            estimatedPrice: quote.estimatedPrice.toString(),
          },
          maxBoostFeeBps: quote.maxBoostFeeBps,
        },
      },
    })
    .json()
  return swapChannelTransformer.parse(swapChannelResponse)
}

const getQuote = async (
  inputChainAndAsset: InputChainAndAsset,
  inputValue: ValueBigInt,
  outputChainAndAsset: OutputChainAndAsset,
): Promise<TransformedQuote> => {
  using _ = getContext()
  const quoteResponse = await chainflipApi
    .get('v2/quote', {
      searchParams: {
        amount: inputValue.toString(),
        srcChain: inputChainAndAsset.chain,
        srcAsset: inputChainAndAsset.asset,
        destChain: outputChainAndAsset.chain,
        destAsset: outputChainAndAsset.asset,
      },
    })
    .json()
  const parsedQuotes = quoteTransformer.parse(quoteResponse)
  const sortedQuotes = parsedQuotes.sort(
    (a, b) => b.estimatedPrice - a.estimatedPrice,
  )
  const sortedBoostQuotes = sortedQuotes.filter((quote) => quote.maxBoostFeeBps)
  if (sortedBoostQuotes?.[0]) {
    return sortedBoostQuotes[0]
  }
  assert(sortedQuotes?.[0], 'No quotes found')
  return sortedQuotes[0]
}

const createBitcoinUsdcSwap = async (account: BitcoinAccount, db = mainDB) => {
  using ctx = getContext()
  const upsertSwapParams = await db.transaction().execute(async (trx) => {
    assert(account.validatedOwner, 'Account is not initialized')
    const [entityNanoid, usdcOutputAddress] =
      await getNanoidAndPaymentHandlerAddressFromAccountId(
        account.validatedOwner.ownerId,
      )
    const pendingSwaps = await getPendingSwapsByOutputAddress(
      usdcOutputAddress,
      trx,
    )
    if (pendingSwaps.length) {
      ctx.logger.warn(
        `Other swap(s) are already in progress: ${pendingSwaps.map((swap) => swap.channel_id).join(', ')}`,
      )
    }
    const sweepTransaction = await createSweepTransaction(account)
    const inputValue = sweepTransaction.maxOutputValue
    assert(inputValue, 'Could not get swap input value')
    const includesChange = sweepTransaction
      .uniqueInputAddresses()
      .includes(account.validatedOwner.changeAddress)
    const newRefundedSwaps = (
      await validateAndUpsertPreviousSwaps(
        pendingSwaps,
        account.validatedOwner,
        trx,
      )
    ).filter((swap) => swap?.refund_txid)
    if (includesChange && !newRefundedSwaps.length) {
      ctx.logger.warn(
        `Change address ${account.validatedOwner.changeAddress} found in inputs for ${entityNanoid} swap, but could not find any newly refunded swaps`,
      )
    }
    if (newRefundedSwaps.length && !includesChange) {
      ctx.logger.warn(
        `Newly refunded swaps found for ${entityNanoid} swap, but change address ${account.validatedOwner.changeAddress} not found in inputs`,
      )
      // Consider replacing with assert?
    }
    const includesExternalDeposit = sweepTransaction
      .uniqueInputAddresses()
      .includes(account.validatedOwner.depositAddress)
    if (!includesExternalDeposit) {
      assert(
        newRefundedSwaps.some((swap) => !swap.previous_swap_ids.length),
        'All inputs for this swap are from previously refunded swaps; cancelling to prevent infinite loop.',
      )
    }
    const inputChainAndAsset = {
      chain: 'Bitcoin' as InputChain,
      asset: 'BTC' as InputAsset,
    }
    const outputChainAndAsset = {
      chain: 'Arbitrum' as OutputChain,
      asset: 'USDC' as OutputAsset,
    }
    const quote = await getQuote(
      inputChainAndAsset,
      inputValue,
      outputChainAndAsset,
    )
    const swapChannel = await openSwapChannel(
      outputChainAndAsset,
      usdcOutputAddress,
      inputChainAndAsset,
      account.validatedOwner.changeAddress,
      inputValue,
      quote,
      // TODO: Get max slippage tolerance from env
    )
    const previousSwapIds = newRefundedSwaps.map((swap) => swap.channel_id)
    sweepTransaction.addSweepOutput(swapChannel.channelAddress, inputValue)
    sweepTransaction.signTx()
    const inputTxHash = await broadcastSweepTransaction(sweepTransaction)
    return {
      channelId: swapChannel.channelId,
      validatedOwner: account.validatedOwner,
      newSwapMetadata: swapMetadataSchema.parse({
        entityNanoid,
        inputAddresses: sweepTransaction.uniqueInputAddresses(),
        inputNetworkFee: sweepTransaction.fee(),
        inputSentAt: new Date(),
        inputTxHash,
        inputValue,
        outputAddress: usdcOutputAddress,
        previousSwapIds,
      }),
    }
  })
  await new Promise((resolve) => setTimeout(resolve, 30_000)) // Wait for 30 seconds to allow the transaction to propagate
  return await db.transaction().execute(async (trx) => {
    return await validateAndUpsertSwap(
      upsertSwapParams.channelId,
      upsertSwapParams.validatedOwner,
      trx,
      upsertSwapParams.newSwapMetadata,
    )
  })
}

export { createBitcoinUsdcSwap, getPendingSwaps, processNewSwapChannelOutputs }
