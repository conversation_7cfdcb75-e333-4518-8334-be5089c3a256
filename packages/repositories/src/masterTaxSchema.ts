import type { z } from 'zod'
import type { payroll_header } from '../../contracts/src/codegen/zod/mastertax/payroll_header.js'

// Master Tax Enums based on PTS file specification
// https://my.mastertax.com/wp-content/uploads/2025/03/pts_spec_v29.pdf

// Enums for Master Tax Fields that require specific values,
// with mappings from DB to file format
// Enum for Payroll Frequency
const payroll_frequency = {
  weekly: 0, // W - Weekly
  bi_weekly: 1, // B - Bi-Weekly
  semi_monthly: 2, // S - Semi-Monthly
  monthly: 3, // M - Monthly
} as const

export type PayrollFrequencyKey = keyof typeof payroll_frequency

export const payrollFrequencyKeyToCodeMap: Record<PayrollFrequencyKey, string> =
  {
    weekly: 'W',
    bi_weekly: 'B',
    semi_monthly: 'S',
    monthly: 'M',
  }
// Enum for Employee Count Sign
const sign = {
  positive: 0, // +
  negative: 1, // -
} as const

export type SignKey = keyof typeof sign

export const signKeyToSymbolMap: Record<SignKey, string> = {
  positive: '+',
  negative: '-',
}
// Enum for Country Codes
const country_code = {
  US: 0, // United States
  CA: 1, // Canada
  MX: 2, // Mexico
} as const

// Type representing the keys
export type CountryCodeKey = keyof typeof country_code

// Map from string key
export const countryCodeKeyToNameMap: Record<CountryCodeKey, string> = {
  US: 'US',
  CA: 'CA',
  MX: 'MX',
}

// Enum for Company Status
const company_status = {
  active: 0, // A
  inactive: 1, // I
} as const

// Type representing the keys ("active" | "inactive")
export type CompanyStatusKey = keyof typeof company_status

// Map from string key (e.g., from DB) to status code
export const companyStatusKeyToCodeMap: Record<CompanyStatusKey, string> = {
  active: 'A',
  inactive: 'I',
}

// Enum for Service Level
const service_level = {
  full_service: 0, // F
  return_only: 1, // R
  balance_only: 2, // B
} as const

// Type representing the keys ("full_service" | "return_only" | "balance_only")
export type ServiceLevelKey = keyof typeof service_level

// Map from string key (e.g., from DB) to code
export const serviceLevelKeyToCodeMap: Record<ServiceLevelKey, string> = {
  full_service: 'F',
  return_only: 'R',
  balance_only: 'B',
}

// Enum for FEIN Type
const fein_type = {
  applied_for: 0, // Y
  registered: 1, // N
  common_pay_parent: 2, // P
  common_pay_child: 3, // C
} as const

// Type representing the keys ("applied_for" | "registered" | "common_pay_parent" | "common_pay_child")
export type FeinTypeKey = keyof typeof fein_type

// Map from string key (e.g., from DB) to code
export const feinTypeKeyToCodeMap: Record<FeinTypeKey, string> = {
  applied_for: 'Y',
  registered: 'N',
  common_pay_parent: 'P',
  common_pay_child: 'C',
}

// Enum for Bank Account Type
const bank_account_type = {
  checking: 0, // C
  savings: 1, // S
} as const

// Type representing the keys ("checking" | "savings")
export type BankAccountTypeKey = keyof typeof bank_account_type

// Map from string key (e.g., from DB) to code
export const bankAccountTypeKeyToCodeMap: Record<BankAccountTypeKey, string> = {
  checking: 'C',
  savings: 'S',
}

// Enum for FUTA Exempt Categories (Y/N fields)
const futa_exemption = {
  applicable: 0, // Y
  not_applicable: 1, // N
} as const

// Type representing the keys ("applicable" | "not_applicable")
export type FutaExemptionKey = keyof typeof futa_exemption

// Map from string key (e.g., from DB) to code
export const futaExemptionKeyToCodeMap: Record<FutaExemptionKey, string> = {
  applicable: 'Y',
  not_applicable: 'N',
}

// Enum for Agent/Client Type
const agent_client_type = {
  agent_3504: 0, // 1
  cpeo_3511a: 1, // 2
  cpeo_3511c: 2, // 3
  cpeo_3504: 3, // 4
  cpeo_mixed: 4, // 0
  cpeo_client_3511a: 5, // A
  cpeo_client_3511c: 6, // B
  cpeo_client_31_3504: 7, // C
  other_third_party: 8, // T
  none: 9, // N
} as const

// Type representing the keys
export type AgentClientTypeKey = keyof typeof agent_client_type

// Map from string key (e.g., from DB) to code
export const agentClientTypeKeyToCodeMap: Record<AgentClientTypeKey, string> = {
  agent_3504: '1',
  cpeo_3511a: '2',
  cpeo_3511c: '3',
  cpeo_3504: '4',
  cpeo_mixed: '0',
  cpeo_client_3511a: 'A',
  cpeo_client_3511c: 'B',
  cpeo_client_31_3504: 'C',
  other_third_party: 'T',
  none: 'N',
}

// Enum for Yes/No Fields
const yes_no = {
  yes: 0, // Y
  no: 1, // N
} as const

// Type representing the keys ("yes" | "no")
export type YesNoKey = keyof typeof yes_no

// Map from string key (e.g., from DB) to code
export const yesNoKeyToCodeMap: Record<YesNoKey, string> = {
  yes: 'Y',
  no: 'N',
}

// Enum for Cash Service Level
const cash_service_level = {
  full: 0, // F
  variances_only: 1, // V
} as const

// Type representing the keys ("full" | "variances_only")
export type CashServiceLevelKey = keyof typeof cash_service_level

// Map from string key (e.g., from DB) to code
export const cashServiceLevelKeyToCodeMap: Record<CashServiceLevelKey, string> =
  {
    full: 'F',
    variances_only: 'V',
  }

// Enum for Kind of Employer
const kind_of_employer = {
  federal_government: 0, // F
  state_local_government: 1, // S
  tax_exempt: 2, // T
  state_local_tax_exempt: 3, // Y
  none: 4, // N
} as const

// Type representing the keys
export type KindOfEmployerKey = keyof typeof kind_of_employer

// Map from string key (e.g., from DB) to code
export const kindOfEmployerKeyToCodeMap: Record<KindOfEmployerKey, string> = {
  federal_government: 'F',
  state_local_government: 'S',
  tax_exempt: 'T',
  state_local_tax_exempt: 'Y',
  none: 'N',
}

// Enum for Company Tax Status
const company_tax_status = {
  active: 0, // A
  inactive: 1, // I
} as const

// Type representing the keys ("active" | "inactive")
export type CompanyTaxStatusKey = keyof typeof company_tax_status

// Map from string key (e.g., from DB) to code
export const companyTaxStatusKeyToCodeMap: Record<CompanyTaxStatusKey, string> =
  {
    active: 'A',
    inactive: 'I',
  }

// Enum for EIN Type
const ein_type = {
  applied_for: 0, // Y
  registered: 1, // N
  reimbursable: 2, // I
  same_as_fein: 3, // F
  exempt: 4, // E
  common_pay_parent: 5, // P
  common_pay_child: 6, // C
  michigan_501: 7, // 5
} as const

// Type representing the keys
export type EinTypeKey = keyof typeof ein_type

// Map from string key (e.g., from DB) to code
export const einTypeKeyToCodeMap: Record<EinTypeKey, string> = {
  applied_for: 'Y',
  registered: 'N',
  reimbursable: 'I',
  same_as_fein: 'F',
  exempt: 'E',
  common_pay_parent: 'P',
  common_pay_child: 'C',
  michigan_501: '5',
}

// Enum for Payment Method
const payment_method = {
  check: 0, // 01
  eft_credit: 1, // 11
  eft_debit: 2, // 21
  eft_debit_touch_tone: 3, // 22
  eft_debit_online: 4, // 23
  eft_debit_file: 5, // 24
  eft_debit_return: 6, // 25
} as const

// Type representing the keys
export type PaymentMethodKey = keyof typeof payment_method

// Map from string key (e.g., from DB) to code
export const paymentMethodKeyToCodeMap: Record<PaymentMethodKey, string> = {
  check: '01',
  eft_credit: '11',
  eft_debit: '21',
  eft_debit_touch_tone: '22',
  eft_debit_online: '23',
  eft_debit_file: '24',
  eft_debit_return: '25',
}

// Enum for Company Tax Service Level
const company_tax_service_level = {
  return_only: 0, // R
  balance_only: 1, // B
} as const

// Type representing the keys ("return_only" | "balance_only")
export type CompanyTaxServiceLevelKey = keyof typeof company_tax_service_level

// Map from string key (e.g., from DB) to code
export const companyTaxServiceLevelKeyToCodeMap: Record<
  CompanyTaxServiceLevelKey,
  string
> = {
  return_only: 'R',
  balance_only: 'B',
}

// helper types for dealing with enums
export type RawPayrollHeaderRow = Omit<
  z.infer<typeof payroll_header>,
  | 'tax_liabilities'
  | 'company_setup'
  | 'variance_payroll_code'
  | 'bank_setup'
  | 'filer_944'
  | 'quarterly_wage_reporting'
  | 'year_end_employee_filing'
  | 'worksite_reporting'
  | 'wage_attachment_flag'
  | 'company_status'
  | 'service_level'
  | 'fein_type'
  | 'bank_account_type'
  | 'cash_service_level'
  | 'agent_client_type'
  | 'kind_of_employer'
  | 'cafeteria_plan_benefits'
  | 'group_term_life_insurance'
  | 'dependent_care_assistance'
  | 'business_expense_reimbursement'
  | 'employer_contribution_401k'
  | 'employer_contribution_sep_ira'
  | 'employer_contribution_simple'
  | 'accident_health_insurance_premiums'
  | 'sick_pay'
  | 'workers_compensation'
  | 'payments_to_family_employees'
  | 'payments_to_hospital_interns'
  | 'payments_to_hospital_patients'
  | 'payments_to_general_partnership'
  | 'state_govt_employee_salaries'
  | 'payments_to_election_workers'
  | 'supplemental_unemployment_benefits'
  | 'nonqualified_deferred_comp'
  | 'meals_furnished_in_kind'
  | 'qualified_moving_expense'
  | 'hsa'
  | 'exempt_501c3_organization'
  | 'employee_stock_purchase_plan'
  | 'non_taxable_fringe_payments'
  | 'public_transportation_non_tax'
  | 'wc_housing_employment_condition'
  | 'chaplain_housing'
  | 'clergy_housing_poverty_vow'
  | 'foreign_source_income'
  | 'student_exempt'
> & {
  tax_liabilities: YesNoKey
  company_setup: YesNoKey
  variance_payroll_code?: YesNoKey
  bank_setup?: YesNoKey
  filer_944?: YesNoKey
  quarterly_wage_reporting?: YesNoKey
  year_end_employee_filing?: YesNoKey
  worksite_reporting?: YesNoKey
  wage_attachment_flag?: YesNoKey

  company_status: CompanyStatusKey
  service_level: ServiceLevelKey
  fein_type?: FeinTypeKey
  bank_account_type?: BankAccountTypeKey
  cash_service_level?: CashServiceLevelKey
  agent_client_type?: AgentClientTypeKey
  kind_of_employer?: KindOfEmployerKey

  cafeteria_plan_benefits?: FutaExemptionKey
  group_term_life_insurance?: FutaExemptionKey
  dependent_care_assistance?: FutaExemptionKey
  business_expense_reimbursement?: FutaExemptionKey
  employer_contribution_401k?: FutaExemptionKey
  employer_contribution_sep_ira?: FutaExemptionKey
  employer_contribution_simple?: FutaExemptionKey
  accident_health_insurance_premiums?: FutaExemptionKey
  sick_pay?: FutaExemptionKey
  workers_compensation?: FutaExemptionKey
  payments_to_family_employees?: FutaExemptionKey
  payments_to_hospital_interns?: FutaExemptionKey
  payments_to_hospital_patients?: FutaExemptionKey
  payments_to_general_partnership?: FutaExemptionKey
  state_govt_employee_salaries?: FutaExemptionKey
  payments_to_election_workers?: FutaExemptionKey
  supplemental_unemployment_benefits?: FutaExemptionKey
  nonqualified_deferred_comp?: FutaExemptionKey
  meals_furnished_in_kind?: FutaExemptionKey
  qualified_moving_expense?: FutaExemptionKey
  hsa?: FutaExemptionKey
  exempt_501c3_organization?: FutaExemptionKey
  employee_stock_purchase_plan?: FutaExemptionKey
  non_taxable_fringe_payments?: FutaExemptionKey
  public_transportation_non_tax?: FutaExemptionKey
  wc_housing_employment_condition?: FutaExemptionKey
  chaplain_housing?: FutaExemptionKey
  clergy_housing_poverty_vow?: FutaExemptionKey
  foreign_source_income?: FutaExemptionKey
  student_exempt?: FutaExemptionKey
}
