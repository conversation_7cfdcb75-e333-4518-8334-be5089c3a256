import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import type {
  InsertableUsersRsk,
  SelectableUsersRsk,
  UpdateableUsersRsk,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { db as mainDB } from 'db/src/index.js'
import { getContext } from 'utils/src/common/requestContext.js'

export const insertUserRsk = (data: InsertableUsersRsk, db = mainDB) => {
  using _ = getContext()
  return db.insertInto('rise.users_rsk').values(data).execute()
}

export const updateUserRsk = (
  nanoid: UserNanoid,
  data: UpdateableUsersRsk,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .updateTable('rise.users_rsk')
    .set(data)
    .where('nanoid', '=', nanoid)
    .execute()
}

export const getUsersRsk = (
  resetStatus: Array<SelectableUsersRsk['reset_status']> = [
    'pending',
    'started',
  ],
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.users_rsk as ur')
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 'ur.nanoid')
    .selectAll(['ur'])
    .select(['ud.email', 'ud.first_name', 'ud.last_name'])
    .where('ur.reset_status', 'in', resetStatus)
    .execute()
}

export const getUserRskByNanoid = (
  nanoid: SelectableUsersRsk['nanoid'],
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.users_rsk as ur')
    .innerJoin('rise.users_data as ud', 'ud.nanoid', 'ur.nanoid')
    .selectAll(['ur'])
    .select(['ud.email', 'ud.first_name', 'ud.last_name'])
    .where('ur.nanoid', '=', nanoid)
    .executeTakeFirst()
}

export const getUsersRequiringPasskey = async (db = mainDB) => {
  using _ = getContext()
  const result = await db
    .selectFrom('rise.users_rsk as ur')
    .innerJoin('rise.users_onboarding as ub', 'ur.nanoid', 'ub.nanoid')
    .where('ub.step', '=', 'complete')
    .where('ur.has_passkey', 'is', false)
    .select(['ur.nanoid'])
    .limit(10)
    .execute()

  return result
}
