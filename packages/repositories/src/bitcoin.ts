import {
  type BitcoinOutput,
  type BlockHash,
  type BlockHeight,
  type BlockSummary,
  type BlockTx,
  type SegwitAddress,
  bitcoinAddressRegex,
  bitcoinTxHashRegex,
  mempoolAddressUtxosTransformer,
  mempoolBlockSummariesTransformer,
  mempoolBlockTxsTransformer,
  mempoolFeeRateTransformer,
  segwitRegex,
} from '@riseworks/contracts/src/bitcoin.js'
import { eventDataSchema } from '@riseworks/contracts/src/events/transaction_events/bitcoin.new_outputs.js'
import type { BitcoinAccount } from 'utils/src/blockchain/bitcoin/account.js'
import { BitcoinSweepTransaction } from 'utils/src/blockchain/bitcoin/sweep.js'
import assert from 'utils/src/common/assertHTTP.js'
import { isProduction } from 'utils/src/common/env.js'
import { type HTTPUrl, createHTTPClient } from 'utils/src/common/httpClient.js'
import { is } from 'utils/src/common/is.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { setEventData } from './events.js'

const mempoolApiPrefixUrl = (
  isProduction
    ? 'https://mempool.space/api/'
    : 'https://mempool.space/testnet/api/'
) as HTTPUrl

const mempoolApi = createHTTPClient({
  prefixUrl: mempoolApiPrefixUrl,
})

const getAddressUtxos = async (address: SegwitAddress) => {
  using _ = getContext()
  const addressUtxosResponse = await mempoolApi
    .get(`address/${address}/utxo`)
    .json()
  return mempoolAddressUtxosTransformer.parse({
    address,
    utxos: addressUtxosResponse,
  })
}

// Unfortunately, Mempool has currently disabled their new bulk endpoint, so we're forced to use the old one.
const getBlockSummaries = async (
  startIndex?: BlockHeight,
  stopIndex?: BlockHeight,
) => {
  using _ = getContext()
  const requestPath = startIndex ? `v1/blocks/${startIndex}` : 'v1/blocks'
  const blockSummariesResponse = await mempoolApi.get(requestPath).json()
  const blockSummaries = mempoolBlockSummariesTransformer.parse(
    blockSummariesResponse,
  )
  if (!stopIndex) {
    return blockSummaries
  }
  return blockSummaries.filter((summary) => summary.blockHeight >= stopIndex)
}

const MAX_BLOCK_TXS = 25 // https://mempool.space/docs/api/rest#get-block-transactions

const getBlockTxs = async (blockHash: BlockHash, startTxIndex: number) => {
  using _ = getContext()
  const blockTxsResponse = await mempoolApi
    .get(`block/${blockHash}/txs/${startTxIndex}`)
    .json()
  return mempoolBlockTxsTransformer.parse({
    startTxIndex,
    blockTxs: blockTxsResponse,
  })
}

const getFallbackOutputs = async (lastProcessedHeight: BlockHeight | -1) => {
  using _ = getContext()
  const blockSummarySet = new Set<BlockSummary>()
  let highestHeight = lastProcessedHeight
  if (lastProcessedHeight > 0) {
    let lowestHeight: number | undefined
    while (!lowestHeight || lowestHeight > lastProcessedHeight + 1) {
      if (lowestHeight && lowestHeight - lastProcessedHeight > 30) {
        await setEventData(
          'transaction_events-bitcoin-new-outputs',
          eventDataSchema.parse({
            lastProcessedBlockHeight: -1,
            // TODO: Figure out why updated_at is not being set automatically, then remove this
            lastUpdated: new Date(),
          }),
        )
        assert(
          false,
          `More than 30 blocks (${lowestHeight - lastProcessedHeight}) have been skipped. Cancelling fallback and resetting bitcoin.new_outputs event data.`,
        )
      }
      const blockSummaries = await getBlockSummaries(
        lowestHeight ? lowestHeight - 1 : undefined,
        lastProcessedHeight + 1,
      )
      for (const blockSummary of blockSummaries) {
        // No need to check if the block height is greater than the last processed height, because we're limiting the height to the block after the last processed block.
        blockSummarySet.add(blockSummary)
      }
      lowestHeight = Math.min(
        lowestHeight ?? lastProcessedHeight,
        ...blockSummaries.map((summary) => summary.blockHeight),
      )
      highestHeight = Math.max(
        highestHeight,
        ...blockSummaries.map((summary) => summary.blockHeight),
      )
    }
  } else {
    const latestBlockSummaries = await getBlockSummaries()
    for (const blockSummary of latestBlockSummaries) {
      blockSummarySet.add(blockSummary)
    }
    highestHeight = Math.max(
      highestHeight,
      ...latestBlockSummaries.map((summary) => summary.blockHeight),
    )
  }
  const fallbackOutputs = new Set<BitcoinOutput>()
  for (const blockSummary of blockSummarySet) {
    const blockTxSet = new Set<BlockTx>()
    const blocksToFetch = Math.ceil(blockSummary.txCount / MAX_BLOCK_TXS)
    const blockTxsPromises: Promise<BlockTx[]>[] = []
    for (let i = 0; i < blocksToFetch; i++) {
      blockTxsPromises.push(
        getBlockTxs(blockSummary.blockHash, i * MAX_BLOCK_TXS),
      )
    }
    const blockTxs = (await Promise.all(blockTxsPromises)).flat()
    for (const blockTx of blockTxs) {
      blockTxSet.add(blockTx)
    }
    assert(
      blockTxSet.size === blockSummary.txCount,
      `Expected ${blockSummary.txCount} block transactions, but got ${blockTxSet.size}`,
    )
    for (const blockTx of blockTxSet) {
      const validInputs =
        blockTx.vin?.filter(({ sourceAddress }) =>
          is(sourceAddress, segwitRegex),
        ) ?? []
      for (const input of validInputs) {
        fallbackOutputs.add(`${input.sourceAddress}:1`)
      }
      const validOutputs =
        blockTx.vout?.filter(({ address }) =>
          is(address, bitcoinAddressRegex),
        ) ?? []
      for (const output of validOutputs) {
        fallbackOutputs.add(`${output.address}:0`)
      }
    }
  }
  return {
    fallbackOutputs: Array.from(fallbackOutputs),
    highestBlockHeight: highestHeight,
  }
}

const getFeeRate = async () => {
  using _ = getContext()
  const feeRateResponse = await mempoolApi.get('v1/fees/recommended').json()
  return mempoolFeeRateTransformer.parse(feeRateResponse)
}

const createSweepTransaction = async (account: BitcoinAccount) => {
  using _ = getContext()
  assert(account.validatedOwner, 'BitcoinAccount is not initialized')
  const inputs = (
    await Promise.all([
      getAddressUtxos(account.validatedOwner.depositAddress),
      getAddressUtxos(account.validatedOwner.changeAddress),
    ])
  ).flat()
  assert(
    inputs.length,
    `Could not find any inputs for ${account.validatedOwner.depositAddress} or ${account.validatedOwner.changeAddress}`,
  )
  const feeRate = await getFeeRate()
  return new BitcoinSweepTransaction(account, inputs, feeRate)
}

const broadcastSweepTransaction = async (
  sweepTransaction: BitcoinSweepTransaction,
) => {
  using _ = getContext()
  assert(sweepTransaction.isValid(), 'Sweep tx is invalid')
  const hex = sweepTransaction.txHex()
  assert(hex, 'Could not get tx hex')
  const broadcastedTxResponse = await mempoolApi
    .post('tx', {
      body: hex,
    })
    .text()
  return bitcoinTxHashRegex.parse(broadcastedTxResponse)
}

export {
  getAddressUtxos,
  getFallbackOutputs,
  createSweepTransaction,
  broadcastSweepTransaction,
}
