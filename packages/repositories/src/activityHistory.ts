import {
  type CompanyNanoid,
  type EmployeePayrollRiseAccount,
  type TeamNanoid,
  type UserNanoid,
  companyNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableActivityHistory } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { db as mainDB } from 'db/src/index.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { getContext } from 'utils/src/common/requestContext.js'

export const upsertActivityHistory = async (
  activity: InsertableActivityHistory,
  db = mainDB,
) => {
  using _ = getContext()
  await db
    .insertInto('rise.activity_history')
    .values(activity)
    .onDuplicateKeyUpdate(activity)
    .execute()
}

export const upsertPaynowActivityHistory = async (
  {
    payment_nanoid,
    from,
    to,
  }: {
    payment_nanoid: string
    from: TeamNanoid
    to: UserNanoid | CompanyNanoid
  },
  db = mainDB,
) => {
  using _ = getContext()

  const teamRel = await db
    .selectFrom('rise.rise_entities as rel')
    .innerJoin('rise.rise_entities as team', 'team.riseid', 'rel.riseid')
    .innerJoin('rise.rise_entities as user', 'user.riseid', 'rel.parent_riseid')
    .where('rel.type', 'in', ['team_employee', 'contractor'])
    .where('team.nanoid', '=', from)
    .where('user.nanoid', '=', to)
    .selectAll(['rel'])
    .executeTakeFirst()
  assert(teamRel, 'Team relation not found', '404')

  await upsertActivityHistory(
    {
      nanoid: payment_nanoid,
      entity_nanoid: from,
      entity_type: 'team',
      workspace_nanoid: from,
      workspace_type: 'team',
      type: 'payment',
    },
    db,
  )
  await upsertActivityHistory(
    {
      nanoid: payment_nanoid,
      entity_nanoid: to,
      entity_type: is(to, companyNanoid) ? 'company' : 'user',
      workspace_nanoid: teamRel.type === 'contractor' ? to : from,
      workspace_type: teamRel.type,
      type: 'payment',
    },
    db,
  )
}

export const upsertBatchPayNowActivityHistory = async (
  {
    payments_nanoids,
    from,
    to,
  }: {
    payments_nanoids: string[]
    from: TeamNanoid
    to: (UserNanoid | CompanyNanoid)[]
  },
  db = mainDB,
) => {
  using _ = getContext()
  await Promise.all(
    payments_nanoids.map(async (payment_nanoid, idx) => {
      const _to = to[idx]
      assert(_to, 'To user not found', '404')
      await upsertPaynowActivityHistory(
        {
          payment_nanoid,
          from,
          to: _to,
        },
        db,
      )
    }),
  )
}

export const upsertCashRequirementPaymentActivityHistory = async (
  {
    payment_onchain_id,
    team_nanoid,
  }: {
    payment_onchain_id: string
    team_nanoid: TeamNanoid
  },
  db = mainDB,
) => {
  using _ = getContext()
  await upsertActivityHistory(
    {
      nanoid: payment_onchain_id,
      entity_nanoid: team_nanoid,
      entity_type: 'team',
      workspace_nanoid: team_nanoid,
      workspace_type: 'team',
      type: 'payment',
    },
    db,
  )
}

export const upsertPayrollDailyPaymentActivityHistory = async (
  {
    payment_onchain_id,
    payer_account,
    recipient_account,
  }: {
    payment_onchain_id: string
    payer_account: string
    recipient_account: string
  },
  db = mainDB,
) => {
  using _ = getContext()

  const payrollSettings = await db
    .selectFrom('rise.employee_payroll_settings')
    .where('rise_account', '=', payer_account as EmployeePayrollRiseAccount)
    .selectAll()
    .executeTakeFirst()

  const recipientPayrollSettings = await db
    .selectFrom('rise.employee_payroll_settings')
    .where('rise_account', '=', recipient_account as EmployeePayrollRiseAccount)
    .selectAll()
    .executeTakeFirst()

  // payment from hidden payroll account to employee account
  if (payrollSettings && recipientPayrollSettings) {
    await upsertPaynowActivityHistory(
      {
        payment_nanoid: payment_onchain_id,
        from: payrollSettings.team_nanoid,
        to: payrollSettings.user_nanoid,
      },
      db,
    )
    return
  }
}
