import type {
  CompanyNanoid,
  TeamNanoid,
  UserNanoid,
  WebhookEndpointNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { UpdateableWebhookEndpoints } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import { db as mainDB } from 'db/src/index.js'
import assert from 'utils/src/common/assertHTTP.js'
import { encrypt } from 'utils/src/common/crypter.js'
import { nanoid as _nanoid } from 'utils/src/common/nanoid.js'
import { getContext } from 'utils/src/common/requestContext.js'

// =================================================================================================
// WEBHOOK ENDPOINTS
// =================================================================================================

export const createWebhookEndpoint = async (
  {
    company_nanoid,
    team_nanoid,
    created_by,
    url,
    events,
    secret,
    is_active = true,
  }: {
    company_nanoid: CompanyNanoid
    team_nanoid?: TeamNanoid | null
    created_by: UserNanoid
    url: string
    events: string[]
    secret: string
    is_active?: boolean
  },
  db = mainDB,
): Promise<WebhookEndpointNanoid> => {
  using _ = getContext()
  const nanoid = _nanoid('webhook_endpoint')

  await db
    .insertInto('rise.webhook_endpoints')
    .values({
      nanoid,
      company_nanoid,
      is_removed: false,
      team_nanoid,
      created_by,
      last_modified_by: created_by,
      url,
      secret_encrypted: encrypt({ value: secret }),
      events: JSON.stringify(events),
      is_active,
    })
    .execute()

  return nanoid
}

export const getWebhookEndpointById = async (
  id: WebhookEndpointNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return await db
    .selectFrom('rise.webhook_endpoints')
    .selectAll()
    .where('nanoid', '=', id)
    .where('is_removed', '=', false)
    .executeTakeFirst()
}

export const getWebhookEndpointsByCompany = async (
  company_nanoid: CompanyNanoid,
  {
    team_nanoids,
    cursor,
    limit = 50,
    active_only = false,
  }: {
    team_nanoids?: TeamNanoid[] | null
    cursor?: string
    limit?: number
    active_only?: boolean
  } = {},
  db = mainDB,
) => {
  using _ = getContext()

  let query = db
    .selectFrom('rise.webhook_endpoints')
    .selectAll()
    .where('company_nanoid', '=', company_nanoid)
    .where('is_removed', '=', false)
    .orderBy('created_at', 'desc')

  // Handle team filtering
  if (team_nanoids !== undefined) {
    if (team_nanoids === null) {
      // Query for company-level webhooks (team_nanoid IS NULL)
      query = query.where('team_nanoid', 'is', null)
    } else if (team_nanoids.length === 0) {
      // No teams specified, return empty result
      return []
    } else {
      // Query for specific teams using IN clause
      query = query.where('team_nanoid', 'in', team_nanoids)
    }
  }

  if (active_only) {
    query = query.where('is_active', '=', true)
  }

  if (cursor) {
    query = query.where('created_at', '<', new Date(cursor))
  }

  return await query.limit(limit + 1).execute()
}

export const updateWebhookEndpoint = async (
  id: WebhookEndpointNanoid,
  updates: {
    url?: string
    events?: string[]
    secret?: string ////todo: encrypt this properly in (https://github.com/orgs/riseworks/projects/14/views/1?pane=issue&itemId=*********&issue=riseworks%7Capi-monorepo%7C2776)
    is_active?: boolean
    last_modified_by: UserNanoid
  },
  db = mainDB,
) => {
  using _ = getContext()

  // First check if the webhook endpoint exists and is not removed
  const existing = await db
    .selectFrom('rise.webhook_endpoints')
    .select(['nanoid'])
    .where('nanoid', '=', id)
    .where('is_removed', '=', false)
    .executeTakeFirst()

  assert(existing, `Webhook endpoint ${id} not found`, '404')

  const updateData: Partial<UpdateableWebhookEndpoints> = {
    last_modified_by: updates.last_modified_by,
    updated_at: new Date(),
  }

  if (updates.url !== undefined) {
    updateData.url = updates.url
  }

  if (updates.events !== undefined) {
    updateData.events = JSON.stringify(updates.events)
  }

  if (updates.is_active !== undefined) {
    updateData.is_active = updates.is_active
  }

  if (updates.secret !== undefined) {
    updateData.secret_encrypted = encrypt({ value: updates.secret })
  }

  const result = await db
    .updateTable('rise.webhook_endpoints')
    .set(updateData)
    .where('nanoid', '=', id)
    .where('is_removed', '=', false)
    .execute()

  return result
}

export const deleteWebhookEndpoint = async (
  id: WebhookEndpointNanoid,
  deleted_by: UserNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  // First check if the webhook endpoint exists and is not already removed
  const existing = await db
    .selectFrom('rise.webhook_endpoints')
    .select(['nanoid'])
    .where('nanoid', '=', id)
    .where('is_removed', '=', false)
    .executeTakeFirst()

  assert(existing, `Webhook endpoint ${id} not found`, '404')

  // Soft delete by setting is_removed to true
  const result = await db
    .updateTable('rise.webhook_endpoints')
    .set({
      is_removed: true,
      last_modified_by: deleted_by,
      updated_at: new Date(),
    })
    .where('nanoid', '=', id)
    .where('is_removed', '=', false)
    .execute()

  return result
}
