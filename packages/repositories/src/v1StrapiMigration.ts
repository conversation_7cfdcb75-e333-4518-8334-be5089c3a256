import type {
  AllRiseAccounts,
  AllRiseids,
  CompanyNanoid,
  CompanyRiseAccount,
  CompanyRiseid,
  TeamNanoid,
  TeamRiseAccount,
  UserNanoid,
  UserRiseAccount,
  UserRiseid,
} from '@riseworks/contracts/src/brands.js'
import type {
  InsertableCompaniesData,
  InsertableCompanySettings,
  InsertableRiseEntities,
  InsertableTeamsData,
  InsertableUsersData,
  InsertableUsersOnboarding,
  InsertableV1V2MigrationEntity,
  RiseEntities,
  UpdateableUsersOnboarding,
} from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type {
  InsertableAddresses,
  InsertablePrivateData,
} from '@riseworks/contracts/src/codegen/db/models_rise_private.js'
import type { users_withdraw_account } from '@riseworks/contracts/src/codegen/zod/rise_private/users_withdraw_account.js'
import {
  blockchainAddress,
  phone,
  website,
} from '@riseworks/contracts/src/formats.js'
import { type DBTransaction, db as mainDB } from 'db/src/index.js'
import { sql } from 'kysely'
import {
  type ChainNumbers,
  getNetwork,
  null_address,
} from 'utils/src/blockchain/helpers.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { is } from 'utils/src/common/is.js'
import { nanoid } from 'utils/src/common/nanoid.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { v4 as uuidv4 } from 'uuid'
import { z } from 'zod'
import * as clerk from './clerk.js'
import { upsertEntityPermission } from './entityPermissions.js'
import { createExistingFundingAccount } from './fundInstructions.js'
import { createRiseId } from './smartContracts.js'
import { getFullUserData } from './users.js'
import { upsertInternationalUSDAccount } from './withdrawManual.js'
import { upsertOrumWithdrawAccount } from './withdrawOrum.js'
import { upsertInternationalAccount } from './withdrawRoutefusion.js'
import { upsertUnblockWithdrawAccount } from './withdrawUnblock.js'
import { upsertBlockchainWithdrawAccount } from './withdrawWallet.js'

type RiseAccountStatus = z.infer<typeof users_withdraw_account.shape.status>
const v1ExternalAccountStatusTov2Status = (
  status?: string | null,
): RiseAccountStatus => {
  switch (status) {
    case 'active':
      return 'active'
    case 'default':
      return 'default'
    case 'removed':
      return 'removed'
    case 'verifying':
      return 'removed'
    case 'processing':
      return 'pending'
    default:
      return 'removed'
  }
}

export const getV1RiseIdForEntity = async (
  nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  const address = await db
    .selectFrom('rise.v1_v2_migration_entity as m')
    .leftJoin('strapi.companies as c', (join) =>
      join
        .onRef('m.entity_v1_id', '=', 'c.id')
        .on('m.entity_type', 'in', ['company', 'team']),
    )
    .leftJoin('strapi.users-permissions_user as u', (join) =>
      join
        .onRef('m.entity_v1_id', '=', 'u.id')
        .on('m.entity_type', '=', 'user'),
    )
    .select((qb) => [qb.fn.coalesce('c.riseId', 'u.riseId').as('riseid')])
    .where('m.entity_v2_nanoid', '=', nanoid)
    .executeTakeFirst()

  return address?.riseid
}

export const getV1EntityMigration = (
  entity_id: number,
  entity_type: 'company' | 'team' | 'user',
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.v1_v2_migration_entity')
    .selectAll()
    .where('entity_v1_id', '=', entity_id)
    .where('entity_type', '=', entity_type)
    .executeTakeFirst()
}

export const getV1EntityMigrationByNanoid = (
  entity_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('rise.v1_v2_migration_entity')
    .selectAll()
    .where('entity_v2_nanoid', '=', entity_nanoid)
    .executeTakeFirst()
}

const insertV1EntityMigration = async (
  entity_v1_id: number,
  entity_type: 'user' | 'company' | 'team',
  entity_v2_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  riseid: AllRiseids,
  rise_account: AllRiseAccounts,
  migration_status: 'in_progress' | 'completed' | 'failed',
  owner_id: number | null,
  owner_type: 'user' | 'company' | null,
  company_relationship_id: number | null,
  company_relationship_type: 'company' | 'team' | null,
  inngest_event_id: string | null,
  db = mainDB,
) => {
  using _ = getContext()
  return await db
    .insertInto('rise.v1_v2_migration_entity')
    .values({
      entity_v1_id: entity_v1_id,
      entity_v2_nanoid,
      entity_type,
      riseid,
      rise_account,
      migration_status,
      failure_reason: '',
      failure_step: '',
      company_relationship_id,
      company_relationship_type,
      owner_id,
      owner_type,
    })
    .execute()
}

const createEntityRiseId = async (
  v2_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  owner_address: string,
  type: 'user' | 'company' | 'team',
  db = mainDB,
) => {
  using _ = getContext()
  const riseid = await createRiseId({
    owner_address,
    parent_account: null,
    type,
    network: 'arbitrum',
    purpose: 'generic',
    owner_nanoid: v2_nanoid,
    db,
  })
  return riseid
}

const createEntityRiseAccount = async (
  v2_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  riseid: AllRiseids,
  db = mainDB,
) => {
  using _ = getContext()
  return (await createRiseId({
    owner_address: riseid,
    parent_account: null,
    type: 'rise_account',
    network: 'arbitrum',
    purpose: 'generic',
    owner_nanoid: v2_nanoid,
    db,
  })) as unknown as AllRiseAccounts
}

export const getOrCreateV1EntityMigration = async (
  entity_id: number,
  entity_type: 'company' | 'user' | 'team',
  riseid_owner_address: string,
  owner_id: number | null,
  owner_type: 'user' | 'company' | null,
  company_relationship_id: number | null,
  company_relationship_type: 'company' | 'team' | null,
  eventId: string | null,
  db = mainDB,
) => {
  using _ = getContext()

  const migration = await getV1EntityMigration(entity_id, entity_type)

  if (migration) {
    await setEntityMigrationStep(
      migration.entity_v1_id,
      migration.entity_type,
      {
        migration_status: 'in_progress',
        inngest_event_id: eventId,
      },
    )

    return migration
  }

  const v2_nanoid = nanoid(entity_type)
  const riseid = await createEntityRiseId(
    v2_nanoid,
    riseid_owner_address,
    entity_type,
    db,
  )
  const riseAccount = await createEntityRiseAccount(v2_nanoid, riseid, db)
  await insertV1EntityMigration(
    entity_id,
    entity_type,
    v2_nanoid,
    riseid,
    riseAccount,
    'in_progress',
    owner_id,
    owner_type,
    company_relationship_id,
    company_relationship_type,
    eventId,
    db,
  )
  const _migration = await getV1EntityMigration(entity_id, entity_type)
  assert(_migration, 'v1/v2 Migration entry not created', '500')

  return _migration
}

export const setEntityMigrationStep = (
  entity_id: number,
  entity_type: 'company' | 'user' | 'team',
  steps: Partial<
    Pick<
      InsertableV1V2MigrationEntity,
      | 'migrated_completed'
      | 'migrated_clerk_ids'
      | 'migrated_sumsub_ids'
      | 'migration_status'
      | 'inngest_event_id'
    >
  >,
  db = mainDB,
) => {
  using _ = getContext()

  return db
    .updateTable('rise.v1_v2_migration_entity')
    .set(steps)
    .where('entity_v1_id', '=', entity_id)
    .where('entity_type', '=', entity_type)
    .execute()
}

export const v1StrapiUserById = (id: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.users-permissions_user')
    .selectAll()
    .where('id', '=', id)
    .executeTakeFirst()
}

export const v1StrapiRole = (role: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.users-permissions_role')
    .selectAll()
    .where('id', '=', role)
    .executeTakeFirst()
}

export const v1StrapiCompanyById = (v1_company: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.companies')
    .selectAll()
    .where('id', '=', v1_company)
    .executeTakeFirst()
}

export const v1StrapiCompanyConfig = (v1_company: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.company-config')
    .selectAll()
    .where('company', '=', v1_company)
    .executeTakeFirst()
}

export const v1StrapiCompaniesByOwner = (
  v1_owner: number,
  older_first: boolean,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.companies')
    .selectAll()
    .where('owner', '=', v1_owner)
    .orderBy('created_at', older_first ? 'asc' : 'desc')
    .execute()
}

export const v1StrapiCompanyMembers = async (
  v1_company: number,
  roles: ('client' | 'contractor')[],
  db = mainDB,
) => {
  using _ = getContext()

  const _roles = await db
    .selectFrom('strapi.users-permissions_role')
    .where('type', 'in', roles)
    .select(['id'])
    .execute()

  return db
    .selectFrom('strapi.companies_users__users_companies as c')
    .selectAll(['c'])
    .innerJoin('strapi.users-permissions_user as u', 'u.id', 'c.user_id')
    .where('c.company_id', '=', v1_company)
    .where(
      'u.role',
      'in',
      _roles.map((r) => r.id),
    )
    .execute()
}

export const v1ExternalAccountsByUser = (v1_owner: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.pay_external_accounts')
    .selectAll()
    .where('user', '=', v1_owner)
    .execute()
}

const v1ExternalAccountById = (account_id: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.pay_external_accounts')
    .selectAll()
    .where('id', '=', account_id)
    .executeTakeFirst()
}

const v1BankAccountByExternalAccountId = (account_id: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('private_data.bank_accounts')
    .selectAll()
    .where('strapi_external_account_id', '=', account_id)
    .executeTakeFirst()
}

const v1RiseOrumAccountByExternalAccountId = (
  account_id: number,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('remittance.rise_orum_account')
    .selectAll()
    .where('strapi_external_account_id', '=', account_id)
    .executeTakeFirst()
}

const v1RiseRoutefusionAccountByExternalAccountId = (
  account_id: number,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('remittance.rise_routefusion_beneficiary')
    .selectAll()
    .where('rise_external_account_id', '=', account_id)
    .executeTakeFirst()
}

const v1RiseUnblockAccountByExternalAccountId = (
  account_id: number,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('remittance.rise_unblock_account')
    .selectAll()
    .where('strapi_external_account_id', '=', account_id)
    .executeTakeFirst()
}

const v1RiseUnblockClientByUserId = (user_id: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('remittance.rise_unblock_client')
    .selectAll()
    .where('rise_entity_id', '=', user_id)
    .where('type', '=', 'user')
    .executeTakeFirst()
}

const v1VirtualAccountsById = (v1_company: number, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('remittance.rise_routefusion_funding_account')
    .selectAll()
    .where('strapi_company_id', '=', v1_company)
    .execute()
}

const v1StrapiEntityCompliance = (
  id: number,
  type: 'person' | 'company',
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('strapi.private_compliance')
    .selectAll()
    .where('userOrCompanyId', '=', id)
    .where('userType', '=', type)
    .executeTakeFirst()
}

const v1StrapiAddress = (address_id: string, db = mainDB) => {
  using _ = getContext()
  return db
    .selectFrom('private_data.address')
    .selectAll()
    .where('id', '=', address_id)
    .executeTakeFirst()
}

const v2StrapiTaxInfo = (
  entity_id: string,
  entity_type: 'company' | 'user',
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .selectFrom('private_data.tax_info')
    .selectAll()
    .where('entity_id', '=', entity_id)
    .where('entity_type', '=', entity_type)
    .executeTakeFirst()
}

export const upsertEntityMigration = async (
  entity: InsertableRiseEntities,
  db = mainDB,
) => {
  using _ = getContext()
  await db
    .insertInto('rise.rise_entities')
    .values(entity)
    .onDuplicateKeyUpdate(entity)
    .execute()
  await upsertEntityPermission(
    {
      nanoid: entity.nanoid as UserNanoid,
      pay_app: true,
    },
    db,
  )
}

const upsertUserData = async (data: InsertableUsersData, db = mainDB) => {
  using _ = getContext()
  await db
    .insertInto('rise.users_data')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

const getUserOnboarding = async (nanoid: UserNanoid, db = mainDB) => {
  using _ = getContext()
  return await db
    .selectFrom('rise.users_onboarding')
    .selectAll()
    .where('nanoid', '=', nanoid)
    .executeTakeFirst()
}

const upsertUserOnboard = async (
  data: InsertableUsersOnboarding,
  db = mainDB,
) => {
  using _ = getContext()
  await db
    .insertInto('rise.users_onboarding')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

export const updateUserOnboard = (
  nanoid: UserNanoid,
  data: Omit<UpdateableUsersOnboarding, 'nanoid'>,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .updateTable('rise.users_onboarding')
    .set(data)
    .where('nanoid', '=', nanoid)
    .execute()
}

const upsertAddress = (data: InsertableAddresses, db = mainDB) => {
  using _ = getContext()
  return db
    .insertInto('rise_private.addresses')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

const upsertPrivateData = (data: InsertablePrivateData, db = mainDB) => {
  using _ = getContext()
  return db
    .insertInto('rise_private.private_data')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}
const upsertUserPrivateData = (nanoid: UserNanoid, db = mainDB) => {
  using _ = getContext()
  return db
    .insertInto('rise_private.users_data')
    .values({ nanoid, pk_salt: uuidv4() })
    .onDuplicateKeyUpdate({ nanoid, pk_salt: uuidv4() })
    .execute()
}

const upsertCompanySettings = (
  data: InsertableCompanySettings,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .insertInto('rise.company_settings')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

const upsertComplianceData = (
  nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  compliance_id: string,
  external_id: string,
  db = mainDB,
) => {
  using _ = getContext()
  return db
    .insertInto('rise.compliance_provider_data')
    .values({
      nanoid,
      compliance_id,
      external_id,
    })
    .onDuplicateKeyUpdate({
      nanoid,
      compliance_id,
      external_id,
    })
    .execute()
}

export const migrateUserData = async (
  v1_user: number,
  v2_nanoid: UserNanoid,
  riseid: UserRiseid,
  db = mainDB,
) => {
  const entityMigration = await getV1EntityMigration(v1_user, 'user', db)
  assert(entityMigration, 'Entity migration not found', '404')

  const strapiUser = await v1StrapiUserById(v1_user, db)
  assert(strapiUser, 'Strapi user not found', '404')
  assert(
    strapiUser.firstname && strapiUser.lastname,
    'Strapi user missing name',
    '404',
  )
  assert(strapiUser.addressId, 'Strapi user missing address', '404')
  assert(strapiUser.role, 'Strapi user missing role', '404')

  const userRole = await v1StrapiRole(strapiUser.role, db)
  assert(userRole, 'Strapi user role not found', '404')
  assert(userRole.type, 'Strapi user role type not found', '404')

  const strapiUserAddress = await v1StrapiAddress(strapiUser.addressId, db)
  assert(strapiUserAddress, 'Strapi user address not found', '404')

  const strapiUserTaxInfo = await v2StrapiTaxInfo(`${v1_user}`, 'user', db)
  const v2UserData = await getFullUserData(v2_nanoid)

  await upsertEntityMigration(
    {
      nanoid: v2_nanoid,
      parent_riseid: riseid,
      riseid,
      type: 'user',
      avatar:
        strapiUser.avatar ??
        strapiUser.gravatar ??
        strapiUser.providerAvatar ??
        '',
    },
    db,
  )
  await upsertUserData(
    {
      nanoid: v2_nanoid,
      rise_account: entityMigration.rise_account as UserRiseAccount,
      email: strapiUser.email,
      first_name: strapiUser.firstname,
      middle_name: strapiUser.middlename ?? undefined,
      last_name: strapiUser.lastname,
      account_status: v2UserData?.account_status ?? 'created',
      advanced_security: !!strapiUser.advancedSecurity,
      alias: strapiUser.alias ?? undefined,
      country: strapiUserAddress.country,
      state: strapiUserAddress.state,
      dob: strapiUser.birthdate,
      linkedin: strapiUser.linkedin ?? undefined,
      website: strapiUser.occupation ?? undefined,
      x: undefined,
      discord: undefined,
      occupation: strapiUser.occupation ?? undefined,
      phone: strapiUser.phone ?? undefined,
      recovery_email: strapiUser.recoveryEmail ?? undefined,
      v1_id: v1_user.toString(),
    },
    db,
  )

  await upsertUserPrivateData(v2_nanoid, db)

  const onboarding = await getUserOnboarding(v2_nanoid, db)
  if (!onboarding) {
    await upsertUserOnboard(
      {
        nanoid: v2_nanoid,
        register_business: strapiUser.registrationType === 'business',
        role: userRole.type === 'client' ? 'payer' : 'payee',
        step: 'v1_migration_in_progress',
        moderation_status: 'approved',
      },
      db,
    )
  }
  await upsertAddress(
    {
      nanoid: v2_nanoid,
      country: strapiUserAddress.country,
      state: strapiUserAddress.state,
      city: strapiUserAddress.city,
      line_1: strapiUserAddress.address1,
      line_2: strapiUserAddress.address2 ?? undefined,
      zip_code: strapiUserAddress.zip,
    },
    db,
  )
  await upsertPrivateData(
    {
      nanoid: v2_nanoid,
      us_based: !!strapiUser.usBased,
      us_work: !!strapiUser.usWork,
      tax_id: strapiUserTaxInfo?.ssn,
    },
    db,
  )
}

export const migrateUserClerk = async (
  v1_user: number,
  v2_nanoid: UserNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  const strapiUser = await v1StrapiUserById(v1_user, db)
  assert(strapiUser, 'Strapi user not found', '404')
  assert(strapiUser.authProviderId, 'Strapi user has no clerk id', '404')

  const clerkId = strapiUser.authProviderId
  await clerk.updateUser(clerkId, {
    publicMetadata: {
      user_nanoid: v2_nanoid,
    },
  })

  await setEntityMigrationStep(
    v1_user,
    'user',
    {
      migrated_clerk_ids: true,
    },
    db,
  )
}

export const migrateSumsub = async (
  v1_user: number,
  type: 'person' | 'company',
  v2_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  if (type === 'person') {
    const strapiUser = await v1StrapiUserById(v1_user, db)
    assert(strapiUser, 'Strapi user not found', '404')

    const strapiUserCompliance = await v1StrapiEntityCompliance(
      v1_user,
      type,
      db,
    )
    if (!strapiUserCompliance) return

    await upsertComplianceData(
      v2_nanoid,
      strapiUserCompliance.compliance_id,
      strapiUser.email,
      db,
    )
  }

  if (type === 'company') {
    const strapiCompany = await v1StrapiCompanyById(v1_user, db)
    assert(strapiCompany, 'Strapi company not found', '404')

    const strapiCompanyCompliance = await v1StrapiEntityCompliance(
      v1_user,
      type,
      db,
    )
    if (!strapiCompanyCompliance) return

    await upsertComplianceData(
      v2_nanoid,
      strapiCompanyCompliance.compliance_id,
      strapiCompany.id.toString(),
      db,
    )
  }

  await setEntityMigrationStep(
    v1_user,
    type === 'person' ? 'user' : 'company',
    {
      migrated_sumsub_ids: true,
    },
    db,
  )
}

const upsertCompanyData = async (
  data: InsertableCompaniesData,
  db = mainDB,
) => {
  using _ = getContext()
  await db
    .insertInto('rise.companies_data')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

const upsertTeamData = async (data: InsertableTeamsData, db = mainDB) => {
  using _ = getContext()
  await db
    .insertInto('rise.teams_data')
    .values(data)
    .onDuplicateKeyUpdate(data)
    .execute()
}

export const migrateCompanyData = async (
  v1_company: number,
  v2_nanoid: CompanyNanoid | TeamNanoid,
  riseid: AllRiseids,
  parent_riseid: AllRiseids,
  type: 'team' | 'company',
  parent_user_nanoid: UserNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  const entityMigration = await getV1EntityMigration(v1_company, type, db)
  assert(entityMigration, 'Entity migration not found', '404')

  const strapiCompany = await v1StrapiCompanyById(v1_company, db)
  assert(strapiCompany, 'Strapi company not found', '404')
  assert(strapiCompany.name, 'Strapi company name not found', '404')
  assert(strapiCompany.phone, 'Strapi company phone not found', '404')
  assert(
    strapiCompany.country &&
      strapiCompany.state &&
      strapiCompany.city &&
      strapiCompany.address1 &&
      strapiCompany.zip,
    'Strapi company address not found',
    '404',
  )

  await upsertEntityMigration(
    {
      nanoid: v2_nanoid,
      parent_riseid: parent_riseid,
      riseid,
      type,
      avatar:
        strapiCompany.avatar === '/images/company-empty.jpg'
          ? ''
          : (strapiCompany.avatar ?? ''),
    },
    db,
  )
  if (type === 'company') {
    await upsertCompanyData(
      {
        nanoid: v2_nanoid as CompanyNanoid,
        rise_account: entityMigration.rise_account as CompanyRiseAccount,
        name: strapiCompany.name,
        incorporation_country: strapiCompany.country ?? undefined,
        website:
          strapiCompany.website && is(strapiCompany.website, website)
            ? strapiCompany.website
            : undefined,
        phone:
          strapiCompany.phone && is(strapiCompany.phone, phone)
            ? strapiCompany.phone
            : '',
        is_dao: false,
        country: strapiCompany.country ?? undefined,
        state: strapiCompany.state ?? undefined,
        v1_id: strapiCompany.id.toString(),
      },
      db,
    )
    await upsertAddress(
      {
        nanoid: v2_nanoid as CompanyNanoid,
        country: strapiCompany.country,
        state: strapiCompany.state,
        city: strapiCompany.city,
        line_1: strapiCompany.address1,
        line_2: strapiCompany.address2 ?? undefined,
        zip_code: strapiCompany.zip,
      },
      db,
    )
    await upsertPrivateData(
      {
        nanoid: v2_nanoid as CompanyNanoid,
        us_based: !!strapiCompany.usBased,
        us_work: !!strapiCompany.usWork,
        tax_id: strapiCompany.tax_id,
      },
      db,
    )
    await upsertCompanySettings({
      nanoid: v2_nanoid as CompanyNanoid,
      default_signer: parent_user_nanoid as UserNanoid,
      enabled_document_types: JSON.stringify([]),
      enabled_payroll_programs: JSON.stringify([]),
    })
  }
  if (type === 'team') {
    await upsertTeamData({
      nanoid: v2_nanoid as TeamNanoid,
      rise_account: entityMigration.rise_account as TeamRiseAccount,
      name: strapiCompany.name,
    })
  }

  const companyConfig = await v1StrapiCompanyConfig(v1_company, db)
  if (companyConfig) {
    await upsertCompanySettings({
      nanoid: v2_nanoid as CompanyNanoid,
      invoicing_enabled: companyConfig.payee_invoices ?? undefined,
      enabled_document_types: JSON.stringify([]),
      anonymous_users: companyConfig.allow_payee_anonymous ?? undefined,
      enabled_payroll_programs: JSON.stringify([]),
    })
  }
}

export const migratePayerCompanyAccounts = async (
  v1_company: number,
  v2_nanoid: CompanyNanoid,
  db: DBTransaction,
) => {
  using _ = getContext()

  // Migrate only Routefusion virtual accounts

  const strapiCompany = await v1StrapiCompanyById(v1_company, db)
  assert(strapiCompany, 'Strapi company not found', '404')

  const virtualAccounts = await v1VirtualAccountsById(v1_company, db)
  for (const v of virtualAccounts) {
    assert(v.routefusion_account_id, 'Routefusion account does not have id')
  }

  await mapAsync(virtualAccounts, async (virtualAccount) => {
    assert(
      virtualAccount.routefusion_account_id,
      'Routefusion account does not have id',
    )
    await createExistingFundingAccount(
      v2_nanoid,
      virtualAccount.routefusion_account_name,
      virtualAccount.routefusion_account_id,
      db,
    )
  })
}

export const migrateWithdrawAccount = async (
  account_id: number,
  db: DBTransaction,
) => {
  using ctx = getContext()
  const { logger } = ctx

  logger.info('Loading account')
  const withdrawAccount = await v1ExternalAccountById(account_id, db)
  if (!withdrawAccount) return
  if (!withdrawAccount.user) return
  if (!withdrawAccount.type) return

  logger.info('Loading strapi user data')
  const strapiUser = await v1StrapiUserById(withdrawAccount.user, db)
  assert(strapiUser, 'Strapi user not found', '404')

  const getName = (full_name: string) => {
    const split = full_name.split(' ')
    if (!split[0])
      return {
        first_name: full_name,
        middle_name: null,
        last_name: null,
      }
    return {
      first_name: split[0],
      middle_name: split.length === 2 ? null : split[1],
      last_name: split.length === 2 ? split[1] : split[2],
    }
  }

  logger.info('Loading user migration')
  const userMigration = await getV1EntityMigration(
    withdrawAccount.user,
    'user',
    db,
  )
  assert(userMigration, 'User migration not found', '404')

  logger.info('Loading user data')
  const user = await getFullUserData(
    userMigration.entity_v2_nanoid as UserNanoid,
    db,
  )
  assert(user, `User ${userMigration.entity_v2_nanoid} not found`, '404')

  logger.info('Loading company migration')
  const companyMigration = withdrawAccount.company
    ? await getV1EntityMigration(withdrawAccount.company, 'company', db)
    : null

  if (withdrawAccount.type === 'eth_address') {
    if (!withdrawAccount.token) return
    const addressDataPayload = z.object({
      address: blockchainAddress,
      chainId: z.number(),
    })
    const { data } = addressDataPayload.safeParse(
      JSON.parse(withdrawAccount.data),
    )
    if (!data) return
    const network = getNetwork(data.chainId as ChainNumbers)
    await upsertBlockchainWithdrawAccount(
      {
        v1_id: withdrawAccount.id,
        account_name: withdrawAccount.name ?? 'Wallet',
        wallet_address: data.address,
        token_name: withdrawAccount.token,
        // @ts-ignore
        network,
        logo: withdrawAccount.logo ?? '',
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        company_nanoid: companyMigration?.entity_v2_nanoid as CompanyNanoid,
        status: v1ExternalAccountStatusTov2Status(withdrawAccount.status),
      },
      db,
    )
  }

  if (withdrawAccount.type === 'eth_address_mainnet') {
    if (!withdrawAccount.token) return
    const ethAddressMainnetPayload = z.object({
      address: blockchainAddress,
    })
    const { data } = ethAddressMainnetPayload.safeParse(withdrawAccount.data)
    if (!data) return
    await upsertBlockchainWithdrawAccount(
      {
        v1_id: withdrawAccount.id,
        account_name: withdrawAccount.name ?? 'Wallet',
        wallet_address: data.address,
        token_name: withdrawAccount.token,
        // @ts-ignore
        network: 'mainnet',
        logo: withdrawAccount.logo ?? '',
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        company_nanoid: companyMigration?.entity_v2_nanoid as CompanyNanoid,
        status: v1ExternalAccountStatusTov2Status(withdrawAccount.status),
      },
      db,
    )
  }

  if (withdrawAccount.type === 'USD') {
    const bankAccount = await v1BankAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!bankAccount) return // no bank account data
    const orumData = await v1RiseOrumAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!orumData) return // no orum account data

    if (!(bankAccount.account_number && bankAccount.routing_number)) return
    if (!bankAccount.beneficiary_name) return

    const { first_name, middle_name, last_name } = getName(
      bankAccount.beneficiary_name,
    )

    await upsertOrumWithdrawAccount(
      {
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        company_nanoid: companyMigration?.entity_v2_nanoid as CompanyNanoid,

        // Bank data
        v1_id: withdrawAccount.id,
        account_name: withdrawAccount.name ?? 'USD Account',
        account_number: bankAccount.account_number,
        routing_number: bankAccount.routing_number,
        logo: withdrawAccount.logo ?? '',

        // Orum Verification
        verification_external_id: orumData.verification_id ?? undefined,
        verification_status: orumData.verification_status ?? undefined,
        verification_status_reason:
          orumData.verification_status_reason ?? undefined,

        // Status
        status: v1ExternalAccountStatusTov2Status(withdrawAccount.status),

        // Beneficiary
        beneficiary_address_1: bankAccount.beneficiary_address_line1 || '',
        beneficiary_address_2:
          bankAccount.beneficiary_address_line2 ?? undefined,
        beneficiary_city: bankAccount.beneficiary_city || '',
        beneficiary_state: bankAccount.beneficiary_state || '',
        beneficiary_zip: bankAccount.beneficiary_postal_code || '',
        beneficiary_country: bankAccount.beneficiary_country_code || '',
        beneficiary_first_name: first_name,
        beneficiary_middle_name: middle_name ?? undefined,
        beneficiary_last_name: last_name ?? '',
        beneficiary_dob: user.dob ?? undefined,
        beneficiary_email: user.email ?? undefined,
        beneficiary_phone: user.phone ?? undefined,

        // Orum data
        orum_account_id: orumData.orum_account_id ?? undefined,
        orum_entity_id: orumData.orum_customer_id ?? undefined,
      },
      db,
    )
  }

  if (['FOREX', 'USD_INTERNATIONAL_AUTO'].includes(withdrawAccount.type)) {
    logger.info(`Processing ${withdrawAccount.type} account`)
    logger.info('Loading bank account data')
    const bankAccount = await v1BankAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!bankAccount) return // no bank account data

    logger.info('Loading routefusion account data')
    const rfData = await v1RiseRoutefusionAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!rfData) return // no routefusion account data

    logger.info('Checking if bank account is valid')
    if (!bankAccount.target_currency) return
    if (!(bankAccount.account_number && bankAccount.routing_number)) return
    if (!bankAccount.bank_name) return

    logger.info('Creating international account')
    await upsertInternationalAccount(
      {
        v1_id: withdrawAccount.id,
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        company_nanoid: companyMigration?.entity_v2_nanoid as CompanyNanoid,
        account_name: withdrawAccount.name ?? 'Forex Account',
        ramp:
          withdrawAccount.type === 'FOREX'
            ? 'international_exchange'
            : 'international_usd',
        currency: bankAccount.target_currency,
        status: v1ExternalAccountStatusTov2Status(withdrawAccount.status),
        account_number: bankAccount.account_number,
        routing_number: bankAccount.routing_number,
        bank_address_1: bankAccount.address_line1 || '',
        bank_address_2: bankAccount.address_line2 ?? undefined,
        bank_city: bankAccount.city || '',
        bank_state: bankAccount.state || '',
        bank_country: bankAccount.country_code || '',
        bank_name: bankAccount.bank_name,
        bank_postal_code: bankAccount.postal_code || '',
        provider_account_id: rfData.routefusion_beneficiary_id,
        provider_beneficiary_id: rfData.routefusion_beneficiary_id,
      },
      db,
    )
  }

  if (withdrawAccount.type === 'USD_INTERNATIONAL') {
    const bankAccount = await v1BankAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!bankAccount) return // no bank account data

    if (!bankAccount.target_currency) return
    if (!(bankAccount.account_number && bankAccount.routing_number)) return
    if (!bankAccount.bank_name) return
    if (!bankAccount.beneficiary_name) return

    const { first_name, middle_name, last_name } = getName(
      bankAccount.beneficiary_name,
    )

    await upsertInternationalUSDAccount(
      {
        v1_id: withdrawAccount.id,
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        company_nanoid: companyMigration?.entity_v2_nanoid as CompanyNanoid,
        account_name: withdrawAccount.name ?? 'International Account',
        status: 'active',
        currency: bankAccount.target_currency,
        account_number: bankAccount.account_number,
        routing_number: bankAccount.routing_number,
        bank_name: bankAccount.bank_name,
        bank_city: bankAccount.city || '',
        bank_country: bankAccount.country_code || '',
        bank_address_1: bankAccount.address_line1 || '',
        bank_address_2: bankAccount.address_line2 ?? undefined,
        bank_state: bankAccount.state || '',
        bank_postal_code: bankAccount.postal_code || '',
        beneficiary_first_name: first_name,
        beneficiary_middle_name: middle_name ?? undefined,
        beneficiary_last_name: last_name ?? '',
        beneficiary_address_1:
          bankAccount.beneficiary_address_line1 ?? undefined,
        beneficiary_address_2:
          bankAccount.beneficiary_address_line2 ?? undefined,
        beneficiary_city: bankAccount.beneficiary_city ?? undefined,
        beneficiary_state: bankAccount.beneficiary_state ?? undefined,
        beneficiary_postal_code:
          bankAccount.beneficiary_postal_code ?? undefined,
        beneficiary_country: bankAccount.beneficiary_country_code ?? undefined,
        beneficiary_dob: user.dob ?? undefined,
        beneficiary_email: user.email,
        beneficiary_phone: user.phone,
      },
      db,
    )
  }

  if (['GBP', 'NGN', 'EUR'].includes(withdrawAccount.type)) {
    const bankAccount = await v1BankAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!bankAccount) return // no bank account data
    const unblockData = await v1RiseUnblockAccountByExternalAccountId(
      withdrawAccount.id,
      db,
    )
    if (!unblockData) return // no unblock account data

    const unblockClient = await v1RiseUnblockClientByUserId(strapiUser.id, db)

    if (!unblockClient) return // no unblock client data
    if (!bankAccount.account_number) return
    if (!bankAccount.routing_number) return
    if (!unblockClient.wallet_data) return
    if (!unblockClient.unblock_id) return

    await upsertUnblockWithdrawAccount(
      {
        v1_id: withdrawAccount.id,
        account_name: withdrawAccount.name || 'International Account',
        currency: bankAccount.target_currency as 'EUR' | 'GBP' | 'NGN',
        account_number: bankAccount.account_number,
        routing_number: bankAccount.routing_number,
        status: v1ExternalAccountStatusTov2Status(withdrawAccount.status),
        user_nanoid: userMigration.entity_v2_nanoid as UserNanoid,
        wallet_data: unblockClient.wallet_data as string,
        provider_account_id: unblockData.unblock_account_id,
        provider_beneficiary_id: unblockClient.unblock_id,
      },
      db,
    )
  }
}

const migratePayeeWithdrawAccounts = async (
  v1_user: number,
  db: DBTransaction,
) => {
  using _ = getContext()

  const withdrawAccounts = await v1ExternalAccountsByUser(v1_user, db)
  await mapAsync(withdrawAccounts, (account) =>
    migrateWithdrawAccount(account.id, db),
  )
}

export const migrateRelationshipFromV1 = async (
  v1_company: number,
  v1_user: number,
  db: DBTransaction,
) => {
  using _ = getContext()

  const strapiCompany = await v1StrapiCompanyById(v1_company, db)
  assert(strapiCompany, 'Strapi company not found', '404')

  const strapiUser = await v1StrapiUserById(v1_user, db)
  assert(strapiUser, 'Strapi user not found', '404')
  assert(strapiUser.role, 'Strapi user role not found', '404')

  if (strapiCompany.owner === strapiUser.id) return // owner should be migrated and relationship already exists

  const userRole = await v1StrapiRole(strapiUser.role, db)
  assert(userRole, 'Strapi user role not found', '404')
  assert(userRole.type, 'Strapi user role not found', '404')

  const companyMigration = await getV1EntityMigration(
    strapiCompany.id,
    'company',
    db,
  )
  assert(companyMigration, 'Company migration not found', '404')

  const userMigration = await getV1EntityMigration(strapiUser.id, 'user', db)
  assert(userMigration, 'User migration not found', '404')

  const nanoidType =
    companyMigration.entity_type === 'team' ? 'team_role' : 'company_role'
  let type: RiseEntities['type']

  if (userRole.type === 'client') {
    type = companyMigration.entity_type === 'team' ? 'team_admin' : 'org_admin'
  } else {
    type = 'contractor'
  }

  await upsertEntityMigration(
    {
      nanoid: nanoid(nanoidType),
      type,
      parent_riseid: userMigration.riseid as AllRiseids,
      riseid: companyMigration.riseid as AllRiseids,
    },
    db,
  )

  if (type === 'contractor') {
    // deploy payment handler
    await createRiseId({
      owner_address: userMigration.riseid,
      parent_account: userMigration.rise_account,
      type: 'pay_handler',
      network: 'arbitrum',
      purpose: 'payment',
      db,
    })
  }
}

const migrateCompanyMembers = async (
  v1_company: number,
  v2_nanoid: CompanyNanoid,
  db: DBTransaction,
) => {
  using _ = getContext()

  const members = await v1StrapiCompanyMembers(v1_company, ['client'], db)
  await mapAsync(members, async (member) => {
    if (!member.user_id) return
    await migrateUserFromV1(member.user_id, db)
    await migrateRelationshipFromV1(v1_company, member.user_id, db)
  })
}

export const migrateCompanyFromV1 = async (
  v1_company: number,
  v1_owner_id: number,
  owner_type: 'user' | 'company',
  type: 'team' | 'company',
  db: DBTransaction,
) => {
  using _ = getContext()

  const strapiCompany = await v1StrapiCompanyById(v1_company, db)
  assert(strapiCompany, 'Strapi company not found', '404')
  assert(strapiCompany.owner, 'Strapi company owner not found', '404')

  const ownerMigration = await getV1EntityMigration(v1_owner_id, owner_type, db)
  if (!ownerMigration) return // owner not migrated yet

  const entityMigration = await getOrCreateV1EntityMigration(
    v1_company,
    type,
    ownerMigration.riseid,
    v1_owner_id,
    owner_type,
    null,
    null,
    null,
    db,
  )
  assert(entityMigration, 'Entity migration not found', '404')

  await migrateCompanyData(
    entityMigration.entity_v1_id,
    entityMigration.entity_v2_nanoid as CompanyNanoid,
    entityMigration.riseid as CompanyRiseid,
    ownerMigration.riseid as CompanyRiseid,
    type,
    ownerMigration.entity_v2_nanoid as UserNanoid,
    db,
  )

  if (!entityMigration.migrated_sumsub_ids && type === 'company')
    await migrateSumsub(
      v1_company,
      'company',
      entityMigration.entity_v2_nanoid as CompanyNanoid,
    )

  await migratePayerCompanyAccounts(
    v1_company,
    entityMigration.entity_v2_nanoid as CompanyNanoid,
    db,
  )

  await migrateCompanyMembers(
    v1_company,
    entityMigration.entity_v2_nanoid as CompanyNanoid,
    db,
  )
}

export const migrateUserCompanies = async (
  v1_user: number,
  db: DBTransaction,
) => {
  using _ = getContext()

  const strapiUser = await v1StrapiUserById(v1_user, db)
  assert(strapiUser, 'Strapi user not found', '404')

  const strapiCompanies = await v1StrapiCompaniesByOwner(v1_user, true, db)
  const masterCompany = strapiCompanies?.[0]
  if (!masterCompany) return // no companies

  // migrate master company as company
  await migrateCompanyFromV1(
    masterCompany.id,
    strapiUser.id,
    'user',
    'company',
    db,
  )
  // migrate teams as team
  await mapAsync(strapiCompanies.slice(1), async (company) =>
    migrateCompanyFromV1(company.id, masterCompany.id, 'company', 'team', db),
  )
}

export const migrateUserFromV1 = async (user: number, db: DBTransaction) => {
  using _ = getContext()

  const strapiUser = await v1StrapiUserById(user, db)
  assert(strapiUser, 'Strapi user not found', '404')

  const entityMigration = await getOrCreateV1EntityMigration(
    user,
    'user',
    null_address,
    null,
    null,
    null,
    null,
    null,
    db,
  )
  assert(entityMigration, 'Entity migration not found', '404')

  await migrateUserData(
    entityMigration.entity_v1_id,
    entityMigration.entity_v2_nanoid as UserNanoid,
    entityMigration.riseid as UserRiseid,
    db,
  )

  if (!entityMigration.migrated_clerk_ids)
    await migrateUserClerk(
      entityMigration.entity_v1_id,
      entityMigration.entity_v2_nanoid as UserNanoid,
      db,
    )

  if (!entityMigration.migrated_sumsub_ids)
    await migrateSumsub(
      entityMigration.entity_v1_id,
      'person',
      entityMigration.entity_v2_nanoid as UserNanoid,
      db,
    )

  await migrateUserCompanies(entityMigration.entity_v1_id, db)

  await migratePayeeWithdrawAccounts(strapiUser.id, db)
}

export const getEntityV1MigratedData = async (
  entity_nanoid: UserNanoid | CompanyNanoid | TeamNanoid,
  db: typeof mainDB,
) => {
  const result = await db
    .selectFrom('rise.v1_v2_migration_entity')
    .select(['migrated_completed'])
    .where('entity_v2_nanoid', '=', entity_nanoid)
    .executeTakeFirst()
  return result
}

export const getV1UsersWithMigrationStatus = async (
  page_index: number,
  page_size: number,
  search: string | undefined,
  sort_by: string | undefined,
  sort_dir: string | undefined,
  db = mainDB,
) => {
  using _ = getContext()

  const fullNameExpr = sql<string>`COALESCE(CONCAT(u.firstname, ' ', u.lastname), u.username)`
  const riseIdCreatedExpr = sql<boolean>`CASE WHEN m.riseid IS NOT NULL AND m.riseid != '' THEN TRUE ELSE FALSE END`
  const riseAccountCreatedExpr = sql<boolean>`CASE WHEN m.rise_account IS NOT NULL AND m.rise_account != '' THEN TRUE ELSE FALSE END`

  const companyRelationshipIdExpr = sql<number | null>`
  CASE 
    WHEN m.company_relationship_id IS NULL THEN c.company_id 
    ELSE m.company_relationship_id 
  END
`

  const companyIdSubquery = db
    .selectFrom('strapi.companies_users__users_companies as c')
    .select(['c.user_id', sql<number>`MIN(c.company_id)`.as('min_company_id')])
    .groupBy('c.user_id')

  let companyQuery = db
    .selectFrom('strapi.users-permissions_user as u')
    .innerJoin('strapi.users-permissions_role as r', 'u.role', 'r.id')
    .innerJoin(companyIdSubquery.as('min_c'), 'u.id', 'min_c.user_id')
    .leftJoin('rise.v1_v2_migration_entity as m', (join) =>
      join
        .onRef('u.id', '=', 'm.entity_v1_id')
        .on('m.entity_type', '=', 'user'),
    )
    .leftJoin('strapi.companies_users__users_companies as c', (join) =>
      join
        .onRef('u.id', '=', 'c.user_id')
        .onRef('c.company_id', '=', 'min_c.min_company_id'),
    )
    .leftJoin('strapi.companies as com', 'com.id', 'c.company_id')
    .where('r.type', '=', 'client')
    .whereRef('com.owner', '=', 'u.id')
    .where('c.company_id', 'is not', null)
    .where('u.onboarding', 'is not', null)

  if (search) {
    companyQuery = companyQuery.where((eb) =>
      eb.or([
        eb('u.email', 'like', `%${search}%`),
        eb('u.firstname', 'like', `%${search}%`),
        eb('u.lastname', 'like', `%${search}%`),
      ]),
    )
  }

  const companySelectData = companyQuery.select([
    'u.id as user_id',
    fullNameExpr.as('full_name'),
    companyRelationshipIdExpr.as('company_relationship_id'),
    sql<string | null>`'company'`.as('company_relationship_type'),
    'm.migration_status',
    sql<boolean>`COALESCE(m.migrated_completed, false)`.as(
      'migrated_completed',
    ),
    'm.failure_reason',
    'm.failure_step',
    'm.riseid',
    'm.rise_account',
    'm.entity_type',
    sql`NULL`.as('owner_id'),
    sql`NULL`.as('owner_type'),
    riseIdCreatedExpr.as('rise_id_created'),
    riseAccountCreatedExpr.as('rise_account_created'),
    sql<string | null>`com.name`.as('company_name'),
    'u.email',
    'm.inngest_event_id',
  ])

  let teamQuery = db
    .selectFrom('strapi.users-permissions_user as u')
    .innerJoin('strapi.users-permissions_role as r', 'u.role', 'r.id')
    .leftJoin('rise.v1_v2_migration_entity as m', (join) =>
      join
        .onRef('u.id', '=', 'm.entity_v1_id')
        .on('m.entity_type', '=', 'user'),
    )
    .leftJoin(
      'strapi.companies_users__users_companies as c',
      'u.id',
      'c.user_id',
    )
    .leftJoin('strapi.companies as com', 'com.id', 'c.company_id')
    .where('r.type', 'in', ['contractor', 'authenticated'])
    .whereRef('com.owner', '!=', 'u.id')
    .where('c.company_id', 'is not', null)
    .where('u.onboarding', 'is not', null)

  if (search) {
    teamQuery = teamQuery.where((eb) =>
      eb.or([
        eb('u.email', 'like', `%${search}%`),
        eb('u.firstname', 'like', `%${search}%`),
        eb('u.lastname', 'like', `%${search}%`),
      ]),
    )
  }

  const teamSelectData = teamQuery.select([
    'u.id as user_id',
    fullNameExpr.as('full_name'),
    companyRelationshipIdExpr.as('company_relationship_id'),
    sql<string | null>`'team'`.as('company_relationship_type'),
    'm.migration_status',
    sql<boolean>`COALESCE(m.migrated_completed, false)`.as(
      'migrated_completed',
    ),
    'm.failure_reason',
    'm.failure_step',
    'm.riseid',
    'm.rise_account',
    'm.entity_type',
    sql`NULL`.as('owner_id'),
    sql`NULL`.as('owner_type'),
    riseIdCreatedExpr.as('rise_id_created'),
    riseAccountCreatedExpr.as('rise_account_created'),
    sql<string | null>`com.name`.as('company_name'),
    'u.email',
    'm.inngest_event_id',
  ])

  let userWithoutTeamOrComapnyQuery = db
    .selectFrom('strapi.users-permissions_user as u')
    .innerJoin('strapi.users-permissions_role as r', 'u.role', 'r.id')
    .leftJoin('rise.v1_v2_migration_entity as m', (join) =>
      join
        .onRef('u.id', '=', 'm.entity_v1_id')
        .on('m.entity_type', '=', 'user'),
    )
    .where('r.type', 'in', ['authenticated'])
    .where(
      'u.id',
      'not in',
      db
        .selectFrom('strapi.companies_users__users_companies as c')
        .select('c.user_id'),
    )
    .where('u.onboarding', 'is not', null)

  if (search) {
    userWithoutTeamOrComapnyQuery = userWithoutTeamOrComapnyQuery.where((eb) =>
      eb.or([
        eb('u.email', 'like', `%${search}%`),
        eb('u.firstname', 'like', `%${search}%`),
        eb('u.lastname', 'like', `%${search}%`),
      ]),
    )
  }

  const userWithoutTeamOrComapnySelectData =
    userWithoutTeamOrComapnyQuery.select([
      'u.id as user_id',
      fullNameExpr.as('full_name'),
      sql<number | null>`NULL`.as('company_relationship_id'),
      sql<string | null>`NULL`.as('company_relationship_type'),
      'm.migration_status',
      sql<boolean>`COALESCE(m.migrated_completed, false)`.as(
        'migrated_completed',
      ),
      'm.failure_reason',
      'm.failure_step',
      'm.riseid',
      'm.rise_account',
      'm.entity_type',
      sql`NULL`.as('owner_id'),
      sql`NULL`.as('owner_type'),
      riseIdCreatedExpr.as('rise_id_created'),
      riseAccountCreatedExpr.as('rise_account_created'),
      sql<string | null>`NULL`.as('company_name'),
      'u.email',
      'm.inngest_event_id',
    ])

  const allUsersQuery = companySelectData
    .unionAll(teamSelectData)
    .unionAll(userWithoutTeamOrComapnySelectData)

  const filteredQuery = db
    .with('all_users', (db) => allUsersQuery)
    .selectFrom('all_users')

  const orderClause =
    sort_dir === 'asc'
      ? sql`${sql.ref(sort_by || 'company_relationship_id')} is null, ${sql.ref(sort_by || 'company_relationship_id')} asc`
      : sql`${sql.ref(sort_by || 'company_relationship_id')} is not null, ${sql.ref(sort_by || 'company_relationship_id')} desc`

  const data = (
    (await filteredQuery
      .selectAll()
      .limit(page_size)
      .offset(page_index * page_size)
      .orderBy(orderClause)
      .execute()) as {
      user_id: number
      full_name: string | null
      company_relationship_id: number | null
      company_relationship_type: 'team' | 'company' | null
      migration_status: 'in_progress' | 'completed' | 'failed' | null
      migrated_completed: boolean | null
      failure_reason: string | null
      riseid: string | null
      rise_account: string | null
      owner_id: number | null
      owner_type: 'company' | 'user' | null
      rise_id_created: boolean
      rise_account_created: boolean
      failure_step: string | null
      entity_type: 'company' | 'team' | 'user' | null
      company_name: string | null
      email: string
      inngest_event_id: string | null
    }[]
  ).map((row) => ({
    ...row,
    rise_id_created: Boolean(row.rise_id_created),
    rise_account_created: Boolean(row.rise_account_created),
    migrated_completed: Boolean(row.migrated_completed),
  }))

  const totalRecordsResult = await filteredQuery
    .clearSelect()
    .select((eb) => eb.fn.countAll().as('count'))
    .executeTakeFirstOrThrow()

  const totalRecords = Number(totalRecordsResult.count)

  return {
    page_index,
    page_size,
    total_records: totalRecords,
    data,
  }
}
