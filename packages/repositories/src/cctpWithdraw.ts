import type {
  TransactionNanoid,
  WithdrawNanoid,
} from '@riseworks/contracts/src/brands.js'
import type { InsertableCctpWithdrawals } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import axios from 'axios'
import { db as mainDB } from 'db/src/index.js'
import { ethers } from 'ethers'
import assert from 'utils/src/common/assertHTTP.js'
import { getContext } from 'utils/src/common/requestContext.js'
import type { ValidNetworks } from './smartContracts.js'

// CCTP Domain IDs
export const CCTP_DOMAINS: Record<ValidNetworks | 'ethereum', number> = {
  arbitrum: 3,
  base: 6,
  avalanche: 1,
  optimism: 2,
  polygon: 7,
  ethereum: 0,
}

const MessageTransmitterABI = [
  'function receiveMessage(bytes message, bytes attestation) external returns (bool success)',
]
interface WithdrawResult {
  txHash?: string
  nonce?: string
  destinationDomain?: number
  success: boolean
  error?: string
}

/**
 * Interface for CCTP complete parameters
 */
interface CCTPCompleteParams {
  signer: ethers.Signer
  network: string
}

/**
 * Get all pending CCTP withdrawals
 * @returns Promise with all pending CCTP withdrawals
 */
export const getPendingCctpWithdrawals = async () => {
  using _ = getContext()

  return await mainDB
    .selectFrom('rise.cctp_withdrawals')
    .selectAll()
    .where('status', '=', 'pending')
    .execute()
}

/**
 * Complete a CCTP transfer on the destination chain
 *
 * @param params Parameters to complete the CCTP transfer
 * @returns Promise with the transaction result
 */
export const cctpComplete = async (
  params: CCTPCompleteParams,
): Promise<WithdrawResult> => {
  const ctx = getContext()

  const { signer, network } = params

  // Message transmitter addresses per network
  const MESSAGE_TRANSMITTERS: Record<string, string> = {
    arbitrum_sepolia: '******************************************',
    base_sepolia: '******************************************',
    // Add more networks as needed
  }

  // Get message transmitter address for current network
  const messageTransmitterAddress = MESSAGE_TRANSMITTERS[network]

  assert(
    messageTransmitterAddress,
    `No message transmitter address configured for ${network}`,
  )

  ctx.logger.info(
    `Using MessageTransmitter at ${messageTransmitterAddress} on ${network}`,
  )

  // Fetch all pending transactions
  const pendingWithdrawals = await getPendingCctpWithdrawals()
  ctx.logger.info(`Found ${pendingWithdrawals.length} pending withdrawals`)

  // Process each pending transaction
  for (const withdrawal of pendingWithdrawals) {
    try {
      ctx.logger.info(
        `Processing withdrawal ${withdrawal.nanoid} with tx hash ${withdrawal.tx_hash}`,
      )

      // Fetch message and attestation from Circle API
      ctx.logger.info(
        `Fetching message and attestation from Circle for transaction ${withdrawal.tx_hash} (source domain: ${withdrawal.domain})...`,
      )

      const response = await axios.get(
        `https://iris-api-sandbox.circle.com/v1/messages/${withdrawal.domain}/${withdrawal.tx_hash}`,
      )

      const hasValidResponse = response.data?.messages?.length > 0

      assert(hasValidResponse, 'No messages found in Circle API response')

      const messageData = response.data.messages[0]

      const hasRequiredData = messageData.message && messageData.attestation

      assert(
        hasRequiredData,
        'Missing message data or attestation in Circle API response',
      )

      assert(
        messageData.attestation !== 'PENDING',
        'Attestation is still pending. Please try again in a few minutes.',
      )

      const messageBytes = messageData.message
      const attestation = messageData.attestation
      // Initialize MessageTransmitter contract
      const messageTransmitter: ethers.Contract = new ethers.Contract(
        messageTransmitterAddress,
        MessageTransmitterABI,
        signer,
      )

      assert(
        messageTransmitter,
        'Message transmitter contract is not configured',
      )

      // Call receiveMessage
      ctx.logger.info(
        `Calling receiveMessage for withdrawal ${withdrawal.nanoid} on ${network}...`,
      )

      assert(
        messageTransmitter.receiveMessage,
        'receiveMessage method not found on messageTransmitter contract',
      )

      const receiveTx = await messageTransmitter.receiveMessage(
        messageBytes,
        attestation,
      )

      // Update transaction status to complete
      await updateCctpWithdrawAttestation(withdrawal.nanoid, 'complete')

      ctx.logger.info(
        `Successfully completed withdrawal ${withdrawal.nanoid} with tx hash ${receiveTx.hash}`,
      )
    } catch (error) {
      ctx.logger.error(
        `Error processing withdrawal ${withdrawal.nanoid}:`,
        error,
      )
      // Update transaction status to failed in case of error
      await updateCctpWithdrawAttestation(withdrawal.nanoid, 'failed')
    }
  }

  return {
    success: true,
  }
}

/**
 * Interface for monitoring attestation parameters
 */
interface CCTPMonitorAttestationParams {
  sourceTx: string
  sourceDomain: number
  maxAttempts?: number
}

/**
 * Interface for attestation monitoring results
 */
interface AttestationResult {
  message?: string
  attestation?: string
  eventNonce?: string
  error?: string
  success: boolean
}

export const createCCTPWithdraw = async (
  withdrawals: InsertableCctpWithdrawals,
) => {
  using _ = getContext()

  await mainDB
    .insertInto('rise.cctp_withdrawals')
    .values(withdrawals)
    .onDuplicateKeyUpdate(withdrawals)
    .execute()
}

/**
 * Monitors the attestation status of a CCTP transaction
 *
 * @param params Parameters to monitor the attestation
 * @returns Promise with the attestation result
 */
export const cctpMonitorAttestation = async (
  params: CCTPMonitorAttestationParams,
): Promise<AttestationResult> => {
  const { sourceTx, sourceDomain, maxAttempts = 30 } = params

  const ctx = getContext()

  ctx.logger.info(
    `Monitoring attestation for transaction ${sourceTx} (source domain: ${sourceDomain})...`,
  )
  let isPending = true
  let attempts = 0

  while (isPending && attempts < maxAttempts) {
    const response = await axios.get(
      `https://iris-api-sandbox.circle.com/v1/messages/${sourceDomain}/${sourceTx}`,
    )

    assert(
      response.data?.messages && response.data.messages.length > 0,
      'No messages found in Circle API response',
    )

    const messageData = response.data.messages[0]

    if (messageData.attestation === 'PENDING') {
      ctx.logger.info(
        `Trying ${attempts + 1}/${maxAttempts}: Attestation still pending...`,
      )
      await new Promise((resolve) => setTimeout(resolve, 10000)) // Wait 10 seconds
      attempts++
    } else {
      isPending = false
      ctx.logger.info('\n========== ATTESTATION READY ==========')
      ctx.logger.info('Attestation received!')
      ctx.logger.info(`Message: ${messageData.message}`)
      ctx.logger.info(`Attestation: ${messageData.attestation}`)
      ctx.logger.info(`Event Nonce: ${messageData.eventNonce}`)

      return {
        message: messageData.message,
        attestation: messageData.attestation,
        eventNonce: messageData.eventNonce,
        success: true,
      }
    }
  }

  if (isPending) {
    ctx.logger.info('\n========== TIMEOUT ==========')
    ctx.logger.info(
      'Maximum wait time exceeded. The attestation is still pending.',
    )
    return {
      error: 'Timeout - Attestation still pending after 5 minutes',
      success: false,
    }
  }

  // This should never happen due to the while loop logic, but TypeScript requires a return
  return {
    error: 'Unknown error',
    success: false,
  }
}

/**
 * Update the status of a CCTP withdraw
 * @param nanoid The nanoid of the CCTP withdraw
 * @param status The new status
 * @returns Promise<void>
 */
export const updateCctpWithdrawStatus = async (
  nanoid: TransactionNanoid,
  status: 'pending' | 'complete' | 'failed',
  attestation?: string,
) => {
  using _ = getContext()

  const updateObj = {
    status,
    tx_nanoid: nanoid,
  }

  assert(attestation, 'Attestation is required')

  // First, get the nanoid from cctp_withdrawals
  const cctpWithdraw = await mainDB
    .selectFrom('rise.cctp_withdrawals')
    .select('nanoid')
    .where('attestation', '=', attestation)
    .executeTakeFirst()

  assert(cctpWithdraw?.nanoid, 'CCTP withdrawal not found')

  // Atualizar cctp_withdrawals
  const query = mainDB
    .updateTable('rise.cctp_withdrawals')
    .set(updateObj)
    .where('attestation', '=', attestation)

  await query.execute()

  await mainDB
    .updateTable('rise.withdrawals')
    .set({ status: 'completed' })
    .where('nanoid', '=', cctpWithdraw.nanoid)
    .execute()
}

/**
 * Update the attestation of a CCTP withdraw
 * @param nanoid The nanoid of the CCTP withdraw
 * @param attestation The attestation data
 * @param tx_nanoid The transaction nanoid
 * @returns Promise<void>
 */
export const updateCctpWithdrawAttestation = async (
  nanoid: WithdrawNanoid,
  attestation: string,
  tx_nanoid?: TransactionNanoid,
) => {
  using _ = getContext()

  const updateData: { attestation: string; tx_nanoid?: TransactionNanoid } = {
    attestation,
  }

  if (tx_nanoid) {
    updateData.tx_nanoid = tx_nanoid
  }

  await mainDB
    .updateTable('rise.cctp_withdrawals')
    .set(updateData)
    .where('nanoid', '=', nanoid)
    .execute()
}

/**
 * Get a CCTP withdraw by nanoid
 * @param nanoid The nanoid of the CCTP withdraw
 * @returns Promise with the CCTP withdraw data
 */
export const getCctpWithdraw = async (nanoid: WithdrawNanoid) => {
  using _ = getContext()

  return await mainDB
    .selectFrom('rise.cctp_withdrawals')
    .selectAll()
    .where('nanoid', '=', nanoid)
    .executeTakeFirst()
}

/**
 * Get a CCTP withdraw by transaction hash
 * @param txHash The transaction hash
 * @returns Promise with the CCTP withdraw data
 */
export const getCctpWithdrawByTxHash = async (txHash: string) => {
  using _ = getContext()

  return await mainDB
    .selectFrom('rise.cctp_withdrawals')
    .selectAll()
    .where('tx_hash', '=', txHash)
    .executeTakeFirst()
}
