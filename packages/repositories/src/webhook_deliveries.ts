import type {
  WebhookDeliveryNanoid,
  WebhookEndpointNanoid,
  WebhookEventNanoid,
} from '@riseworks/contracts/src/brands.js'
import { db as mainDB } from 'db/src/index.js'
import assert from 'utils/src/common/assertHTTP.js'
import { nanoid as _nanoid } from 'utils/src/common/nanoid.js'
import { getContext } from 'utils/src/common/requestContext.js'

export const createWebhookDelivery = async (
  {
    webhook_nanoid,
    event_nanoid,
    status = 'queued',
  }: {
    webhook_nanoid: WebhookEndpointNanoid
    event_nanoid: WebhookEventNanoid
    status?: 'queued' | 'success' | 'failed' | 'retrying'
  },
  db = mainDB,
): Promise<WebhookDeliveryNanoid> => {
  using _ = getContext()

  // First check if the webhook endpoint exists and is not removed
  const webhook = await db
    .selectFrom('rise.webhook_endpoints')
    .select(['nanoid'])
    .where('nanoid', '=', webhook_nanoid)
    .where('is_removed', '=', false)
    .executeTakeFirst()

  assert(webhook, `Webhook endpoint ${webhook_nanoid} not found`, '404')

  const nanoid = _nanoid('webhook_delivery')

  await db
    .insertInto('rise.webhook_deliveries')
    .values({
      nanoid,
      webhook_nanoid,
      event_nanoid,
      status,
    })
    .execute()

  return nanoid
}

export const getWebhookDeliveryById = async (
  id: WebhookDeliveryNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  // Join with webhook_endpoints to ensure we only return deliveries for non-removed webhooks
  return await db
    .selectFrom('rise.webhook_deliveries')
    .innerJoin(
      'rise.webhook_endpoints',
      'rise.webhook_deliveries.webhook_nanoid',
      'rise.webhook_endpoints.nanoid',
    )
    .selectAll('rise.webhook_deliveries')
    .where('rise.webhook_deliveries.nanoid', '=', id)
    .where('rise.webhook_endpoints.is_removed', '=', false)
    .executeTakeFirst()
}

export const getWebhookDeliveriesByWebhook = async (
  webhook_nanoid: WebhookEndpointNanoid,
  {
    status,
    cursor,
    limit = 50,
  }: {
    status?: 'queued' | 'success' | 'failed' | 'retrying'
    cursor?: string
    limit?: number
  } = {},
  db = mainDB,
) => {
  using _ = getContext()

  // First check if the webhook endpoint exists and is not removed
  const webhook = await db
    .selectFrom('rise.webhook_endpoints')
    .select(['nanoid'])
    .where('nanoid', '=', webhook_nanoid)
    .where('is_removed', '=', false)
    .executeTakeFirst()

  assert(webhook, `Webhook endpoint ${webhook_nanoid} not found`, '404')

  let query = db
    .selectFrom('rise.webhook_deliveries')
    .selectAll()
    .where('webhook_nanoid', '=', webhook_nanoid)
    .orderBy('created_at', 'desc')

  if (status) {
    query = query.where('status', '=', status)
  }

  if (cursor) {
    query = query.where('created_at', '<', new Date(cursor))
  }

  return await query.limit(limit + 1).execute()
}

export const updateWebhookDeliveryStatus = async (
  id: WebhookDeliveryNanoid,
  {
    status,
    response_code,
    error_message,
    response_body,
  }: {
    status: 'queued' | 'success' | 'failed' | 'retrying'
    response_code?: number | null
    error_message?: string | null
    response_body?: Record<
      string,
      string | number | boolean | object | null
    > | null
  },
  db = mainDB,
) => {
  using _ = getContext()

  // First check if the delivery exists and its webhook is not removed
  const delivery = await db
    .selectFrom('rise.webhook_deliveries')
    .innerJoin(
      'rise.webhook_endpoints',
      'rise.webhook_deliveries.webhook_nanoid',
      'rise.webhook_endpoints.nanoid',
    )
    .select(['rise.webhook_deliveries.nanoid'])
    .where('rise.webhook_deliveries.nanoid', '=', id)
    .where('rise.webhook_endpoints.is_removed', '=', false)
    .executeTakeFirst()

  assert(delivery, `Webhook delivery ${id} not found`, '404')

  const result = await db
    .updateTable('rise.webhook_deliveries')
    .set({
      status,
      response_code,
      error_message,
      response_body: response_body ? JSON.stringify(response_body) : null,
      updated_at: new Date(),
    })
    .where('nanoid', '=', id)
    .execute()

  return result
}

export const retryWebhookDelivery = async (
  delivery_id: WebhookDeliveryNanoid,
  db = mainDB,
) => {
  using _ = getContext()

  const delivery = await getWebhookDeliveryById(delivery_id, db)
  assert(delivery, `Webhook delivery ${delivery_id} not found`, '404')

  // Reset the delivery to queued status for retry
  await updateWebhookDeliveryStatus(
    delivery_id,
    {
      status: 'retrying',
      error_message: null,
      response_code: null,
      response_body: null,
    },
    db,
  )

  return delivery
}
