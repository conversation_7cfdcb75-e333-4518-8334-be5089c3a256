import type { CompanyNanoid } from '@riseworks/contracts/src/brands.js'
import crc32 from 'crc/crc32'
import converter from 'us-state-converter'
import type { MasterTaxField } from './masterTaxSpec.js'

// Map of Symmetry code prefixes to MasterTax state abbreviations
const stateMap: Record<string, string> = {
  '00': 'FE',
  '01': 'AL',
  '02': 'AK',
  '04': 'AZ',
  '05': 'AR',
  '06': 'CA',
  '08': 'CO',
  '09': 'CT',
  '10': 'DE',
  '11': 'DC',
  '12': 'FL',
  '13': 'GA',
  '15': 'HI',
  '16': 'ID',
  '17': 'IL',
  '18': 'IN',
  '19': 'IA',
  '20': 'KS',
  '21': 'KY',
  '22': 'LA',
  '23': 'ME',
  '24': 'MD',
  '25': 'MA',
  '26': 'MI',
  '27': 'MN',
  '28': 'MS',
  '29': 'MO',
  '30': 'MT',
  '31': 'NE',
  '32': 'NV',
  '33': 'NH',
  '34': 'NJ',
  '35': 'NM',
  '36': 'NY',
  '37': 'NC',
  '38': 'ND',
  '39': 'OH',
  '40': 'OK',
  '41': 'OR',
  '42': 'PA',
  '44': 'RI',
  '45': 'SC',
  '46': 'SD',
  '47': 'TN',
  '48': 'TX',
  '49': 'UT',
  '50': 'VT',
  '51': 'VA',
  '53': 'WA',
  '54': 'WV',
  '55': 'WI',
  '56': 'WY',
  // U.S. Territories
  '60': 'AS',
  '66': 'GU',
  '69': 'MP',
  '72': 'PR',
  '78': 'VI',
}

export const formatMasterTaxValue = (
  value: string | Date | null | undefined,
  field: MasterTaxField,
): string => {
  // Handle null or undefined by returning padded blank space
  if (value === null || value === undefined) {
    return ' '.repeat(field.size)
  }

  // Handle Date
  if (value instanceof Date) {
    const yyyy = value.getUTCFullYear().toString()
    const mm = (value.getUTCMonth() + 1).toString().padStart(2, '0')
    const dd = value.getUTCDate().toString().padStart(2, '0')
    return `${yyyy}${mm}${dd}`
  }

  // Handle Amounts
  if (field.type === 'Amount') {
    const sign = Number.parseFloat(value) >= 0 ? '+' : '-'
    const [whole, decimal] = value.split('.')
    const wholePadded = whole
      ? whole.padStart(field.size - 3, '0') // for 2 decimal points & sign
      : ''.padStart(field.size - 3, '0') // for 2 decimal points & sign
    const decimal_sig_digits = decimal ? decimal.slice(0, 2) : '00'
    return `${wholePadded}${decimal_sig_digits}${sign}`
  }

  // Handle Numeric (as numbers)
  if (field.type === 'Numeric') {
    if (typeof value === 'string') {
      // this might be a special case of string to number mapping
      const numeric_value = mapDatabaseSchematoMasterTax(String(value))
      if (numeric_value !== value) {
        return numeric_value
      }
      // splt if it has a decimal
      const [whole, decimal] = value.split('.')
      // if it has a decimal, pad the whole number to 2 digits and the decimal to 4 digits
      const wholePadded = whole ? whole.padStart(2, '0') : '00'
      if (decimal) {
        let decimalPadded = decimal
        if (decimal.length > 4) {
          // if the decimal is more than 4 digits, truncate it to 4 digits
          decimalPadded = decimal.slice(0, 4)
        } else {
          decimalPadded = decimal.padEnd(4, '0')
        }

        return `${wholePadded}${decimalPadded}`
      }
    }
    return value.toString().padStart(field.size, '0')
  }

  // Handle strings
  const raw = mapDatabaseSchematoMasterTax(String(value))

  const padChar = ' '

  const padded =
    field.justification === 'right'
      ? raw.padStart(field.size, padChar)
      : raw.padEnd(field.size, padChar)

  return padded.length > field.size ? padded.slice(0, field.size) : padded
}

export const formatter = (
  record: Record<string, string | number | Date | null | undefined>,
  spec: MasterTaxField[],
): string => {
  // Convert to fixed-width line
  const fixedWidthLine = spec
    .map((field) =>
      formatMasterTaxValue(
        (record as Record<string, string | Date | null | undefined>)[
          field.field
        ],
        field,
      ),
    )
    .join('')

  return fixedWidthLine
}

export const companyNanoidToPayrollCode = (
  company_nanoid: CompanyNanoid,
): string => {
  // convert company_nanoid to string
  const company_nanoid_str = company_nanoid.toString()
  // convert to payroll code by removing the first 3 characters and grabbing the first 10 characters
  const payroll_code = company_nanoid_str.slice(3, 13)
  return payroll_code
}

export const processingTimeFromDate = (date: Date): string => {
  const hours = String(date.getUTCHours()).padStart(2, '0')
  const minutes = String(date.getUTCMinutes()).padStart(2, '0')
  const seconds = String(date.getUTCSeconds()).padStart(2, '0')

  return `${hours}${minutes}${seconds}`
}

export const getStateAbbreviation = (state: string): string => {
  const stateInfo = converter(state)
  if (typeof stateInfo === 'string') {
    throw new Error(`State not found for ${state}`)
  }
  return stateInfo.usps
}

/**
 * Generate a unique, 20-character MasterTax code from a Symmetry code.
 * Infers state from the first two digits of the Symmetry code.
 * Federal codes (prefix "00") map to "FE".
 */
export const symmetryToMasterTaxCodeMapper = (
  symmetry_tax_id: string,
): string => {
  // Derive state abbreviation
  const prefix = symmetry_tax_id.split('-', 1)[0]
  if (!prefix || prefix.length !== 2) {
    throw new Error(`Invalid Symmetry tax ID: '${symmetry_tax_id}'`)
  }
  const state = stateMap[prefix]
  if (!state) {
    throw new Error(`Unknown Symmetry state prefix: '${prefix}'`)
  }

  // Extract & sanitize the type segment (4th token)
  const parts = symmetry_tax_id.split('-')
  const rawType = parts[3] ?? 'GEN'
  const typePart = rawType
    .replace(/[^A-Z0-9]/gi, '')
    .slice(0, 6)
    .toUpperCase()

  // Compute 32-bit CRC, hex, first 6 chars
  const hash = (crc32(symmetry_tax_id) >>> 0)
    .toString(16)
    .toUpperCase()
    .padStart(6, '0')
    .slice(0, 6)

  // Assemble and ensure max length of 20
  const candidate = `${state}-${typePart}-${hash}`
  return candidate.length <= 20 ? candidate : candidate.slice(0, 20)
}

export const referenceEINRules = (
  symmetry_tax_id: string,
  tax_pay_schedule = '',
): string => {
  if (symmetry_tax_id === '06-000-0000-SIT-000') return 'H'
  if (symmetry_tax_id === '09-000-0000-SIT-000') {
    if (tax_pay_schedule === 'weekly') return 'W'
    if (tax_pay_schedule === 'biweekly') return 'B'
    if (tax_pay_schedule === 'bimonthly') return 'S'
    if (tax_pay_schedule === 'monthly') return 'M'
    return 'O'
  }
  // will implement the rest of the rules as needed
  return ''
}

export const payCycleFromDateRise = (date: Date): string => {
  const year = date.getUTCFullYear()
  const month = String(date.getUTCMonth() + 1).padStart(2, '0')
  const cycle = date.getUTCDate() > 15 ? '2' : '1'

  return `${year}-${month}-P${cycle}`
}

export const payCycleFromDateSymmetry = (date: Date): string => {
  const year = date.getUTCFullYear()
  const month = String(date.getUTCMonth() + 1).padStart(2, '0')
  const day = String(date.getUTCDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

export const mapDatabaseSchematoMasterTax = (value: string) => {
  let mappedValue: string

  switch (value) {
    case 'yes':
      mappedValue = 'Y'
      break
    case 'no':
      mappedValue = 'N'
      break
    case 'active':
      mappedValue = 'A'
      break
    case 'inactive':
      mappedValue = 'I'
      break
    case 'full_service':
      mappedValue = 'F'
      break
    case 'return_only':
      mappedValue = 'R'
      break
    case 'balance_only':
      mappedValue = 'B'
      break
    case 'applied_for':
      mappedValue = 'Y'
      break
    case 'registered':
      mappedValue = 'N'
      break
    case 'common_pay_parent':
      mappedValue = 'P'
      break
    case 'common_pay_child':
      mappedValue = 'C'
      break
    case 'checking':
      mappedValue = 'C'
      break
    case 'savings':
      mappedValue = 'S'
      break
    case 'applicable':
      mappedValue = 'Y'
      break
    case 'not_applicable':
      mappedValue = 'N'
      break
    case 'agent_3504':
      mappedValue = '1'
      break
    case 'cpeo_3511a':
      mappedValue = '2'
      break
    case 'cpeo_3511c':
      mappedValue = '3'
      break
    case 'cpeo_3504':
      mappedValue = '4'
      break
    case 'cpeo_mixed':
      mappedValue = '0'
      break
    case 'cpeo_client_3511a':
      mappedValue = 'A'
      break
    case 'cpeo_client_3511c':
      mappedValue = 'B'
      break
    case 'cpeo_client_31_3504':
      mappedValue = 'C'
      break
    case 'other_third_party':
      mappedValue = 'T'
      break
    case 'none':
      mappedValue = 'N'
      break
    case 'full':
      mappedValue = 'F'
      break
    case 'variances_ony':
      mappedValue = 'V'
      break
    case 'federal_government':
      mappedValue = 'F'
      break
    case 'state_local_government':
      mappedValue = 'S'
      break
    case 'tax_exempt':
      mappedValue = 'T'
      break
    case 'state_local_tax_exempt':
      mappedValue = 'Y'
      break
    case 'US':
      mappedValue = 'US'
      break
    case 'CA':
      mappedValue = 'CA'
      break
    case 'MX':
      mappedValue = 'MX'
      break
    case 'reimbursable':
      mappedValue = 'I'
      break
    case 'same_as_fein':
      mappedValue = 'F'
      break
    case 'exempt':
      mappedValue = 'E'
      break
    case 'michingan_501':
      mappedValue = '5'
      break
    case 'check':
      mappedValue = '01'
      break
    case 'eft_credit':
      mappedValue = '11'
      break
    case 'eft_debit':
      mappedValue = '21'
      break
    case 'eft_debit_touch_tone':
      mappedValue = '22'
      break
    case 'eft_debit_online':
      mappedValue = '23'
      break
    case 'eft_debit_file':
      mappedValue = '24'
      break
    case 'eft_debit_return':
      mappedValue = '25'
      break
    case 'positive':
      mappedValue = '+'
      break
    case 'negative':
      mappedValue = '-'
      break
    case 'weekly':
      mappedValue = 'W'
      break
    case 'bi_weekly':
      mappedValue = 'B'
      break
    case 'semi_monthly':
      mappedValue = 'S'
      break
    case 'monthly':
      mappedValue = 'M'
      break
    default:
      mappedValue = value
  }
  return mappedValue
}

export const roundToTwoDecimalPlaces = (value: number): number => {
  return Number(value.toFixed(2))
}
