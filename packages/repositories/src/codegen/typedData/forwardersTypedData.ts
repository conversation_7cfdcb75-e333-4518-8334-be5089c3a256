import {
  getForwarderDomain,
  sendTxn,
  getContract,
  staticCallForwarder,
} from '../../smartContracts.js'
import type { UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { ValidNetworks } from '../../smartContracts.js'
import assert from 'utils/src/common/assertHTTP.js'
import type { InsertableBlockchainTransactions } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import {
  type RiseAccountForwarderCreatePaymentRequest,
  type RiseAccountForwarderCreatePaymentAndExecuteRequest,
  type RiseAccountForwarderCreatePaymentByScheduleRequest,
  type RiseAccountForwarderCreatePaymentsRequest,
  type RiseAccountForwarderCreatePaymentsAndExecuteRequest,
  type RiseAccountForwarderIntentPaymentToScheduledRequest,
  type RiseAccountForwarderIntentPaymentToScheduledAndExecuteRequest,
  type RiseAccountForwarderRemovePaymentsRequest,
  type RiseAccountForwarderRemovePaymentsByGroupIDRequest,
  type RiseAccountForwarderSendEtherRequest,
  type RiseAccountForwarderSetRolesRequest,
  type RiseAccountForwarderSetTokenTransferApprovalRequest,
  type RiseAccountForwarderSetTransactionLimitsRequest,
  type RiseAccountForwarderTokenTransferRequest,
  type RisePaymentHandlerForwarderProcessTokenTransfersWithConfigRequest,
  type RisePaymentHandlerForwarderSetTransferRulesRequest,
  type RiseIDForwarderApproveRequest,
  type RiseIDForwarderCallRequest,
  type RiseIDForwarderExecuteRequest,
  type RiseIDForwarderSetDataRequest,
  type RiseIDForwarderSetRolesRequest,
  type RiseIDForwarderTransferRequest,
  type RiseIDForwarderTransferFromRequest,
} from '@riseworks/contracts/src/forwardersTypedDataSchemas.js'
import { db as mainDB } from 'db/src/index.js'

type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>
type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

const genSalt = () => Math.floor(Math.random() * 1000000).toString()
const oneDayUnix = 24 * 60 * 60
const genExpires = () => (Math.floor(Date.now() / 1000) + oneDayUnix).toString()

export const RiseAccountForwarder = {
  createPayment: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderCreatePaymentRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        CreatePaymentForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePayment' },
        ],
        RisePayment: [
          { name: 'id', type: 'bytes32' },
          { name: 'groupID', type: 'bytes32' },
          { name: 'payAtTime', type: 'uint128' },
          { name: 'validMinutes', type: 'uint32' },
          { name: 'payType', type: 'uint16' },
          { name: 'token', type: 'address' },
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
          { name: 'data', type: 'bytes32' },
        ],
      },
      primary_type: 'CreatePaymentForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderCreatePaymentRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.createPayment.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'createPayment transaction will fail')

      const _tx = await forwarder.createPayment.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  createPaymentAndExecute: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderCreatePaymentAndExecuteRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        CreatePaymentForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePayment' },
        ],
        RisePayment: [
          { name: 'id', type: 'bytes32' },
          { name: 'groupID', type: 'bytes32' },
          { name: 'payAtTime', type: 'uint128' },
          { name: 'validMinutes', type: 'uint32' },
          { name: 'payType', type: 'uint16' },
          { name: 'token', type: 'address' },
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
          { name: 'data', type: 'bytes32' },
        ],
      },
      primary_type: 'CreatePaymentForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderCreatePaymentAndExecuteRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.createPaymentAndExecute.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'createPaymentAndExecute transaction will fail',
      )

      const _tx = await forwarder.createPaymentAndExecute.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  createPaymentBySchedule: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderCreatePaymentByScheduleRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        CreatePaymentByScheduleForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePaymentScheduleRequest' },
        ],
        RisePaymentScheduleRequest: [
          { name: 'count', type: 'uint32' },
          { name: 'minuteInterval', type: 'uint64' },
          { name: 'payment', type: 'RisePayment' },
        ],
        RisePayment: [
          { name: 'id', type: 'bytes32' },
          { name: 'groupID', type: 'bytes32' },
          { name: 'payAtTime', type: 'uint128' },
          { name: 'validMinutes', type: 'uint32' },
          { name: 'payType', type: 'uint16' },
          { name: 'token', type: 'address' },
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
          { name: 'data', type: 'bytes32' },
        ],
      },
      primary_type: 'CreatePaymentByScheduleForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderCreatePaymentByScheduleRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.createPaymentBySchedule.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'createPaymentBySchedule transaction will fail',
      )

      const _tx = await forwarder.createPaymentBySchedule.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  createPayments: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderCreatePaymentsRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        CreatePaymentsForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePaymentsRequest' },
        ],
        RisePaymentsRequest: [{ name: 'payments', type: 'RisePayment[]' }],
        RisePayment: [
          { name: 'id', type: 'bytes32' },
          { name: 'groupID', type: 'bytes32' },
          { name: 'payAtTime', type: 'uint128' },
          { name: 'validMinutes', type: 'uint32' },
          { name: 'payType', type: 'uint16' },
          { name: 'token', type: 'address' },
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
          { name: 'data', type: 'bytes32' },
        ],
      },
      primary_type: 'CreatePaymentsForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderCreatePaymentsRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.createPayments.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'createPayments transaction will fail')

      const _tx = await forwarder.createPayments.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  createPaymentsAndExecute: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderCreatePaymentsAndExecuteRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        CreatePaymentsForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePaymentsRequest' },
        ],
        RisePaymentsRequest: [{ name: 'payments', type: 'RisePayment[]' }],
        RisePayment: [
          { name: 'id', type: 'bytes32' },
          { name: 'groupID', type: 'bytes32' },
          { name: 'payAtTime', type: 'uint128' },
          { name: 'validMinutes', type: 'uint32' },
          { name: 'payType', type: 'uint16' },
          { name: 'token', type: 'address' },
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
          { name: 'data', type: 'bytes32' },
        ],
      },
      primary_type: 'CreatePaymentsForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderCreatePaymentsAndExecuteRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.createPaymentsAndExecute.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'createPaymentsAndExecute transaction will fail',
      )

      const _tx = await forwarder.createPaymentsAndExecute.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  intentPaymentToScheduled: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderIntentPaymentToScheduledRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        IntentPaymentToScheduledForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseIntentPaymentsToScheduledRequest' },
        ],
        RiseIntentPaymentsToScheduledRequest: [
          { name: 'paymentIDs', type: 'bytes32[]' },
          { name: 'payAtTime', type: 'uint128[]' },
        ],
      },
      primary_type: 'IntentPaymentToScheduledForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderIntentPaymentToScheduledRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.intentPaymentToScheduled.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'intentPaymentToScheduled transaction will fail',
      )

      const _tx = await forwarder.intentPaymentToScheduled.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  intentPaymentToScheduledAndExecute: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderIntentPaymentToScheduledAndExecuteRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        IntentPaymentToScheduledForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseIntentPaymentsToScheduledRequest' },
        ],
        RiseIntentPaymentsToScheduledRequest: [
          { name: 'paymentIDs', type: 'bytes32[]' },
          { name: 'payAtTime', type: 'uint128[]' },
        ],
      },
      primary_type: 'IntentPaymentToScheduledForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderIntentPaymentToScheduledAndExecuteRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.intentPaymentToScheduledAndExecute.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'intentPaymentToScheduledAndExecute transaction will fail',
      )

      const _tx =
        await forwarder.intentPaymentToScheduledAndExecute.populateTransaction(
          req,
          signature,
        )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  removePayments: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderRemovePaymentsRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        RemovePaymentsForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseRemovePaymentsRequest' },
        ],
        RiseRemovePaymentsRequest: [{ name: 'paymentIDs', type: 'bytes32[]' }],
      },
      primary_type: 'RemovePaymentsForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderRemovePaymentsRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.removePayments.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'removePayments transaction will fail')

      const _tx = await forwarder.removePayments.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  removePaymentsByGroupID: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderRemovePaymentsByGroupIDRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        RemovePaymentsByGroupIDForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseRemovePaymentByGroupRequest' },
        ],
        RiseRemovePaymentByGroupRequest: [
          { name: 'groupID', type: 'bytes32' },
          { name: 'idx', type: 'uint256' },
          { name: 'count', type: 'uint256' },
        ],
      },
      primary_type: 'RemovePaymentsByGroupIDForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderRemovePaymentsByGroupIDRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.removePaymentsByGroupID.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'removePaymentsByGroupID transaction will fail',
      )

      const _tx = await forwarder.removePaymentsByGroupID.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  sendEther: {
    typedData: async (
      req: PartialBy<RiseAccountForwarderSendEtherRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        SendEtherForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseEtherTransferRequest' },
        ],
        RiseEtherTransferRequest: [
          { name: 'recipient', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'SendEtherForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderSendEtherRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.sendEther.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'sendEther transaction will fail')

      const _tx = await forwarder.sendEther.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setRoles: {
    typedData: async (
      req: PartialBy<RiseAccountForwarderSetRolesRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        SetRolesForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'SetRole[]' },
        ],
        SetRole: [
          { name: 'role', type: 'uint8' },
          { name: 'account', type: 'address' },
        ],
      },
      primary_type: 'SetRolesForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderSetRolesRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.setRoles.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'setRoles transaction will fail')

      const _tx = await forwarder.setRoles.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setTokenTransferApproval: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderSetTokenTransferApprovalRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        SetTokenTransferApprovalForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseTokenApprovalRequest' },
        ],
        RiseTokenApprovalRequest: [
          { name: 'token', type: 'address' },
          { name: 'spender', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'SetTokenTransferApprovalForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderSetTokenTransferApprovalRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.setTokenTransferApproval.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'setTokenTransferApproval transaction will fail',
      )

      const _tx = await forwarder.setTokenTransferApproval.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setTransactionLimits: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderSetTransactionLimitsRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        SetTransactionLimitsForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseTransactionLimitRequest' },
        ],
        RiseTransactionLimitRequest: [
          { name: 'spender', type: 'address' },
          { name: 'token', type: 'address' },
          { name: 'dailyLimit', type: 'uint256' },
          { name: 'transactionLimit', type: 'uint256' },
        ],
      },
      primary_type: 'SetTransactionLimitsForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderSetTransactionLimitsRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.setTransactionLimits.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'setTransactionLimits transaction will fail')

      const _tx = await forwarder.setTransactionLimits.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  tokenTransfer: {
    typedData: async (
      req: PartialBy<
        RiseAccountForwarderTokenTransferRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseAccountForwarder_impl', db),
      types: {
        TokenTransferForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RiseTokenTransferRequest' },
        ],
        RiseTokenTransferRequest: [
          { name: 'token', type: 'address' },
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'TokenTransferForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseAccountForwarderTokenTransferRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseAccountForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.tokenTransfer.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'tokenTransfer transaction will fail')

      const _tx = await forwarder.tokenTransfer.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
}

export const RisePaymentHandlerForwarder = {
  processTokenTransfersWithConfig: {
    typedData: async (
      req: PartialBy<
        RisePaymentHandlerForwarderProcessTokenTransfersWithConfigRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RisePaymentHandlerForwarder_impl', db),
      types: {
        ProcessTokenTransfersWithConfigForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePaymentHandlerConfigRequest' },
        ],
        RisePaymentHandlerConfigRequest: [
          { name: 'token', type: 'address' },
          { name: 'configs', type: 'RisePaymentHandlerConfig[]' },
        ],
        RisePaymentHandlerConfig: [
          { name: 'amount', type: 'uint256' },
          { name: 'transferType', type: 'uint8' },
          { name: 'fixedOrPercent', type: 'uint8' },
          { name: 'ramp', type: 'address' },
          { name: 'source', type: 'address' },
          { name: 'destination', type: 'address' },
          { name: 'offChainReference', type: 'bytes32' },
          { name: 'data', type: 'bytes' },
        ],
      },
      primary_type: 'ProcessTokenTransfersWithConfigForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RisePaymentHandlerForwarderProcessTokenTransfersWithConfigRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract(
        'RisePaymentHandlerForwarder_impl',
        network,
      )
      const result = await staticCallForwarder(
        forwarder.processTokenTransfersWithConfig.staticCall,
        req,
        signature,
      )
      assert(
        result[0] === true,
        'processTokenTransfersWithConfig transaction will fail',
      )

      const _tx =
        await forwarder.processTokenTransfersWithConfig.populateTransaction(
          req,
          signature,
        )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setTransferRules: {
    typedData: async (
      req: PartialBy<
        RisePaymentHandlerForwarderSetTransferRulesRequest,
        'salt' | 'expires'
      >,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RisePaymentHandlerForwarder_impl', db),
      types: {
        SetTransferRulesForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'RisePaymentHandlerConfigRequest[]' },
        ],
        RisePaymentHandlerConfigRequest: [
          { name: 'token', type: 'address' },
          { name: 'configs', type: 'RisePaymentHandlerConfig[]' },
        ],
        RisePaymentHandlerConfig: [
          { name: 'amount', type: 'uint256' },
          { name: 'transferType', type: 'uint8' },
          { name: 'fixedOrPercent', type: 'uint8' },
          { name: 'ramp', type: 'address' },
          { name: 'source', type: 'address' },
          { name: 'destination', type: 'address' },
          { name: 'offChainReference', type: 'bytes32' },
          { name: 'data', type: 'bytes' },
        ],
      },
      primary_type: 'SetTransferRulesForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RisePaymentHandlerForwarderSetTransferRulesRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract(
        'RisePaymentHandlerForwarder_impl',
        network,
      )
      const result = await staticCallForwarder(
        forwarder.setTransferRules.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'setTransferRules transaction will fail')

      const _tx = await forwarder.setTransferRules.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
}

export const RiseIDForwarder = {
  approve: {
    typedData: async (
      req: PartialBy<RiseIDForwarderApproveRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        ApproveForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'Approve' },
        ],
        Approve: [
          { name: 'token', type: 'address' },
          { name: 'spender', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'ApproveForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderApproveRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.approve.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'approve transaction will fail')

      const _tx = await forwarder.approve.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  call: {
    typedData: async (
      req: PartialBy<RiseIDForwarderCallRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        CallForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'Execution' },
        ],
        Execution: [
          { name: 'to', type: 'address' },
          { name: 'method', type: 'bytes' },
          { name: 'data', type: 'bytes' },
        ],
      },
      primary_type: 'CallForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderCallRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.call.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'call transaction will fail')

      const _tx = await forwarder.call.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  execute: {
    typedData: async (
      req: PartialBy<RiseIDForwarderExecuteRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        ExecuteForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'Execution' },
        ],
        Execution: [
          { name: 'to', type: 'address' },
          { name: 'method', type: 'bytes' },
          { name: 'data', type: 'bytes' },
        ],
      },
      primary_type: 'ExecuteForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderExecuteRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.execute.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'execute transaction will fail')

      const _tx = await forwarder.execute.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setData: {
    typedData: async (
      req: PartialBy<RiseIDForwarderSetDataRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        SetDataForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'Dataset' },
        ],
        Dataset: [
          { name: 'dataKey', type: 'bytes32' },
          { name: 'dataValue', type: 'bytes' },
        ],
      },
      primary_type: 'SetDataForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderSetDataRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.setData.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'setData transaction will fail')

      const _tx = await forwarder.setData.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  setRoles: {
    typedData: async (
      req: PartialBy<RiseIDForwarderSetRolesRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        SetRolesForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'SetRole[]' },
        ],
        SetRole: [
          { name: 'role', type: 'uint8' },
          { name: 'account', type: 'address' },
        ],
      },
      primary_type: 'SetRolesForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderSetRolesRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.setRoles.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'setRoles transaction will fail')

      const _tx = await forwarder.setRoles.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  transfer: {
    typedData: async (
      req: PartialBy<RiseIDForwarderTransferRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        TransferForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'Transfer' },
        ],
        Transfer: [
          { name: 'token', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'TransferForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderTransferRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.transfer.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'transfer transaction will fail')

      const _tx = await forwarder.transfer.populateTransaction(req, signature)

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
  transferFrom: {
    typedData: async (
      req: PartialBy<RiseIDForwarderTransferFromRequest, 'salt' | 'expires'>,
      db = mainDB,
    ) => ({
      domain: await getForwarderDomain('RiseIDForwarder_impl', db),
      types: {
        TransferFromForwardRequest: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'salt', type: 'uint64' },
          { name: 'expires', type: 'uint48' },
          { name: 'data', type: 'TransferFrom' },
        ],
        TransferFrom: [
          { name: 'token', type: 'address' },
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'amount', type: 'uint256' },
        ],
      },
      primary_type: 'TransferFromForwardRequest',
      typed_data: {
        from: req.from,
        to: req.to,
        salt: req.salt ?? genSalt(),
        expires: req.expires ?? genExpires(),
        data: req.data,
      },
    }),
    execute: async (
      req: RiseIDForwarderTransferFromRequest,
      signature: string,
      {
        friendly_type,
        nanoids,
      }: {
        nanoids?: UserNanoid[]
        friendly_type?: InsertableBlockchainTransactions['friendly_type']
      } = {},
      network: ValidNetworks = 'arbitrum',
      db = mainDB,
    ) => {
      const forwarder = await getContract('RiseIDForwarder_impl', network)
      const result = await staticCallForwarder(
        forwarder.transferFrom.staticCall,
        req,
        signature,
      )
      assert(result[0] === true, 'transferFrom transaction will fail')

      const _tx = await forwarder.transferFrom.populateTransaction(
        req,
        signature,
      )

      return await sendTxn({
        to: _tx.to,
        data: _tx.data,
        nanoids,
        friendly_type,
        network,
      })
    },
  },
}
