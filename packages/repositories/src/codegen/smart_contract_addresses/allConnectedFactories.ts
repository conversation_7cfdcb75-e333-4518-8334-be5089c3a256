// This file is autogenerated, do not edit manually
import type { RiseDeployFactoryGovernor_impl_ethereum } from '../abi_types/RiseDeployFactoryGovernor_impl_ethereum.js'
import type { RiseRampDepositCCTP_ethereum } from '../abi_types/RiseRampDepositCCTP_ethereum.js'
import type { RiseRampDepositCCTP_arbitrum } from '../abi_types/RiseRampDepositCCTP_arbitrum.js'
import type { RiseRampDepositSwap_impl_arbitrum } from '../abi_types/RiseRampDepositSwap_impl_arbitrum.js'
import type { RiseRampWithdrawCCTP_impl_arbitrum } from '../abi_types/RiseRampWithdrawCCTP_impl_arbitrum.js'
import type { RiseAccount_impl_arbitrum } from '../abi_types/RiseAccount_impl_arbitrum.js'
import type { RiseRampWithdrawExchange_impl_arbitrum } from '../abi_types/RiseRampWithdrawExchange_impl_arbitrum.js'
import type { RiseRampWithdrawUniSwap_impl_arbitrum } from '../abi_types/RiseRampWithdrawUniSwap_impl_arbitrum.js'
import type { RiseAccountGovernor_impl_arbitrum } from '../abi_types/RiseAccountGovernor_impl_arbitrum.js'
import type { RiseAccountSubscriptionUsage_impl_arbitrum } from '../abi_types/RiseAccountSubscriptionUsage_impl_arbitrum.js'
import type { RisePaymentHandler_impl_arbitrum } from '../abi_types/RisePaymentHandler_impl_arbitrum.js'
import type { RiseRampWithdrawUnblock_impl_arbitrum } from '../abi_types/RiseRampWithdrawUnblock_impl_arbitrum.js'
import type { RiseRampDeposit_impl_arbitrum } from '../abi_types/RiseRampDeposit_impl_arbitrum.js'
import type { RiseRampWithdrawInternationalUSD_impl_arbitrum } from '../abi_types/RiseRampWithdrawInternationalUSD_impl_arbitrum.js'
import type { RiseAccountForwarder_impl_arbitrum } from '../abi_types/RiseAccountForwarder_impl_arbitrum.js'
import type { RisePricesOracle_impl_arbitrum } from '../abi_types/RisePricesOracle_impl_arbitrum.js'
import type { RiseRampDepositSwap_arbitrum } from '../abi_types/RiseRampDepositSwap_arbitrum.js'
import type { RiseRampWithdrawUSDUS_impl_arbitrum } from '../abi_types/RiseRampWithdrawUSDUS_impl_arbitrum.js'
import type { RiseRampWithdrawERC20Token_impl_arbitrum } from '../abi_types/RiseRampWithdrawERC20Token_impl_arbitrum.js'
import type { RiseRampWithdrawInternationalUSDManual_impl_arbitrum } from '../abi_types/RiseRampWithdrawInternationalUSDManual_impl_arbitrum.js'
import type { RiseRampWithdrawSwap_impl_arbitrum } from '../abi_types/RiseRampWithdrawSwap_impl_arbitrum.js'
import type { RiseRampDepositCCIP_impl_arbitrum } from '../abi_types/RiseRampDepositCCIP_impl_arbitrum.js'
import type { RiseID_impl_arbitrum } from '../abi_types/RiseID_impl_arbitrum.js'
import type { RiseRampWithdrawCCIP_impl_arbitrum } from '../abi_types/RiseRampWithdrawCCIP_impl_arbitrum.js'
import type { RisePayToken_impl_arbitrum } from '../abi_types/RisePayToken_impl_arbitrum.js'
import type { RisePricesOracle_arbitrum } from '../abi_types/RisePricesOracle_arbitrum.js'
import type { RiseForwarder_impl_arbitrum } from '../abi_types/RiseForwarder_impl_arbitrum.js'
import type { RisePaymentHandlerForwarder_impl_arbitrum } from '../abi_types/RisePaymentHandlerForwarder_impl_arbitrum.js'
import type { RiseDepositGovernor_impl_arbitrum } from '../abi_types/RiseDepositGovernor_impl_arbitrum.js'
import type { RiseRampWithdrawUniSwap_arbitrum } from '../abi_types/RiseRampWithdrawUniSwap_arbitrum.js'
import type { RiseRouter_impl_arbitrum } from '../abi_types/RiseRouter_impl_arbitrum.js'
import type { RiseRampDeposit_arbitrum } from '../abi_types/RiseRampDeposit_arbitrum.js'
import type { RiseIDForwarder_impl_arbitrum } from '../abi_types/RiseIDForwarder_impl_arbitrum.js'
import type { RiseRampDepositCCIP_arbitrum } from '../abi_types/RiseRampDepositCCIP_arbitrum.js'
import type { RiseAccountSubscriptionUsage_arbitrum } from '../abi_types/RiseAccountSubscriptionUsage_arbitrum.js'
import type { RiseRampWithdrawSwap_arbitrum } from '../abi_types/RiseRampWithdrawSwap_arbitrum.js'
import type { RiseRampWithdrawCCTP_arbitrum } from '../abi_types/RiseRampWithdrawCCTP_arbitrum.js'
import type { RiseRampWithdrawUSDUS_arbitrum } from '../abi_types/RiseRampWithdrawUSDUS_arbitrum.js'
import type { RiseRampWithdrawUnblock_arbitrum } from '../abi_types/RiseRampWithdrawUnblock_arbitrum.js'
import type { RiseRampWithdrawERC20Token_arbitrum } from '../abi_types/RiseRampWithdrawERC20Token_arbitrum.js'
import type { RiseRampWithdrawExchange_arbitrum } from '../abi_types/RiseRampWithdrawExchange_arbitrum.js'
import type { RiseDeployFactoryGovernor_impl_arbitrum } from '../abi_types/RiseDeployFactoryGovernor_impl_arbitrum.js'
import type { RiseDeployFactory_impl_arbitrum } from '../abi_types/RiseDeployFactory_impl_arbitrum.js'
import type { RiseAccountGovernor_arbitrum } from '../abi_types/RiseAccountGovernor_arbitrum.js'
import type { RiseAccessGovernor_arbitrum } from '../abi_types/RiseAccessGovernor_arbitrum.js'
import type { RiseAccessGovernor_impl_arbitrum } from '../abi_types/RiseAccessGovernor_impl_arbitrum.js'
import type { RiseDepositGovernor_arbitrum } from '../abi_types/RiseDepositGovernor_arbitrum.js'
import type { RiseTokenGovernor_impl_arbitrum } from '../abi_types/RiseTokenGovernor_impl_arbitrum.js'
import type { PYUSD_arbitrum } from '../abi_types/PYUSD_arbitrum.js'
import type { DAI_arbitrum } from '../abi_types/DAI_arbitrum.js'
import type { RisePaymentIdentifiers_arbitrum } from '../abi_types/RisePaymentIdentifiers_arbitrum.js'
import type { RiseAccess_impl_arbitrum } from '../abi_types/RiseAccess_impl_arbitrum.js'
import type { TransferHelper_arbitrum } from '../abi_types/TransferHelper_arbitrum.js'
import type { RiseProxy_impl_arbitrum } from '../abi_types/RiseProxy_impl_arbitrum.js'
import type { RiseUSD_arbitrum } from '../abi_types/RiseUSD_arbitrum.js'
import type { RiseEUR_arbitrum } from '../abi_types/RiseEUR_arbitrum.js'
import type { RiseGBP_arbitrum } from '../abi_types/RiseGBP_arbitrum.js'
import type { RiseRampWithdrawInternationalUSD_arbitrum } from '../abi_types/RiseRampWithdrawInternationalUSD_arbitrum.js'
import type { RiseRampWithdrawInternationalUSDManual_arbitrum } from '../abi_types/RiseRampWithdrawInternationalUSDManual_arbitrum.js'
import type { BokkyPooBahsDateTimeLibrary_arbitrum } from '../abi_types/BokkyPooBahsDateTimeLibrary_arbitrum.js'
import type { RiseRouter_arbitrum } from '../abi_types/RiseRouter_arbitrum.js'
import type { RiseRampWithdrawCCIP_arbitrum } from '../abi_types/RiseRampWithdrawCCIP_arbitrum.js'
import type { RisePayToken_arbitrum } from '../abi_types/RisePayToken_arbitrum.js'
import type { RiseDedicatedFund_arbitrum } from '../abi_types/RiseDedicatedFund_arbitrum.js'
import type { RiseForwarder_arbitrum } from '../abi_types/RiseForwarder_arbitrum.js'
import type { RiseAccess_arbitrum } from '../abi_types/RiseAccess_arbitrum.js'
import type { RisePayRampEURGBP_arbitrum } from '../abi_types/RisePayRampEURGBP_arbitrum.js'
import type { RisePaySchedules_arbitrum } from '../abi_types/RisePaySchedules_arbitrum.js'
import type { RisePayRampNGN_arbitrum } from '../abi_types/RisePayRampNGN_arbitrum.js'
import type { RisePay_arbitrum } from '../abi_types/RisePay_arbitrum.js'
import type { RisePayRampUSDCMainnet_arbitrum } from '../abi_types/RisePayRampUSDCMainnet_arbitrum.js'
import type { USDC_arbitrum } from '../abi_types/USDC_arbitrum.js'
import type { EURC_arbitrum } from '../abi_types/EURC_arbitrum.js'
import type { RiseIDBusiness_arbitrum } from '../abi_types/RiseIDBusiness_arbitrum.js'
import type { RisePayRampUniswap_arbitrum } from '../abi_types/RisePayRampUniswap_arbitrum.js'
import type { RiseFundFulfillment_arbitrum } from '../abi_types/RiseFundFulfillment_arbitrum.js'
import type { RiseIDIndividual_arbitrum } from '../abi_types/RiseIDIndividual_arbitrum.js'
import type { RisePayRampUSDC_arbitrum } from '../abi_types/RisePayRampUSDC_arbitrum.js'
import type { RisePlannedPayments_arbitrum } from '../abi_types/RisePlannedPayments_arbitrum.js'
import type { RiseIDDAO_arbitrum } from '../abi_types/RiseIDDAO_arbitrum.js'
import type { RiseIDFactory_arbitrum } from '../abi_types/RiseIDFactory_arbitrum.js'
import type { RisePayRampForEx_arbitrum } from '../abi_types/RisePayRampForEx_arbitrum.js'
import type { RisePayRampUSDInternational_arbitrum } from '../abi_types/RisePayRampUSDInternational_arbitrum.js'
import type { RisePayRampUSDUS_arbitrum } from '../abi_types/RisePayRampUSDUS_arbitrum.js'
import type { RiseGovernor_arbitrum } from '../abi_types/RiseGovernor_arbitrum.js'
import type { RiseStorage_arbitrum } from '../abi_types/RiseStorage_arbitrum.js'
import type { RiseDeterministicDeployFactory_arbitrum } from '../abi_types/RiseDeterministicDeployFactory_arbitrum.js'
import type { RiseDeductionsAndCredits_arbitrum } from '../abi_types/RiseDeductionsAndCredits_arbitrum.js'
import type { RisePayTokenV1_arbitrum } from '../abi_types/RisePayTokenV1_arbitrum.js'
import type { RiseFinanceGovernor_arbitrum } from '../abi_types/RiseFinanceGovernor_arbitrum.js'
import type { RiseRampCurrencySwap_arbitrum } from '../abi_types/RiseRampCurrencySwap_arbitrum.js'
import type { RiseRampCurrencySwap_impl_arbitrum } from '../abi_types/RiseRampCurrencySwap_impl_arbitrum.js'
import type { MessageTransmitter_ethereum } from '../abi_types/MessageTransmitter_ethereum.js'
import type { MessageTransmitter_base } from '../abi_types/MessageTransmitter_base.js'
import type { MessageTransmitter_arbitrum } from '../abi_types/MessageTransmitter_arbitrum.js'
import type { MessageTransmitter_polygon } from '../abi_types/MessageTransmitter_polygon.js'
import type { MessageTransmitter_avalanche } from '../abi_types/MessageTransmitter_avalanche.js'
import type { MessageTransmitter_optimism } from '../abi_types/MessageTransmitter_optimism.js'

import { getSmartContract } from '../../smartContracts.js'
import { Contract, type ContractRunner, type Interface } from 'ethers'
import { loggifyContract } from 'utils/src/blockchain/ethers.js'
import assert from 'utils/src/common/assertHTTP.js'

export const contracts = {
  RiseDeployFactoryGovernor_impl: {
    ethereum: async () => {
      const contract = await getSmartContract(
        'RiseDeployFactoryGovernor_impl',
        'ethereum',
      )
      assert(
        contract,
        'RiseDeployFactoryGovernor_impl contract not found for ethereum',
      )
      const _contract = await factories['RiseDeployFactoryGovernor_impl'][
        'ethereum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseDeployFactoryGovernor_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseDeployFactoryGovernor_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseDeployFactoryGovernor_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDepositCCTP: {
    ethereum: async () => {
      const contract = await getSmartContract('RiseRampDepositCCTP', 'ethereum')
      assert(contract, 'RiseRampDepositCCTP contract not found for ethereum')
      const _contract = await factories['RiseRampDepositCCTP'][
        'ethereum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRampDepositCCTP', 'arbitrum')
      assert(contract, 'RiseRampDepositCCTP contract not found for arbitrum')
      const _contract = await factories['RiseRampDepositCCTP'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDepositSwap_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampDepositSwap_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampDepositSwap_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampDepositSwap_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawCCTP_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawCCTP_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawCCTP_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawCCTP_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccount_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseAccount_impl', 'arbitrum')
      assert(contract, 'RiseAccount_impl contract not found for arbitrum')
      const _contract = await factories['RiseAccount_impl']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawExchange_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawExchange_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawExchange_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawExchange_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUniSwap_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUniSwap_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawUniSwap_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawUniSwap_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccountGovernor_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseAccountGovernor_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseAccountGovernor_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseAccountGovernor_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccountSubscriptionUsage_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseAccountSubscriptionUsage_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseAccountSubscriptionUsage_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseAccountSubscriptionUsage_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePaymentHandler_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePaymentHandler_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RisePaymentHandler_impl contract not found for arbitrum',
      )
      const _contract = await factories['RisePaymentHandler_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUnblock_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUnblock_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawUnblock_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawUnblock_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDeposit_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampDeposit_impl',
        'arbitrum',
      )
      assert(contract, 'RiseRampDeposit_impl contract not found for arbitrum')
      const _contract = await factories['RiseRampDeposit_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawInternationalUSD_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawInternationalUSD_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawInternationalUSD_impl contract not found for arbitrum',
      )
      const _contract = await factories[
        'RiseRampWithdrawInternationalUSD_impl'
      ]['arbitrum'].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccountForwarder_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseAccountForwarder_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseAccountForwarder_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseAccountForwarder_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePricesOracle_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePricesOracle_impl',
        'arbitrum',
      )
      assert(contract, 'RisePricesOracle_impl contract not found for arbitrum')
      const _contract = await factories['RisePricesOracle_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDepositSwap: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRampDepositSwap', 'arbitrum')
      assert(contract, 'RiseRampDepositSwap contract not found for arbitrum')
      const _contract = await factories['RiseRampDepositSwap'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUSDUS_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUSDUS_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawUSDUS_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawUSDUS_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawERC20Token_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawERC20Token_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawERC20Token_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawERC20Token_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawInternationalUSDManual_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawInternationalUSDManual_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawInternationalUSDManual_impl contract not found for arbitrum',
      )
      const _contract = await factories[
        'RiseRampWithdrawInternationalUSDManual_impl'
      ]['arbitrum'].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawSwap_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawSwap_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawSwap_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawSwap_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDepositCCIP_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampDepositCCIP_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampDepositCCIP_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampDepositCCIP_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseID_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseID_impl', 'arbitrum')
      assert(contract, 'RiseID_impl contract not found for arbitrum')
      const _contract = await factories['RiseID_impl']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawCCIP_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawCCIP_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawCCIP_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawCCIP_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePayToken_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayToken_impl', 'arbitrum')
      assert(contract, 'RisePayToken_impl contract not found for arbitrum')
      const _contract = await factories['RisePayToken_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePricesOracle: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePricesOracle', 'arbitrum')
      assert(contract, 'RisePricesOracle contract not found for arbitrum')
      const _contract = await factories['RisePricesOracle']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseForwarder_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseForwarder_impl', 'arbitrum')
      assert(contract, 'RiseForwarder_impl contract not found for arbitrum')
      const _contract = await factories['RiseForwarder_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePaymentHandlerForwarder_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePaymentHandlerForwarder_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RisePaymentHandlerForwarder_impl contract not found for arbitrum',
      )
      const _contract = await factories['RisePaymentHandlerForwarder_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseDepositGovernor_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseDepositGovernor_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseDepositGovernor_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseDepositGovernor_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUniSwap: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUniSwap',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawUniSwap contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawUniSwap'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRouter_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRouter_impl', 'arbitrum')
      assert(contract, 'RiseRouter_impl contract not found for arbitrum')
      const _contract = await factories['RiseRouter_impl']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseRampDeposit: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRampDeposit', 'arbitrum')
      assert(contract, 'RiseRampDeposit contract not found for arbitrum')
      const _contract = await factories['RiseRampDeposit']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseIDForwarder_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseIDForwarder_impl',
        'arbitrum',
      )
      assert(contract, 'RiseIDForwarder_impl contract not found for arbitrum')
      const _contract = await factories['RiseIDForwarder_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampDepositCCIP: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRampDepositCCIP', 'arbitrum')
      assert(contract, 'RiseRampDepositCCIP contract not found for arbitrum')
      const _contract = await factories['RiseRampDepositCCIP'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccountSubscriptionUsage: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseAccountSubscriptionUsage',
        'arbitrum',
      )
      assert(
        contract,
        'RiseAccountSubscriptionUsage contract not found for arbitrum',
      )
      const _contract = await factories['RiseAccountSubscriptionUsage'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawSwap: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawSwap',
        'arbitrum',
      )
      assert(contract, 'RiseRampWithdrawSwap contract not found for arbitrum')
      const _contract = await factories['RiseRampWithdrawSwap'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawCCTP: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawCCTP',
        'arbitrum',
      )
      assert(contract, 'RiseRampWithdrawCCTP contract not found for arbitrum')
      const _contract = await factories['RiseRampWithdrawCCTP'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUSDUS: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUSDUS',
        'arbitrum',
      )
      assert(contract, 'RiseRampWithdrawUSDUS contract not found for arbitrum')
      const _contract = await factories['RiseRampWithdrawUSDUS'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawUnblock: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawUnblock',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawUnblock contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawUnblock'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawERC20Token: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawERC20Token',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawERC20Token contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawERC20Token'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawExchange: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawExchange',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawExchange contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawExchange'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseDeployFactory_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseDeployFactory_impl',
        'arbitrum',
      )
      assert(contract, 'RiseDeployFactory_impl contract not found for arbitrum')
      const _contract = await factories['RiseDeployFactory_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccountGovernor: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseAccountGovernor', 'arbitrum')
      assert(contract, 'RiseAccountGovernor contract not found for arbitrum')
      const _contract = await factories['RiseAccountGovernor'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccessGovernor: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseAccessGovernor', 'arbitrum')
      assert(contract, 'RiseAccessGovernor contract not found for arbitrum')
      const _contract = await factories['RiseAccessGovernor'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccessGovernor_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseAccessGovernor_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseAccessGovernor_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseAccessGovernor_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseDepositGovernor: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseDepositGovernor', 'arbitrum')
      assert(contract, 'RiseDepositGovernor contract not found for arbitrum')
      const _contract = await factories['RiseDepositGovernor'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseTokenGovernor_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseTokenGovernor_impl',
        'arbitrum',
      )
      assert(contract, 'RiseTokenGovernor_impl contract not found for arbitrum')
      const _contract = await factories['RiseTokenGovernor_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  PYUSD: {
    arbitrum: async () => {
      const contract = await getSmartContract('PYUSD', 'arbitrum')
      assert(contract, 'PYUSD contract not found for arbitrum')
      const _contract = await factories['PYUSD']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  DAI: {
    arbitrum: async () => {
      const contract = await getSmartContract('DAI', 'arbitrum')
      assert(contract, 'DAI contract not found for arbitrum')
      const _contract = await factories['DAI']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePaymentIdentifiers: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePaymentIdentifiers',
        'arbitrum',
      )
      assert(contract, 'RisePaymentIdentifiers contract not found for arbitrum')
      const _contract = await factories['RisePaymentIdentifiers'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseAccess_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseAccess_impl', 'arbitrum')
      assert(contract, 'RiseAccess_impl contract not found for arbitrum')
      const _contract = await factories['RiseAccess_impl']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  TransferHelper: {
    arbitrum: async () => {
      const contract = await getSmartContract('TransferHelper', 'arbitrum')
      assert(contract, 'TransferHelper contract not found for arbitrum')
      const _contract = await factories['TransferHelper']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseProxy_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseProxy_impl', 'arbitrum')
      assert(contract, 'RiseProxy_impl contract not found for arbitrum')
      const _contract = await factories['RiseProxy_impl']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseUSD: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseUSD', 'arbitrum')
      assert(contract, 'RiseUSD contract not found for arbitrum')
      const _contract = await factories['RiseUSD']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseEUR: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseEUR', 'arbitrum')
      assert(contract, 'RiseEUR contract not found for arbitrum')
      const _contract = await factories['RiseEUR']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseGBP: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseGBP', 'arbitrum')
      assert(contract, 'RiseGBP contract not found for arbitrum')
      const _contract = await factories['RiseGBP']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawInternationalUSD: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawInternationalUSD',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawInternationalUSD contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampWithdrawInternationalUSD'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawInternationalUSDManual: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawInternationalUSDManual',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampWithdrawInternationalUSDManual contract not found for arbitrum',
      )
      const _contract = await factories[
        'RiseRampWithdrawInternationalUSDManual'
      ]['arbitrum'].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  BokkyPooBahsDateTimeLibrary: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'BokkyPooBahsDateTimeLibrary',
        'arbitrum',
      )
      assert(
        contract,
        'BokkyPooBahsDateTimeLibrary contract not found for arbitrum',
      )
      const _contract = await factories['BokkyPooBahsDateTimeLibrary'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRouter: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseRouter', 'arbitrum')
      assert(contract, 'RiseRouter contract not found for arbitrum')
      const _contract = await factories['RiseRouter']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseRampWithdrawCCIP: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampWithdrawCCIP',
        'arbitrum',
      )
      assert(contract, 'RiseRampWithdrawCCIP contract not found for arbitrum')
      const _contract = await factories['RiseRampWithdrawCCIP'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePayToken: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayToken', 'arbitrum')
      assert(contract, 'RisePayToken contract not found for arbitrum')
      const _contract = await factories['RisePayToken']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseDedicatedFund: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseDedicatedFund', 'arbitrum')
      assert(contract, 'RiseDedicatedFund contract not found for arbitrum')
      const _contract = await factories['RiseDedicatedFund'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseForwarder: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseForwarder', 'arbitrum')
      assert(contract, 'RiseForwarder contract not found for arbitrum')
      const _contract = await factories['RiseForwarder']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseAccess: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseAccess', 'arbitrum')
      assert(contract, 'RiseAccess contract not found for arbitrum')
      const _contract = await factories['RiseAccess']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampEURGBP: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampEURGBP', 'arbitrum')
      assert(contract, 'RisePayRampEURGBP contract not found for arbitrum')
      const _contract = await factories['RisePayRampEURGBP'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePaySchedules: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePaySchedules', 'arbitrum')
      assert(contract, 'RisePaySchedules contract not found for arbitrum')
      const _contract = await factories['RisePaySchedules']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampNGN: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampNGN', 'arbitrum')
      assert(contract, 'RisePayRampNGN contract not found for arbitrum')
      const _contract = await factories['RisePayRampNGN']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePay: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePay', 'arbitrum')
      assert(contract, 'RisePay contract not found for arbitrum')
      const _contract = await factories['RisePay']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampUSDCMainnet: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePayRampUSDCMainnet',
        'arbitrum',
      )
      assert(contract, 'RisePayRampUSDCMainnet contract not found for arbitrum')
      const _contract = await factories['RisePayRampUSDCMainnet'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  USDC: {
    arbitrum: async () => {
      const contract = await getSmartContract('USDC', 'arbitrum')
      assert(contract, 'USDC contract not found for arbitrum')
      const _contract = await factories['USDC']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  EURC: {
    arbitrum: async () => {
      const contract = await getSmartContract('EURC', 'arbitrum')
      assert(contract, 'EURC contract not found for arbitrum')
      const _contract = await factories['EURC']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseIDBusiness: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseIDBusiness', 'arbitrum')
      assert(contract, 'RiseIDBusiness contract not found for arbitrum')
      const _contract = await factories['RiseIDBusiness']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampUniswap: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampUniswap', 'arbitrum')
      assert(contract, 'RisePayRampUniswap contract not found for arbitrum')
      const _contract = await factories['RisePayRampUniswap'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseFundFulfillment: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseFundFulfillment', 'arbitrum')
      assert(contract, 'RiseFundFulfillment contract not found for arbitrum')
      const _contract = await factories['RiseFundFulfillment'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseIDIndividual: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseIDIndividual', 'arbitrum')
      assert(contract, 'RiseIDIndividual contract not found for arbitrum')
      const _contract = await factories['RiseIDIndividual']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampUSDC: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampUSDC', 'arbitrum')
      assert(contract, 'RisePayRampUSDC contract not found for arbitrum')
      const _contract = await factories['RisePayRampUSDC']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePlannedPayments: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePlannedPayments', 'arbitrum')
      assert(contract, 'RisePlannedPayments contract not found for arbitrum')
      const _contract = await factories['RisePlannedPayments'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseIDDAO: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseIDDAO', 'arbitrum')
      assert(contract, 'RiseIDDAO contract not found for arbitrum')
      const _contract = await factories['RiseIDDAO']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseIDFactory: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseIDFactory', 'arbitrum')
      assert(contract, 'RiseIDFactory contract not found for arbitrum')
      const _contract = await factories['RiseIDFactory']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampForEx: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampForEx', 'arbitrum')
      assert(contract, 'RisePayRampForEx contract not found for arbitrum')
      const _contract = await factories['RisePayRampForEx']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RisePayRampUSDInternational: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RisePayRampUSDInternational',
        'arbitrum',
      )
      assert(
        contract,
        'RisePayRampUSDInternational contract not found for arbitrum',
      )
      const _contract = await factories['RisePayRampUSDInternational'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePayRampUSDUS: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayRampUSDUS', 'arbitrum')
      assert(contract, 'RisePayRampUSDUS contract not found for arbitrum')
      const _contract = await factories['RisePayRampUSDUS']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseGovernor: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseGovernor', 'arbitrum')
      assert(contract, 'RiseGovernor contract not found for arbitrum')
      const _contract = await factories['RiseGovernor']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseStorage: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseStorage', 'arbitrum')
      assert(contract, 'RiseStorage contract not found for arbitrum')
      const _contract = await factories['RiseStorage']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseDeterministicDeployFactory: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseDeterministicDeployFactory',
        'arbitrum',
      )
      assert(
        contract,
        'RiseDeterministicDeployFactory contract not found for arbitrum',
      )
      const _contract = await factories['RiseDeterministicDeployFactory'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseDeductionsAndCredits: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseDeductionsAndCredits',
        'arbitrum',
      )
      assert(
        contract,
        'RiseDeductionsAndCredits contract not found for arbitrum',
      )
      const _contract = await factories['RiseDeductionsAndCredits'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RisePayTokenV1: {
    arbitrum: async () => {
      const contract = await getSmartContract('RisePayTokenV1', 'arbitrum')
      assert(contract, 'RisePayTokenV1 contract not found for arbitrum')
      const _contract = await factories['RisePayTokenV1']['arbitrum'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
  },
  RiseFinanceGovernor: {
    arbitrum: async () => {
      const contract = await getSmartContract('RiseFinanceGovernor', 'arbitrum')
      assert(contract, 'RiseFinanceGovernor contract not found for arbitrum')
      const _contract = await factories['RiseFinanceGovernor'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampCurrencySwap: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampCurrencySwap',
        'arbitrum',
      )
      assert(contract, 'RiseRampCurrencySwap contract not found for arbitrum')
      const _contract = await factories['RiseRampCurrencySwap'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  RiseRampCurrencySwap_impl: {
    arbitrum: async () => {
      const contract = await getSmartContract(
        'RiseRampCurrencySwap_impl',
        'arbitrum',
      )
      assert(
        contract,
        'RiseRampCurrencySwap_impl contract not found for arbitrum',
      )
      const _contract = await factories['RiseRampCurrencySwap_impl'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
  MessageTransmitter: {
    ethereum: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'ethereum')
      assert(contract, 'MessageTransmitter contract not found for ethereum')
      const _contract = await factories['MessageTransmitter'][
        'ethereum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    base: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'base')
      assert(contract, 'MessageTransmitter contract not found for base')
      const _contract = await factories['MessageTransmitter']['base'].connect(
        contract.address,
      )
      return _contract as typeof _contract
    },
    arbitrum: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'arbitrum')
      assert(contract, 'MessageTransmitter contract not found for arbitrum')
      const _contract = await factories['MessageTransmitter'][
        'arbitrum'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    polygon: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'polygon')
      assert(contract, 'MessageTransmitter contract not found for polygon')
      const _contract = await factories['MessageTransmitter'][
        'polygon'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    avalanche: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'avalanche')
      assert(contract, 'MessageTransmitter contract not found for avalanche')
      const _contract = await factories['MessageTransmitter'][
        'avalanche'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
    optimism: async () => {
      const contract = await getSmartContract('MessageTransmitter', 'optimism')
      assert(contract, 'MessageTransmitter contract not found for optimism')
      const _contract = await factories['MessageTransmitter'][
        'optimism'
      ].connect(contract.address)
      return _contract as typeof _contract
    },
  },
}
export const factories = {
  RiseDeployFactoryGovernor_impl: {
    ethereum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDeployFactoryGovernor_impl',
            'ethereum',
          )
          assert(
            contract,
            'RiseDeployFactoryGovernor_impl contract not found for ethereum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDeployFactoryGovernor_impl_ethereum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDeployFactoryGovernor_impl_ethereum
      },
    },
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDeployFactoryGovernor_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDeployFactoryGovernor_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDeployFactoryGovernor_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDeployFactoryGovernor_impl_arbitrum
      },
    },
  },

  RiseRampDepositCCTP: {
    ethereum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositCCTP',
            'ethereum',
          )
          assert(
            contract,
            'RiseRampDepositCCTP contract not found for ethereum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositCCTP_ethereum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositCCTP_ethereum
      },
    },
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositCCTP',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDepositCCTP contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositCCTP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositCCTP_arbitrum
      },
    },
  },

  RiseRampDepositSwap_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositSwap_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDepositSwap_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositSwap_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositSwap_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawCCTP_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawCCTP_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawCCTP_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawCCTP_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawCCTP_impl_arbitrum
      },
    },
  },

  RiseAccount_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccount_impl',
            'arbitrum',
          )
          assert(contract, 'RiseAccount_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccount_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccount_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawExchange_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawExchange_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawExchange_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawExchange_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawExchange_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawUniSwap_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUniSwap_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUniSwap_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUniSwap_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUniSwap_impl_arbitrum
      },
    },
  },

  RiseAccountGovernor_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccountGovernor_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccountGovernor_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccountGovernor_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccountGovernor_impl_arbitrum
      },
    },
  },

  RiseAccountSubscriptionUsage_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccountSubscriptionUsage_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccountSubscriptionUsage_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccountSubscriptionUsage_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccountSubscriptionUsage_impl_arbitrum
      },
    },
  },

  RisePaymentHandler_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePaymentHandler_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RisePaymentHandler_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePaymentHandler_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePaymentHandler_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawUnblock_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUnblock_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUnblock_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUnblock_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUnblock_impl_arbitrum
      },
    },
  },

  RiseRampDeposit_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDeposit_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDeposit_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDeposit_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDeposit_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawInternationalUSD_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawInternationalUSD_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawInternationalUSD_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawInternationalUSD_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawInternationalUSD_impl_arbitrum
      },
    },
  },

  RiseAccountForwarder_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccountForwarder_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccountForwarder_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccountForwarder_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccountForwarder_impl_arbitrum
      },
    },
  },

  RisePricesOracle_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePricesOracle_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RisePricesOracle_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePricesOracle_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePricesOracle_impl_arbitrum
      },
    },
  },

  RiseRampDepositSwap: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositSwap',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDepositSwap contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositSwap_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositSwap_arbitrum
      },
    },
  },

  RiseRampWithdrawUSDUS_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUSDUS_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUSDUS_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUSDUS_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUSDUS_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawERC20Token_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawERC20Token_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawERC20Token_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawERC20Token_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawERC20Token_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawInternationalUSDManual_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawInternationalUSDManual_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawInternationalUSDManual_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawInternationalUSDManual_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawInternationalUSDManual_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawSwap_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawSwap_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawSwap_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawSwap_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawSwap_impl_arbitrum
      },
    },
  },

  RiseRampDepositCCIP_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositCCIP_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDepositCCIP_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositCCIP_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositCCIP_impl_arbitrum
      },
    },
  },

  RiseID_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseID_impl', 'arbitrum')
          assert(contract, 'RiseID_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseID_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseID_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawCCIP_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawCCIP_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawCCIP_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawCCIP_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawCCIP_impl_arbitrum
      },
    },
  },

  RisePayToken_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayToken_impl',
            'arbitrum',
          )
          assert(contract, 'RisePayToken_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayToken_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayToken_impl_arbitrum
      },
    },
  },

  RisePricesOracle: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePricesOracle',
            'arbitrum',
          )
          assert(contract, 'RisePricesOracle contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePricesOracle_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePricesOracle_arbitrum
      },
    },
  },

  RiseForwarder_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseForwarder_impl',
            'arbitrum',
          )
          assert(contract, 'RiseForwarder_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseForwarder_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseForwarder_impl_arbitrum
      },
    },
  },

  RisePaymentHandlerForwarder_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePaymentHandlerForwarder_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RisePaymentHandlerForwarder_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePaymentHandlerForwarder_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePaymentHandlerForwarder_impl_arbitrum
      },
    },
  },

  RiseDepositGovernor_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDepositGovernor_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDepositGovernor_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDepositGovernor_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDepositGovernor_impl_arbitrum
      },
    },
  },

  RiseRampWithdrawUniSwap: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUniSwap',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUniSwap contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUniSwap_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUniSwap_arbitrum
      },
    },
  },

  RiseRouter_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseRouter_impl', 'arbitrum')
          assert(contract, 'RiseRouter_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRouter_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRouter_impl_arbitrum
      },
    },
  },

  RiseRampDeposit: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseRampDeposit', 'arbitrum')
          assert(contract, 'RiseRampDeposit contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDeposit_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDeposit_arbitrum
      },
    },
  },

  RiseIDForwarder_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseIDForwarder_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseIDForwarder_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseIDForwarder_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseIDForwarder_impl_arbitrum
      },
    },
  },

  RiseRampDepositCCIP: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampDepositCCIP',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampDepositCCIP contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampDepositCCIP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampDepositCCIP_arbitrum
      },
    },
  },

  RiseAccountSubscriptionUsage: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccountSubscriptionUsage',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccountSubscriptionUsage contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccountSubscriptionUsage_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccountSubscriptionUsage_arbitrum
      },
    },
  },

  RiseRampWithdrawSwap: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawSwap',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawSwap contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawSwap_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawSwap_arbitrum
      },
    },
  },

  RiseRampWithdrawCCTP: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawCCTP',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawCCTP contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawCCTP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawCCTP_arbitrum
      },
    },
  },

  RiseRampWithdrawUSDUS: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUSDUS',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUSDUS contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUSDUS_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUSDUS_arbitrum
      },
    },
  },

  RiseRampWithdrawUnblock: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawUnblock',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawUnblock contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawUnblock_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawUnblock_arbitrum
      },
    },
  },

  RiseRampWithdrawERC20Token: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawERC20Token',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawERC20Token contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawERC20Token_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawERC20Token_arbitrum
      },
    },
  },

  RiseRampWithdrawExchange: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawExchange',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawExchange contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawExchange_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawExchange_arbitrum
      },
    },
  },

  RiseDeployFactory_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDeployFactory_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDeployFactory_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDeployFactory_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDeployFactory_impl_arbitrum
      },
    },
  },

  RiseAccountGovernor: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccountGovernor',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccountGovernor contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccountGovernor_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccountGovernor_arbitrum
      },
    },
  },

  RiseAccessGovernor: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccessGovernor',
            'arbitrum',
          )
          assert(contract, 'RiseAccessGovernor contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccessGovernor_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccessGovernor_arbitrum
      },
    },
  },

  RiseAccessGovernor_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseAccessGovernor_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseAccessGovernor_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccessGovernor_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccessGovernor_impl_arbitrum
      },
    },
  },

  RiseDepositGovernor: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDepositGovernor',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDepositGovernor contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDepositGovernor_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDepositGovernor_arbitrum
      },
    },
  },

  RiseTokenGovernor_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseTokenGovernor_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseTokenGovernor_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseTokenGovernor_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseTokenGovernor_impl_arbitrum
      },
    },
  },

  PYUSD: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('PYUSD', 'arbitrum')
          assert(contract, 'PYUSD contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'PYUSD_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as PYUSD_arbitrum
      },
    },
  },

  DAI: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('DAI', 'arbitrum')
          assert(contract, 'DAI contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'DAI_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as DAI_arbitrum
      },
    },
  },

  RisePaymentIdentifiers: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePaymentIdentifiers',
            'arbitrum',
          )
          assert(
            contract,
            'RisePaymentIdentifiers contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePaymentIdentifiers_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePaymentIdentifiers_arbitrum
      },
    },
  },

  RiseAccess_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseAccess_impl', 'arbitrum')
          assert(contract, 'RiseAccess_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccess_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccess_impl_arbitrum
      },
    },
  },

  TransferHelper: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('TransferHelper', 'arbitrum')
          assert(contract, 'TransferHelper contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'TransferHelper_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as TransferHelper_arbitrum
      },
    },
  },

  RiseProxy_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseProxy_impl', 'arbitrum')
          assert(contract, 'RiseProxy_impl contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseProxy_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseProxy_impl_arbitrum
      },
    },
  },

  RiseUSD: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseUSD', 'arbitrum')
          assert(contract, 'RiseUSD contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseUSD_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseUSD_arbitrum
      },
    },
  },

  RiseEUR: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseEUR', 'arbitrum')
          assert(contract, 'RiseEUR contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseEUR_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseEUR_arbitrum
      },
    },
  },

  RiseGBP: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseGBP', 'arbitrum')
          assert(contract, 'RiseGBP contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseGBP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseGBP_arbitrum
      },
    },
  },

  RiseRampWithdrawInternationalUSD: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawInternationalUSD',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawInternationalUSD contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawInternationalUSD_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawInternationalUSD_arbitrum
      },
    },
  },

  RiseRampWithdrawInternationalUSDManual: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawInternationalUSDManual',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawInternationalUSDManual contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawInternationalUSDManual_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawInternationalUSDManual_arbitrum
      },
    },
  },

  BokkyPooBahsDateTimeLibrary: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'BokkyPooBahsDateTimeLibrary',
            'arbitrum',
          )
          assert(
            contract,
            'BokkyPooBahsDateTimeLibrary contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'BokkyPooBahsDateTimeLibrary_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as BokkyPooBahsDateTimeLibrary_arbitrum
      },
    },
  },

  RiseRouter: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseRouter', 'arbitrum')
          assert(contract, 'RiseRouter contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRouter_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRouter_arbitrum
      },
    },
  },

  RiseRampWithdrawCCIP: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampWithdrawCCIP',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampWithdrawCCIP contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampWithdrawCCIP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampWithdrawCCIP_arbitrum
      },
    },
  },

  RisePayToken: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RisePayToken', 'arbitrum')
          assert(contract, 'RisePayToken contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayToken_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayToken_arbitrum
      },
    },
  },

  RiseDedicatedFund: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDedicatedFund',
            'arbitrum',
          )
          assert(contract, 'RiseDedicatedFund contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDedicatedFund_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDedicatedFund_arbitrum
      },
    },
  },

  RiseForwarder: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseForwarder', 'arbitrum')
          assert(contract, 'RiseForwarder contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseForwarder_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseForwarder_arbitrum
      },
    },
  },

  RiseAccess: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseAccess', 'arbitrum')
          assert(contract, 'RiseAccess contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseAccess_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseAccess_arbitrum
      },
    },
  },

  RisePayRampEURGBP: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampEURGBP',
            'arbitrum',
          )
          assert(contract, 'RisePayRampEURGBP contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampEURGBP_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampEURGBP_arbitrum
      },
    },
  },

  RisePaySchedules: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePaySchedules',
            'arbitrum',
          )
          assert(contract, 'RisePaySchedules contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePaySchedules_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePaySchedules_arbitrum
      },
    },
  },

  RisePayRampNGN: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RisePayRampNGN', 'arbitrum')
          assert(contract, 'RisePayRampNGN contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampNGN_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampNGN_arbitrum
      },
    },
  },

  RisePay: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RisePay', 'arbitrum')
          assert(contract, 'RisePay contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePay_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePay_arbitrum
      },
    },
  },

  RisePayRampUSDCMainnet: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampUSDCMainnet',
            'arbitrum',
          )
          assert(
            contract,
            'RisePayRampUSDCMainnet contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampUSDCMainnet_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampUSDCMainnet_arbitrum
      },
    },
  },

  USDC: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('USDC', 'arbitrum')
          assert(contract, 'USDC contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'USDC_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as USDC_arbitrum
      },
    },
  },

  EURC: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('EURC', 'arbitrum')
          assert(contract, 'EURC contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'EURC_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as EURC_arbitrum
      },
    },
  },

  RiseIDBusiness: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseIDBusiness', 'arbitrum')
          assert(contract, 'RiseIDBusiness contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseIDBusiness_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseIDBusiness_arbitrum
      },
    },
  },

  RisePayRampUniswap: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampUniswap',
            'arbitrum',
          )
          assert(contract, 'RisePayRampUniswap contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampUniswap_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampUniswap_arbitrum
      },
    },
  },

  RiseFundFulfillment: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseFundFulfillment',
            'arbitrum',
          )
          assert(
            contract,
            'RiseFundFulfillment contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseFundFulfillment_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseFundFulfillment_arbitrum
      },
    },
  },

  RiseIDIndividual: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseIDIndividual',
            'arbitrum',
          )
          assert(contract, 'RiseIDIndividual contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseIDIndividual_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseIDIndividual_arbitrum
      },
    },
  },

  RisePayRampUSDC: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RisePayRampUSDC', 'arbitrum')
          assert(contract, 'RisePayRampUSDC contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampUSDC_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampUSDC_arbitrum
      },
    },
  },

  RisePlannedPayments: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePlannedPayments',
            'arbitrum',
          )
          assert(
            contract,
            'RisePlannedPayments contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePlannedPayments_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePlannedPayments_arbitrum
      },
    },
  },

  RiseIDDAO: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseIDDAO', 'arbitrum')
          assert(contract, 'RiseIDDAO contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseIDDAO_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseIDDAO_arbitrum
      },
    },
  },

  RiseIDFactory: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseIDFactory', 'arbitrum')
          assert(contract, 'RiseIDFactory contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseIDFactory_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseIDFactory_arbitrum
      },
    },
  },

  RisePayRampForEx: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampForEx',
            'arbitrum',
          )
          assert(contract, 'RisePayRampForEx contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampForEx_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampForEx_arbitrum
      },
    },
  },

  RisePayRampUSDInternational: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampUSDInternational',
            'arbitrum',
          )
          assert(
            contract,
            'RisePayRampUSDInternational contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampUSDInternational_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampUSDInternational_arbitrum
      },
    },
  },

  RisePayRampUSDUS: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RisePayRampUSDUS',
            'arbitrum',
          )
          assert(contract, 'RisePayRampUSDUS contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayRampUSDUS_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayRampUSDUS_arbitrum
      },
    },
  },

  RiseGovernor: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseGovernor', 'arbitrum')
          assert(contract, 'RiseGovernor contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseGovernor_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseGovernor_arbitrum
      },
    },
  },

  RiseStorage: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RiseStorage', 'arbitrum')
          assert(contract, 'RiseStorage contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseStorage_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseStorage_arbitrum
      },
    },
  },

  RiseDeterministicDeployFactory: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDeterministicDeployFactory',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDeterministicDeployFactory contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDeterministicDeployFactory_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDeterministicDeployFactory_arbitrum
      },
    },
  },

  RiseDeductionsAndCredits: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseDeductionsAndCredits',
            'arbitrum',
          )
          assert(
            contract,
            'RiseDeductionsAndCredits contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseDeductionsAndCredits_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseDeductionsAndCredits_arbitrum
      },
    },
  },

  RisePayTokenV1: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('RisePayTokenV1', 'arbitrum')
          assert(contract, 'RisePayTokenV1 contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RisePayTokenV1_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RisePayTokenV1_arbitrum
      },
    },
  },

  RiseFinanceGovernor: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseFinanceGovernor',
            'arbitrum',
          )
          assert(
            contract,
            'RiseFinanceGovernor contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseFinanceGovernor_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseFinanceGovernor_arbitrum
      },
    },
  },

  RiseRampCurrencySwap: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampCurrencySwap',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampCurrencySwap contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampCurrencySwap_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampCurrencySwap_arbitrum
      },
    },
  },

  RiseRampCurrencySwap_impl: {
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'RiseRampCurrencySwap_impl',
            'arbitrum',
          )
          assert(
            contract,
            'RiseRampCurrencySwap_impl contract not found for arbitrum',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'RiseRampCurrencySwap_impl_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as RiseRampCurrencySwap_impl_arbitrum
      },
    },
  },

  MessageTransmitter: {
    ethereum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'MessageTransmitter',
            'ethereum',
          )
          assert(contract, 'MessageTransmitter contract not found for ethereum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_ethereum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_ethereum
      },
    },
    base: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract('MessageTransmitter', 'base')
          assert(contract, 'MessageTransmitter contract not found for base')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_base',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_base
      },
    },
    arbitrum: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'MessageTransmitter',
            'arbitrum',
          )
          assert(contract, 'MessageTransmitter contract not found for arbitrum')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_arbitrum',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_arbitrum
      },
    },
    polygon: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'MessageTransmitter',
            'polygon',
          )
          assert(contract, 'MessageTransmitter contract not found for polygon')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_polygon',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_polygon
      },
    },
    avalanche: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'MessageTransmitter',
            'avalanche',
          )
          assert(
            contract,
            'MessageTransmitter contract not found for avalanche',
          )
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_avalanche',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_avalanche
      },
    },
    optimism: {
      connect: async (
        address: string,
        runner?: ContractRunner | null,
        abi?: Interface,
      ) => {
        const _abi = await (async () => {
          if (abi) return abi
          const contract = await getSmartContract(
            'MessageTransmitter',
            'optimism',
          )
          assert(contract, 'MessageTransmitter contract not found for optimism')
          return contract.abi as unknown as Interface
        })()
        const contract = loggifyContract(
          'MessageTransmitter_optimism',
          new Contract(address, _abi, runner),
          address,
        )
        return contract as unknown as MessageTransmitter_optimism
      },
    },
  },
}
