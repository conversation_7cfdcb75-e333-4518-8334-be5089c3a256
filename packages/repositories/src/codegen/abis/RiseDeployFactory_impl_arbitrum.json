[{"type": "constructor", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "AddressEmptyCode", "type": "error", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"name": "AddressInsufficientBalance", "type": "error", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"name": "Create2EmptyBytecode", "type": "error", "inputs": []}, {"name": "Create2FailedDeployment", "type": "error", "inputs": []}, {"name": "Create2InsufficientBalance", "type": "error", "inputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"name": "ERC1167FailedCreateClone", "type": "error", "inputs": []}, {"name": "FailedInnerCall", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_Unauthorized", "type": "error", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "NewInstance", "type": "event", "inputs": [{"name": "instance", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseOwnerAdded", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseOwnerRemoved", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "addOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "clone", "type": "function", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "initData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "computeCloneAddress", "type": "function", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "computeDeployAddress", "type": "function", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "bytecodeHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "deploy", "type": "function", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "bytecode", "type": "bytes", "internalType": "bytes"}, {"name": "initData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "getOwners", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getOwnersLength", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "isOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "removeOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}]