[{"type": "constructor", "inputs": [{"name": "_riseAccess", "type": "address", "internalType": "address"}, {"name": "_risePayToken", "type": "address", "internalType": "address"}, {"name": "_riseStorage", "type": "address", "internalType": "address"}, {"name": "_riseDeductionsCredits", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "PayHash", "type": "event", "inputs": [{"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "salt", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"name": "ScheduleComplete", "type": "event", "inputs": [{"name": "schedule", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "ScheduleCreated", "type": "event", "inputs": [{"name": "schedule", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "relationshipHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"name": "ScheduleExecuted", "type": "event", "inputs": [{"name": "schedule", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "remaining", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"name": "ScheduleFailed", "type": "event", "inputs": [{"name": "schedule", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "ScheduleRemoved", "type": "event", "inputs": [{"name": "schedule", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "payee", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "payable"}, {"name": "addSchedule", "type": "function", "inputs": [{"name": "payeeIdx", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "batchExecuteSchedule", "type": "function", "inputs": [{"name": "paySchedules", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "canBePaid", "type": "function", "inputs": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "canExecute", "type": "function", "inputs": [{"name": "ps", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}, {"name": "blockTime", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "canPay", "type": "function", "inputs": [{"name": "payer", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "executeSchedule", "type": "function", "inputs": [{"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getPaySchedule", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "stateMutability": "view"}, {"name": "getPayScheduleHashPaymentsHashes", "type": "function", "inputs": [{"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "getPayScheduleHashRange", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "startIndex", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getPaySchedulePaymentsHashes", "type": "function", "inputs": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"name": "getPayScheduleRange", "type": "function", "inputs": [{"name": "relationshipHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "startIndex", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "results", "type": "tuple[]", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule[]"}, {"name": "totalAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "ps", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "nextPaymentTime", "type": "function", "inputs": [{"name": "ps", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"name": "remainingPayments", "type": "function", "inputs": [{"name": "ps", "type": "tuple", "components": [{"name": "payer", "type": "address", "internalType": "address"}, {"name": "payee", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "startTime", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint256", "internalType": "uint256"}, {"name": "total", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "internalType": "struct IRiseStorageTypes.PaySchedule"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"name": "removeSchedule", "type": "function", "inputs": [{"name": "psHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseDeductionsAndCredits", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseDeductionsAndCredits"}], "stateMutability": "view"}, {"name": "risePayToken", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRisePayToken"}], "stateMutability": "view"}, {"name": "riseStorage", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseStorage"}], "stateMutability": "view"}, {"name": "upgradeDeductionsAndCedits", "type": "function", "inputs": [{"name": "_riseDeductionsAndCredits", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]