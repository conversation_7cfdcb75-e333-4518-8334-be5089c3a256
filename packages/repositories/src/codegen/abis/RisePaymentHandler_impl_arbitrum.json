[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "AddressInsufficientBalance", "type": "error", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"name": "FailedInnerCall", "type": "error", "inputs": []}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "ReentrancyGuardReentrantCall", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_Unauthorized", "type": "error", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"name": "RiseOwnerAdded", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseOwnerRemoved", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "payable"}, {"name": "addOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "getConfigs", "type": "function", "inputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "configs", "type": "tuple[][]", "components": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "transferType", "type": "uint8", "internalType": "uint8"}, {"name": "fixedOrPercent", "type": "uint8", "internalType": "uint8"}, {"name": "ramp", "type": "address", "internalType": "address"}, {"name": "source", "type": "address", "internalType": "address"}, {"name": "destination", "type": "address", "internalType": "address"}, {"name": "offChainReference", "type": "bytes32", "internalType": "bytes32"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfig[][]"}], "stateMutability": "view"}, {"name": "getCountAndVolumeByHash", "type": "function", "inputs": [{"name": "hash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "volume", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getDayPaymentTrackingHashes", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "epoch", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getMonthPaymentTrackingHashes", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "epoch", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getOwners", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getOwnersLength", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getSenders", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getSettings", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "getTokenAndSenderVolumeByHash", "type": "function", "inputs": [{"name": "tokenHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "senderHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "tokenVolume", "type": "uint256", "internalType": "uint256"}, {"name": "senderVolume", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getTokensUsed", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "_riseRouter", "type": "address", "internalType": "address"}, {"name": "_parentAccount", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "isOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "processEtherTransfer", "type": "function", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"name": "processTokenTransfers", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "processTokenTransfersWithConfig", "type": "function", "inputs": [{"name": "request", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "configs", "type": "tuple[]", "components": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "transferType", "type": "uint8", "internalType": "uint8"}, {"name": "fixedOrPercent", "type": "uint8", "internalType": "uint8"}, {"name": "ramp", "type": "address", "internalType": "address"}, {"name": "source", "type": "address", "internalType": "address"}, {"name": "destination", "type": "address", "internalType": "address"}, {"name": "offChainReference", "type": "bytes32", "internalType": "bytes32"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfig[]"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfigRequest"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "removeOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setSettings", "type": "function", "inputs": [{"name": "accountType", "type": "bytes32", "internalType": "bytes32"}, {"name": "parentAccount", "type": "address", "internalType": "address"}, {"name": "sourceOfFunds", "type": "address", "internalType": "address"}, {"name": "hiddenRiseTokenTransfers", "type": "bool", "internalType": "bool"}, {"name": "sponsorAccount", "type": "address", "internalType": "address"}, {"name": "feeRecipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setTransferRules", "type": "function", "inputs": [{"name": "requests", "type": "tuple[]", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "configs", "type": "tuple[]", "components": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "transferType", "type": "uint8", "internalType": "uint8"}, {"name": "fixedOrPercent", "type": "uint8", "internalType": "uint8"}, {"name": "ramp", "type": "address", "internalType": "address"}, {"name": "source", "type": "address", "internalType": "address"}, {"name": "destination", "type": "address", "internalType": "address"}, {"name": "offChainReference", "type": "bytes32", "internalType": "bytes32"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfig[]"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfigRequest[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]