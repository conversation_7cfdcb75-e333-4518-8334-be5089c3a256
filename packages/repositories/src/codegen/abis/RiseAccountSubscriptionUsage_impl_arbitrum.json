[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"name": "RiseSubscriptionCharge", "type": "event", "inputs": [{"name": "paymentID", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "monthlyChargeHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "getAccountSubscriptionCharge", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "totalDiscount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getAccountSubscriptionChargeRecipients", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "components": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}, {"name": "discount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsed[]"}], "stateMutability": "view"}, {"name": "getAccountSubscriptionConfig", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "subscriptionType", "type": "uint8", "internalType": "enum IRiseAccountSubscriptionUsage.SubscriptionType"}, {"name": "percentDiscount", "type": "uint16", "internalType": "uint16"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseAccountSubscriptionUsage.SubscriptionConfig"}], "stateMutability": "view"}, {"name": "getAccountSubscriptionConfigByMonth", "type": "function", "inputs": [{"name": "monthHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "subscriptionType", "type": "uint8", "internalType": "enum IRiseAccountSubscriptionUsage.SubscriptionType"}, {"name": "percentDiscount", "type": "uint16", "internalType": "uint16"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseAccountSubscriptionUsage.SubscriptionConfig"}], "stateMutability": "view"}, {"name": "getAccountSubscriptionConfigHash", "type": "function", "inputs": [{"name": "monthHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "init", "type": "function", "inputs": [{"name": "_riseRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "predictMonthlySubscriptionSlice", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "contract IRiseAccount"}, {"name": "recipientsByMonthIdx", "type": "uint256", "internalType": "uint256"}, {"name": "recipients<PERSON><PERSON><PERSON><PERSON>hCount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "charges", "type": "tuple[]", "components": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}, {"name": "discount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsed[]"}], "stateMutability": "view"}, {"name": "processMonthlySubscription", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "contract IRiseAccount"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "processMonthlySubscriptionSlice", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "contract IRiseAccount"}, {"name": "recipientsByMonthIdx", "type": "uint256", "internalType": "uint256"}, {"name": "recipients<PERSON><PERSON><PERSON><PERSON>hCount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "setAccountSubscriptionConfig", "type": "function", "inputs": [{"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "config", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "subscriptionType", "type": "uint8", "internalType": "enum IRiseAccountSubscriptionUsage.SubscriptionType"}, {"name": "percentDiscount", "type": "uint16", "internalType": "uint16"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}], "internalType": "struct IRiseAccountSubscriptionUsage.SubscriptionConfig"}, {"name": "setAsDefault", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}]