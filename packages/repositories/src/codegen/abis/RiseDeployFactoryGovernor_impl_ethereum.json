[{"type": "constructor", "inputs": [{"name": "_factory", "type": "address", "internalType": "contract IRiseDeployFactory"}], "stateMutability": "nonpayable"}, {"name": "AddressEmptyCode", "type": "error", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"name": "FailedCall", "type": "error", "inputs": []}, {"name": "InsufficientBalance", "type": "error", "inputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "RiseOwnerAdded", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"name": "RiseOwnerRemoved", "type": "event", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "addOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "deploy", "type": "function", "inputs": [{"name": "implenentationReference", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "initData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "deployAccount", "type": "function", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "parentAccount", "type": "address", "internalType": "address"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "deployPayHandler", "type": "function", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "parentAccount", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "deployRiseID", "type": "function", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "factory", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseDeployFactory"}], "stateMutability": "view"}, {"name": "getOwners", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"name": "getOwnersLength", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "isOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "removeOwner", "type": "function", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setFactory", "type": "function", "inputs": [{"name": "_factory", "type": "address", "internalType": "contract IRiseDeployFactory"}], "outputs": [], "stateMutability": "nonpayable"}]