[{"type": "constructor", "inputs": [{"name": "_riseAccess", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "NewInstance", "type": "event", "inputs": [{"name": "instance", "type": "address", "indexed": true, "internalType": "address"}, {"name": "idx", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"name": "clone", "type": "function", "inputs": [{"name": "implementationIdx", "type": "uint256", "internalType": "uint256"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}]