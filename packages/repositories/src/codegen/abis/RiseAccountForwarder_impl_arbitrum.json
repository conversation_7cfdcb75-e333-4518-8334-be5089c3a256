[{"name": "ERC2771ForwarderExpiredRequest", "type": "error", "inputs": [{"name": "expires", "type": "uint48", "internalType": "uint48"}]}, {"name": "ERC2771ForwarderInvalidSigner", "type": "error", "inputs": [{"name": "from", "type": "address", "internalType": "address"}]}, {"name": "FailedInnerCall", "type": "error", "inputs": []}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_Unauthorized", "type": "error", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "EIP712DomainChanged", "type": "event", "inputs": [], "anonymous": false}, {"name": "ExecutedForwardRequest", "type": "event", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "hash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "success", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "GET_BYTES32_ARRAY_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_CREATEPAYMENTBYSCHEDULEFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "count", "type": "uint32", "internalType": "uint32"}, {"name": "minuteInterval", "type": "uint64", "internalType": "uint64"}, {"name": "payment", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.RisePaymentScheduleRequest"}], "internalType": "struct RiseAccountForwarder.CreatePaymentByScheduleForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_CREATEPAYMENTFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.CreatePaymentForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_CREATEPAYMENTSFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "payments", "type": "tuple[]", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment[]"}], "internalType": "struct RiseAccountForwarder.RisePaymentsRequest"}], "internalType": "struct RiseAccountForwarder.CreatePaymentsForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_INTENTPAYMENTTOSCHEDULEDFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "payAtTime", "type": "uint128[]", "internalType": "uint128[]"}], "internalType": "struct RiseAccountForwarder.RiseIntentPaymentsToScheduledRequest"}], "internalType": "struct RiseAccountForwarder.IntentPaymentToScheduledForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_REMOVEPAYMENTSBYGROUPIDFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentByGroupRequest"}], "internalType": "struct RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_REMOVEPAYMENTSFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentsRequest"}], "internalType": "struct RiseAccountForwarder.RemovePaymentsForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEETHERTRANSFERREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseEtherTransferRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEINTENTPAYMENTSTOSCHEDULEDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "payAtTime", "type": "uint128[]", "internalType": "uint128[]"}], "internalType": "struct RiseAccountForwarder.RiseIntentPaymentsToScheduledRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEPAYMENTSCHEDULEREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "count", "type": "uint32", "internalType": "uint32"}, {"name": "minuteInterval", "type": "uint64", "internalType": "uint64"}, {"name": "payment", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.RisePaymentScheduleRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEPAYMENTSREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "payments", "type": "tuple[]", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment[]"}], "internalType": "struct RiseAccountForwarder.RisePaymentsRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEPAYMENT_ARRAY_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple[]", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEPAYMENT_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEREMOVEPAYMENTBYGROUPREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentByGroupRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEREMOVEPAYMENTSREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentsRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISETOKENAPPROVALREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenApprovalRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISETOKENTRANSFERREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenTransferRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISETRANSACTIONLIMITREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "dailyLimit", "type": "uint256", "internalType": "uint256"}, {"name": "transactionLimit", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTransactionLimitRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_RISEUPDATEAMOUNTPAYMENTSREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "newAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "internalType": "struct RiseAccountForwarder.RiseUpdateAmountPaymentsRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SENDETHERFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseEtherTransferRequest"}], "internalType": "struct RiseAccountForwarder.SendEtherForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SETROLESFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple[]", "components": [{"name": "role", "type": "uint8", "internalType": "uint8"}, {"name": "account", "type": "address", "internalType": "address"}], "internalType": "struct RiseAccountForwarder.SetRole[]"}], "internalType": "struct RiseAccountForwarder.SetRolesForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SETROLE_ARRAY_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple[]", "components": [{"name": "role", "type": "uint8", "internalType": "uint8"}, {"name": "account", "type": "address", "internalType": "address"}], "internalType": "struct RiseAccountForwarder.SetRole[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SETROLE_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "role", "type": "uint8", "internalType": "uint8"}, {"name": "account", "type": "address", "internalType": "address"}], "internalType": "struct RiseAccountForwarder.SetRole"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SETTOKENTRANSFERAPPROVALFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenApprovalRequest"}], "internalType": "struct RiseAccountForwarder.SetTokenTransferApprovalForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_SETTRANSACTIONLIMITSFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "dailyLimit", "type": "uint256", "internalType": "uint256"}, {"name": "transactionLimit", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTransactionLimitRequest"}], "internalType": "struct RiseAccountForwarder.SetTransactionLimitsForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_TOKENTRANSFERFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenTransferRequest"}], "internalType": "struct RiseAccountForwarder.TokenTransferForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_UINT128_ARRAY_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "uint128[]", "internalType": "uint128[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_UINT256_ARRAY_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "GET_UPDATEPAYMENTAMOUNTFORWARDREQUEST_PACKET_HASH", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "newAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "internalType": "struct RiseAccountForwarder.RiseUpdateAmountPaymentsRequest"}], "internalType": "struct RiseAccountForwarder.UpdatePaymentAmountForwardRequest"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "createPayment", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.CreatePaymentForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "createPaymentAndExecute", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.CreatePaymentForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "createPaymentBySchedule", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "count", "type": "uint32", "internalType": "uint32"}, {"name": "minuteInterval", "type": "uint64", "internalType": "uint64"}, {"name": "payment", "type": "tuple", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment"}], "internalType": "struct RiseAccountForwarder.RisePaymentScheduleRequest"}], "internalType": "struct RiseAccountForwarder.CreatePaymentByScheduleForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "createPayments", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "payments", "type": "tuple[]", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment[]"}], "internalType": "struct RiseAccountForwarder.RisePaymentsRequest"}], "internalType": "struct RiseAccountForwarder.CreatePaymentsForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "createPaymentsAndExecute", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "payments", "type": "tuple[]", "components": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}, {"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "payAtTime", "type": "uint128", "internalType": "uint128"}, {"name": "validMinutes", "type": "uint32", "internalType": "uint32"}, {"name": "payType", "type": "uint16", "internalType": "uint16"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes32", "internalType": "bytes32"}], "internalType": "struct RiseAccountForwarder.RisePayment[]"}], "internalType": "struct RiseAccountForwarder.RisePaymentsRequest"}], "internalType": "struct RiseAccountForwarder.CreatePaymentsForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "eip712Domain", "type": "function", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "_riseRouter", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "intentPaymentToScheduled", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "payAtTime", "type": "uint128[]", "internalType": "uint128[]"}], "internalType": "struct RiseAccountForwarder.RiseIntentPaymentsToScheduledRequest"}], "internalType": "struct RiseAccountForwarder.IntentPaymentToScheduledForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "intentPaymentToScheduledAndExecute", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "payAtTime", "type": "uint128[]", "internalType": "uint128[]"}], "internalType": "struct RiseAccountForwarder.RiseIntentPaymentsToScheduledRequest"}], "internalType": "struct RiseAccountForwarder.IntentPaymentToScheduledForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "removePayments", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentsRequest"}], "internalType": "struct RiseAccountForwarder.RemovePaymentsForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "removePaymentsByGroupID", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "groupID", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseRemovePaymentByGroupRequest"}], "internalType": "struct RiseAccountForwarder.RemovePaymentsByGroupIDForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "sendEther", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseEtherTransferRequest"}], "internalType": "struct RiseAccountForwarder.SendEtherForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "setRoles", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple[]", "components": [{"name": "role", "type": "uint8", "internalType": "uint8"}, {"name": "account", "type": "address", "internalType": "address"}], "internalType": "struct RiseAccountForwarder.SetRole[]"}], "internalType": "struct RiseAccountForwarder.SetRolesForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setTokenTransferApproval", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenApprovalRequest"}], "internalType": "struct RiseAccountForwarder.SetTokenTransferApprovalForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "setTransactionLimits", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "dailyLimit", "type": "uint256", "internalType": "uint256"}, {"name": "transactionLimit", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTransactionLimitRequest"}], "internalType": "struct RiseAccountForwarder.SetTransactionLimitsForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "tokenTransfer", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "internalType": "struct RiseAccountForwarder.RiseTokenTransferRequest"}], "internalType": "struct RiseAccountForwarder.TokenTransferForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"name": "updatePaymentAmount", "type": "function", "inputs": [{"name": "_input", "type": "tuple", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "salt", "type": "uint64", "internalType": "uint64"}, {"name": "expires", "type": "uint48", "internalType": "uint48"}, {"name": "data", "type": "tuple", "components": [{"name": "paymentIDs", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "newAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "internalType": "struct RiseAccountForwarder.RiseUpdateAmountPaymentsRequest"}], "internalType": "struct RiseAccountForwarder.UpdatePaymentAmountForwardRequest"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}]