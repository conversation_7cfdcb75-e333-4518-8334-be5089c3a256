[{"name": "getDaySlotHash", "type": "function", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getMonthSlotHash", "type": "function", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getYearSlotHash", "type": "function", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}]