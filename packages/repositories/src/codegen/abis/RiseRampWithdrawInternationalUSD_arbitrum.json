[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"name": "InvalidInitialization", "type": "error", "inputs": []}, {"name": "NotInitializing", "type": "error", "inputs": []}, {"name": "ReentrancyGuardReentrantCall", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequest", "type": "error", "inputs": []}, {"name": "Rise_InvalidRequestWithReason", "type": "error", "inputs": [{"name": "reason", "type": "string", "internalType": "string"}]}, {"name": "Rise_InvalidRequest_Fallback", "type": "error", "inputs": []}, {"name": "Rise_UnauthorizedRole", "type": "error", "inputs": [{"name": "roleHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "caller", "type": "address", "internalType": "address"}]}, {"name": "Initialized", "type": "event", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"name": "RiseRampExecute", "type": "event", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "destination", "type": "address", "indexed": false, "internalType": "address"}, {"name": "references", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "data", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"name": "RiseRampSourceFee", "type": "event", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "destination", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"name": "RiseRampSourceFeeCovered", "type": "event", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "sponsor", "type": "address", "indexed": false, "internalType": "address"}, {"name": "paymentID", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"name": "RiseRampTargetAmount", "type": "event", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "destination", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"name": "RiseRampTargetFee", "type": "event", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "destination", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "nonpayable"}, {"name": "execute", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "config", "type": "tuple", "components": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "transferType", "type": "uint8", "internalType": "uint8"}, {"name": "fixedOrPercent", "type": "uint8", "internalType": "uint8"}, {"name": "ramp", "type": "address", "internalType": "address"}, {"name": "source", "type": "address", "internalType": "address"}, {"name": "destination", "type": "address", "internalType": "address"}, {"name": "offChainReference", "type": "bytes32", "internalType": "bytes32"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "internalType": "struct RiseRequests.RisePaymentHandlerConfig"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"name": "getFeeData", "type": "function", "inputs": [{"name": "keys", "type": "bytes20", "internalType": "bytes20"}], "outputs": [{"name": "", "type": "tuple", "components": [{"name": "amountFlat", "type": "uint96", "internalType": "uint96"}, {"name": "amountPercent", "type": "uint16", "internalType": "uint16"}, {"name": "isSet", "type": "bool", "internalType": "bool"}], "internalType": "struct RiseRampFeeMapStorage.FeeData"}], "stateMutability": "view"}, {"name": "getFeeUSD", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getKeyHash", "type": "function", "inputs": [{"name": "keys", "type": "bytes20", "internalType": "bytes20"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"name": "getMinimumExecuteAmount", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"name": "getMultipleFeeData", "type": "function", "inputs": [{"name": "keys", "type": "bytes20[]", "internalType": "bytes20[]"}], "outputs": [{"name": "", "type": "tuple[]", "components": [{"name": "amountFlat", "type": "uint96", "internalType": "uint96"}, {"name": "amountPercent", "type": "uint16", "internalType": "uint16"}, {"name": "isSet", "type": "bool", "internalType": "bool"}], "internalType": "struct RiseRampFeeMapStorage.FeeData[]"}], "stateMutability": "view"}, {"name": "init", "type": "function", "inputs": [{"name": "_riseRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "type": "function", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"name": "recoverToken", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "riseRouter", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseRouter"}], "stateMutability": "view"}, {"name": "setFee", "type": "function", "inputs": [{"name": "feeData", "type": "tuple[]", "components": [{"name": "key", "type": "bytes20", "internalType": "bytes20"}, {"name": "amountFlat", "type": "uint96", "internalType": "uint96"}, {"name": "amountPercent", "type": "uint16", "internalType": "uint16"}, {"name": "isSet", "type": "bool", "internalType": "bool"}], "internalType": "struct RiseRampFeeMap.FeeDataSet[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setFeeUSD", "type": "function", "inputs": [{"name": "_fee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setMinimumExecuteAmount", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"name": "setRouter", "type": "function", "inputs": [{"name": "_router", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}]