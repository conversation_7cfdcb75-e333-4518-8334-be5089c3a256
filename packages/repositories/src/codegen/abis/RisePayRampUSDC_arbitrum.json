[{"type": "constructor", "inputs": [{"name": "_riseAccess", "type": "address", "internalType": "address"}, {"name": "_riseTreasury", "type": "address", "internalType": "address"}, {"name": "_usdc", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"name": "Fee", "type": "event", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"name": "FeeChargeBack", "type": "event", "inputs": [{"name": "chargedAccount", "type": "address", "indexed": true, "internalType": "address"}, {"name": "coveredAccount", "type": "address", "indexed": true, "internalType": "address"}, {"name": "feeAmount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"name": "Funded", "type": "event", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "feeAmount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"name": "<PERSON><PERSON><PERSON>", "type": "event", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "feeAmount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "dest", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "fallback", "stateMutability": "payable"}, {"name": "USDC", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"name": "fund", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "fundAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"name": "name", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"name": "nameHash", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"name": "riseAccess", "type": "function", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiseAccess"}], "stateMutability": "view"}, {"name": "withdraw", "type": "function", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "dest", "type": "address", "internalType": "address"}], "outputs": [{"name": "withdrawAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}]