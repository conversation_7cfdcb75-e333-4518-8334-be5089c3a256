/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRampDepositCCTP {
  export type DepositCCTPParamsStruct = {
    destinationDomain: BigNumberish
    mintRecipient: BytesLike
  }

  export type DepositCCTPParamsStructOutput = [
    destinationDomain: bigint,
    mintRecipient: string,
  ] & { destinationDomain: bigint; mintRecipient: string }
}

export declare namespace RiseRequests {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }
}

export interface RiseRampDepositCCTP_ethereumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'encodeDepositCCTPParams'
      | 'execute'
      | 'getMinimumExecuteAmount'
      | 'getTokenMessenger'
      | 'init'
      | 'isTrustedForwarder'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setMinimumExecuteAmount'
      | 'setRouter'
      | 'setTokenMessenger',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'DepositCCTPTransferInitiated'
      | 'Initialized'
      | 'RiseRampExecute'
      | 'RiseRampSourceFee'
      | 'RiseRampSourceFeeCovered'
      | 'RiseRampTargetAmount'
      | 'RiseRampTargetFee',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'encodeDepositCCTPParams',
    values: [RiseRampDepositCCTP.DepositCCTPParamsStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'execute',
    values: [
      AddressLike,
      BigNumberish,
      RiseRequests.RisePaymentHandlerConfigStruct,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getMinimumExecuteAmount',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTokenMessenger',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setMinimumExecuteAmount',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setTokenMessenger',
    values: [AddressLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'encodeDepositCCTPParams',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTokenMessenger',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTokenMessenger',
    data: BytesLike,
  ): Result
}

export namespace DepositCCTPTransferInitiatedEvent {
  export type InputTuple = [
    nonce: BigNumberish,
    destinationDomain: BigNumberish,
    mintRecipient: BytesLike,
    amount: BigNumberish,
  ]
  export type OutputTuple = [
    nonce: bigint,
    destinationDomain: bigint,
    mintRecipient: string,
    amount: bigint,
  ]
  export interface OutputObject {
    nonce: bigint
    destinationDomain: bigint
    mintRecipient: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampExecuteEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
    references: BytesLike,
    data: BytesLike,
  ]
  export type OutputTuple = [
    token: string,
    amount: bigint,
    destination: string,
    references: string,
    data: string,
  ]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
    references: string
    data: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeCoveredEvent {
  export type InputTuple = [
    amount: BigNumberish,
    sponsor: AddressLike,
    paymentID: BytesLike,
  ]
  export type OutputTuple = [amount: bigint, sponsor: string, paymentID: string]
  export interface OutputObject {
    amount: bigint
    sponsor: string
    paymentID: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetAmountEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
  ]
  export type OutputTuple = [token: string, amount: bigint, destination: string]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseRampDepositCCTP_ethereum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseRampDepositCCTP_ethereum
  waitForDeployment(): Promise<this>

  interface: RiseRampDepositCCTP_ethereumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  encodeDepositCCTPParams: TypedContractMethod<
    [params: RiseRampDepositCCTP.DepositCCTPParamsStruct],
    [string],
    'view'
  >

  execute: TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >

  getMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike],
    [bigint],
    'view'
  >

  getTokenMessenger: TypedContractMethod<[], [string], 'view'>

  init: TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setTokenMessenger: TypedContractMethod<
    [tokenMessenger: AddressLike],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'encodeDepositCCTPParams',
  ): TypedContractMethod<
    [params: RiseRampDepositCCTP.DepositCCTPParamsStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'execute',
  ): TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getMinimumExecuteAmount',
  ): TypedContractMethod<[token: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getTokenMessenger',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setMinimumExecuteAmount',
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setTokenMessenger',
  ): TypedContractMethod<[tokenMessenger: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'DepositCCTPTransferInitiated',
  ): TypedContractEvent<
    DepositCCTPTransferInitiatedEvent.InputTuple,
    DepositCCTPTransferInitiatedEvent.OutputTuple,
    DepositCCTPTransferInitiatedEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampExecute',
  ): TypedContractEvent<
    RiseRampExecuteEvent.InputTuple,
    RiseRampExecuteEvent.OutputTuple,
    RiseRampExecuteEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFee',
  ): TypedContractEvent<
    RiseRampSourceFeeEvent.InputTuple,
    RiseRampSourceFeeEvent.OutputTuple,
    RiseRampSourceFeeEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFeeCovered',
  ): TypedContractEvent<
    RiseRampSourceFeeCoveredEvent.InputTuple,
    RiseRampSourceFeeCoveredEvent.OutputTuple,
    RiseRampSourceFeeCoveredEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetAmount',
  ): TypedContractEvent<
    RiseRampTargetAmountEvent.InputTuple,
    RiseRampTargetAmountEvent.OutputTuple,
    RiseRampTargetAmountEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetFee',
  ): TypedContractEvent<
    RiseRampTargetFeeEvent.InputTuple,
    RiseRampTargetFeeEvent.OutputTuple,
    RiseRampTargetFeeEvent.OutputObject
  >

  filters: {
    'DepositCCTPTransferInitiated(uint64,uint32,bytes32,uint256)': TypedContractEvent<
      DepositCCTPTransferInitiatedEvent.InputTuple,
      DepositCCTPTransferInitiatedEvent.OutputTuple,
      DepositCCTPTransferInitiatedEvent.OutputObject
    >
    DepositCCTPTransferInitiated: TypedContractEvent<
      DepositCCTPTransferInitiatedEvent.InputTuple,
      DepositCCTPTransferInitiatedEvent.OutputTuple,
      DepositCCTPTransferInitiatedEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseRampExecute(address,uint256,address,bytes32,bytes)': TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >
    RiseRampExecute: TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >

    'RiseRampSourceFee(uint256,address)': TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >
    RiseRampSourceFee: TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >

    'RiseRampSourceFeeCovered(uint256,address,bytes32)': TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >
    RiseRampSourceFeeCovered: TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >

    'RiseRampTargetAmount(address,uint256,address)': TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >
    RiseRampTargetAmount: TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >

    'RiseRampTargetFee(uint256,address)': TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
    RiseRampTargetFee: TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
  }
}
