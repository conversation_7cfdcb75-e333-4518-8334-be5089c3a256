/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  BokkyPooBahsDateTimeLibrary_arbitrum,
  BokkyPooBahsDateTimeLibrary_arbitrumInterface,
} from '../BokkyPooBahsDateTimeLibrary_arbitrum.js'

const _abi = [
  {
    name: 'getDaySlotHash',
    type: 'function',
    inputs: [
      {
        name: 'timestamp',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'getMonthSlotHash',
    type: 'function',
    inputs: [
      {
        name: 'timestamp',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'getYearSlotHash',
    type: 'function',
    inputs: [
      {
        name: 'timestamp',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
] as const

export class BokkyPooBahsDateTimeLibrary_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): BokkyPooBahsDateTimeLibrary_arbitrumInterface {
    return new Interface(_abi) as BokkyPooBahsDateTimeLibrary_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): BokkyPooBahsDateTimeLibrary_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as BokkyPooBahsDateTimeLibrary_arbitrum
  }
}
