/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePaymentHandlerForwarder_impl_arbitrum,
  RisePaymentHandlerForwarder_impl_arbitrumInterface,
} from '../RisePaymentHandlerForwarder_impl_arbitrum.js'

const _abi = [
  {
    name: 'ERC2771ForwarderExpiredRequest',
    type: 'error',
    inputs: [
      {
        name: 'expires',
        type: 'uint48',
        internalType: 'uint48',
      },
    ],
  },
  {
    name: 'ERC2771ForwarderInvalidSigner',
    type: 'error',
    inputs: [
      {
        name: 'from',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'FailedInnerCall',
    type: 'error',
    inputs: [],
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_Unauthorized',
    type: 'error',
    inputs: [
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'EIP712DomainChanged',
    type: 'event',
    inputs: [],
    anonymous: false,
  },
  {
    name: 'ExecutedForwardRequest',
    type: 'event',
    inputs: [
      {
        name: 'from',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'to',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'hash',
        type: 'bytes32',
        indexed: false,
        internalType: 'bytes32',
      },
      {
        name: 'success',
        type: 'bool',
        indexed: false,
        internalType: 'bool',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: 'GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'configs',
                type: 'tuple[]',
                components: [
                  {
                    name: 'amount',
                    type: 'uint256',
                    internalType: 'uint256',
                  },
                  {
                    name: 'transferType',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'fixedOrPercent',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'ramp',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'source',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'destination',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'offChainReference',
                    type: 'bytes32',
                    internalType: 'bytes32',
                  },
                  {
                    name: 'data',
                    type: 'bytes',
                    internalType: 'bytes',
                  },
                ],
                internalType:
                  'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple[]',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'configs',
            type: 'tuple[]',
            components: [
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'transferType',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'fixedOrPercent',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'ramp',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'source',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'destination',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'offChainReference',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'configs',
            type: 'tuple[]',
            components: [
              {
                name: 'amount',
                type: 'uint256',
                internalType: 'uint256',
              },
              {
                name: 'transferType',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'fixedOrPercent',
                type: 'uint8',
                internalType: 'uint8',
              },
              {
                name: 'ramp',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'source',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'destination',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'offChainReference',
                type: 'bytes32',
                internalType: 'bytes32',
              },
              {
                name: 'data',
                type: 'bytes',
                internalType: 'bytes',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple[]',
        components: [
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'transferType',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'fixedOrPercent',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'ramp',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'source',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'destination',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'offChainReference',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'data',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'transferType',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'fixedOrPercent',
            type: 'uint8',
            internalType: 'uint8',
          },
          {
            name: 'ramp',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'source',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'destination',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'offChainReference',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'data',
            type: 'bytes',
            internalType: 'bytes',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple[]',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'configs',
                type: 'tuple[]',
                components: [
                  {
                    name: 'amount',
                    type: 'uint256',
                    internalType: 'uint256',
                  },
                  {
                    name: 'transferType',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'fixedOrPercent',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'ramp',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'source',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'destination',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'offChainReference',
                    type: 'bytes32',
                    internalType: 'bytes32',
                  },
                  {
                    name: 'data',
                    type: 'bytes',
                    internalType: 'bytes',
                  },
                ],
                internalType:
                  'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest[]',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.SetTransferRulesForwardRequest',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'eip712Domain',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: 'fields',
        type: 'bytes1',
        internalType: 'bytes1',
      },
      {
        name: 'name',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'version',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'verifyingContract',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'salt',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'extensions',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_riseRouter',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'name',
        type: 'string',
        internalType: 'string',
      },
      {
        name: 'version',
        type: 'string',
        internalType: 'string',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'processTokenTransfersWithConfig',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'configs',
                type: 'tuple[]',
                components: [
                  {
                    name: 'amount',
                    type: 'uint256',
                    internalType: 'uint256',
                  },
                  {
                    name: 'transferType',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'fixedOrPercent',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'ramp',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'source',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'destination',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'offChainReference',
                    type: 'bytes32',
                    internalType: 'bytes32',
                  },
                  {
                    name: 'data',
                    type: 'bytes',
                    internalType: 'bytes',
                  },
                ],
                internalType:
                  'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setTransferRules',
    type: 'function',
    inputs: [
      {
        name: '_input',
        type: 'tuple',
        components: [
          {
            name: 'from',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'to',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'salt',
            type: 'uint64',
            internalType: 'uint64',
          },
          {
            name: 'expires',
            type: 'uint48',
            internalType: 'uint48',
          },
          {
            name: 'data',
            type: 'tuple[]',
            components: [
              {
                name: 'token',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'configs',
                type: 'tuple[]',
                components: [
                  {
                    name: 'amount',
                    type: 'uint256',
                    internalType: 'uint256',
                  },
                  {
                    name: 'transferType',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'fixedOrPercent',
                    type: 'uint8',
                    internalType: 'uint8',
                  },
                  {
                    name: 'ramp',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'source',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'destination',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'offChainReference',
                    type: 'bytes32',
                    internalType: 'bytes32',
                  },
                  {
                    name: 'data',
                    type: 'bytes',
                    internalType: 'bytes',
                  },
                ],
                internalType:
                  'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfig[]',
              },
            ],
            internalType:
              'struct RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequest[]',
          },
        ],
        internalType:
          'struct RisePaymentHandlerForwarder.SetTransferRulesForwardRequest',
      },
      {
        name: 'signature',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    outputs: [
      {
        name: 'success',
        type: 'bool',
        internalType: 'bool',
      },
      {
        name: 'returnData',
        type: 'bytes',
        internalType: 'bytes',
      },
    ],
    stateMutability: 'nonpayable',
  },
] as const

export class RisePaymentHandlerForwarder_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePaymentHandlerForwarder_impl_arbitrumInterface {
    return new Interface(
      _abi,
    ) as RisePaymentHandlerForwarder_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePaymentHandlerForwarder_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RisePaymentHandlerForwarder_impl_arbitrum
  }
}
