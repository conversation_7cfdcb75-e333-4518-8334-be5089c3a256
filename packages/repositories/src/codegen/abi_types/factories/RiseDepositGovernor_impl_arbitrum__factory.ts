/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseDepositGovernor_impl_arbitrum,
  RiseDepositGovernor_impl_arbitrumInterface,
} from '../RiseDepositGovernor_impl_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest_Fallback',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'DepositProcessed',
    type: 'event',
    inputs: [
      {
        name: 'id',
        type: 'bytes32',
        indexed: true,
        internalType: 'bytes32',
      },
      {
        name: 'newStatus',
        type: 'uint8',
        indexed: false,
        internalType: 'enum RiseGovernors.DepositStatus',
      },
      {
        name: 'oldStatus',
        type: 'uint8',
        indexed: false,
        internalType: 'enum RiseGovernors.DepositStatus',
      },
      {
        name: 'references',
        type: 'bytes32',
        indexed: false,
        internalType: 'bytes32',
      },
    ],
    anonymous: false,
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'nonpayable',
  },
  {
    name: 'depositStatuses',
    type: 'function',
    inputs: [
      {
        name: 'ids',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint8[]',
        internalType: 'enum RiseGovernors.DepositStatus[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'deposits',
    type: 'function',
    inputs: [
      {
        name: 'ids',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'references',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseGovernors.Deposit[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: 'riseRouter',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'processDeposits',
    type: 'function',
    inputs: [
      {
        name: '_deposits',
        type: 'tuple[]',
        components: [
          {
            name: 'id',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'token',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'recipient',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'references',
            type: 'bytes32',
            internalType: 'bytes32',
          },
        ],
        internalType: 'struct RiseGovernors.Deposit[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseDepositGovernor_impl_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseDepositGovernor_impl_arbitrumInterface {
    return new Interface(_abi) as RiseDepositGovernor_impl_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseDepositGovernor_impl_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseDepositGovernor_impl_arbitrum
  }
}
