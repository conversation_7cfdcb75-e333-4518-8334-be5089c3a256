/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePay_arbitrum,
  RisePay_arbitrumInterface,
} from '../RisePay_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_risePayToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseStorage',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseDeductionsCredits',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'PayHash',
    type: 'event',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'salt',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    name: 'PayeeAdded',
    type: 'event',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    name: 'PayeeRemoved',
    type: 'event',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'addPayee',
    type: 'function',
    inputs: [
      {
        name: 'payeeIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'batchPay',
    type: 'function',
    inputs: [
      {
        name: 'totalAmount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'payments',
        type: 'tuple[]',
        components: [
          {
            name: 'recipientIdx',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'amount',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'salt',
            type: 'uint256',
            internalType: 'uint256',
          },
        ],
        internalType: 'struct IRisePay.Payment[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'canBePaid',
    type: 'function',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'payee',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'canPay',
    type: 'function',
    inputs: [
      {
        name: 'payer',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'canRampFund',
    type: 'function',
    inputs: [
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'canRampWithdraw',
    type: 'function',
    inputs: [
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'sender',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'fund',
    type: 'function',
    inputs: [
      {
        name: 'tokenIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'fund',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'isTrustedForwarder',
    type: 'function',
    inputs: [
      {
        name: 'forwarder',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'pay',
    type: 'function',
    inputs: [
      {
        name: 'recipientIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'salt',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'removePayee',
    type: 'function',
    inputs: [
      {
        name: 'payeeIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseDeductionsAndCredits',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseDeductionsAndCredits',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseFundID',
    type: 'function',
    inputs: [
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'accountIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'risePayToken',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRisePayToken',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseStorage',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseStorage',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'upgradeDeductionsAndCedits',
    type: 'function',
    inputs: [
      {
        name: '_riseDeductionsAndCredits',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'tokenIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'destIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'dest',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'destIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'withdraw',
    type: 'function',
    inputs: [
      {
        name: 'ramp',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RisePay_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePay_arbitrumInterface {
    return new Interface(_abi) as RisePay_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePay_arbitrum {
    return new Contract(address, _abi, runner) as unknown as RisePay_arbitrum
  }
}
