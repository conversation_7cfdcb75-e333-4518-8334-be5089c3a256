/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseFundFulfillment_arbitrum,
  RiseFundFulfillment_arbitrumInterface,
} from '../RiseFundFulfillment_arbitrum.js'

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: '_risePay',
        type: 'address',
        internalType: 'address',
      },
      {
        name: '_riseAccess',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'fallback',
    stateMutability: 'payable',
  },
  {
    name: 'contractLimit',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'dailyLimits',
    type: 'function',
    inputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'fulfill',
    type: 'function',
    inputs: [
      {
        name: 'txHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'logIndex',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'fulfilled',
    type: 'function',
    inputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getDailyLimitKey',
    type: 'function',
    inputs: [
      {
        name: 'riseId',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getFulfillKey',
    type: 'function',
    inputs: [
      {
        name: 'txHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'logIndex',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bytes32',
        internalType: 'bytes32',
      },
    ],
    stateMutability: 'pure',
  },
  {
    name: 'isTransferFulfilled',
    type: 'function',
    inputs: [
      {
        name: 'txHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'logIndex',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'manualFulfill',
    type: 'function',
    inputs: [
      {
        name: 'txHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'logIndex',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'chainId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'rampIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'risePay',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRisePay',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setContractLimits',
    type: 'function',
    inputs: [
      {
        name: '_contractLimit',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: '_t3Limit',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: '_t2Limit',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRisePay',
    type: 'function',
    inputs: [
      {
        name: '_risePay',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 't2Limit',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 't3Limit',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'withinLimits',
    type: 'function',
    inputs: [
      {
        name: 'riseId',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'dailyLimitKey',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'withinTodaysLimit',
    type: 'function',
    inputs: [
      {
        name: 'riseId',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'zeroLimit',
    type: 'function',
    inputs: [
      {
        name: 'riseIdIdx',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
] as const

export class RiseFundFulfillment_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseFundFulfillment_arbitrumInterface {
    return new Interface(_abi) as RiseFundFulfillment_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseFundFulfillment_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseFundFulfillment_arbitrum
  }
}
