/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RisePricesOracle_arbitrum,
  RisePricesOracle_arbitrumInterface,
} from '../RisePricesOracle_arbitrum.js'

const _abi = [
  {
    name: 'InvalidInitialization',
    type: 'error',
    inputs: [],
  },
  {
    name: 'NotInitializing',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequest',
    type: 'error',
    inputs: [],
  },
  {
    name: 'Rise_InvalidRequestWithReason',
    type: 'error',
    inputs: [
      {
        name: 'reason',
        type: 'string',
        internalType: 'string',
      },
    ],
  },
  {
    name: 'Rise_UnauthorizedRole',
    type: 'error',
    inputs: [
      {
        name: 'roleHash',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'caller',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    name: 'Initialized',
    type: 'event',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    name: 'calculatePrice',
    type: 'function',
    inputs: [
      {
        name: 'sourceToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'destinationToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'getPriceRate',
    type: 'function',
    inputs: [
      {
        name: 'sourceToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'destinationToken',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'init',
    type: 'function',
    inputs: [
      {
        name: '_riseRouter',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'recoverToken',
    type: 'function',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'riseAccess',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseAccess',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'riseRouter',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IRiseRouter',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'setPriceRate',
    type: 'function',
    inputs: [
      {
        name: 'sourceToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'destinationToken',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'rate',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'setRouter',
    type: 'function',
    inputs: [
      {
        name: '_router',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RisePricesOracle_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RisePricesOracle_arbitrumInterface {
    return new Interface(_abi) as RisePricesOracle_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RisePricesOracle_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RisePricesOracle_arbitrum
  }
}
