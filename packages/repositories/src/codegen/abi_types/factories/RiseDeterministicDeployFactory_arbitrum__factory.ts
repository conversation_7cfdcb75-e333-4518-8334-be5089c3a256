/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from 'ethers'
import type {
  RiseDeterministicDeployFactory_arbitrum,
  RiseDeterministicDeployFactory_arbitrumInterface,
} from '../RiseDeterministicDeployFactory_arbitrum.js'

const _abi = [
  {
    name: 'NewInstance',
    type: 'event',
    inputs: [
      {
        name: 'instance',
        type: 'address',
        indexed: false,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    name: 'OwnershipTransferred',
    type: 'event',
    inputs: [
      {
        name: 'previousOwner',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
      {
        name: 'newOwner',
        type: 'address',
        indexed: true,
        internalType: 'address',
      },
    ],
    anonymous: false,
  },
  {
    name: 'clone',
    type: 'function',
    inputs: [
      {
        name: 'implementation',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'salt',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    name: 'owner',
    type: 'function',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'predict',
    type: 'function',
    inputs: [
      {
        name: 'implementation',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'salt',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    name: 'renounceOwnership',
    type: 'function',
    inputs: [],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    name: 'transferOwnership',
    type: 'function',
    inputs: [
      {
        name: 'newOwner',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const

export class RiseDeterministicDeployFactory_arbitrum__factory {
  static readonly abi = _abi
  static createInterface(): RiseDeterministicDeployFactory_arbitrumInterface {
    return new Interface(
      _abi,
    ) as RiseDeterministicDeployFactory_arbitrumInterface
  }
  static connect(
    address: string,
    runner?: ContractRunner | null,
  ): RiseDeterministicDeployFactory_arbitrum {
    return new Contract(
      address,
      _abi,
      runner,
    ) as unknown as RiseDeterministicDeployFactory_arbitrum
  }
}
