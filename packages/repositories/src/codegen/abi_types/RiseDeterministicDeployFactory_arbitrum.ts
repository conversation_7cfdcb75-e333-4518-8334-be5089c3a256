/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseDeterministicDeployFactory_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'clone'
      | 'owner'
      | 'predict'
      | 'renounceOwnership'
      | 'transferOwnership',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'NewInstance' | 'OwnershipTransferred',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'clone',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'predict',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'renounceOwnership',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'transferOwnership',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'clone', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'predict', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'renounceOwnership',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'transferOwnership',
    data: BytesLike,
  ): Result
}

export namespace NewInstanceEvent {
  export type InputTuple = [instance: AddressLike]
  export type OutputTuple = [instance: string]
  export interface OutputObject {
    instance: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike]
  export type OutputTuple = [previousOwner: string, newOwner: string]
  export interface OutputObject {
    previousOwner: string
    newOwner: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseDeterministicDeployFactory_arbitrum extends BaseContract {
  connect(
    runner?: ContractRunner | null,
  ): RiseDeterministicDeployFactory_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseDeterministicDeployFactory_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  clone: TypedContractMethod<
    [implementation: AddressLike, salt: BigNumberish],
    [string],
    'nonpayable'
  >

  owner: TypedContractMethod<[], [string], 'view'>

  predict: TypedContractMethod<
    [implementation: AddressLike, salt: BigNumberish],
    [string],
    'view'
  >

  renounceOwnership: TypedContractMethod<[], [void], 'nonpayable'>

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'clone',
  ): TypedContractMethod<
    [implementation: AddressLike, salt: BigNumberish],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'owner',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'predict',
  ): TypedContractMethod<
    [implementation: AddressLike, salt: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'renounceOwnership',
  ): TypedContractMethod<[], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'transferOwnership',
  ): TypedContractMethod<[newOwner: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'NewInstance',
  ): TypedContractEvent<
    NewInstanceEvent.InputTuple,
    NewInstanceEvent.OutputTuple,
    NewInstanceEvent.OutputObject
  >
  getEvent(
    key: 'OwnershipTransferred',
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >

  filters: {
    'NewInstance(address)': TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >
    NewInstance: TypedContractEvent<
      NewInstanceEvent.InputTuple,
      NewInstanceEvent.OutputTuple,
      NewInstanceEvent.OutputObject
    >

    'OwnershipTransferred(address,address)': TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >
  }
}
