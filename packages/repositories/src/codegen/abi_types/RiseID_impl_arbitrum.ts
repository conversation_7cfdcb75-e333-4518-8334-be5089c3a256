/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type ApprovalChangeStruct = {
    incOrDec: boolean
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type ApprovalChangeStructOutput = [
    incOrDec: boolean,
    token: string,
    spender: string,
    amount: bigint,
  ] & { incOrDec: boolean; token: string; spender: string; amount: bigint }

  export type ApproveStruct = {
    token: AddressLike
    spender: AddressLike
    amount: BigNumberish
  }

  export type ApproveStructOutput = [
    token: string,
    spender: string,
    amount: bigint,
  ] & { token: string; spender: string; amount: bigint }

  export type ExecutionStruct = {
    to: AddressLike
    method: BytesLike
    data: BytesLike
  }

  export type ExecutionStructOutput = [
    to: string,
    method: string,
    data: string,
  ] & { to: string; method: string; data: string }

  export type MemberRoleStruct = { member: AddressLike; role: BigNumberish }

  export type MemberRoleStructOutput = [member: string, role: bigint] & {
    member: string
    role: bigint
  }

  export type DatasetStruct = { dataKey: BytesLike; dataValue: BytesLike }

  export type DatasetStructOutput = [dataKey: string, dataValue: string] & {
    dataKey: string
    dataValue: string
  }

  export type SetRoleStruct = { role: BigNumberish; account: AddressLike }

  export type SetRoleStructOutput = [role: bigint, account: string] & {
    role: bigint
    account: string
  }

  export type TransferStruct = {
    token: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type TransferStructOutput = [
    token: string,
    to: string,
    amount: bigint,
  ] & { token: string; to: string; amount: bigint }

  export type TransferFromStruct = {
    token: AddressLike
    from: AddressLike
    to: AddressLike
    amount: BigNumberish
  }

  export type TransferFromStructOutput = [
    token: string,
    from: string,
    to: string,
    amount: bigint,
  ] & { token: string; from: string; to: string; amount: bigint }
}

export interface RiseID_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'approve((bool,address,address,uint256))'
      | 'approve((address,address,uint256))'
      | 'call'
      | 'execute(uint256,address,uint256,bytes)'
      | 'execute((address,bytes,bytes))'
      | 'executeBatch'
      | 'getData'
      | 'getDataBatch'
      | 'getMemberRole'
      | 'getMembers'
      | 'getMembersRoles'
      | 'getRoleMembers'
      | 'init'
      | 'initData'
      | 'isTrustedForwarder'
      | 'isValidSignature'
      | 'recoverOwnership'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setData(bytes32,bytes)'
      | 'setData((bytes32,bytes))'
      | 'setDataBatch'
      | 'setRoles'
      | 'setRouter'
      | 'supportsInterface'
      | 'transfer'
      | 'transferFrom',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'ContractCreated'
      | 'DataChanged'
      | 'Executed'
      | 'Initialized',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'approve((bool,address,address,uint256))',
    values: [RiseRequests.ApprovalChangeStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'approve((address,address,uint256))',
    values: [RiseRequests.ApproveStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'call',
    values: [RiseRequests.ExecutionStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'execute(uint256,address,uint256,bytes)',
    values: [BigNumberish, AddressLike, BigNumberish, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'execute((address,bytes,bytes))',
    values: [RiseRequests.ExecutionStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'executeBatch',
    values: [BigNumberish[], AddressLike[], BigNumberish[], BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'getData', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'getDataBatch',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'getMemberRole',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'getMembers', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getMembersRoles',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getRoleMembers',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'initData', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'isValidSignature',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverOwnership',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setData(bytes32,bytes)',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setData((bytes32,bytes))',
    values: [RiseRequests.DatasetStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'setDataBatch',
    values: [BytesLike[], BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setRoles',
    values: [RiseRequests.SetRoleStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'transfer',
    values: [RiseRequests.TransferStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom',
    values: [RiseRequests.TransferFromStruct],
  ): string

  decodeFunctionResult(
    functionFragment: 'approve((bool,address,address,uint256))',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'approve((address,address,uint256))',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'call', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'execute(uint256,address,uint256,bytes)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'execute((address,bytes,bytes))',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executeBatch',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getDataBatch',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMemberRole',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getMembers', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMembersRoles',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getRoleMembers',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'initData', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isValidSignature',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverOwnership',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setData(bytes32,bytes)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setData((bytes32,bytes))',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setDataBatch',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRoles', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'transfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom',
    data: BytesLike,
  ): Result
}

export namespace ContractCreatedEvent {
  export type InputTuple = [
    operationType: BigNumberish,
    contractAddress: AddressLike,
    value: BigNumberish,
    salt: BytesLike,
  ]
  export type OutputTuple = [
    operationType: bigint,
    contractAddress: string,
    value: bigint,
    salt: string,
  ]
  export interface OutputObject {
    operationType: bigint
    contractAddress: string
    value: bigint
    salt: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace DataChangedEvent {
  export type InputTuple = [dataKey: BytesLike, dataValue: BytesLike]
  export type OutputTuple = [dataKey: string, dataValue: string]
  export interface OutputObject {
    dataKey: string
    dataValue: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ExecutedEvent {
  export type InputTuple = [
    operationType: BigNumberish,
    target: AddressLike,
    value: BigNumberish,
    selector: BytesLike,
  ]
  export type OutputTuple = [
    operationType: bigint,
    target: string,
    value: bigint,
    selector: string,
  ]
  export interface OutputObject {
    operationType: bigint
    target: string
    value: bigint
    selector: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseID_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseID_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseID_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  'approve((bool,address,address,uint256))': TypedContractMethod<
    [req: RiseRequests.ApprovalChangeStruct],
    [void],
    'nonpayable'
  >

  'approve((address,address,uint256))': TypedContractMethod<
    [req: RiseRequests.ApproveStruct],
    [void],
    'nonpayable'
  >

  call: TypedContractMethod<
    [req: RiseRequests.ExecutionStruct],
    [string],
    'view'
  >

  'execute(uint256,address,uint256,bytes)': TypedContractMethod<
    [
      operationType: BigNumberish,
      target: AddressLike,
      value: BigNumberish,
      data: BytesLike,
    ],
    [string],
    'payable'
  >

  'execute((address,bytes,bytes))': TypedContractMethod<
    [req: RiseRequests.ExecutionStruct],
    [void],
    'nonpayable'
  >

  executeBatch: TypedContractMethod<
    [
      operationsType: BigNumberish[],
      targets: AddressLike[],
      values: BigNumberish[],
      datas: BytesLike[],
    ],
    [string[]],
    'payable'
  >

  getData: TypedContractMethod<[dataKey: BytesLike], [string], 'view'>

  getDataBatch: TypedContractMethod<[dataKeys: BytesLike[]], [string[]], 'view'>

  getMemberRole: TypedContractMethod<[member: AddressLike], [bigint], 'view'>

  getMembers: TypedContractMethod<[], [string[]], 'view'>

  getMembersRoles: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [RiseRequests.MemberRoleStructOutput[]],
    'view'
  >

  getRoleMembers: TypedContractMethod<[role: BigNumberish], [string[]], 'view'>

  init: TypedContractMethod<
    [_riseRouter: AddressLike, newOwner: AddressLike],
    [void],
    'nonpayable'
  >

  initData: TypedContractMethod<[data: BytesLike], [string], 'view'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  isValidSignature: TypedContractMethod<
    [hash: BytesLike, signature: BytesLike],
    [string],
    'view'
  >

  recoverOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  'setData(bytes32,bytes)': TypedContractMethod<
    [dataKey: BytesLike, dataValue: BytesLike],
    [void],
    'payable'
  >

  'setData((bytes32,bytes))': TypedContractMethod<
    [req: RiseRequests.DatasetStruct],
    [void],
    'nonpayable'
  >

  setDataBatch: TypedContractMethod<
    [dataKeys: BytesLike[], dataValues: BytesLike[]],
    [void],
    'payable'
  >

  setRoles: TypedContractMethod<
    [req: RiseRequests.SetRoleStruct[]],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  supportsInterface: TypedContractMethod<
    [interfaceId: BytesLike],
    [boolean],
    'view'
  >

  transfer: TypedContractMethod<
    [req: RiseRequests.TransferStruct],
    [void],
    'nonpayable'
  >

  transferFrom: TypedContractMethod<
    [req: RiseRequests.TransferFromStruct],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'approve((bool,address,address,uint256))',
  ): TypedContractMethod<
    [req: RiseRequests.ApprovalChangeStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'approve((address,address,uint256))',
  ): TypedContractMethod<
    [req: RiseRequests.ApproveStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'call',
  ): TypedContractMethod<[req: RiseRequests.ExecutionStruct], [string], 'view'>
  getFunction(
    nameOrSignature: 'execute(uint256,address,uint256,bytes)',
  ): TypedContractMethod<
    [
      operationType: BigNumberish,
      target: AddressLike,
      value: BigNumberish,
      data: BytesLike,
    ],
    [string],
    'payable'
  >
  getFunction(
    nameOrSignature: 'execute((address,bytes,bytes))',
  ): TypedContractMethod<
    [req: RiseRequests.ExecutionStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'executeBatch',
  ): TypedContractMethod<
    [
      operationsType: BigNumberish[],
      targets: AddressLike[],
      values: BigNumberish[],
      datas: BytesLike[],
    ],
    [string[]],
    'payable'
  >
  getFunction(
    nameOrSignature: 'getData',
  ): TypedContractMethod<[dataKey: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getDataBatch',
  ): TypedContractMethod<[dataKeys: BytesLike[]], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getMemberRole',
  ): TypedContractMethod<[member: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getMembers',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getMembersRoles',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [RiseRequests.MemberRoleStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getRoleMembers',
  ): TypedContractMethod<[role: BigNumberish], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, newOwner: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'initData',
  ): TypedContractMethod<[data: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'isValidSignature',
  ): TypedContractMethod<
    [hash: BytesLike, signature: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'recoverOwnership',
  ): TypedContractMethod<[newOwner: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setData(bytes32,bytes)',
  ): TypedContractMethod<
    [dataKey: BytesLike, dataValue: BytesLike],
    [void],
    'payable'
  >
  getFunction(
    nameOrSignature: 'setData((bytes32,bytes))',
  ): TypedContractMethod<
    [req: RiseRequests.DatasetStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setDataBatch',
  ): TypedContractMethod<
    [dataKeys: BytesLike[], dataValues: BytesLike[]],
    [void],
    'payable'
  >
  getFunction(
    nameOrSignature: 'setRoles',
  ): TypedContractMethod<
    [req: RiseRequests.SetRoleStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'supportsInterface',
  ): TypedContractMethod<[interfaceId: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'transfer',
  ): TypedContractMethod<
    [req: RiseRequests.TransferStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom',
  ): TypedContractMethod<
    [req: RiseRequests.TransferFromStruct],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'ContractCreated',
  ): TypedContractEvent<
    ContractCreatedEvent.InputTuple,
    ContractCreatedEvent.OutputTuple,
    ContractCreatedEvent.OutputObject
  >
  getEvent(
    key: 'DataChanged',
  ): TypedContractEvent<
    DataChangedEvent.InputTuple,
    DataChangedEvent.OutputTuple,
    DataChangedEvent.OutputObject
  >
  getEvent(
    key: 'Executed',
  ): TypedContractEvent<
    ExecutedEvent.InputTuple,
    ExecutedEvent.OutputTuple,
    ExecutedEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'ContractCreated(uint256,address,uint256,bytes32)': TypedContractEvent<
      ContractCreatedEvent.InputTuple,
      ContractCreatedEvent.OutputTuple,
      ContractCreatedEvent.OutputObject
    >
    ContractCreated: TypedContractEvent<
      ContractCreatedEvent.InputTuple,
      ContractCreatedEvent.OutputTuple,
      ContractCreatedEvent.OutputObject
    >

    'DataChanged(bytes32,bytes)': TypedContractEvent<
      DataChangedEvent.InputTuple,
      DataChangedEvent.OutputTuple,
      DataChangedEvent.OutputObject
    >
    DataChanged: TypedContractEvent<
      DataChangedEvent.InputTuple,
      DataChangedEvent.OutputTuple,
      DataChangedEvent.OutputObject
    >

    'Executed(uint256,address,uint256,bytes4)': TypedContractEvent<
      ExecutedEvent.InputTuple,
      ExecutedEvent.OutputTuple,
      ExecutedEvent.OutputObject
    >
    Executed: TypedContractEvent<
      ExecutedEvent.InputTuple,
      ExecutedEvent.OutputTuple,
      ExecutedEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
