/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseDeductionsAndCredits_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'USDCInstance'
      | 'afterFund'
      | 'afterPay'
      | 'afterWithdraw'
      | 'beforeFund'
      | 'beforePay'
      | 'beforeWithdraw'
      | 'chargeFees'
      | 'riseAccess'
      | 'riseFeeRecipient'
      | 'risePayToken'
      | 'riseTreasury',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'Credit' | 'Deduction' | 'Discount' | 'Fee',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'USDCInstance',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'afterFund',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'afterPay',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'afterWithdraw',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'beforeFund',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'beforePay',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'beforeWithdraw',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'chargeFees', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'riseFeeRecipient',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'risePayToken',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'riseTreasury',
    values?: undefined,
  ): string

  decodeFunctionResult(
    functionFragment: 'USDCInstance',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'afterFund', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'afterPay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'afterWithdraw',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'beforeFund', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'beforePay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'beforeWithdraw',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'chargeFees', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'riseFeeRecipient',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'risePayToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'riseTreasury',
    data: BytesLike,
  ): Result
}

export namespace CreditEvent {
  export type InputTuple = [
    eventType: BytesLike,
    account: AddressLike,
    amount: BigNumberish,
  ]
  export type OutputTuple = [eventType: string, account: string, amount: bigint]
  export interface OutputObject {
    eventType: string
    account: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace DeductionEvent {
  export type InputTuple = [
    eventType: BytesLike,
    account: AddressLike,
    amount: BigNumberish,
  ]
  export type OutputTuple = [eventType: string, account: string, amount: bigint]
  export interface OutputObject {
    eventType: string
    account: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace DiscountEvent {
  export type InputTuple = [
    eventType: BytesLike,
    account: AddressLike,
    equationAmount: BigNumberish,
    amount: BigNumberish,
  ]
  export type OutputTuple = [
    eventType: string,
    account: string,
    equationAmount: bigint,
    amount: bigint,
  ]
  export interface OutputObject {
    eventType: string
    account: string
    equationAmount: bigint
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace FeeEvent {
  export type InputTuple = [
    eventType: BytesLike,
    account: AddressLike,
    equationAmount: BigNumberish,
    amount: BigNumberish,
  ]
  export type OutputTuple = [
    eventType: string,
    account: string,
    equationAmount: bigint,
    amount: bigint,
  ]
  export interface OutputObject {
    eventType: string
    account: string
    equationAmount: bigint
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseDeductionsAndCredits_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseDeductionsAndCredits_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseDeductionsAndCredits_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  USDCInstance: TypedContractMethod<[], [string], 'view'>

  afterFund: TypedContractMethod<
    [funder: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >

  afterPay: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, amount: BigNumberish],
    [
      [bigint, bigint] & {
        payerAlteredAmount: bigint
        payeeAlteredAmount: bigint
      },
    ],
    'nonpayable'
  >

  afterWithdraw: TypedContractMethod<
    [withdrawer: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >

  beforeFund: TypedContractMethod<
    [funder: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >

  beforePay: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, amount: BigNumberish],
    [
      [bigint, bigint] & {
        payerAlteredAmount: bigint
        payeeAlteredAmount: bigint
      },
    ],
    'nonpayable'
  >

  beforeWithdraw: TypedContractMethod<
    [withdrawer: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >

  chargeFees: TypedContractMethod<[], [bigint[]], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseFeeRecipient: TypedContractMethod<[], [string], 'view'>

  risePayToken: TypedContractMethod<[], [string], 'view'>

  riseTreasury: TypedContractMethod<[], [string], 'view'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'USDCInstance',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'afterFund',
  ): TypedContractMethod<
    [funder: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >
  getFunction(nameOrSignature: 'afterPay'): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, amount: BigNumberish],
    [
      [bigint, bigint] & {
        payerAlteredAmount: bigint
        payeeAlteredAmount: bigint
      },
    ],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'afterWithdraw',
  ): TypedContractMethod<
    [withdrawer: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'beforeFund',
  ): TypedContractMethod<
    [funder: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >
  getFunction(nameOrSignature: 'beforePay'): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike, amount: BigNumberish],
    [
      [bigint, bigint] & {
        payerAlteredAmount: bigint
        payeeAlteredAmount: bigint
      },
    ],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'beforeWithdraw',
  ): TypedContractMethod<
    [withdrawer: AddressLike, amount: BigNumberish],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'chargeFees',
  ): TypedContractMethod<[], [bigint[]], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseFeeRecipient',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'risePayToken',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseTreasury',
  ): TypedContractMethod<[], [string], 'view'>

  getEvent(
    key: 'Credit',
  ): TypedContractEvent<
    CreditEvent.InputTuple,
    CreditEvent.OutputTuple,
    CreditEvent.OutputObject
  >
  getEvent(
    key: 'Deduction',
  ): TypedContractEvent<
    DeductionEvent.InputTuple,
    DeductionEvent.OutputTuple,
    DeductionEvent.OutputObject
  >
  getEvent(
    key: 'Discount',
  ): TypedContractEvent<
    DiscountEvent.InputTuple,
    DiscountEvent.OutputTuple,
    DiscountEvent.OutputObject
  >
  getEvent(
    key: 'Fee',
  ): TypedContractEvent<
    FeeEvent.InputTuple,
    FeeEvent.OutputTuple,
    FeeEvent.OutputObject
  >

  filters: {
    'Credit(bytes32,address,uint256)': TypedContractEvent<
      CreditEvent.InputTuple,
      CreditEvent.OutputTuple,
      CreditEvent.OutputObject
    >
    Credit: TypedContractEvent<
      CreditEvent.InputTuple,
      CreditEvent.OutputTuple,
      CreditEvent.OutputObject
    >

    'Deduction(bytes32,address,uint256)': TypedContractEvent<
      DeductionEvent.InputTuple,
      DeductionEvent.OutputTuple,
      DeductionEvent.OutputObject
    >
    Deduction: TypedContractEvent<
      DeductionEvent.InputTuple,
      DeductionEvent.OutputTuple,
      DeductionEvent.OutputObject
    >

    'Discount(bytes32,address,uint256,uint256)': TypedContractEvent<
      DiscountEvent.InputTuple,
      DiscountEvent.OutputTuple,
      DiscountEvent.OutputObject
    >
    Discount: TypedContractEvent<
      DiscountEvent.InputTuple,
      DiscountEvent.OutputTuple,
      DiscountEvent.OutputObject
    >

    'Fee(bytes32,address,uint256,uint256)': TypedContractEvent<
      FeeEvent.InputTuple,
      FeeEvent.OutputTuple,
      FeeEvent.OutputObject
    >
    Fee: TypedContractEvent<
      FeeEvent.InputTuple,
      FeeEvent.OutputTuple,
      FeeEvent.OutputObject
    >
  }
}
