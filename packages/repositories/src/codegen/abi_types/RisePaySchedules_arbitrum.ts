/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace IRiseStorageTypes {
  export type PayScheduleStruct = {
    payer: AddressLike
    payee: AddressLike
    amount: BigNumberish
    salt: BigNumberish
    startTime: BigNumberish
    interval: BigNumberish
    total: BigNumberish
    count: BigNumberish
    enabled: boolean
  }

  export type PayScheduleStructOutput = [
    payer: string,
    payee: string,
    amount: bigint,
    salt: bigint,
    startTime: bigint,
    interval: bigint,
    total: bigint,
    count: bigint,
    enabled: boolean,
  ] & {
    payer: string
    payee: string
    amount: bigint
    salt: bigint
    startTime: bigint
    interval: bigint
    total: bigint
    count: bigint
    enabled: boolean
  }
}

export interface RisePaySchedules_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addSchedule'
      | 'batchExecuteSchedule'
      | 'canBePaid'
      | 'canExecute'
      | 'canPay'
      | 'executeSchedule'
      | 'getPaySchedule'
      | 'getPayScheduleHashPaymentsHashes'
      | 'getPayScheduleHashRange'
      | 'getPaySchedulePaymentsHashes'
      | 'getPayScheduleRange'
      | 'isTrustedForwarder'
      | 'isValid'
      | 'nextPaymentTime'
      | 'remainingPayments'
      | 'removeSchedule'
      | 'riseAccess'
      | 'riseDeductionsAndCredits'
      | 'risePayToken'
      | 'riseStorage'
      | 'upgradeDeductionsAndCedits',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'PayHash'
      | 'ScheduleComplete'
      | 'ScheduleCreated'
      | 'ScheduleExecuted'
      | 'ScheduleFailed'
      | 'ScheduleRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addSchedule',
    values: [
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'batchExecuteSchedule',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'canBePaid',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'canExecute',
    values: [IRiseStorageTypes.PayScheduleStruct, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'canPay', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'executeSchedule',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaySchedule',
    values: [BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayScheduleHashPaymentsHashes',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayScheduleHashRange',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getPaySchedulePaymentsHashes',
    values: [
      AddressLike,
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getPayScheduleRange',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'isValid',
    values: [IRiseStorageTypes.PayScheduleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'nextPaymentTime',
    values: [IRiseStorageTypes.PayScheduleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'remainingPayments',
    values: [IRiseStorageTypes.PayScheduleStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'removeSchedule',
    values: [BytesLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'riseDeductionsAndCredits',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'risePayToken',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'riseStorage',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeDeductionsAndCedits',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'addSchedule', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'batchExecuteSchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'canBePaid', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canExecute', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canPay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'executeSchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaySchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPayScheduleHashPaymentsHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPayScheduleHashRange',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPaySchedulePaymentsHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getPayScheduleRange',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'isValid', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'nextPaymentTime',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'remainingPayments',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'removeSchedule',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'riseDeductionsAndCredits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'risePayToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseStorage', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'upgradeDeductionsAndCedits',
    data: BytesLike,
  ): Result
}

export namespace PayHashEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    amount: BigNumberish,
    salt: BigNumberish,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    amount: bigint,
    salt: bigint,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    amount: bigint
    salt: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ScheduleCompleteEvent {
  export type InputTuple = [
    schedule: BytesLike,
    payee: AddressLike,
    payer: AddressLike,
  ]
  export type OutputTuple = [schedule: string, payee: string, payer: string]
  export interface OutputObject {
    schedule: string
    payee: string
    payer: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ScheduleCreatedEvent {
  export type InputTuple = [
    schedule: BytesLike,
    payee: AddressLike,
    payer: AddressLike,
    relationshipHash: BytesLike,
  ]
  export type OutputTuple = [
    schedule: string,
    payee: string,
    payer: string,
    relationshipHash: string,
  ]
  export interface OutputObject {
    schedule: string
    payee: string
    payer: string
    relationshipHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ScheduleExecutedEvent {
  export type InputTuple = [
    schedule: BytesLike,
    amount: BigNumberish,
    remaining: BigNumberish,
  ]
  export type OutputTuple = [
    schedule: string,
    amount: bigint,
    remaining: bigint,
  ]
  export interface OutputObject {
    schedule: string
    amount: bigint
    remaining: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ScheduleFailedEvent {
  export type InputTuple = [schedule: BytesLike]
  export type OutputTuple = [schedule: string]
  export interface OutputObject {
    schedule: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ScheduleRemovedEvent {
  export type InputTuple = [
    schedule: BytesLike,
    payee: AddressLike,
    payer: AddressLike,
  ]
  export type OutputTuple = [schedule: string, payee: string, payer: string]
  export interface OutputObject {
    schedule: string
    payee: string
    payer: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePaySchedules_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePaySchedules_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePaySchedules_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addSchedule: TypedContractMethod<
    [
      payeeIdx: BigNumberish,
      amount: BigNumberish,
      salt: BigNumberish,
      startTime: BigNumberish,
      interval: BigNumberish,
      total: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  batchExecuteSchedule: TypedContractMethod<
    [paySchedules: BytesLike[]],
    [void],
    'nonpayable'
  >

  canBePaid: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >

  canExecute: TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct, blockTime: BigNumberish],
    [boolean],
    'view'
  >

  canPay: TypedContractMethod<[payer: AddressLike], [boolean], 'view'>

  executeSchedule: TypedContractMethod<
    [psHash: BytesLike],
    [void],
    'nonpayable'
  >

  getPaySchedule: TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [IRiseStorageTypes.PayScheduleStructOutput],
    'view'
  >

  getPayScheduleHashPaymentsHashes: TypedContractMethod<
    [psHash: BytesLike],
    [string[]],
    'view'
  >

  getPayScheduleHashRange: TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [[string[], bigint]],
    'view'
  >

  getPaySchedulePaymentsHashes: TypedContractMethod<
    [
      payer: AddressLike,
      payee: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
      total: BigNumberish,
    ],
    [string[]],
    'view'
  >

  getPayScheduleRange: TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [
      [IRiseStorageTypes.PayScheduleStructOutput[], bigint] & {
        results: IRiseStorageTypes.PayScheduleStructOutput[]
        totalAmount: bigint
      },
    ],
    'view'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  isValid: TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [boolean],
    'view'
  >

  nextPaymentTime: TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [bigint],
    'view'
  >

  remainingPayments: TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [bigint],
    'view'
  >

  removeSchedule: TypedContractMethod<[psHash: BytesLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseDeductionsAndCredits: TypedContractMethod<[], [string], 'view'>

  risePayToken: TypedContractMethod<[], [string], 'view'>

  riseStorage: TypedContractMethod<[], [string], 'view'>

  upgradeDeductionsAndCedits: TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addSchedule',
  ): TypedContractMethod<
    [
      payeeIdx: BigNumberish,
      amount: BigNumberish,
      salt: BigNumberish,
      startTime: BigNumberish,
      interval: BigNumberish,
      total: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchExecuteSchedule',
  ): TypedContractMethod<[paySchedules: BytesLike[]], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'canBePaid',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'canExecute',
  ): TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct, blockTime: BigNumberish],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'canPay',
  ): TypedContractMethod<[payer: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'executeSchedule',
  ): TypedContractMethod<[psHash: BytesLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'getPaySchedule',
  ): TypedContractMethod<
    [relationshipHash: BytesLike, idx: BigNumberish],
    [IRiseStorageTypes.PayScheduleStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPayScheduleHashPaymentsHashes',
  ): TypedContractMethod<[psHash: BytesLike], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getPayScheduleHashRange',
  ): TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [[string[], bigint]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getPaySchedulePaymentsHashes',
  ): TypedContractMethod<
    [
      payer: AddressLike,
      payee: AddressLike,
      amount: BigNumberish,
      salt: BigNumberish,
      total: BigNumberish,
    ],
    [string[]],
    'view'
  >
  getFunction(nameOrSignature: 'getPayScheduleRange'): TypedContractMethod<
    [
      relationshipHash: BytesLike,
      startIndex: BigNumberish,
      count: BigNumberish,
    ],
    [
      [IRiseStorageTypes.PayScheduleStructOutput[], bigint] & {
        results: IRiseStorageTypes.PayScheduleStructOutput[]
        totalAmount: bigint
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'isValid',
  ): TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'nextPaymentTime',
  ): TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'remainingPayments',
  ): TypedContractMethod<
    [ps: IRiseStorageTypes.PayScheduleStruct],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'removeSchedule',
  ): TypedContractMethod<[psHash: BytesLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseDeductionsAndCredits',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'risePayToken',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseStorage',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'upgradeDeductionsAndCedits',
  ): TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'PayHash',
  ): TypedContractEvent<
    PayHashEvent.InputTuple,
    PayHashEvent.OutputTuple,
    PayHashEvent.OutputObject
  >
  getEvent(
    key: 'ScheduleComplete',
  ): TypedContractEvent<
    ScheduleCompleteEvent.InputTuple,
    ScheduleCompleteEvent.OutputTuple,
    ScheduleCompleteEvent.OutputObject
  >
  getEvent(
    key: 'ScheduleCreated',
  ): TypedContractEvent<
    ScheduleCreatedEvent.InputTuple,
    ScheduleCreatedEvent.OutputTuple,
    ScheduleCreatedEvent.OutputObject
  >
  getEvent(
    key: 'ScheduleExecuted',
  ): TypedContractEvent<
    ScheduleExecutedEvent.InputTuple,
    ScheduleExecutedEvent.OutputTuple,
    ScheduleExecutedEvent.OutputObject
  >
  getEvent(
    key: 'ScheduleFailed',
  ): TypedContractEvent<
    ScheduleFailedEvent.InputTuple,
    ScheduleFailedEvent.OutputTuple,
    ScheduleFailedEvent.OutputObject
  >
  getEvent(
    key: 'ScheduleRemoved',
  ): TypedContractEvent<
    ScheduleRemovedEvent.InputTuple,
    ScheduleRemovedEvent.OutputTuple,
    ScheduleRemovedEvent.OutputObject
  >

  filters: {
    'PayHash(address,address,uint256,uint256)': TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >
    PayHash: TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >

    'ScheduleComplete(bytes32,address,address)': TypedContractEvent<
      ScheduleCompleteEvent.InputTuple,
      ScheduleCompleteEvent.OutputTuple,
      ScheduleCompleteEvent.OutputObject
    >
    ScheduleComplete: TypedContractEvent<
      ScheduleCompleteEvent.InputTuple,
      ScheduleCompleteEvent.OutputTuple,
      ScheduleCompleteEvent.OutputObject
    >

    'ScheduleCreated(bytes32,address,address,bytes32)': TypedContractEvent<
      ScheduleCreatedEvent.InputTuple,
      ScheduleCreatedEvent.OutputTuple,
      ScheduleCreatedEvent.OutputObject
    >
    ScheduleCreated: TypedContractEvent<
      ScheduleCreatedEvent.InputTuple,
      ScheduleCreatedEvent.OutputTuple,
      ScheduleCreatedEvent.OutputObject
    >

    'ScheduleExecuted(bytes32,uint256,uint256)': TypedContractEvent<
      ScheduleExecutedEvent.InputTuple,
      ScheduleExecutedEvent.OutputTuple,
      ScheduleExecutedEvent.OutputObject
    >
    ScheduleExecuted: TypedContractEvent<
      ScheduleExecutedEvent.InputTuple,
      ScheduleExecutedEvent.OutputTuple,
      ScheduleExecutedEvent.OutputObject
    >

    'ScheduleFailed(bytes32)': TypedContractEvent<
      ScheduleFailedEvent.InputTuple,
      ScheduleFailedEvent.OutputTuple,
      ScheduleFailedEvent.OutputObject
    >
    ScheduleFailed: TypedContractEvent<
      ScheduleFailedEvent.InputTuple,
      ScheduleFailedEvent.OutputTuple,
      ScheduleFailedEvent.OutputObject
    >

    'ScheduleRemoved(bytes32,address,address)': TypedContractEvent<
      ScheduleRemovedEvent.InputTuple,
      ScheduleRemovedEvent.OutputTuple,
      ScheduleRemovedEvent.OutputObject
    >
    ScheduleRemoved: TypedContractEvent<
      ScheduleRemovedEvent.InputTuple,
      ScheduleRemovedEvent.OutputTuple,
      ScheduleRemovedEvent.OutputObject
    >
  }
}
