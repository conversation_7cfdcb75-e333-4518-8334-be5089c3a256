/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseGovernor_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addFlatRateCount'
      | 'batchGrant'
      | 'batchRegister'
      | 'batchRevoke'
      | 'isPayerAndPayee'
      | 'isTrustedForwarder'
      | 'multiLookup'
      | 'multiReadBalances'
      | 'multiRoleGrants'
      | 'multiRoleRevokes'
      | 'multiRolesGet'
      | 'riseAccess'
      | 'risePayToken'
      | 'setFlatRate'
      | 'setFlatRateCreditCard'
      | 'setMultipleFlatRateCreditCard'
      | 'setSubscriptionPercent'
      | 'upgradeDeductionsAndCredits'
      | 'upgradeIds',
  ): FunctionFragment

  encodeFunctionData(
    functionFragment: 'addFlatRateCount',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'batchGrant',
    values: [BytesLike, BigNumberish[]],
  ): string
  encodeFunctionData(
    functionFragment: 'batchRegister',
    values: [AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'batchRevoke',
    values: [BytesLike, BigNumberish[]],
  ): string
  encodeFunctionData(
    functionFragment: 'isPayerAndPayee',
    values: [AddressLike, AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'multiLookup',
    values: [AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'multiReadBalances',
    values: [AddressLike[], AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'multiRoleGrants',
    values: [BigNumberish, BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'multiRoleRevokes',
    values: [BigNumberish, BytesLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'multiRolesGet',
    values: [AddressLike, BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'risePayToken',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'setFlatRate',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setFlatRateCreditCard',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setMultipleFlatRateCreditCard',
    values: [BigNumberish[], BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setSubscriptionPercent',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeDeductionsAndCredits',
    values: [BigNumberish, BigNumberish[]],
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeIds',
    values: [BigNumberish, BigNumberish[]],
  ): string

  decodeFunctionResult(
    functionFragment: 'addFlatRateCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'batchGrant', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'batchRegister',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'batchRevoke', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isPayerAndPayee',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'multiLookup', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'multiReadBalances',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'multiRoleGrants',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'multiRoleRevokes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'multiRolesGet',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'risePayToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setFlatRate', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setFlatRateCreditCard',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setMultipleFlatRateCreditCard',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setSubscriptionPercent',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'upgradeDeductionsAndCredits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'upgradeIds', data: BytesLike): Result
}

export interface RiseGovernor_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseGovernor_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseGovernor_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addFlatRateCount: TypedContractMethod<
    [riseIdIdx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >

  batchGrant: TypedContractMethod<
    [role: BytesLike, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >

  batchRegister: TypedContractMethod<
    [addresses: AddressLike[]],
    [void],
    'nonpayable'
  >

  batchRevoke: TypedContractMethod<
    [role: BytesLike, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >

  isPayerAndPayee: TypedContractMethod<
    [payer: AddressLike, payees: AddressLike[]],
    [boolean[]],
    'view'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  multiLookup: TypedContractMethod<
    [addresses: AddressLike[]],
    [bigint[]],
    'view'
  >

  multiReadBalances: TypedContractMethod<
    [addresses: AddressLike[], token: AddressLike],
    [bigint[]],
    'view'
  >

  multiRoleGrants: TypedContractMethod<
    [riseIdIdx: BigNumberish, roles: BytesLike[]],
    [void],
    'nonpayable'
  >

  multiRoleRevokes: TypedContractMethod<
    [riseIdIdx: BigNumberish, roles: BytesLike[]],
    [void],
    'nonpayable'
  >

  multiRolesGet: TypedContractMethod<
    [riseIdAddress: AddressLike, roles: BytesLike[]],
    [boolean[]],
    'view'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  risePayToken: TypedContractMethod<[], [string], 'view'>

  setFlatRate: TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >

  setFlatRateCreditCard: TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >

  setMultipleFlatRateCreditCard: TypedContractMethod<
    [riseIdIdxs: BigNumberish[], value: BigNumberish],
    [void],
    'nonpayable'
  >

  setSubscriptionPercent: TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >

  upgradeDeductionsAndCredits: TypedContractMethod<
    [newDAC: BigNumberish, contractsIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >

  upgradeIds: TypedContractMethod<
    [payIdx: BigNumberish, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addFlatRateCount',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchGrant',
  ): TypedContractMethod<
    [role: BytesLike, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'batchRegister',
  ): TypedContractMethod<[addresses: AddressLike[]], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'batchRevoke',
  ): TypedContractMethod<
    [role: BytesLike, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isPayerAndPayee',
  ): TypedContractMethod<
    [payer: AddressLike, payees: AddressLike[]],
    [boolean[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'multiLookup',
  ): TypedContractMethod<[addresses: AddressLike[]], [bigint[]], 'view'>
  getFunction(
    nameOrSignature: 'multiReadBalances',
  ): TypedContractMethod<
    [addresses: AddressLike[], token: AddressLike],
    [bigint[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'multiRoleGrants',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, roles: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'multiRoleRevokes',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, roles: BytesLike[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'multiRolesGet',
  ): TypedContractMethod<
    [riseIdAddress: AddressLike, roles: BytesLike[]],
    [boolean[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'risePayToken',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setFlatRate',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setFlatRateCreditCard',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMultipleFlatRateCreditCard',
  ): TypedContractMethod<
    [riseIdIdxs: BigNumberish[], value: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setSubscriptionPercent',
  ): TypedContractMethod<
    [riseIdIdx: BigNumberish, value: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'upgradeDeductionsAndCredits',
  ): TypedContractMethod<
    [newDAC: BigNumberish, contractsIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'upgradeIds',
  ): TypedContractMethod<
    [payIdx: BigNumberish, riseIdIdxs: BigNumberish[]],
    [void],
    'nonpayable'
  >

  filters: {}
}
