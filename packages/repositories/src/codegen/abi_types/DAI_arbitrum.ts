/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface DAI_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'allowance'
      | 'approve'
      | 'balanceOf'
      | 'burn'
      | 'decimals'
      | 'decreaseAllowance'
      | 'executeTransfers'
      | 'getTransfer'
      | 'getTransferHash'
      | 'increaseAllowance'
      | 'init'
      | 'internalTransfer'
      | 'isTrustedForwarder'
      | 'mint'
      | 'name'
      | 'pendingTransferHashes'
      | 'pendingTransferHashesSlice'
      | 'pendingTransfersAll'
      | 'pendingTransfersContains'
      | 'pendingTransfersCount'
      | 'recoverToken'
      | 'riseRouter'
      | 'setRouter'
      | 'symbol'
      | 'totalSupply'
      | 'transfer'
      | 'transferFrom',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'Approval' | 'Initialized' | 'Transfer',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'allowance',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'approve',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'balanceOf',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'burn',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'decimals', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'decreaseAllowance',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'executeTransfers',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getTransfer',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTransferHash',
    values: [AddressLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'increaseAllowance',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, string, string],
  ): string
  encodeFunctionData(
    functionFragment: 'internalTransfer',
    values: [AddressLike, BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'mint',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'name', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'pendingTransferHashes',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransferHashesSlice',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersAll',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersContains',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'pendingTransfersCount',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'symbol', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'totalSupply',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'transfer',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'transferFrom',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string

  decodeFunctionResult(functionFragment: 'allowance', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'approve', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'balanceOf', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'burn', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'decimals', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'decreaseAllowance',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'executeTransfers',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getTransfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getTransferHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'increaseAllowance',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'internalTransfer',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'mint', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransferHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransferHashesSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersAll',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersContains',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'pendingTransfersCount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'symbol', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'totalSupply', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'transfer', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'transferFrom',
    data: BytesLike,
  ): Result
}

export namespace ApprovalEvent {
  export type InputTuple = [
    owner: AddressLike,
    spender: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [owner: string, spender: string, value: bigint]
  export interface OutputObject {
    owner: string
    spender: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace TransferEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    value: BigNumberish,
  ]
  export type OutputTuple = [from: string, to: string, value: bigint]
  export interface OutputObject {
    from: string
    to: string
    value: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface DAI_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): DAI_arbitrum
  waitForDeployment(): Promise<this>

  interface: DAI_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  allowance: TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >

  approve: TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  balanceOf: TypedContractMethod<[account: AddressLike], [bigint], 'view'>

  burn: TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  decimals: TypedContractMethod<[], [bigint], 'view'>

  decreaseAllowance: TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  executeTransfers: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >

  getTransfer: TypedContractMethod<
    [transferHash: BytesLike],
    [[bigint, string] & { amount: bigint; recipient: string }],
    'view'
  >

  getTransferHash: TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike],
    [string],
    'view'
  >

  increaseAllowance: TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >

  init: TypedContractMethod<
    [_riseRouter: AddressLike, name: string, symbol: string],
    [void],
    'nonpayable'
  >

  internalTransfer: TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  mint: TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  name: TypedContractMethod<[], [string], 'view'>

  pendingTransferHashes: TypedContractMethod<[], [string[]], 'view'>

  pendingTransferHashesSlice: TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >

  pendingTransfersAll: TypedContractMethod<
    [],
    [[bigint[], string[]] & { amount: bigint[]; recipient: string[] }],
    'view'
  >

  pendingTransfersContains: TypedContractMethod<
    [hash: BytesLike],
    [boolean],
    'view'
  >

  pendingTransfersCount: TypedContractMethod<[], [bigint], 'view'>

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  symbol: TypedContractMethod<[], [string], 'view'>

  totalSupply: TypedContractMethod<[], [bigint], 'view'>

  transfer: TypedContractMethod<
    [to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  transferFrom: TypedContractMethod<
    [from: AddressLike, to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'allowance',
  ): TypedContractMethod<
    [owner: AddressLike, spender: AddressLike],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'approve',
  ): TypedContractMethod<
    [spender: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'balanceOf',
  ): TypedContractMethod<[account: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'burn',
  ): TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'decimals',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'decreaseAllowance',
  ): TypedContractMethod<
    [spender: AddressLike, subtractedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'executeTransfers',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getTransfer',
  ): TypedContractMethod<
    [transferHash: BytesLike],
    [[bigint, string] & { amount: bigint; recipient: string }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTransferHash',
  ): TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'increaseAllowance',
  ): TypedContractMethod<
    [spender: AddressLike, addedValue: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, name: string, symbol: string],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'internalTransfer',
  ): TypedContractMethod<
    [recipient: AddressLike, salt: BytesLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'mint',
  ): TypedContractMethod<
    [account: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'name',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransferHashes',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransferHashesSlice',
  ): TypedContractMethod<
    [idx: BigNumberish, count: BigNumberish],
    [string[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'pendingTransfersAll',
  ): TypedContractMethod<
    [],
    [[bigint[], string[]] & { amount: bigint[]; recipient: string[] }],
    'view'
  >
  getFunction(
    nameOrSignature: 'pendingTransfersContains',
  ): TypedContractMethod<[hash: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'pendingTransfersCount',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'symbol',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'totalSupply',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'transfer',
  ): TypedContractMethod<
    [to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'transferFrom',
  ): TypedContractMethod<
    [from: AddressLike, to: AddressLike, amount: BigNumberish],
    [boolean],
    'nonpayable'
  >

  getEvent(
    key: 'Approval',
  ): TypedContractEvent<
    ApprovalEvent.InputTuple,
    ApprovalEvent.OutputTuple,
    ApprovalEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'Transfer',
  ): TypedContractEvent<
    TransferEvent.InputTuple,
    TransferEvent.OutputTuple,
    TransferEvent.OutputObject
  >

  filters: {
    'Approval(address,address,uint256)': TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >
    Approval: TypedContractEvent<
      ApprovalEvent.InputTuple,
      ApprovalEvent.OutputTuple,
      ApprovalEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'Transfer(address,address,uint256)': TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
    Transfer: TypedContractEvent<
      TransferEvent.InputTuple,
      TransferEvent.OutputTuple,
      TransferEvent.OutputObject
    >
  }
}
