/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }
}

export declare namespace RiseRampFeeMapStorage {
  export type FeeDataStruct = {
    amountFlat: BigNumberish
    amountPercent: BigNumberish
    isSet: boolean
  }

  export type FeeDataStructOutput = [
    amountFlat: bigint,
    amountPercent: bigint,
    isSet: boolean,
  ] & { amountFlat: bigint; amountPercent: bigint; isSet: boolean }
}

export declare namespace RiseRampFeeMap {
  export type FeeDataSetStruct = {
    key: BytesLike
    amountFlat: BigNumberish
    amountPercent: BigNumberish
    isSet: boolean
  }

  export type FeeDataSetStructOutput = [
    key: string,
    amountFlat: bigint,
    amountPercent: bigint,
    isSet: boolean,
  ] & {
    key: string
    amountFlat: bigint
    amountPercent: bigint
    isSet: boolean
  }
}

export interface RiseRampWithdrawExchange_impl_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'execute'
      | 'getFeeData'
      | 'getKeyHash'
      | 'getMinimumExecuteAmount'
      | 'getMinimumFeeAmount'
      | 'getMultipleFeeData'
      | 'init'
      | 'isTrustedForwarder'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setFee'
      | 'setMinimumExecuteAmount'
      | 'setMinimumFeeAmount'
      | 'setRouter',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RiseFeeAmount'
      | 'RiseRampExecute'
      | 'RiseRampSourceFee'
      | 'RiseRampSourceFeeCovered'
      | 'RiseRampTargetAmount'
      | 'RiseRampTargetFee',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'execute',
    values: [
      AddressLike,
      BigNumberish,
      RiseRequests.RisePaymentHandlerConfigStruct,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getFeeData',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getKeyHash',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getMinimumExecuteAmount',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getMinimumFeeAmount',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getMultipleFeeData',
    values: [BytesLike[]],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setFee',
    values: [RiseRampFeeMap.FeeDataSetStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'setMinimumExecuteAmount',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setMinimumFeeAmount',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getFeeData', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getKeyHash', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMinimumFeeAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMultipleFeeData',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setFee', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'setMinimumFeeAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseFeeAmountEvent {
  export type InputTuple = [account: AddressLike, amount: BigNumberish]
  export type OutputTuple = [account: string, amount: bigint]
  export interface OutputObject {
    account: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampExecuteEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
    references: BytesLike,
    data: BytesLike,
  ]
  export type OutputTuple = [
    token: string,
    amount: bigint,
    destination: string,
    references: string,
    data: string,
  ]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
    references: string
    data: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeCoveredEvent {
  export type InputTuple = [
    amount: BigNumberish,
    sponsor: AddressLike,
    paymentID: BytesLike,
  ]
  export type OutputTuple = [amount: bigint, sponsor: string, paymentID: string]
  export interface OutputObject {
    amount: bigint
    sponsor: string
    paymentID: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetAmountEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
  ]
  export type OutputTuple = [token: string, amount: bigint, destination: string]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseRampWithdrawExchange_impl_arbitrum extends BaseContract {
  connect(
    runner?: ContractRunner | null,
  ): RiseRampWithdrawExchange_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseRampWithdrawExchange_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  execute: TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >

  getFeeData: TypedContractMethod<
    [keys: BytesLike],
    [RiseRampFeeMapStorage.FeeDataStructOutput],
    'view'
  >

  getKeyHash: TypedContractMethod<[keys: BytesLike], [string], 'view'>

  getMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike],
    [bigint],
    'view'
  >

  getMinimumFeeAmount: TypedContractMethod<[], [bigint], 'view'>

  getMultipleFeeData: TypedContractMethod<
    [keys: BytesLike[]],
    [RiseRampFeeMapStorage.FeeDataStructOutput[]],
    'view'
  >

  init: TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setFee: TypedContractMethod<
    [feeData: RiseRampFeeMap.FeeDataSetStruct[]],
    [void],
    'nonpayable'
  >

  setMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  setMinimumFeeAmount: TypedContractMethod<
    [amount: BigNumberish],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'execute',
  ): TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getFeeData',
  ): TypedContractMethod<
    [keys: BytesLike],
    [RiseRampFeeMapStorage.FeeDataStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getKeyHash',
  ): TypedContractMethod<[keys: BytesLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getMinimumExecuteAmount',
  ): TypedContractMethod<[token: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getMinimumFeeAmount',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getMultipleFeeData',
  ): TypedContractMethod<
    [keys: BytesLike[]],
    [RiseRampFeeMapStorage.FeeDataStructOutput[]],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setFee',
  ): TypedContractMethod<
    [feeData: RiseRampFeeMap.FeeDataSetStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMinimumExecuteAmount',
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMinimumFeeAmount',
  ): TypedContractMethod<[amount: BigNumberish], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseFeeAmount',
  ): TypedContractEvent<
    RiseFeeAmountEvent.InputTuple,
    RiseFeeAmountEvent.OutputTuple,
    RiseFeeAmountEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampExecute',
  ): TypedContractEvent<
    RiseRampExecuteEvent.InputTuple,
    RiseRampExecuteEvent.OutputTuple,
    RiseRampExecuteEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFee',
  ): TypedContractEvent<
    RiseRampSourceFeeEvent.InputTuple,
    RiseRampSourceFeeEvent.OutputTuple,
    RiseRampSourceFeeEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFeeCovered',
  ): TypedContractEvent<
    RiseRampSourceFeeCoveredEvent.InputTuple,
    RiseRampSourceFeeCoveredEvent.OutputTuple,
    RiseRampSourceFeeCoveredEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetAmount',
  ): TypedContractEvent<
    RiseRampTargetAmountEvent.InputTuple,
    RiseRampTargetAmountEvent.OutputTuple,
    RiseRampTargetAmountEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetFee',
  ): TypedContractEvent<
    RiseRampTargetFeeEvent.InputTuple,
    RiseRampTargetFeeEvent.OutputTuple,
    RiseRampTargetFeeEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseFeeAmount(address,uint256)': TypedContractEvent<
      RiseFeeAmountEvent.InputTuple,
      RiseFeeAmountEvent.OutputTuple,
      RiseFeeAmountEvent.OutputObject
    >
    RiseFeeAmount: TypedContractEvent<
      RiseFeeAmountEvent.InputTuple,
      RiseFeeAmountEvent.OutputTuple,
      RiseFeeAmountEvent.OutputObject
    >

    'RiseRampExecute(address,uint256,address,bytes32,bytes)': TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >
    RiseRampExecute: TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >

    'RiseRampSourceFee(uint256,address)': TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >
    RiseRampSourceFee: TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >

    'RiseRampSourceFeeCovered(uint256,address,bytes32)': TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >
    RiseRampSourceFeeCovered: TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >

    'RiseRampTargetAmount(address,uint256,address)': TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >
    RiseRampTargetAmount: TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >

    'RiseRampTargetFee(uint256,address)': TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
    RiseRampTargetFee: TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
  }
}
