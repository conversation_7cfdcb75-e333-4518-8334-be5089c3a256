/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRequests {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }

  export type RisePaymentHandlerConfigRequestStruct = {
    token: AddressLike
    configs: RiseRequests.RisePaymentHandlerConfigStruct[]
  }

  export type RisePaymentHandlerConfigRequestStructOutput = [
    token: string,
    configs: RiseRequests.RisePaymentHandlerConfigStructOutput[],
  ] & {
    token: string
    configs: RiseRequests.RisePaymentHandlerConfigStructOutput[]
  }
}

export interface RisePaymentHandler_impl_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addOwner'
      | 'getConfigs'
      | 'getCountAndVolumeByHash'
      | 'getDayPaymentTrackingHashes'
      | 'getMonthPaymentTrackingHashes'
      | 'getOwners'
      | 'getOwnersLength'
      | 'getSenders'
      | 'getSettings'
      | 'getTokenAndSenderVolumeByHash'
      | 'getTokensUsed'
      | 'init'
      | 'isOwner'
      | 'isTrustedForwarder'
      | 'processEtherTransfer'
      | 'processTokenTransfers'
      | 'processTokenTransfersWithConfig'
      | 'recoverToken'
      | 'removeOwner'
      | 'riseAccess'
      | 'riseRouter'
      | 'setRouter'
      | 'setSettings'
      | 'setTransferRules',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'Initialized'
      | 'RiseOwnerAdded'
      | 'RiseOwnerRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getConfigs',
    values: [AddressLike[]],
  ): string
  encodeFunctionData(
    functionFragment: 'getCountAndVolumeByHash',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getDayPaymentTrackingHashes',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getMonthPaymentTrackingHashes',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'getOwners', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getOwnersLength',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'getSenders', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getSettings',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'getTokenAndSenderVolumeByHash',
    values: [BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTokensUsed',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, AddressLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'isOwner', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'processEtherTransfer',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'processTokenTransfers',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'processTokenTransfersWithConfig',
    values: [RiseRequests.RisePaymentHandlerConfigRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'removeOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setSettings',
    values: [
      BytesLike,
      AddressLike,
      AddressLike,
      boolean,
      AddressLike,
      AddressLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'setTransferRules',
    values: [RiseRequests.RisePaymentHandlerConfigRequestStruct[]],
  ): string

  decodeFunctionResult(functionFragment: 'addOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getConfigs', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getCountAndVolumeByHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getDayPaymentTrackingHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getMonthPaymentTrackingHashes',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getOwners', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getOwnersLength',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'getSenders', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getSettings', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getTokenAndSenderVolumeByHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTokensUsed',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'isOwner', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'processEtherTransfer',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'processTokenTransfers',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'processTokenTransfersWithConfig',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'removeOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setSettings', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTransferRules',
    data: BytesLike,
  ): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerAddedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerRemovedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePaymentHandler_impl_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePaymentHandler_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePaymentHandler_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  getConfigs: TypedContractMethod<
    [tokens: AddressLike[]],
    [RiseRequests.RisePaymentHandlerConfigStructOutput[][]],
    'view'
  >

  getCountAndVolumeByHash: TypedContractMethod<
    [hash: BytesLike],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >

  getDayPaymentTrackingHashes: TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >

  getMonthPaymentTrackingHashes: TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >

  getOwners: TypedContractMethod<[], [string[]], 'view'>

  getOwnersLength: TypedContractMethod<[], [bigint], 'view'>

  getSenders: TypedContractMethod<[], [string[]], 'view'>

  getSettings: TypedContractMethod<
    [],
    [[string, string, string, boolean, string, string]],
    'view'
  >

  getTokenAndSenderVolumeByHash: TypedContractMethod<
    [tokenHash: BytesLike, senderHash: BytesLike],
    [[bigint, bigint] & { tokenVolume: bigint; senderVolume: bigint }],
    'view'
  >

  getTokensUsed: TypedContractMethod<[], [string[]], 'view'>

  init: TypedContractMethod<
    [
      _riseRouter: AddressLike,
      _parentAccount: AddressLike,
      _owner: AddressLike,
    ],
    [void],
    'nonpayable'
  >

  isOwner: TypedContractMethod<[account: AddressLike], [boolean], 'view'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  processEtherTransfer: TypedContractMethod<[], [void], 'nonpayable'>

  processTokenTransfers: TypedContractMethod<
    [token: AddressLike],
    [void],
    'nonpayable'
  >

  processTokenTransfersWithConfig: TypedContractMethod<
    [request: RiseRequests.RisePaymentHandlerConfigRequestStruct],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  removeOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setSettings: TypedContractMethod<
    [
      accountType: BytesLike,
      parentAccount: AddressLike,
      sourceOfFunds: AddressLike,
      hiddenRiseTokenTransfers: boolean,
      sponsorAccount: AddressLike,
      feeRecipient: AddressLike,
    ],
    [void],
    'nonpayable'
  >

  setTransferRules: TypedContractMethod<
    [requests: RiseRequests.RisePaymentHandlerConfigRequestStruct[]],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'getConfigs',
  ): TypedContractMethod<
    [tokens: AddressLike[]],
    [RiseRequests.RisePaymentHandlerConfigStructOutput[][]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getCountAndVolumeByHash',
  ): TypedContractMethod<
    [hash: BytesLike],
    [[bigint, bigint] & { count: bigint; volume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getDayPaymentTrackingHashes',
  ): TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getMonthPaymentTrackingHashes',
  ): TypedContractMethod<
    [token: AddressLike, sender: AddressLike, epoch: BigNumberish],
    [[string, string]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getOwners',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getOwnersLength',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getSenders',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getSettings',
  ): TypedContractMethod<
    [],
    [[string, string, string, boolean, string, string]],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTokenAndSenderVolumeByHash',
  ): TypedContractMethod<
    [tokenHash: BytesLike, senderHash: BytesLike],
    [[bigint, bigint] & { tokenVolume: bigint; senderVolume: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getTokensUsed',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [
      _riseRouter: AddressLike,
      _parentAccount: AddressLike,
      _owner: AddressLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isOwner',
  ): TypedContractMethod<[account: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'processEtherTransfer',
  ): TypedContractMethod<[], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'processTokenTransfers',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'processTokenTransfersWithConfig',
  ): TypedContractMethod<
    [request: RiseRequests.RisePaymentHandlerConfigRequestStruct],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'removeOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setSettings',
  ): TypedContractMethod<
    [
      accountType: BytesLike,
      parentAccount: AddressLike,
      sourceOfFunds: AddressLike,
      hiddenRiseTokenTransfers: boolean,
      sponsorAccount: AddressLike,
      feeRecipient: AddressLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setTransferRules',
  ): TypedContractMethod<
    [requests: RiseRequests.RisePaymentHandlerConfigRequestStruct[]],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerAdded',
  ): TypedContractEvent<
    RiseOwnerAddedEvent.InputTuple,
    RiseOwnerAddedEvent.OutputTuple,
    RiseOwnerAddedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerRemoved',
  ): TypedContractEvent<
    RiseOwnerRemovedEvent.InputTuple,
    RiseOwnerRemovedEvent.OutputTuple,
    RiseOwnerRemovedEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseOwnerAdded(address)': TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >
    RiseOwnerAdded: TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >

    'RiseOwnerRemoved(address)': TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
    RiseOwnerRemoved: TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
  }
}
