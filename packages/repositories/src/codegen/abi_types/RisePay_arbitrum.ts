/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace IRisePay {
  export type PaymentStruct = {
    recipientIdx: BigNumberish
    amount: BigNumberish
    salt: BigNumberish
  }

  export type PaymentStructOutput = [
    recipientIdx: bigint,
    amount: bigint,
    salt: bigint,
  ] & { recipientIdx: bigint; amount: bigint; salt: bigint }
}

export interface RisePay_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'addPayee'
      | 'batchPay'
      | 'canBePaid'
      | 'canPay'
      | 'canRampFund'
      | 'canRampWithdraw'
      | 'fund(uint256,uint256,uint256)'
      | 'fund(address,address,uint256)'
      | 'isTrustedForwarder'
      | 'pay'
      | 'removePayee'
      | 'riseAccess'
      | 'riseDeductionsAndCredits'
      | 'riseFundID'
      | 'risePayToken'
      | 'riseStorage'
      | 'upgradeDeductionsAndCedits'
      | 'withdraw(address,address,uint256,address)'
      | 'withdraw(uint256,uint256)'
      | 'withdraw(uint256,uint256,uint256,uint256)'
      | 'withdraw(address,uint256,address)'
      | 'withdraw(uint256,uint256,uint256)'
      | 'withdraw(address,uint256)',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'PayHash' | 'PayeeAdded' | 'PayeeRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addPayee',
    values: [BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'batchPay',
    values: [BigNumberish, IRisePay.PaymentStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'canBePaid',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'canPay',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'canRampFund',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'canRampWithdraw',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'fund(uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'fund(address,address,uint256)',
    values: [AddressLike, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'pay',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'removePayee',
    values: [BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'riseDeductionsAndCredits',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'riseFundID',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'risePayToken',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'riseStorage',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'upgradeDeductionsAndCedits',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(address,address,uint256,address)',
    values: [AddressLike, AddressLike, BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(uint256,uint256)',
    values: [BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(uint256,uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(address,uint256,address)',
    values: [AddressLike, BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(uint256,uint256,uint256)',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'withdraw(address,uint256)',
    values: [AddressLike, BigNumberish],
  ): string

  decodeFunctionResult(functionFragment: 'addPayee', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'batchPay', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canBePaid', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canPay', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'canRampFund', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'canRampWithdraw',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'fund(uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'fund(address,address,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'pay', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'removePayee', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'riseDeductionsAndCredits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseFundID', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'risePayToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseStorage', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'upgradeDeductionsAndCedits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(address,address,uint256,address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(uint256,uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(address,uint256,address)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(uint256,uint256,uint256)',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withdraw(address,uint256)',
    data: BytesLike,
  ): Result
}

export namespace PayHashEvent {
  export type InputTuple = [
    payer: AddressLike,
    payee: AddressLike,
    amount: BigNumberish,
    salt: BigNumberish,
  ]
  export type OutputTuple = [
    payer: string,
    payee: string,
    amount: bigint,
    salt: bigint,
  ]
  export interface OutputObject {
    payer: string
    payee: string
    amount: bigint
    salt: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PayeeAddedEvent {
  export type InputTuple = [payer: AddressLike, payee: AddressLike]
  export type OutputTuple = [payer: string, payee: string]
  export interface OutputObject {
    payer: string
    payee: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace PayeeRemovedEvent {
  export type InputTuple = [payer: AddressLike, payee: AddressLike]
  export type OutputTuple = [payer: string, payee: string]
  export interface OutputObject {
    payer: string
    payee: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePay_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RisePay_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePay_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addPayee: TypedContractMethod<[payeeIdx: BigNumberish], [void], 'nonpayable'>

  batchPay: TypedContractMethod<
    [totalAmount: BigNumberish, payments: IRisePay.PaymentStruct[]],
    [void],
    'nonpayable'
  >

  canBePaid: TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >

  canPay: TypedContractMethod<
    [payer: AddressLike, amount: BigNumberish],
    [boolean],
    'view'
  >

  canRampFund: TypedContractMethod<
    [ramp: AddressLike, sender: AddressLike],
    [boolean],
    'view'
  >

  canRampWithdraw: TypedContractMethod<
    [ramp: AddressLike, sender: AddressLike],
    [boolean],
    'view'
  >

  'fund(uint256,uint256,uint256)': TypedContractMethod<
    [tokenIdx: BigNumberish, rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  'fund(address,address,uint256)': TypedContractMethod<
    [token: AddressLike, ramp: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  pay: TypedContractMethod<
    [recipientIdx: BigNumberish, amount: BigNumberish, salt: BigNumberish],
    [void],
    'nonpayable'
  >

  removePayee: TypedContractMethod<
    [payeeIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseDeductionsAndCredits: TypedContractMethod<[], [string], 'view'>

  riseFundID: TypedContractMethod<
    [rampIdx: BigNumberish, accountIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  risePayToken: TypedContractMethod<[], [string], 'view'>

  riseStorage: TypedContractMethod<[], [string], 'view'>

  upgradeDeductionsAndCedits: TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >

  'withdraw(address,address,uint256,address)': TypedContractMethod<
    [
      token: AddressLike,
      ramp: AddressLike,
      amount: BigNumberish,
      dest: AddressLike,
    ],
    [void],
    'nonpayable'
  >

  'withdraw(uint256,uint256)': TypedContractMethod<
    [rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  'withdraw(uint256,uint256,uint256,uint256)': TypedContractMethod<
    [
      tokenIdx: BigNumberish,
      rampIdx: BigNumberish,
      amount: BigNumberish,
      destIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  'withdraw(address,uint256,address)': TypedContractMethod<
    [ramp: AddressLike, amount: BigNumberish, dest: AddressLike],
    [void],
    'nonpayable'
  >

  'withdraw(uint256,uint256,uint256)': TypedContractMethod<
    [rampIdx: BigNumberish, amount: BigNumberish, destIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  'withdraw(address,uint256)': TypedContractMethod<
    [ramp: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addPayee',
  ): TypedContractMethod<[payeeIdx: BigNumberish], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'batchPay',
  ): TypedContractMethod<
    [totalAmount: BigNumberish, payments: IRisePay.PaymentStruct[]],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'canBePaid',
  ): TypedContractMethod<
    [payer: AddressLike, payee: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'canPay',
  ): TypedContractMethod<
    [payer: AddressLike, amount: BigNumberish],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'canRampFund',
  ): TypedContractMethod<
    [ramp: AddressLike, sender: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'canRampWithdraw',
  ): TypedContractMethod<
    [ramp: AddressLike, sender: AddressLike],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'fund(uint256,uint256,uint256)',
  ): TypedContractMethod<
    [tokenIdx: BigNumberish, rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'fund(address,address,uint256)',
  ): TypedContractMethod<
    [token: AddressLike, ramp: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'pay',
  ): TypedContractMethod<
    [recipientIdx: BigNumberish, amount: BigNumberish, salt: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'removePayee',
  ): TypedContractMethod<[payeeIdx: BigNumberish], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseDeductionsAndCredits',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseFundID',
  ): TypedContractMethod<
    [rampIdx: BigNumberish, accountIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'risePayToken',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseStorage',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'upgradeDeductionsAndCedits',
  ): TypedContractMethod<
    [_riseDeductionsAndCredits: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(address,address,uint256,address)',
  ): TypedContractMethod<
    [
      token: AddressLike,
      ramp: AddressLike,
      amount: BigNumberish,
      dest: AddressLike,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(uint256,uint256)',
  ): TypedContractMethod<
    [rampIdx: BigNumberish, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(uint256,uint256,uint256,uint256)',
  ): TypedContractMethod<
    [
      tokenIdx: BigNumberish,
      rampIdx: BigNumberish,
      amount: BigNumberish,
      destIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(address,uint256,address)',
  ): TypedContractMethod<
    [ramp: AddressLike, amount: BigNumberish, dest: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(uint256,uint256,uint256)',
  ): TypedContractMethod<
    [rampIdx: BigNumberish, amount: BigNumberish, destIdx: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'withdraw(address,uint256)',
  ): TypedContractMethod<
    [ramp: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'PayHash',
  ): TypedContractEvent<
    PayHashEvent.InputTuple,
    PayHashEvent.OutputTuple,
    PayHashEvent.OutputObject
  >
  getEvent(
    key: 'PayeeAdded',
  ): TypedContractEvent<
    PayeeAddedEvent.InputTuple,
    PayeeAddedEvent.OutputTuple,
    PayeeAddedEvent.OutputObject
  >
  getEvent(
    key: 'PayeeRemoved',
  ): TypedContractEvent<
    PayeeRemovedEvent.InputTuple,
    PayeeRemovedEvent.OutputTuple,
    PayeeRemovedEvent.OutputObject
  >

  filters: {
    'PayHash(address,address,uint256,uint256)': TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >
    PayHash: TypedContractEvent<
      PayHashEvent.InputTuple,
      PayHashEvent.OutputTuple,
      PayHashEvent.OutputObject
    >

    'PayeeAdded(address,address)': TypedContractEvent<
      PayeeAddedEvent.InputTuple,
      PayeeAddedEvent.OutputTuple,
      PayeeAddedEvent.OutputObject
    >
    PayeeAdded: TypedContractEvent<
      PayeeAddedEvent.InputTuple,
      PayeeAddedEvent.OutputTuple,
      PayeeAddedEvent.OutputObject
    >

    'PayeeRemoved(address,address)': TypedContractEvent<
      PayeeRemovedEvent.InputTuple,
      PayeeRemovedEvent.OutputTuple,
      PayeeRemovedEvent.OutputObject
    >
    PayeeRemoved: TypedContractEvent<
      PayeeRemovedEvent.InputTuple,
      PayeeRemovedEvent.OutputTuple,
      PayeeRemovedEvent.OutputObject
    >
  }
}
