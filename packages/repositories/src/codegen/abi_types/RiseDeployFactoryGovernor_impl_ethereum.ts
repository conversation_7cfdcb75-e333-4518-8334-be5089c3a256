/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseDeployFactoryGovernor_impl_ethereumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'addOwner'
      | 'deploy'
      | 'deployAccount'
      | 'deployPayHandler'
      | 'deployRiseID'
      | 'factory'
      | 'getOwners'
      | 'getOwnersLength'
      | 'isOwner'
      | 'removeOwner'
      | 'setFactory',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'RiseOwnerAdded' | 'RiseOwnerRemoved',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'addOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'deploy',
    values: [BytesLike, BytesLike, BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'deployAccount',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'deployPayHandler',
    values: [BytesLike, AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'deployRiseID',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'factory', values?: undefined): string
  encodeFunctionData(functionFragment: 'getOwners', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'getOwnersLength',
    values?: undefined,
  ): string
  encodeFunctionData(functionFragment: 'isOwner', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'removeOwner',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setFactory',
    values: [AddressLike],
  ): string

  decodeFunctionResult(functionFragment: 'addOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'deploy', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'deployAccount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'deployPayHandler',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'deployRiseID',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'factory', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getOwners', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getOwnersLength',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'isOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'removeOwner', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setFactory', data: BytesLike): Result
}

export namespace RiseOwnerAddedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseOwnerRemovedEvent {
  export type InputTuple = [user: AddressLike]
  export type OutputTuple = [user: string]
  export interface OutputObject {
    user: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseDeployFactoryGovernor_impl_ethereum extends BaseContract {
  connect(
    runner?: ContractRunner | null,
  ): RiseDeployFactoryGovernor_impl_ethereum
  waitForDeployment(): Promise<this>

  interface: RiseDeployFactoryGovernor_impl_ethereumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  addOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  deploy: TypedContractMethod<
    [implenentationReference: BytesLike, salt: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >

  deployAccount: TypedContractMethod<
    [salt: BytesLike, parentAccount: AddressLike],
    [string],
    'nonpayable'
  >

  deployPayHandler: TypedContractMethod<
    [salt: BytesLike, parentAccount: AddressLike, owner: AddressLike],
    [string],
    'nonpayable'
  >

  deployRiseID: TypedContractMethod<
    [salt: BytesLike, newOwner: AddressLike],
    [string],
    'nonpayable'
  >

  factory: TypedContractMethod<[], [string], 'view'>

  getOwners: TypedContractMethod<[], [string[]], 'view'>

  getOwnersLength: TypedContractMethod<[], [bigint], 'view'>

  isOwner: TypedContractMethod<[account: AddressLike], [boolean], 'view'>

  removeOwner: TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>

  setFactory: TypedContractMethod<[_factory: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'addOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'deploy',
  ): TypedContractMethod<
    [implenentationReference: BytesLike, salt: BytesLike, initData: BytesLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'deployAccount',
  ): TypedContractMethod<
    [salt: BytesLike, parentAccount: AddressLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'deployPayHandler',
  ): TypedContractMethod<
    [salt: BytesLike, parentAccount: AddressLike, owner: AddressLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'deployRiseID',
  ): TypedContractMethod<
    [salt: BytesLike, newOwner: AddressLike],
    [string],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'factory',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'getOwners',
  ): TypedContractMethod<[], [string[]], 'view'>
  getFunction(
    nameOrSignature: 'getOwnersLength',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'isOwner',
  ): TypedContractMethod<[account: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'removeOwner',
  ): TypedContractMethod<[account: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setFactory',
  ): TypedContractMethod<[_factory: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'RiseOwnerAdded',
  ): TypedContractEvent<
    RiseOwnerAddedEvent.InputTuple,
    RiseOwnerAddedEvent.OutputTuple,
    RiseOwnerAddedEvent.OutputObject
  >
  getEvent(
    key: 'RiseOwnerRemoved',
  ): TypedContractEvent<
    RiseOwnerRemovedEvent.InputTuple,
    RiseOwnerRemovedEvent.OutputTuple,
    RiseOwnerRemovedEvent.OutputObject
  >

  filters: {
    'RiseOwnerAdded(address)': TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >
    RiseOwnerAdded: TypedContractEvent<
      RiseOwnerAddedEvent.InputTuple,
      RiseOwnerAddedEvent.OutputTuple,
      RiseOwnerAddedEvent.OutputObject
    >

    'RiseOwnerRemoved(address)': TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
    RiseOwnerRemoved: TypedContractEvent<
      RiseOwnerRemovedEvent.InputTuple,
      RiseOwnerRemovedEvent.OutputTuple,
      RiseOwnerRemovedEvent.OutputObject
    >
  }
}
