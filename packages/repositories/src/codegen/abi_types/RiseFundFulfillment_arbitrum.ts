/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export interface RiseFundFulfillment_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'contractLimit'
      | 'dailyLimits'
      | 'fulfill'
      | 'fulfilled'
      | 'getDailyLimitKey'
      | 'getFulfillKey'
      | 'isTransferFulfilled'
      | 'manualFulfill'
      | 'riseAccess'
      | 'risePay'
      | 'setContractLimits'
      | 'setRisePay'
      | 't2Limit'
      | 't3Limit'
      | 'withinLimits'
      | 'withinTodaysLimit'
      | 'zeroLimit',
  ): FunctionFragment

  encodeFunctionData(
    functionFragment: 'contractLimit',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'dailyLimits',
    values: [BytesLike],
  ): string
  encodeFunctionData(
    functionFragment: 'fulfill',
    values: [
      BytesLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(functionFragment: 'fulfilled', values: [BytesLike]): string
  encodeFunctionData(
    functionFragment: 'getDailyLimitKey',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getFulfillKey',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'isTransferFulfilled',
    values: [BytesLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'manualFulfill',
    values: [
      BytesLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish,
    ],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'risePay', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setContractLimits',
    values: [BigNumberish, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setRisePay',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 't2Limit', values?: undefined): string
  encodeFunctionData(functionFragment: 't3Limit', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'withinLimits',
    values: [AddressLike, BytesLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'withinTodaysLimit',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'zeroLimit',
    values: [BigNumberish],
  ): string

  decodeFunctionResult(
    functionFragment: 'contractLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'dailyLimits', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'fulfill', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'fulfilled', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getDailyLimitKey',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getFulfillKey',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'isTransferFulfilled',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'manualFulfill',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'risePay', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setContractLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRisePay', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 't2Limit', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 't3Limit', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'withinLimits',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'withinTodaysLimit',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'zeroLimit', data: BytesLike): Result
}

export interface RiseFundFulfillment_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseFundFulfillment_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseFundFulfillment_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  contractLimit: TypedContractMethod<[], [bigint], 'view'>

  dailyLimits: TypedContractMethod<[arg0: BytesLike], [bigint], 'view'>

  fulfill: TypedContractMethod<
    [
      txHash: BytesLike,
      logIndex: BigNumberish,
      chainId: BigNumberish,
      amount: BigNumberish,
      riseIdIdx: BigNumberish,
      rampIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  fulfilled: TypedContractMethod<[arg0: BytesLike], [boolean], 'view'>

  getDailyLimitKey: TypedContractMethod<[riseId: AddressLike], [string], 'view'>

  getFulfillKey: TypedContractMethod<
    [txHash: BytesLike, logIndex: BigNumberish, chainId: BigNumberish],
    [string],
    'view'
  >

  isTransferFulfilled: TypedContractMethod<
    [txHash: BytesLike, logIndex: BigNumberish, chainId: BigNumberish],
    [boolean],
    'view'
  >

  manualFulfill: TypedContractMethod<
    [
      txHash: BytesLike,
      logIndex: BigNumberish,
      chainId: BigNumberish,
      amount: BigNumberish,
      riseIdIdx: BigNumberish,
      rampIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  riseAccess: TypedContractMethod<[], [string], 'view'>

  risePay: TypedContractMethod<[], [string], 'view'>

  setContractLimits: TypedContractMethod<
    [
      _contractLimit: BigNumberish,
      _t3Limit: BigNumberish,
      _t2Limit: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  setRisePay: TypedContractMethod<[_risePay: AddressLike], [void], 'nonpayable'>

  t2Limit: TypedContractMethod<[], [bigint], 'view'>

  t3Limit: TypedContractMethod<[], [bigint], 'view'>

  withinLimits: TypedContractMethod<
    [riseId: AddressLike, dailyLimitKey: BytesLike, amount: BigNumberish],
    [boolean],
    'view'
  >

  withinTodaysLimit: TypedContractMethod<
    [riseId: AddressLike, amount: BigNumberish],
    [boolean],
    'view'
  >

  zeroLimit: TypedContractMethod<
    [riseIdIdx: BigNumberish],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'contractLimit',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'dailyLimits',
  ): TypedContractMethod<[arg0: BytesLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'fulfill',
  ): TypedContractMethod<
    [
      txHash: BytesLike,
      logIndex: BigNumberish,
      chainId: BigNumberish,
      amount: BigNumberish,
      riseIdIdx: BigNumberish,
      rampIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'fulfilled',
  ): TypedContractMethod<[arg0: BytesLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'getDailyLimitKey',
  ): TypedContractMethod<[riseId: AddressLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getFulfillKey',
  ): TypedContractMethod<
    [txHash: BytesLike, logIndex: BigNumberish, chainId: BigNumberish],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'isTransferFulfilled',
  ): TypedContractMethod<
    [txHash: BytesLike, logIndex: BigNumberish, chainId: BigNumberish],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'manualFulfill',
  ): TypedContractMethod<
    [
      txHash: BytesLike,
      logIndex: BigNumberish,
      chainId: BigNumberish,
      amount: BigNumberish,
      riseIdIdx: BigNumberish,
      rampIdx: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'risePay',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setContractLimits',
  ): TypedContractMethod<
    [
      _contractLimit: BigNumberish,
      _t3Limit: BigNumberish,
      _t2Limit: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRisePay',
  ): TypedContractMethod<[_risePay: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 't2Limit',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 't3Limit',
  ): TypedContractMethod<[], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'withinLimits',
  ): TypedContractMethod<
    [riseId: AddressLike, dailyLimitKey: BytesLike, amount: BigNumberish],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'withinTodaysLimit',
  ): TypedContractMethod<
    [riseId: AddressLike, amount: BigNumberish],
    [boolean],
    'view'
  >
  getFunction(
    nameOrSignature: 'zeroLimit',
  ): TypedContractMethod<[riseIdIdx: BigNumberish], [void], 'nonpayable'>

  filters: {}
}
