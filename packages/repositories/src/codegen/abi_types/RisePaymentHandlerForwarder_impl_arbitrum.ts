/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RisePaymentHandlerForwarder {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }

  export type RisePaymentHandlerConfigRequestStruct = {
    token: AddressLike
    configs: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct[]
  }

  export type RisePaymentHandlerConfigRequestStructOutput = [
    token: string,
    configs: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStructOutput[],
  ] & {
    token: string
    configs: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStructOutput[]
  }

  export type ProcessTokenTransfersWithConfigForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct
  }

  export type ProcessTokenTransfersWithConfigForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStructOutput,
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStructOutput
  }

  export type SetTransferRulesForwardRequestStruct = {
    from: AddressLike
    to: AddressLike
    salt: BigNumberish
    expires: BigNumberish
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct[]
  }

  export type SetTransferRulesForwardRequestStructOutput = [
    from: string,
    to: string,
    salt: bigint,
    expires: bigint,
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStructOutput[],
  ] & {
    from: string
    to: string
    salt: bigint
    expires: bigint
    data: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStructOutput[]
  }
}

export interface RisePaymentHandlerForwarder_impl_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH'
      | 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH'
      | 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH'
      | 'GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH'
      | 'GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH'
      | 'GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH'
      | 'eip712Domain'
      | 'init'
      | 'processTokenTransfersWithConfig'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setRouter'
      | 'setTransferRules',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'EIP712DomainChanged'
      | 'ExecutedForwardRequest'
      | 'Initialized',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH',
    values: [
      RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH',
    values: [
      RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct[],
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH',
    values: [RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH',
    values: [RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct[]],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH',
    values: [RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH',
    values: [RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'eip712Domain',
    values?: undefined,
  ): string
  encodeFunctionData(
    functionFragment: 'init',
    values: [AddressLike, string, string],
  ): string
  encodeFunctionData(
    functionFragment: 'processTokenTransfersWithConfig',
    values: [
      RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
      BytesLike,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setTransferRules',
    values: [
      RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct,
      BytesLike,
    ],
  ): string

  decodeFunctionResult(
    functionFragment: 'GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'eip712Domain',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'processTokenTransfersWithConfig',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTransferRules',
    data: BytesLike,
  ): Result
}

export namespace EIP712DomainChangedEvent {
  export type InputTuple = []
  export type OutputTuple = []
  export interface OutputObject {}
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace ExecutedForwardRequestEvent {
  export type InputTuple = [
    from: AddressLike,
    to: AddressLike,
    hash: BytesLike,
    success: boolean,
  ]
  export type OutputTuple = [
    from: string,
    to: string,
    hash: string,
    success: boolean,
  ]
  export interface OutputObject {
    from: string
    to: string
    hash: string
    success: boolean
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RisePaymentHandlerForwarder_impl_arbitrum
  extends BaseContract {
  connect(
    runner?: ContractRunner | null,
  ): RisePaymentHandlerForwarder_impl_arbitrum
  waitForDeployment(): Promise<this>

  interface: RisePaymentHandlerForwarder_impl_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
    ],
    [string],
    'view'
  >

  GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH: TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct[],
    ],
    [string],
    'view'
  >

  GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct],
    [string],
    'view'
  >

  GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH: TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct[]],
    [string],
    'view'
  >

  GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH: TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct],
    [string],
    'view'
  >

  GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH: TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct],
    [string],
    'view'
  >

  eip712Domain: TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >

  init: TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >

  processTokenTransfersWithConfig: TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setTransferRules: TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'GET_PROCESSTOKENTRANSFERSWITHCONFIGFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
    ],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_ARRAY_PACKET_HASH',
  ): TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct[],
    ],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTHANDLERCONFIGREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigRequestStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTHANDLERCONFIG_ARRAY_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct[]],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_RISEPAYMENTHANDLERCONFIG_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.RisePaymentHandlerConfigStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'GET_SETTRANSFERRULESFORWARDREQUEST_PACKET_HASH',
  ): TypedContractMethod<
    [_input: RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct],
    [string],
    'view'
  >
  getFunction(nameOrSignature: 'eip712Domain'): TypedContractMethod<
    [],
    [
      [string, string, string, bigint, string, string, bigint[]] & {
        fields: string
        name: string
        version: string
        chainId: bigint
        verifyingContract: string
        salt: string
        extensions: bigint[]
      },
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<
    [_riseRouter: AddressLike, name: string, version: string],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'processTokenTransfersWithConfig',
  ): TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.ProcessTokenTransfersWithConfigForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setTransferRules',
  ): TypedContractMethod<
    [
      _input: RisePaymentHandlerForwarder.SetTransferRulesForwardRequestStruct,
      signature: BytesLike,
    ],
    [[boolean, string] & { success: boolean; returnData: string }],
    'nonpayable'
  >

  getEvent(
    key: 'EIP712DomainChanged',
  ): TypedContractEvent<
    EIP712DomainChangedEvent.InputTuple,
    EIP712DomainChangedEvent.OutputTuple,
    EIP712DomainChangedEvent.OutputObject
  >
  getEvent(
    key: 'ExecutedForwardRequest',
  ): TypedContractEvent<
    ExecutedForwardRequestEvent.InputTuple,
    ExecutedForwardRequestEvent.OutputTuple,
    ExecutedForwardRequestEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >

  filters: {
    'EIP712DomainChanged()': TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >
    EIP712DomainChanged: TypedContractEvent<
      EIP712DomainChangedEvent.InputTuple,
      EIP712DomainChangedEvent.OutputTuple,
      EIP712DomainChangedEvent.OutputObject
    >

    'ExecutedForwardRequest(address,address,bytes32,bool)': TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >
    ExecutedForwardRequest: TypedContractEvent<
      ExecutedForwardRequestEvent.InputTuple,
      ExecutedForwardRequestEvent.OutputTuple,
      ExecutedForwardRequestEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
  }
}
