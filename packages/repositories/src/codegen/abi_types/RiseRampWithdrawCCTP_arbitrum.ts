/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseRampWithdrawCCTP {
  export type CCTPParamsStruct = {
    riseAccount: AddressLike
    destinationDomain: BigNumberish
    mintRecipient: BytesLike
  }

  export type CCTPParamsStructOutput = [
    riseAccount: string,
    destinationDomain: bigint,
    mintRecipient: string,
  ] & { riseAccount: string; destinationDomain: bigint; mintRecipient: string }
}

export declare namespace RiseRequests {
  export type RisePaymentHandlerConfigStruct = {
    amount: BigNumberish
    transferType: BigNumberish
    fixedOrPercent: BigNumberish
    ramp: AddressLike
    source: AddressLike
    destination: AddressLike
    offChainReference: BytesLike
    data: BytesLike
  }

  export type RisePaymentHandlerConfigStructOutput = [
    amount: bigint,
    transferType: bigint,
    fixedOrPercent: bigint,
    ramp: string,
    source: string,
    destination: string,
    offChainReference: string,
    data: string,
  ] & {
    amount: bigint
    transferType: bigint
    fixedOrPercent: bigint
    ramp: string
    source: string
    destination: string
    offChainReference: string
    data: string
  }
}

export interface RiseRampWithdrawCCTP_arbitrumInterface extends Interface {
  getFunction(
    nameOrSignature:
      | 'encodeCCTPParams'
      | 'execute'
      | 'getFee'
      | 'getMapping'
      | 'getMinimumExecuteAmount'
      | 'getTokenMessenger'
      | 'init'
      | 'isTrustedForwarder'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setFee'
      | 'setMapping'
      | 'setMinimumExecuteAmount'
      | 'setRouter'
      | 'setTokenMessenger',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic:
      | 'CCTPTransferInitiated'
      | 'FeeSet'
      | 'Initialized'
      | 'RiseRampExecute'
      | 'RiseRampSourceFee'
      | 'RiseRampSourceFeeCovered'
      | 'RiseRampTargetAmount'
      | 'RiseRampTargetFee',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'encodeCCTPParams',
    values: [RiseRampWithdrawCCTP.CCTPParamsStruct],
  ): string
  encodeFunctionData(
    functionFragment: 'execute',
    values: [
      AddressLike,
      BigNumberish,
      RiseRequests.RisePaymentHandlerConfigStruct,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'getFee',
    values: [BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getMapping',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getMinimumExecuteAmount',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getTokenMessenger',
    values: [BigNumberish],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'isTrustedForwarder',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setFee',
    values: [BigNumberish, AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setMapping',
    values: [AddressLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setMinimumExecuteAmount',
    values: [AddressLike, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'setTokenMessenger',
    values: [BigNumberish, AddressLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'encodeCCTPParams',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getFee', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'getMapping', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'getMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getTokenMessenger',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'isTrustedForwarder',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setFee', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'setMapping', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setMinimumExecuteAmount',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setTokenMessenger',
    data: BytesLike,
  ): Result
}

export namespace CCTPTransferInitiatedEvent {
  export type InputTuple = [
    nonce: BigNumberish,
    destinationDomain: BigNumberish,
    mintRecipient: BytesLike,
    amount: BigNumberish,
  ]
  export type OutputTuple = [
    nonce: bigint,
    destinationDomain: bigint,
    mintRecipient: string,
    amount: bigint,
  ]
  export interface OutputObject {
    nonce: bigint
    destinationDomain: bigint
    mintRecipient: string
    amount: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace FeeSetEvent {
  export type InputTuple = [
    destinationDomain: BigNumberish,
    token: AddressLike,
    fee: BigNumberish,
  ]
  export type OutputTuple = [
    destinationDomain: bigint,
    token: string,
    fee: bigint,
  ]
  export interface OutputObject {
    destinationDomain: bigint
    token: string
    fee: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampExecuteEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
    references: BytesLike,
    data: BytesLike,
  ]
  export type OutputTuple = [
    token: string,
    amount: bigint,
    destination: string,
    references: string,
    data: string,
  ]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
    references: string
    data: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampSourceFeeCoveredEvent {
  export type InputTuple = [
    amount: BigNumberish,
    sponsor: AddressLike,
    paymentID: BytesLike,
  ]
  export type OutputTuple = [amount: bigint, sponsor: string, paymentID: string]
  export interface OutputObject {
    amount: bigint
    sponsor: string
    paymentID: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetAmountEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    destination: AddressLike,
  ]
  export type OutputTuple = [token: string, amount: bigint, destination: string]
  export interface OutputObject {
    token: string
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseRampTargetFeeEvent {
  export type InputTuple = [amount: BigNumberish, destination: AddressLike]
  export type OutputTuple = [amount: bigint, destination: string]
  export interface OutputObject {
    amount: bigint
    destination: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseRampWithdrawCCTP_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseRampWithdrawCCTP_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseRampWithdrawCCTP_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  encodeCCTPParams: TypedContractMethod<
    [params: RiseRampWithdrawCCTP.CCTPParamsStruct],
    [string],
    'view'
  >

  execute: TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >

  getFee: TypedContractMethod<
    [destinationDomain: BigNumberish, token: AddressLike],
    [bigint],
    'view'
  >

  getMapping: TypedContractMethod<[source: AddressLike], [string], 'view'>

  getMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike],
    [bigint],
    'view'
  >

  getTokenMessenger: TypedContractMethod<
    [domain: BigNumberish],
    [string],
    'view'
  >

  init: TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>

  isTrustedForwarder: TypedContractMethod<
    [forwarder: AddressLike],
    [boolean],
    'view'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setFee: TypedContractMethod<
    [destinationDomain: BigNumberish, token: AddressLike, fee: BigNumberish],
    [void],
    'nonpayable'
  >

  setMapping: TypedContractMethod<
    [source: AddressLike, riseToken: AddressLike],
    [void],
    'nonpayable'
  >

  setMinimumExecuteAmount: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  setTokenMessenger: TypedContractMethod<
    [domain: BigNumberish, tokenMessenger: AddressLike],
    [void],
    'nonpayable'
  >

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'encodeCCTPParams',
  ): TypedContractMethod<
    [params: RiseRampWithdrawCCTP.CCTPParamsStruct],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'execute',
  ): TypedContractMethod<
    [
      token: AddressLike,
      amount: BigNumberish,
      config: RiseRequests.RisePaymentHandlerConfigStruct,
    ],
    [bigint],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'getFee',
  ): TypedContractMethod<
    [destinationDomain: BigNumberish, token: AddressLike],
    [bigint],
    'view'
  >
  getFunction(
    nameOrSignature: 'getMapping',
  ): TypedContractMethod<[source: AddressLike], [string], 'view'>
  getFunction(
    nameOrSignature: 'getMinimumExecuteAmount',
  ): TypedContractMethod<[token: AddressLike], [bigint], 'view'>
  getFunction(
    nameOrSignature: 'getTokenMessenger',
  ): TypedContractMethod<[domain: BigNumberish], [string], 'view'>
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'isTrustedForwarder',
  ): TypedContractMethod<[forwarder: AddressLike], [boolean], 'view'>
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setFee',
  ): TypedContractMethod<
    [destinationDomain: BigNumberish, token: AddressLike, fee: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMapping',
  ): TypedContractMethod<
    [source: AddressLike, riseToken: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setMinimumExecuteAmount',
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'setTokenMessenger',
  ): TypedContractMethod<
    [domain: BigNumberish, tokenMessenger: AddressLike],
    [void],
    'nonpayable'
  >

  getEvent(
    key: 'CCTPTransferInitiated',
  ): TypedContractEvent<
    CCTPTransferInitiatedEvent.InputTuple,
    CCTPTransferInitiatedEvent.OutputTuple,
    CCTPTransferInitiatedEvent.OutputObject
  >
  getEvent(
    key: 'FeeSet',
  ): TypedContractEvent<
    FeeSetEvent.InputTuple,
    FeeSetEvent.OutputTuple,
    FeeSetEvent.OutputObject
  >
  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampExecute',
  ): TypedContractEvent<
    RiseRampExecuteEvent.InputTuple,
    RiseRampExecuteEvent.OutputTuple,
    RiseRampExecuteEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFee',
  ): TypedContractEvent<
    RiseRampSourceFeeEvent.InputTuple,
    RiseRampSourceFeeEvent.OutputTuple,
    RiseRampSourceFeeEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampSourceFeeCovered',
  ): TypedContractEvent<
    RiseRampSourceFeeCoveredEvent.InputTuple,
    RiseRampSourceFeeCoveredEvent.OutputTuple,
    RiseRampSourceFeeCoveredEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetAmount',
  ): TypedContractEvent<
    RiseRampTargetAmountEvent.InputTuple,
    RiseRampTargetAmountEvent.OutputTuple,
    RiseRampTargetAmountEvent.OutputObject
  >
  getEvent(
    key: 'RiseRampTargetFee',
  ): TypedContractEvent<
    RiseRampTargetFeeEvent.InputTuple,
    RiseRampTargetFeeEvent.OutputTuple,
    RiseRampTargetFeeEvent.OutputObject
  >

  filters: {
    'CCTPTransferInitiated(uint64,uint32,bytes32,uint256)': TypedContractEvent<
      CCTPTransferInitiatedEvent.InputTuple,
      CCTPTransferInitiatedEvent.OutputTuple,
      CCTPTransferInitiatedEvent.OutputObject
    >
    CCTPTransferInitiated: TypedContractEvent<
      CCTPTransferInitiatedEvent.InputTuple,
      CCTPTransferInitiatedEvent.OutputTuple,
      CCTPTransferInitiatedEvent.OutputObject
    >

    'FeeSet(uint32,address,uint256)': TypedContractEvent<
      FeeSetEvent.InputTuple,
      FeeSetEvent.OutputTuple,
      FeeSetEvent.OutputObject
    >
    FeeSet: TypedContractEvent<
      FeeSetEvent.InputTuple,
      FeeSetEvent.OutputTuple,
      FeeSetEvent.OutputObject
    >

    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseRampExecute(address,uint256,address,bytes32,bytes)': TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >
    RiseRampExecute: TypedContractEvent<
      RiseRampExecuteEvent.InputTuple,
      RiseRampExecuteEvent.OutputTuple,
      RiseRampExecuteEvent.OutputObject
    >

    'RiseRampSourceFee(uint256,address)': TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >
    RiseRampSourceFee: TypedContractEvent<
      RiseRampSourceFeeEvent.InputTuple,
      RiseRampSourceFeeEvent.OutputTuple,
      RiseRampSourceFeeEvent.OutputObject
    >

    'RiseRampSourceFeeCovered(uint256,address,bytes32)': TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >
    RiseRampSourceFeeCovered: TypedContractEvent<
      RiseRampSourceFeeCoveredEvent.InputTuple,
      RiseRampSourceFeeCoveredEvent.OutputTuple,
      RiseRampSourceFeeCoveredEvent.OutputObject
    >

    'RiseRampTargetAmount(address,uint256,address)': TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >
    RiseRampTargetAmount: TypedContractEvent<
      RiseRampTargetAmountEvent.InputTuple,
      RiseRampTargetAmountEvent.OutputTuple,
      RiseRampTargetAmountEvent.OutputObject
    >

    'RiseRampTargetFee(uint256,address)': TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
    RiseRampTargetFee: TypedContractEvent<
      RiseRampTargetFeeEvent.InputTuple,
      RiseRampTargetFeeEvent.OutputTuple,
      RiseRampTargetFeeEvent.OutputObject
    >
  }
}
