/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { ethers } from "ethers";
import {
  DeployContractOptions,
  FactoryOptions,
  HardhatEthersHelpers as HardhatEthersHelpersBase,
} from "@nomicfoundation/hardhat-ethers/types";

import * as Contracts from ".";

declare module "hardhat/types/runtime" {
  interface HardhatEthersHelpers extends HardhatEthersHelpersBase {
    getContractFactory(
      name: "USDC_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.USDC_arbitrum__factory>;
    getContractFactory(
      name: "TransferHelper_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TransferHelper_arbitrum__factory>;
    getContractFactory(
      name: "RiseUSD_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseUSD_arbitrum__factory>;
    getContractFactory(
      name: "RiseTokenGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseTokenGovernor_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseStorage_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseStorage_arbitrum__factory>;
    getContractFactory(
      name: "RiseRouter_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRouter_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRouter_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRouter_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUniSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUniSwap_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUnblock_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUnblock_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUSDUS_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawUSDUS_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawSwap_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawInternationalUSD_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawInternationalUSD_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawInternationalUSDManual_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawInternationalUSDManual_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawExchange_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawExchange_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawERC20Token_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawERC20Token_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawCCTP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawCCTP_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawCCIP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampWithdrawCCIP_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDeposit_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDeposit_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDeposit_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDeposit_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDepositSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositSwap_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDepositSwap_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositSwap_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDepositCCTP_ethereum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositCCTP_ethereum__factory>;
    getContractFactory(
      name: "RiseRampDepositCCTP_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositCCTP_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDepositCCIP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositCCIP_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseRampDepositCCIP_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseRampDepositCCIP_arbitrum__factory>;
    getContractFactory(
      name: "RiseProxy_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseProxy_impl_arbitrum__factory>;
    getContractFactory(
      name: "RisePricesOracle_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePricesOracle_impl_arbitrum__factory>;
    getContractFactory(
      name: "RisePricesOracle_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePricesOracle_arbitrum__factory>;
    getContractFactory(
      name: "RisePlannedPayments_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePlannedPayments_arbitrum__factory>;
    getContractFactory(
      name: "RisePaymentIdentifiers_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePaymentIdentifiers_arbitrum__factory>;
    getContractFactory(
      name: "RisePaymentHandler_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePaymentHandler_impl_arbitrum__factory>;
    getContractFactory(
      name: "RisePaymentHandlerForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePaymentHandlerForwarder_impl_arbitrum__factory>;
    getContractFactory(
      name: "RisePay_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePay_arbitrum__factory>;
    getContractFactory(
      name: "RisePayToken_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayToken_impl_arbitrum__factory>;
    getContractFactory(
      name: "RisePayToken_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayToken_arbitrum__factory>;
    getContractFactory(
      name: "RisePayTokenV1_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayTokenV1_arbitrum__factory>;
    getContractFactory(
      name: "RisePaySchedules_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePaySchedules_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampUniswap_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampUniswap_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampUSDUS_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampUSDUS_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampUSDInternational_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampUSDInternational_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampUSDC_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampUSDC_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampUSDCMainnet_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampUSDCMainnet_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampNGN_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampNGN_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampForEx_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampForEx_arbitrum__factory>;
    getContractFactory(
      name: "RisePayRampEURGBP_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RisePayRampEURGBP_arbitrum__factory>;
    getContractFactory(
      name: "RiseID_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseID_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseIDIndividual_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseIDIndividual_arbitrum__factory>;
    getContractFactory(
      name: "RiseIDForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseIDForwarder_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseIDFactory_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseIDFactory_arbitrum__factory>;
    getContractFactory(
      name: "RiseIDDAO_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseIDDAO_arbitrum__factory>;
    getContractFactory(
      name: "RiseIDBusiness_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseIDBusiness_arbitrum__factory>;
    getContractFactory(
      name: "RiseGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseGovernor_arbitrum__factory>;
    getContractFactory(
      name: "RiseFundFulfillment_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseFundFulfillment_arbitrum__factory>;
    getContractFactory(
      name: "RiseForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseForwarder_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseForwarder_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseForwarder_arbitrum__factory>;
    getContractFactory(
      name: "RiseFinanceGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseFinanceGovernor_arbitrum__factory>;
    getContractFactory(
      name: "RiseEUR_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseEUR_arbitrum__factory>;
    getContractFactory(
      name: "RiseDeterministicDeployFactory_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDeterministicDeployFactory_arbitrum__factory>;
    getContractFactory(
      name: "RiseDepositGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDepositGovernor_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseDepositGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDepositGovernor_arbitrum__factory>;
    getContractFactory(
      name: "RiseDeployFactory_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDeployFactory_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseDeployFactoryGovernor_impl_ethereum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_ethereum__factory>;
    getContractFactory(
      name: "RiseDeployFactoryGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseDeductionsAndCredits_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDeductionsAndCredits_arbitrum__factory>;
    getContractFactory(
      name: "RiseDedicatedFund_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseDedicatedFund_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccount_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccount_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccountSubscriptionUsage_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccountSubscriptionUsage_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccountGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccountGovernor_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccountGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccountGovernor_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccountForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccountForwarder_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccess_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccess_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccess_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccess_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccessGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccessGovernor_impl_arbitrum__factory>;
    getContractFactory(
      name: "RiseAccessGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RiseAccessGovernor_arbitrum__factory>;
    getContractFactory(
      name: "PYUSD_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.PYUSD_arbitrum__factory>;
    getContractFactory(
      name: "MessageTransmitter_polygon",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_polygon__factory>;
    getContractFactory(
      name: "MessageTransmitter_optimism",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_optimism__factory>;
    getContractFactory(
      name: "MessageTransmitter_ethereum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_ethereum__factory>;
    getContractFactory(
      name: "MessageTransmitter_base",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_base__factory>;
    getContractFactory(
      name: "MessageTransmitter_avalanche",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_avalanche__factory>;
    getContractFactory(
      name: "MessageTransmitter_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MessageTransmitter_arbitrum__factory>;
    getContractFactory(
      name: "EURC_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.EURC_arbitrum__factory>;
    getContractFactory(
      name: "DAI_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.DAI_arbitrum__factory>;
    getContractFactory(
      name: "BokkyPooBahsDateTimeLibrary_arbitrum",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.BokkyPooBahsDateTimeLibrary_arbitrum__factory>;

    getContractAt(
      name: "USDC_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.USDC_arbitrum>;
    getContractAt(
      name: "TransferHelper_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.TransferHelper_arbitrum>;
    getContractAt(
      name: "RiseUSD_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseUSD_arbitrum>;
    getContractAt(
      name: "RiseTokenGovernor_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseTokenGovernor_impl_arbitrum>;
    getContractAt(
      name: "RiseStorage_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseStorage_arbitrum>;
    getContractAt(
      name: "RiseRouter_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRouter_impl_arbitrum>;
    getContractAt(
      name: "RiseRouter_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRouter_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUniSwap_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUniSwap_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUniSwap_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUniSwap_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUnblock_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUnblock_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUnblock_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUnblock_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUSDUS_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUSDUS_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawUSDUS_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawUSDUS_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawSwap_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawSwap_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawSwap_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawSwap_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawInternationalUSD_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawInternationalUSD_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawInternationalUSDManual_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawInternationalUSDManual_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawExchange_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawExchange_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawExchange_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawExchange_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawERC20Token_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawERC20Token_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawERC20Token_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawERC20Token_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawCCTP_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawCCTP_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawCCTP_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawCCTP_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawCCIP_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawCCIP_impl_arbitrum>;
    getContractAt(
      name: "RiseRampWithdrawCCIP_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampWithdrawCCIP_arbitrum>;
    getContractAt(
      name: "RiseRampDeposit_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDeposit_impl_arbitrum>;
    getContractAt(
      name: "RiseRampDeposit_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDeposit_arbitrum>;
    getContractAt(
      name: "RiseRampDepositSwap_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositSwap_impl_arbitrum>;
    getContractAt(
      name: "RiseRampDepositSwap_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositSwap_arbitrum>;
    getContractAt(
      name: "RiseRampDepositCCTP_ethereum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositCCTP_ethereum>;
    getContractAt(
      name: "RiseRampDepositCCTP_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositCCTP_arbitrum>;
    getContractAt(
      name: "RiseRampDepositCCIP_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositCCIP_impl_arbitrum>;
    getContractAt(
      name: "RiseRampDepositCCIP_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseRampDepositCCIP_arbitrum>;
    getContractAt(
      name: "RiseProxy_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseProxy_impl_arbitrum>;
    getContractAt(
      name: "RisePricesOracle_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePricesOracle_impl_arbitrum>;
    getContractAt(
      name: "RisePricesOracle_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePricesOracle_arbitrum>;
    getContractAt(
      name: "RisePlannedPayments_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePlannedPayments_arbitrum>;
    getContractAt(
      name: "RisePaymentIdentifiers_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePaymentIdentifiers_arbitrum>;
    getContractAt(
      name: "RisePaymentHandler_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePaymentHandler_impl_arbitrum>;
    getContractAt(
      name: "RisePaymentHandlerForwarder_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePaymentHandlerForwarder_impl_arbitrum>;
    getContractAt(
      name: "RisePay_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePay_arbitrum>;
    getContractAt(
      name: "RisePayToken_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayToken_impl_arbitrum>;
    getContractAt(
      name: "RisePayToken_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayToken_arbitrum>;
    getContractAt(
      name: "RisePayTokenV1_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayTokenV1_arbitrum>;
    getContractAt(
      name: "RisePaySchedules_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePaySchedules_arbitrum>;
    getContractAt(
      name: "RisePayRampUniswap_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampUniswap_arbitrum>;
    getContractAt(
      name: "RisePayRampUSDUS_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampUSDUS_arbitrum>;
    getContractAt(
      name: "RisePayRampUSDInternational_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampUSDInternational_arbitrum>;
    getContractAt(
      name: "RisePayRampUSDC_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampUSDC_arbitrum>;
    getContractAt(
      name: "RisePayRampUSDCMainnet_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampUSDCMainnet_arbitrum>;
    getContractAt(
      name: "RisePayRampNGN_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampNGN_arbitrum>;
    getContractAt(
      name: "RisePayRampForEx_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampForEx_arbitrum>;
    getContractAt(
      name: "RisePayRampEURGBP_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RisePayRampEURGBP_arbitrum>;
    getContractAt(
      name: "RiseID_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseID_impl_arbitrum>;
    getContractAt(
      name: "RiseIDIndividual_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseIDIndividual_arbitrum>;
    getContractAt(
      name: "RiseIDForwarder_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseIDForwarder_impl_arbitrum>;
    getContractAt(
      name: "RiseIDFactory_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseIDFactory_arbitrum>;
    getContractAt(
      name: "RiseIDDAO_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseIDDAO_arbitrum>;
    getContractAt(
      name: "RiseIDBusiness_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseIDBusiness_arbitrum>;
    getContractAt(
      name: "RiseGovernor_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseGovernor_arbitrum>;
    getContractAt(
      name: "RiseFundFulfillment_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseFundFulfillment_arbitrum>;
    getContractAt(
      name: "RiseForwarder_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseForwarder_impl_arbitrum>;
    getContractAt(
      name: "RiseForwarder_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseForwarder_arbitrum>;
    getContractAt(
      name: "RiseFinanceGovernor_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseFinanceGovernor_arbitrum>;
    getContractAt(
      name: "RiseEUR_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseEUR_arbitrum>;
    getContractAt(
      name: "RiseDeterministicDeployFactory_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDeterministicDeployFactory_arbitrum>;
    getContractAt(
      name: "RiseDepositGovernor_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDepositGovernor_impl_arbitrum>;
    getContractAt(
      name: "RiseDepositGovernor_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDepositGovernor_arbitrum>;
    getContractAt(
      name: "RiseDeployFactory_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDeployFactory_impl_arbitrum>;
    getContractAt(
      name: "RiseDeployFactoryGovernor_impl_ethereum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_ethereum>;
    getContractAt(
      name: "RiseDeployFactoryGovernor_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_arbitrum>;
    getContractAt(
      name: "RiseDeductionsAndCredits_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDeductionsAndCredits_arbitrum>;
    getContractAt(
      name: "RiseDedicatedFund_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseDedicatedFund_arbitrum>;
    getContractAt(
      name: "RiseAccount_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccount_impl_arbitrum>;
    getContractAt(
      name: "RiseAccountSubscriptionUsage_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccountSubscriptionUsage_impl_arbitrum>;
    getContractAt(
      name: "RiseAccountSubscriptionUsage_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccountSubscriptionUsage_arbitrum>;
    getContractAt(
      name: "RiseAccountGovernor_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccountGovernor_impl_arbitrum>;
    getContractAt(
      name: "RiseAccountGovernor_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccountGovernor_arbitrum>;
    getContractAt(
      name: "RiseAccountForwarder_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccountForwarder_impl_arbitrum>;
    getContractAt(
      name: "RiseAccess_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccess_impl_arbitrum>;
    getContractAt(
      name: "RiseAccess_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccess_arbitrum>;
    getContractAt(
      name: "RiseAccessGovernor_impl_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccessGovernor_impl_arbitrum>;
    getContractAt(
      name: "RiseAccessGovernor_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RiseAccessGovernor_arbitrum>;
    getContractAt(
      name: "PYUSD_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.PYUSD_arbitrum>;
    getContractAt(
      name: "MessageTransmitter_polygon",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_polygon>;
    getContractAt(
      name: "MessageTransmitter_optimism",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_optimism>;
    getContractAt(
      name: "MessageTransmitter_ethereum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_ethereum>;
    getContractAt(
      name: "MessageTransmitter_base",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_base>;
    getContractAt(
      name: "MessageTransmitter_avalanche",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_avalanche>;
    getContractAt(
      name: "MessageTransmitter_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MessageTransmitter_arbitrum>;
    getContractAt(
      name: "EURC_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.EURC_arbitrum>;
    getContractAt(
      name: "DAI_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.DAI_arbitrum>;
    getContractAt(
      name: "BokkyPooBahsDateTimeLibrary_arbitrum",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.BokkyPooBahsDateTimeLibrary_arbitrum>;

    deployContract(
      name: "USDC_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.USDC_arbitrum>;
    deployContract(
      name: "TransferHelper_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TransferHelper_arbitrum>;
    deployContract(
      name: "RiseUSD_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseUSD_arbitrum>;
    deployContract(
      name: "RiseTokenGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseTokenGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseStorage_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseStorage_arbitrum>;
    deployContract(
      name: "RiseRouter_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRouter_impl_arbitrum>;
    deployContract(
      name: "RiseRouter_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRouter_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUniSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUniSwap_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUnblock_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUnblock_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUSDUS_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUSDUS_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawSwap_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSD_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSD_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSDManual_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSDManual_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawExchange_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawExchange_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawERC20Token_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawERC20Token_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCTP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCTP_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCIP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCIP_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_arbitrum>;
    deployContract(
      name: "RiseRampDeposit_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDeposit_impl_arbitrum>;
    deployContract(
      name: "RiseRampDeposit_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDeposit_arbitrum>;
    deployContract(
      name: "RiseRampDepositSwap_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampDepositSwap_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositSwap_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCTP_ethereum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCTP_ethereum>;
    deployContract(
      name: "RiseRampDepositCCTP_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCTP_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCIP_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCIP_impl_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCIP_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCIP_arbitrum>;
    deployContract(
      name: "RiseProxy_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseProxy_impl_arbitrum>;
    deployContract(
      name: "RisePricesOracle_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePricesOracle_impl_arbitrum>;
    deployContract(
      name: "RisePricesOracle_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePricesOracle_arbitrum>;
    deployContract(
      name: "RisePlannedPayments_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePlannedPayments_arbitrum>;
    deployContract(
      name: "RisePaymentIdentifiers_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentIdentifiers_arbitrum>;
    deployContract(
      name: "RisePaymentHandler_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentHandler_impl_arbitrum>;
    deployContract(
      name: "RisePaymentHandlerForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentHandlerForwarder_impl_arbitrum>;
    deployContract(
      name: "RisePay_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePay_arbitrum>;
    deployContract(
      name: "RisePayToken_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayToken_impl_arbitrum>;
    deployContract(
      name: "RisePayToken_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayToken_arbitrum>;
    deployContract(
      name: "RisePayTokenV1_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayTokenV1_arbitrum>;
    deployContract(
      name: "RisePaySchedules_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaySchedules_arbitrum>;
    deployContract(
      name: "RisePayRampUniswap_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUniswap_arbitrum>;
    deployContract(
      name: "RisePayRampUSDUS_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDUS_arbitrum>;
    deployContract(
      name: "RisePayRampUSDInternational_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDInternational_arbitrum>;
    deployContract(
      name: "RisePayRampUSDC_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDC_arbitrum>;
    deployContract(
      name: "RisePayRampUSDCMainnet_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDCMainnet_arbitrum>;
    deployContract(
      name: "RisePayRampNGN_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampNGN_arbitrum>;
    deployContract(
      name: "RisePayRampForEx_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampForEx_arbitrum>;
    deployContract(
      name: "RisePayRampEURGBP_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampEURGBP_arbitrum>;
    deployContract(
      name: "RiseID_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseID_impl_arbitrum>;
    deployContract(
      name: "RiseIDIndividual_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDIndividual_arbitrum>;
    deployContract(
      name: "RiseIDForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseIDFactory_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDFactory_arbitrum>;
    deployContract(
      name: "RiseIDDAO_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDDAO_arbitrum>;
    deployContract(
      name: "RiseIDBusiness_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDBusiness_arbitrum>;
    deployContract(
      name: "RiseGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseGovernor_arbitrum>;
    deployContract(
      name: "RiseFundFulfillment_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseFundFulfillment_arbitrum>;
    deployContract(
      name: "RiseForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseForwarder_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseForwarder_arbitrum>;
    deployContract(
      name: "RiseFinanceGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseFinanceGovernor_arbitrum>;
    deployContract(
      name: "RiseEUR_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseEUR_arbitrum>;
    deployContract(
      name: "RiseDeterministicDeployFactory_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeterministicDeployFactory_arbitrum>;
    deployContract(
      name: "RiseDepositGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDepositGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseDepositGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDepositGovernor_arbitrum>;
    deployContract(
      name: "RiseDeployFactory_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactory_impl_arbitrum>;
    deployContract(
      name: "RiseDeployFactoryGovernor_impl_ethereum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_ethereum>;
    deployContract(
      name: "RiseDeployFactoryGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseDeductionsAndCredits_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeductionsAndCredits_arbitrum>;
    deployContract(
      name: "RiseDedicatedFund_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDedicatedFund_arbitrum>;
    deployContract(
      name: "RiseAccount_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccount_impl_arbitrum>;
    deployContract(
      name: "RiseAccountSubscriptionUsage_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_impl_arbitrum>;
    deployContract(
      name: "RiseAccountSubscriptionUsage_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_arbitrum>;
    deployContract(
      name: "RiseAccountGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseAccountGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountGovernor_arbitrum>;
    deployContract(
      name: "RiseAccountForwarder_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseAccess_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccess_impl_arbitrum>;
    deployContract(
      name: "RiseAccess_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccess_arbitrum>;
    deployContract(
      name: "RiseAccessGovernor_impl_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccessGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseAccessGovernor_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccessGovernor_arbitrum>;
    deployContract(
      name: "PYUSD_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.PYUSD_arbitrum>;
    deployContract(
      name: "MessageTransmitter_polygon",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_polygon>;
    deployContract(
      name: "MessageTransmitter_optimism",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_optimism>;
    deployContract(
      name: "MessageTransmitter_ethereum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_ethereum>;
    deployContract(
      name: "MessageTransmitter_base",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_base>;
    deployContract(
      name: "MessageTransmitter_avalanche",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_avalanche>;
    deployContract(
      name: "MessageTransmitter_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_arbitrum>;
    deployContract(
      name: "EURC_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.EURC_arbitrum>;
    deployContract(
      name: "DAI_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DAI_arbitrum>;
    deployContract(
      name: "BokkyPooBahsDateTimeLibrary_arbitrum",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BokkyPooBahsDateTimeLibrary_arbitrum>;

    deployContract(
      name: "USDC_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.USDC_arbitrum>;
    deployContract(
      name: "TransferHelper_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TransferHelper_arbitrum>;
    deployContract(
      name: "RiseUSD_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseUSD_arbitrum>;
    deployContract(
      name: "RiseTokenGovernor_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseTokenGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseStorage_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseStorage_arbitrum>;
    deployContract(
      name: "RiseRouter_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRouter_impl_arbitrum>;
    deployContract(
      name: "RiseRouter_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRouter_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUniSwap_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUniSwap_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUniSwap_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUnblock_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUnblock_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUnblock_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUSDUS_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawUSDUS_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawUSDUS_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawSwap_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawSwap_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawSwap_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSD_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSD_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSD_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSDManual_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawInternationalUSDManual_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawInternationalUSDManual_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawExchange_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawExchange_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawExchange_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawERC20Token_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawERC20Token_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawERC20Token_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCTP_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCTP_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCTP_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCIP_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_impl_arbitrum>;
    deployContract(
      name: "RiseRampWithdrawCCIP_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampWithdrawCCIP_arbitrum>;
    deployContract(
      name: "RiseRampDeposit_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDeposit_impl_arbitrum>;
    deployContract(
      name: "RiseRampDeposit_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDeposit_arbitrum>;
    deployContract(
      name: "RiseRampDepositSwap_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositSwap_impl_arbitrum>;
    deployContract(
      name: "RiseRampDepositSwap_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositSwap_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCTP_ethereum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCTP_ethereum>;
    deployContract(
      name: "RiseRampDepositCCTP_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCTP_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCIP_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCIP_impl_arbitrum>;
    deployContract(
      name: "RiseRampDepositCCIP_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseRampDepositCCIP_arbitrum>;
    deployContract(
      name: "RiseProxy_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseProxy_impl_arbitrum>;
    deployContract(
      name: "RisePricesOracle_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePricesOracle_impl_arbitrum>;
    deployContract(
      name: "RisePricesOracle_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePricesOracle_arbitrum>;
    deployContract(
      name: "RisePlannedPayments_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePlannedPayments_arbitrum>;
    deployContract(
      name: "RisePaymentIdentifiers_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentIdentifiers_arbitrum>;
    deployContract(
      name: "RisePaymentHandler_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentHandler_impl_arbitrum>;
    deployContract(
      name: "RisePaymentHandlerForwarder_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaymentHandlerForwarder_impl_arbitrum>;
    deployContract(
      name: "RisePay_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePay_arbitrum>;
    deployContract(
      name: "RisePayToken_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayToken_impl_arbitrum>;
    deployContract(
      name: "RisePayToken_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayToken_arbitrum>;
    deployContract(
      name: "RisePayTokenV1_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayTokenV1_arbitrum>;
    deployContract(
      name: "RisePaySchedules_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePaySchedules_arbitrum>;
    deployContract(
      name: "RisePayRampUniswap_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUniswap_arbitrum>;
    deployContract(
      name: "RisePayRampUSDUS_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDUS_arbitrum>;
    deployContract(
      name: "RisePayRampUSDInternational_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDInternational_arbitrum>;
    deployContract(
      name: "RisePayRampUSDC_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDC_arbitrum>;
    deployContract(
      name: "RisePayRampUSDCMainnet_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampUSDCMainnet_arbitrum>;
    deployContract(
      name: "RisePayRampNGN_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampNGN_arbitrum>;
    deployContract(
      name: "RisePayRampForEx_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampForEx_arbitrum>;
    deployContract(
      name: "RisePayRampEURGBP_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RisePayRampEURGBP_arbitrum>;
    deployContract(
      name: "RiseID_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseID_impl_arbitrum>;
    deployContract(
      name: "RiseIDIndividual_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDIndividual_arbitrum>;
    deployContract(
      name: "RiseIDForwarder_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseIDFactory_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDFactory_arbitrum>;
    deployContract(
      name: "RiseIDDAO_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDDAO_arbitrum>;
    deployContract(
      name: "RiseIDBusiness_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseIDBusiness_arbitrum>;
    deployContract(
      name: "RiseGovernor_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseGovernor_arbitrum>;
    deployContract(
      name: "RiseFundFulfillment_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseFundFulfillment_arbitrum>;
    deployContract(
      name: "RiseForwarder_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseForwarder_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseForwarder_arbitrum>;
    deployContract(
      name: "RiseFinanceGovernor_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseFinanceGovernor_arbitrum>;
    deployContract(
      name: "RiseEUR_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseEUR_arbitrum>;
    deployContract(
      name: "RiseDeterministicDeployFactory_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeterministicDeployFactory_arbitrum>;
    deployContract(
      name: "RiseDepositGovernor_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDepositGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseDepositGovernor_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDepositGovernor_arbitrum>;
    deployContract(
      name: "RiseDeployFactory_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactory_impl_arbitrum>;
    deployContract(
      name: "RiseDeployFactoryGovernor_impl_ethereum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_ethereum>;
    deployContract(
      name: "RiseDeployFactoryGovernor_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeployFactoryGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseDeductionsAndCredits_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDeductionsAndCredits_arbitrum>;
    deployContract(
      name: "RiseDedicatedFund_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseDedicatedFund_arbitrum>;
    deployContract(
      name: "RiseAccount_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccount_impl_arbitrum>;
    deployContract(
      name: "RiseAccountSubscriptionUsage_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_impl_arbitrum>;
    deployContract(
      name: "RiseAccountSubscriptionUsage_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountSubscriptionUsage_arbitrum>;
    deployContract(
      name: "RiseAccountGovernor_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseAccountGovernor_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountGovernor_arbitrum>;
    deployContract(
      name: "RiseAccountForwarder_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccountForwarder_impl_arbitrum>;
    deployContract(
      name: "RiseAccess_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccess_impl_arbitrum>;
    deployContract(
      name: "RiseAccess_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccess_arbitrum>;
    deployContract(
      name: "RiseAccessGovernor_impl_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccessGovernor_impl_arbitrum>;
    deployContract(
      name: "RiseAccessGovernor_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RiseAccessGovernor_arbitrum>;
    deployContract(
      name: "PYUSD_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.PYUSD_arbitrum>;
    deployContract(
      name: "MessageTransmitter_polygon",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_polygon>;
    deployContract(
      name: "MessageTransmitter_optimism",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_optimism>;
    deployContract(
      name: "MessageTransmitter_ethereum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_ethereum>;
    deployContract(
      name: "MessageTransmitter_base",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_base>;
    deployContract(
      name: "MessageTransmitter_avalanche",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_avalanche>;
    deployContract(
      name: "MessageTransmitter_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MessageTransmitter_arbitrum>;
    deployContract(
      name: "EURC_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.EURC_arbitrum>;
    deployContract(
      name: "DAI_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DAI_arbitrum>;
    deployContract(
      name: "BokkyPooBahsDateTimeLibrary_arbitrum",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BokkyPooBahsDateTimeLibrary_arbitrum>;

    // default types
    getContractFactory(
      name: string,
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<ethers.ContractFactory>;
    getContractFactory(
      abi: any[],
      bytecode: ethers.BytesLike,
      signer?: ethers.Signer
    ): Promise<ethers.ContractFactory>;
    getContractAt(
      nameOrAbi: string | any[],
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
  }
}
