/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from 'ethers'
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from './common.js'

export declare namespace RiseAccountSubscriptionUsageBaseStorage {
  export type SubscriptionRecipientConfigUsedStruct = {
    recipient: AddressLike
    rate: BigNumberish
    discount: BigNumberish
  }

  export type SubscriptionRecipientConfigUsedStructOutput = [
    recipient: string,
    rate: bigint,
    discount: bigint,
  ] & { recipient: string; rate: bigint; discount: bigint }
}

export declare namespace IRiseAccountSubscriptionUsage {
  export type SubscriptionConfigStruct = {
    token: AddressLike
    subscriptionType: BigNumberish
    percentDiscount: BigNumberish
    rate: BigNumberish
  }

  export type SubscriptionConfigStructOutput = [
    token: string,
    subscriptionType: bigint,
    percentDiscount: bigint,
    rate: bigint,
  ] & {
    token: string
    subscriptionType: bigint
    percentDiscount: bigint
    rate: bigint
  }
}

export interface RiseAccountSubscriptionUsage_arbitrumInterface
  extends Interface {
  getFunction(
    nameOrSignature:
      | 'getAccountSubscriptionCharge'
      | 'getAccountSubscriptionChargeRecipients'
      | 'getAccountSubscriptionConfig'
      | 'getAccountSubscriptionConfigByMonth'
      | 'getAccountSubscriptionConfigHash'
      | 'init'
      | 'predictMonthlySubscriptionSlice'
      | 'processMonthlySubscription'
      | 'processMonthlySubscriptionSlice'
      | 'recoverToken'
      | 'riseAccess'
      | 'riseRouter'
      | 'setAccountSubscriptionConfig'
      | 'setRouter',
  ): FunctionFragment

  getEvent(
    nameOrSignatureOrTopic: 'Initialized' | 'RiseSubscriptionCharge',
  ): EventFragment

  encodeFunctionData(
    functionFragment: 'getAccountSubscriptionCharge',
    values: [BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getAccountSubscriptionChargeRecipients',
    values: [BigNumberish, AddressLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'getAccountSubscriptionConfig',
    values: [BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getAccountSubscriptionConfigByMonth',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'getAccountSubscriptionConfigHash',
    values: [BytesLike, AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'init', values: [AddressLike]): string
  encodeFunctionData(
    functionFragment: 'predictMonthlySubscriptionSlice',
    values: [AddressLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'processMonthlySubscription',
    values: [BigNumberish, AddressLike],
  ): string
  encodeFunctionData(
    functionFragment: 'processMonthlySubscriptionSlice',
    values: [BigNumberish, AddressLike, BigNumberish, BigNumberish],
  ): string
  encodeFunctionData(
    functionFragment: 'recoverToken',
    values: [AddressLike],
  ): string
  encodeFunctionData(functionFragment: 'riseAccess', values?: undefined): string
  encodeFunctionData(functionFragment: 'riseRouter', values?: undefined): string
  encodeFunctionData(
    functionFragment: 'setAccountSubscriptionConfig',
    values: [
      BigNumberish,
      AddressLike,
      IRiseAccountSubscriptionUsage.SubscriptionConfigStruct,
      boolean,
    ],
  ): string
  encodeFunctionData(
    functionFragment: 'setRouter',
    values: [AddressLike],
  ): string

  decodeFunctionResult(
    functionFragment: 'getAccountSubscriptionCharge',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAccountSubscriptionChargeRecipients',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAccountSubscriptionConfig',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAccountSubscriptionConfigByMonth',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'getAccountSubscriptionConfigHash',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'init', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'predictMonthlySubscriptionSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'processMonthlySubscription',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'processMonthlySubscriptionSlice',
    data: BytesLike,
  ): Result
  decodeFunctionResult(
    functionFragment: 'recoverToken',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'riseAccess', data: BytesLike): Result
  decodeFunctionResult(functionFragment: 'riseRouter', data: BytesLike): Result
  decodeFunctionResult(
    functionFragment: 'setAccountSubscriptionConfig',
    data: BytesLike,
  ): Result
  decodeFunctionResult(functionFragment: 'setRouter', data: BytesLike): Result
}

export namespace InitializedEvent {
  export type InputTuple = [version: BigNumberish]
  export type OutputTuple = [version: bigint]
  export interface OutputObject {
    version: bigint
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export namespace RiseSubscriptionChargeEvent {
  export type InputTuple = [
    paymentID: BytesLike,
    groupID: BytesLike,
    amount: BigNumberish,
    monthlyChargeHash: BytesLike,
  ]
  export type OutputTuple = [
    paymentID: string,
    groupID: string,
    amount: bigint,
    monthlyChargeHash: string,
  ]
  export interface OutputObject {
    paymentID: string
    groupID: string
    amount: bigint
    monthlyChargeHash: string
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
  export type Filter = TypedDeferredTopicFilter<Event>
  export type Log = TypedEventLog<Event>
  export type LogDescription = TypedLogDescription<Event>
}

export interface RiseAccountSubscriptionUsage_arbitrum extends BaseContract {
  connect(runner?: ContractRunner | null): RiseAccountSubscriptionUsage_arbitrum
  waitForDeployment(): Promise<this>

  interface: RiseAccountSubscriptionUsage_arbitrumInterface

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>,
  ): Promise<this>
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>,
  ): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent,
  ): Promise<Array<TypedListener<TCEvent>>>
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent,
  ): Promise<this>

  getAccountSubscriptionCharge: TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [[bigint, bigint] & { amount: bigint; totalDiscount: bigint }],
    'view'
  >

  getAccountSubscriptionChargeRecipients: TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [
      RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsedStructOutput[],
    ],
    'view'
  >

  getAccountSubscriptionConfig: TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [IRiseAccountSubscriptionUsage.SubscriptionConfigStructOutput],
    'view'
  >

  getAccountSubscriptionConfigByMonth: TypedContractMethod<
    [monthHash: BytesLike, account: AddressLike],
    [IRiseAccountSubscriptionUsage.SubscriptionConfigStructOutput],
    'view'
  >

  getAccountSubscriptionConfigHash: TypedContractMethod<
    [monthHash: BytesLike, account: AddressLike],
    [string],
    'view'
  >

  init: TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>

  predictMonthlySubscriptionSlice: TypedContractMethod<
    [
      account: AddressLike,
      recipientsByMonthIdx: BigNumberish,
      recipientsByMonthCount: BigNumberish,
    ],
    [
      RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsedStructOutput[],
    ],
    'view'
  >

  processMonthlySubscription: TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [void],
    'nonpayable'
  >

  processMonthlySubscriptionSlice: TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      recipientsByMonthIdx: BigNumberish,
      recipientsByMonthCount: BigNumberish,
    ],
    [void],
    'nonpayable'
  >

  recoverToken: TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>

  riseAccess: TypedContractMethod<[], [string], 'view'>

  riseRouter: TypedContractMethod<[], [string], 'view'>

  setAccountSubscriptionConfig: TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      config: IRiseAccountSubscriptionUsage.SubscriptionConfigStruct,
      setAsDefault: boolean,
    ],
    [void],
    'nonpayable'
  >

  setRouter: TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment,
  ): T

  getFunction(
    nameOrSignature: 'getAccountSubscriptionCharge',
  ): TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [[bigint, bigint] & { amount: bigint; totalDiscount: bigint }],
    'view'
  >
  getFunction(
    nameOrSignature: 'getAccountSubscriptionChargeRecipients',
  ): TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      idx: BigNumberish,
      count: BigNumberish,
    ],
    [
      RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsedStructOutput[],
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'getAccountSubscriptionConfig',
  ): TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [IRiseAccountSubscriptionUsage.SubscriptionConfigStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getAccountSubscriptionConfigByMonth',
  ): TypedContractMethod<
    [monthHash: BytesLike, account: AddressLike],
    [IRiseAccountSubscriptionUsage.SubscriptionConfigStructOutput],
    'view'
  >
  getFunction(
    nameOrSignature: 'getAccountSubscriptionConfigHash',
  ): TypedContractMethod<
    [monthHash: BytesLike, account: AddressLike],
    [string],
    'view'
  >
  getFunction(
    nameOrSignature: 'init',
  ): TypedContractMethod<[_riseRouter: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'predictMonthlySubscriptionSlice',
  ): TypedContractMethod<
    [
      account: AddressLike,
      recipientsByMonthIdx: BigNumberish,
      recipientsByMonthCount: BigNumberish,
    ],
    [
      RiseAccountSubscriptionUsageBaseStorage.SubscriptionRecipientConfigUsedStructOutput[],
    ],
    'view'
  >
  getFunction(
    nameOrSignature: 'processMonthlySubscription',
  ): TypedContractMethod<
    [epoch: BigNumberish, account: AddressLike],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'processMonthlySubscriptionSlice',
  ): TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      recipientsByMonthIdx: BigNumberish,
      recipientsByMonthCount: BigNumberish,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'recoverToken',
  ): TypedContractMethod<[token: AddressLike], [void], 'nonpayable'>
  getFunction(
    nameOrSignature: 'riseAccess',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'riseRouter',
  ): TypedContractMethod<[], [string], 'view'>
  getFunction(
    nameOrSignature: 'setAccountSubscriptionConfig',
  ): TypedContractMethod<
    [
      epoch: BigNumberish,
      account: AddressLike,
      config: IRiseAccountSubscriptionUsage.SubscriptionConfigStruct,
      setAsDefault: boolean,
    ],
    [void],
    'nonpayable'
  >
  getFunction(
    nameOrSignature: 'setRouter',
  ): TypedContractMethod<[_router: AddressLike], [void], 'nonpayable'>

  getEvent(
    key: 'Initialized',
  ): TypedContractEvent<
    InitializedEvent.InputTuple,
    InitializedEvent.OutputTuple,
    InitializedEvent.OutputObject
  >
  getEvent(
    key: 'RiseSubscriptionCharge',
  ): TypedContractEvent<
    RiseSubscriptionChargeEvent.InputTuple,
    RiseSubscriptionChargeEvent.OutputTuple,
    RiseSubscriptionChargeEvent.OutputObject
  >

  filters: {
    'Initialized(uint64)': TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >
    Initialized: TypedContractEvent<
      InitializedEvent.InputTuple,
      InitializedEvent.OutputTuple,
      InitializedEvent.OutputObject
    >

    'RiseSubscriptionCharge(bytes32,bytes32,uint256,bytes32)': TypedContractEvent<
      RiseSubscriptionChargeEvent.InputTuple,
      RiseSubscriptionChargeEvent.OutputTuple,
      RiseSubscriptionChargeEvent.OutputObject
    >
    RiseSubscriptionCharge: TypedContractEvent<
      RiseSubscriptionChargeEvent.InputTuple,
      RiseSubscriptionChargeEvent.OutputTuple,
      RiseSubscriptionChargeEvent.OutputObject
    >
  }
}
