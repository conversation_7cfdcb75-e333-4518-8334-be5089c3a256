// Helper types to map from DB to response
export type Justification = 'left' | 'right'

export type FieldType = 'Alphanumeric' | 'Numeric' | 'Amount'

export type MasterTaxField<TFieldName extends string = string> = {
  field: TFieldName // name of the field
  size: number // number of characters for the field
  type: FieldType // type of the field
  justification: Justification // justification of the field
}

export const fileHeaderSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'customer_id',
    size: 5,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reserved_1',
    size: 27,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'process_date', size: 8, type: 'Numeric', justification: 'right' },
  { field: 'process_time', size: 6, type: 'Numeric', justification: 'right' },
  { field: 'file_type', size: 7, type: 'Alphanumeric', justification: 'left' },
  { field: 'version', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'reserved_2',
    size: 362,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const payrollHeaderSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'tax_liabilities',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_setup',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reserved_1',
    size: 18,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'variance_payroll_code',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'bank_setup', size: 1, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_description',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'check_date', size: 8, type: 'Numeric', justification: 'right' },
  {
    field: 'company_start_date',
    size: 8,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'company_status',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_name',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'service_level',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },

  { field: 'fein_type', size: 1, type: 'Alphanumeric', justification: 'left' },
  { field: 'fein', size: 9, type: 'Numeric', justification: 'right' },
  {
    field: 'bank_account_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'transit_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'bank_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'bank_account_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'draft_days', size: 2, type: 'Numeric', justification: 'right' },
  {
    field: 'next_check_date',
    size: 8,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'name_control',
    size: 4,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'disbursement_ach_bank_destination',
    size: 9,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'disbursement_bank_account_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'disbursement_bank_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'disbursement_bank_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'cafeteria_plan_benefits',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'group_term_life_insurance',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'dependent_care_assistance',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'business_expense_reimbursement',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'employer_contribution_401k',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'employer_contribution_sep_ira',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'employer_contribution_simple',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'accident_health_insurance_premiums',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sick_pay', size: 1, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'workers_compensation',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payments_to_family_employees',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payments_to_hospital_interns',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payments_to_hospital_patients',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payments_to_general_partnership',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'state_govt_employee_salaries',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payments_to_election_workers',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'supplemental_unemployment_benefits',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'nonqualified_deferred_comp',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'meals_furnished_in_kind',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'qualified_moving_expense',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'hsa', size: 1, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'exempt_501c3_organization',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'employee_stock_purchase_plan',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'non_taxable_fringe_payments',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'public_transportation_non_tax',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wc_housing_employment_condition',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'chaplain_housing',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'clergy_housing_poverty_vow',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'foreign_source_income',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'student_exempt',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_2', size: 2, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'company_group_name',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_3', size: 1, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'agent_client_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'filer_944', size: 1, type: 'Alphanumeric', justification: 'left' },
  { field: 'reserved_4', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'quarterly_wage_reporting',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'year_end_employee_filing',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'cash_service_level',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_run_id',
    size: 20,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'worksite_reporting',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_flag',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'short_reporting_payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_effective_date',
    size: 8,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'kind_of_employer',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'naics_code', size: 6, type: 'Numeric', justification: 'right' },
  {
    field: 'reporting_payroll_code',
    size: 20,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_5', size: 8, type: 'Alphanumeric', justification: 'left' },
]

export const companyDisbursementBanksSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'payroll_tax_disbursement_ach_point',
    size: 9,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_tax_disbursement_bank_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_tax_disbursement_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'payroll_tax_disbursement_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'wage_attachment_disbursement_ach_point',
    size: 9,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_disbursement_bank_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_disbursement_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'wage_attachment_disbursement_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_1',
    size: 291,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyCashCareBanksSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'payroll_tax_bank_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_tax_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'payroll_tax_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_tax_account_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_tax_draft_days',
    size: 2,
    type: 'Numeric',
    justification: 'right',
  },

  {
    field: 'wage_attachment_bank_name',
    size: 23,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_routing_number',
    size: 9,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'wage_attachment_account_number',
    size: 17,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_account_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'wage_attachment_draft_days',
    size: 2,
    type: 'Numeric',
    justification: 'right',
  },

  {
    field: 'cash_care_ach_bank_destination',
    size: 9,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reserved_1',
    size: 294,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyGeneralLedgerSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'chart_of_accounts_code',
    size: 2,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'chart_of_accounts_description',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_code',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'variance_payroll_code',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'gl_payroll_code',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_1',
    size: 245,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const secondaryCompanyGroupsSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'company_group_type_1',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_group_name_1',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'company_group_type_2',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_group_name_2',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'company_group_type_3',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_group_name_3',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'company_group_type_4',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_group_name_4',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_1',
    size: 87,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyAddressSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 2, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'company_dba',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'address_line_1',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'address_line_2',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'city', size: 25, type: 'Alphanumeric', justification: 'left' },
  { field: 'state_code', size: 2, type: 'Alphanumeric', justification: 'left' },
  { field: 'zip_code', size: 9, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'country_code',
    size: 2,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'psd_code', size: 6, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'reserved_2',
    size: 12,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'first_name',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'middle_initial',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'last_name', size: 40, type: 'Alphanumeric', justification: 'left' },
  { field: 'area_code', size: 3, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'telephone_number',
    size: 8,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'extension', size: 10, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'fax_area_code',
    size: 3,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'fax_number', size: 8, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'email_address',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'in_care_of',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reserved_3',
    size: 36,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyMailingAddressSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 2, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'company_dba',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'address_line_1',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'address_line_2',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'city', size: 25, type: 'Alphanumeric', justification: 'left' },
  { field: 'state_code', size: 2, type: 'Alphanumeric', justification: 'left' },
  { field: 'zip_code', size: 9, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'country_code',
    size: 2,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'route_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_2', size: 8, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'first_name',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'middle_initial',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'last_name', size: 40, type: 'Alphanumeric', justification: 'left' },
  { field: 'area_code', size: 3, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'telephone_number',
    size: 8,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'extension', size: 10, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'fax_area_code',
    size: 3,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'fax_number', size: 8, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'email_address',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reserved_3',
    size: 76,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyTaxSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 1, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'work_out_of_state_flag',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'tax_code', size: 20, type: 'Alphanumeric', justification: 'left' },
  { field: 'effective_date', size: 8, type: 'Numeric', justification: 'right' },
  {
    field: 'company_tax_status',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'ein_type', size: 1, type: 'Alphanumeric', justification: 'left' },
  { field: 'ein', size: 25, type: 'Alphanumeric', justification: 'left' },

  { field: 'tax_rate', size: 6, type: 'Numeric', justification: 'right' },
  { field: 'tax_rate_2', size: 6, type: 'Numeric', justification: 'right' },
  {
    field: 'payment_frequency',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'payment_method', size: 2, type: 'Numeric', justification: 'right' },
  {
    field: 'eft_password',
    size: 20,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'reference_ein',
    size: 25,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'county_code',
    size: 5,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'company_tax_service_level',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'mark_all_returns_final',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'final_return_effective_date',
    size: 8,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'expanded_tax_rate',
    size: 7,
    type: 'Numeric',
    justification: 'right',
  },

  {
    field: 'reserved_2',
    size: 229,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const companyWorkersCompCodesSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 2, type: 'Alphanumeric', justification: 'left' },

  { field: 'tax_code', size: 20, type: 'Alphanumeric', justification: 'left' },
  { field: 'effective_date', size: 8, type: 'Numeric', justification: 'right' },
  { field: 'class_code', size: 6, type: 'Numeric', justification: 'right' },
  { field: 'rate', size: 8, type: 'Numeric', justification: 'right' },

  {
    field: 'reserved_2',
    size: 363,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const payrollTaxDetailSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 1, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'work_out_of_state_flag',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'tax_code', size: 20, type: 'Alphanumeric', justification: 'left' },
  { field: 'ein', size: 25, type: 'Alphanumeric', justification: 'left' },
  { field: 'tax_rate', size: 6, type: 'Numeric', justification: 'right' },
  { field: 'tax', size: 14, type: 'Amount', justification: 'right' },
  { field: 'taxable_wages', size: 14, type: 'Amount', justification: 'right' },
  {
    field: 'employee_count',
    size: 13,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'employee_count_sign',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'liability_trace_id',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'gross_wages', size: 14, type: 'Amount', justification: 'right' },
  { field: 'exempt_wages', size: 14, type: 'Amount', justification: 'right' },

  {
    field: 'wc_class_code',
    size: 7,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'payroll_frequency',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'expanded_tax_rate',
    size: 7,
    type: 'Numeric',
    justification: 'right',
  },
  {
    field: 'exempt_overtime_wages_employee_count',
    size: 14,
    type: 'Numeric',
    justification: 'right',
  },

  {
    field: 'reserved_2',
    size: 215,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const payrollTaxUserDefinedFieldsSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'reserved_1', size: 1, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'work_out_of_state_flag',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'tax_code', size: 20, type: 'Alphanumeric', justification: 'left' },

  {
    field: 'user_field_1',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_2',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_3',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_4',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_5',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_6',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_7',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },
  {
    field: 'user_field_8',
    size: 40,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_2',
    size: 65,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const payrollTrailerSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_1',
    size: 22,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'record_count', size: 10, type: 'Numeric', justification: 'right' },
  { field: 'tax_total', size: 14, type: 'Amount', justification: 'right' },
  {
    field: 'reserved_2',
    size: 361,
    type: 'Alphanumeric',
    justification: 'left',
  },
]

export const fileTrailerSpec: MasterTaxField<string>[] = [
  {
    field: 'record_type',
    size: 1,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'sub_type', size: 2, type: 'Alphanumeric', justification: 'left' },
  {
    field: 'payroll_code',
    size: 10,
    type: 'Alphanumeric',
    justification: 'left',
  },

  {
    field: 'reserved_1',
    size: 22,
    type: 'Alphanumeric',
    justification: 'left',
  },
  { field: 'record_count', size: 10, type: 'Numeric', justification: 'right' },
  { field: 'tax_total', size: 14, type: 'Amount', justification: 'right' },
  {
    field: 'reserved_2',
    size: 361,
    type: 'Alphanumeric',
    justification: 'left',
  },
]
