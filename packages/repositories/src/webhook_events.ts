import type { WebhookEventNanoid } from '@riseworks/contracts/src/brands.js'
import type { InsertableWebhookEventsType } from '@riseworks/contracts/src/codegen/zod/rise/webhook_events.js'
import { db as mainDB } from 'db/src/index.js'
import { nanoid as _nanoid } from 'utils/src/common/nanoid.js'
import { getContext } from 'utils/src/common/requestContext.js'

export const createWebhookEvent = async (
  {
    event_type,
    payload,
    version = '1.0',
  }: {
    event_type: InsertableWebhookEventsType['event_type']
    payload: Record<string, string | number | boolean | object | null>
    version?: string
  },
  db = mainDB,
): Promise<WebhookEventNanoid> => {
  using _ = getContext()
  const nanoid = _nanoid('webhook_event')

  await db
    .insertInto('rise.webhook_events')
    .values({
      nanoid,
      event_type,
      payload: JSON.stringify(payload),
      version,
    })
    .execute()

  return nanoid
}

export const getWebhookEventById = async (
  id: WebhookEventNanoid,
  db = mainDB,
) => {
  using _ = getContext()
  return await db
    .selectFrom('rise.webhook_events')
    .selectAll()
    .where('nanoid', '=', id)
    .executeTakeFirst()
}

export const getWebhookEventsByType = async (
  event_type: InsertableWebhookEventsType['event_type'],
  {
    cursor,
    limit = 50,
  }: {
    cursor?: string
    limit?: number
  } = {},
  db = mainDB,
) => {
  using _ = getContext()

  let query = db
    .selectFrom('rise.webhook_events')
    .selectAll()
    .where('event_type', '=', event_type)
    .orderBy('created_at', 'desc')

  if (cursor) {
    query = query.where('created_at', '<', new Date(cursor))
  }

  return await query.limit(limit + 1).execute()
}
