import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import type { PayrollHealthcheckLogs } from '@riseworks/contracts/src/codegen/db/models_rise.js'
import type { PayCycle } from '@riseworks/contracts/src/formats.js'
import { db as mainDB } from 'db/src/index.js'
import {
  getPayCycleDates,
  payCycleToString,
} from 'utils/src/common/payCycles.js'
import {
  type FormattedPayment,
  friendlyFormatPayment,
} from 'utils/src/common/payments.js'
import { getContext } from 'utils/src/common/requestContext.js'
import { groupPaymentsRead } from './riseAccounts.js'

// Configuration for healthcheck thresholds
export const HEALTHCHECK_CONFIG = {
  PAST_DUE_THRESHOLD_HOURS: 1.5,
  UNCHECKED_TEAMS_EXAMPLE_LIMIT: 3,
  MAX_ERROR_TYPES_DISPLAYED: 5,
} as const

type PaymentImbalanceDetails = {
  total_amount_dollars: string
  total_amount_bigint: string
  payments: {
    id: string
    amount_dollars: string
    amount_bigint: string
    pay_type: string
    pay_at_time: string
    recipient: string
  }[]
}
export type PayrollHealthcheckError =
  | {
      name: 'ACCOUNT_MISSING_ERROR'
      data:
        | {
            'teams_data.rise_account'?: string
            'team_payroll.rise_account'?: string
          }
        | {
            'employee_payroll.rise_account'?: string
          }
    }
  | {
      name: 'PAYMENT_FLOW_IMBALANCE_ERROR'
      data: {
        incoming_payments: PaymentImbalanceDetails
        outgoing_payments: PaymentImbalanceDetails
      }
    }
  | {
      name: 'PAST_DUE_SCHEDULED_PAYMENT_ERROR'
      data: { delayed_payments: unknown[] }
    }
  | {
      name: 'CALCULATED_PAYMENT_AMOUNT_MISMATCH_ERROR'
      data: { expected_json: string; actual_json: string }
    }
  | {
      name: 'ERROR_PARSING_FAILED'
      data: { error: string }
    }

export interface PayrollHealthcheckReportProcessedError {
  name: string
  count: number
  affectedTeamsCount: number
  affectedEmployeeEntitiesCount: number
}

export interface PayrollHealthcheckReportData {
  // Context for this specific cycle's data
  payCycle: PayCycle
  payCycleString: string

  // Overall context
  environment: string
  runId: string

  // Data state
  logsFound: boolean

  // Statistics
  totalSystemTeams: number
  totalTeamsChecked: number
  healthyTeams: number
  teamsWithProblems: number
  uncheckedTeams: number

  // New detailed stats
  teamsWithDirectProblems: number
  teamsWithOnlyEmployeeProblems: number
  totalUniqueEmployeesWithProblems: number

  // Status flags derived from stats
  hasProblemsInChecked: boolean
  hasUncheckedItems: boolean

  // Overall Status for this cycle
  overallStatusEmoji: string
  overallStatusText: string

  // Problem details
  topProblemSummary: PayrollHealthcheckReportProcessedError[]
  totalErrorTypes: number

  // SQL Queries for "Need More Details"
  detailsQueryTitle: string
  detailsQuerySQL: string
  uncheckedTeamsQuerySQL?: string
  exampleUncheckedTeams: { nanoid: TeamNanoid; name: string | null }[]
}

export const generateRunId = () => {
  return String(Math.floor(Date.now() / 1000))
}

export const insertPayrollHealthcheckLog = (
  data: {
    runId: string
    teamNanoid: TeamNanoid
    payrollProgram: PayrollHealthcheckLogs['payroll_program']
    userNanoid?: UserNanoid
    targetAccount: PayrollHealthcheckLogs['target_account']
    riseAccount: string
    payCycle: PayCycle
    errors: PayrollHealthcheckError[]
  },
  db = mainDB,
) => {
  using _ = getContext()

  return db
    .insertInto('rise.payroll_healthcheck_logs')
    .values({
      run_id: data.runId,
      team_nanoid: data.teamNanoid,
      payroll_program: data.payrollProgram,
      user_nanoid: data.userNanoid,
      target_account: data.targetAccount,
      rise_account: data.riseAccount,
      pay_cycle_year: data.payCycle.year,
      pay_cycle_month: data.payCycle.month,
      pay_cycle_period: data.payCycle.period,
      success: data.errors.length === 0,
      errors: JSON.stringify(data.errors),
    })
    .execute()
}

export const gatherPayrollHealthcheckData = async (
  payCycle: PayCycle,
  runId: string,
  db = mainDB,
): Promise<PayrollHealthcheckReportData> => {
  using _ = getContext()

  const environment = process.env.NODE_ENV || 'unknown'
  const currentPayCycleString = payCycleToString(payCycle)
  const { start, end } = getPayCycleDates(payCycle)

  const logs = await db
    .selectFrom('rise.payroll_healthcheck_logs')
    .selectAll()
    .where('run_id', '=', runId)
    .where('pay_cycle_year', '=', payCycle.year)
    .where('pay_cycle_month', '=', payCycle.month)
    .where('pay_cycle_period', '=', payCycle.period)
    .orderBy('team_nanoid')
    .orderBy('target_account', 'desc')
    .orderBy('user_nanoid')
    .execute()

  const totalSystemTeamsResult = await db
    .selectFrom('rise.team_payroll as tp')
    .select(db.fn.countAll().as('count'))
    .where('pay_schedule', '=', 'bimonthly')
    .where((eb) =>
      eb.exists(
        eb
          .selectFrom('rise.employee_payroll_settings as eps')
          .select('eps.id')
          .where('eps.team_nanoid', '=', eb.ref('tp.nanoid'))
          .where('eps.payroll_program', '=', eb.ref('tp.payroll_program'))
          .where('eps.start_date', '<=', new Date(end))
          .where((eb) =>
            eb.or([
              eb('eps.end_date', '>=', new Date(start)),
              eb('eps.end_date', 'is', null),
            ]),
          )
          .where('eps.effective_start', '<=', new Date(end))
          .where((eb) =>
            eb.or([
              eb('eps.effective_end', '>=', new Date(start)),
              eb('eps.effective_end', 'is', null),
            ]),
          ),
      ),
    )
    .executeTakeFirst()
  const totalSystemTeams = Number(totalSystemTeamsResult?.count || 0)

  let totalTeamsChecked = 0
  let teamsWithProblems = 0
  let healthyTeams = 0
  let uncheckedTeams = 0
  let overallStatusEmoji = ''
  let overallStatusText = ''
  let detailsQueryTitle = ''
  let detailsQuerySQL = ''
  let uncheckedTeamsQuerySQL: string | undefined
  const topProblemSummary: PayrollHealthcheckReportProcessedError[] = []
  let totalErrorTypes = 0
  let hasProblemsInChecked = false
  let hasUncheckedItems = false
  let teamsWithDirectProblems = 0
  let teamsWithOnlyEmployeeProblems = 0
  const allProblematicEmployeeEntities: Set<string> = new Set() // Stores unique 'team_nanoid:user_nanoid'
  let exampleUncheckedTeams: { nanoid: TeamNanoid; name: string | null }[] = []

  const logsFound = logs.length > 0

  if (logsFound) {
    const teamsData = new Map<
      TeamNanoid,
      {
        hasTeamError: boolean
        employeesWithErrors: Set<UserNanoid>
      }
    >()
    const errorTypeCounts: Map<
      string,
      {
        count: number
        affectedTeams: Set<TeamNanoid>
        affectedEmployeeEntities: Set<string>
      }
    > = new Map()

    for (const log of logs) {
      if (!teamsData.has(log.team_nanoid)) {
        teamsData.set(log.team_nanoid, {
          hasTeamError: false,
          employeesWithErrors: new Set(),
        })
      }
      const teamEntry = teamsData.get(log.team_nanoid)!

      if (!log.success && log.errors) {
        const parsedErrors = parsePayrollHealthcheckErrors(log.errors, log.id)
        if (parsedErrors.length > 0) {
          if (log.target_account === 'team_payroll') {
            teamEntry.hasTeamError = true
          } else if (
            log.target_account === 'employee_payroll' &&
            log.user_nanoid
          ) {
            teamEntry.employeesWithErrors.add(log.user_nanoid)
            allProblematicEmployeeEntities.add(
              `${log.team_nanoid}:${log.user_nanoid}`,
            )
          }

          for (const error of parsedErrors) {
            if (!errorTypeCounts.has(error.name)) {
              errorTypeCounts.set(error.name, {
                count: 0,
                affectedTeams: new Set(),
                affectedEmployeeEntities: new Set(),
              })
            }
            const errorCounter = errorTypeCounts.get(error.name)!
            errorCounter.count++

            if (log.target_account === 'employee_payroll' && log.user_nanoid) {
              errorCounter.affectedEmployeeEntities.add(
                `${log.team_nanoid}:${log.user_nanoid}`,
              )
            } else {
              // Assume team_payroll or other cases affect the team directly
              errorCounter.affectedTeams.add(log.team_nanoid)
            }
          }

          // Handle ERROR_PARSING_FAILED case
          if (parsedErrors.some((e) => e.name === 'ERROR_PARSING_FAILED')) {
            teamEntry.hasTeamError = true
          }
        }
      }
    }

    totalTeamsChecked = teamsData.size
    teamsWithProblems = 0 // Reset and recalculate based on teamsData
    for (const data of teamsData.values()) {
      if (data.hasTeamError || data.employeesWithErrors.size > 0) {
        teamsWithProblems++
      }
      if (data.hasTeamError) {
        teamsWithDirectProblems++
      } else if (data.employeesWithErrors.size > 0) {
        teamsWithOnlyEmployeeProblems++
      }
    }

    healthyTeams = totalTeamsChecked - teamsWithProblems
    uncheckedTeams = Math.max(0, totalSystemTeams - totalTeamsChecked)

    hasProblemsInChecked = teamsWithProblems > 0
    hasUncheckedItems = uncheckedTeams > 0

    if (hasProblemsInChecked && hasUncheckedItems) {
      overallStatusEmoji = '❌'
      overallStatusText = 'Critical Issues & Unchecked Teams'
    } else if (hasProblemsInChecked) {
      overallStatusEmoji = '❌'
      overallStatusText = 'Issues Detected'
    } else if (hasUncheckedItems) {
      overallStatusEmoji = '⚠️'
      overallStatusText = 'Unchecked Teams Detected'
    } else {
      overallStatusEmoji = '✅'
      overallStatusText = 'Healthy'
    }

    const sortedErrors = [...errorTypeCounts.entries()]
      .sort((a, b) => b[1].count - a[1].count)
      .map(([name, data]) => ({
        name,
        count: data.count,
        affectedTeamsCount: data.affectedTeams.size,
        affectedEmployeeEntitiesCount: data.affectedEmployeeEntities.size,
      }))

    totalErrorTypes = sortedErrors.length
    topProblemSummary.push(
      ...sortedErrors.slice(0, HEALTHCHECK_CONFIG.MAX_ERROR_TYPES_DISPLAYED),
    )

    detailsQueryTitle = 'All Healthcheck Logs for this Cycle'
    detailsQuerySQL = `SELECT *\nFROM rise.payroll_healthcheck_logs\nWHERE run_id = '${runId}'\n  AND pay_cycle_year = ${payCycle.year}\n  AND pay_cycle_month = ${payCycle.month}\n  AND pay_cycle_period = ${payCycle.period}\n  AND success = false;`
    if (hasUncheckedItems) {
      uncheckedTeamsQuerySQL = `SELECT tp.nanoid, tp.name FROM rise.team_payroll tp LEFT JOIN (SELECT DISTINCT team_nanoid FROM rise.payroll_healthcheck_logs WHERE run_id = '${runId}' AND pay_cycle_year = ${payCycle.year} AND pay_cycle_month = ${payCycle.month} AND pay_cycle_period = ${payCycle.period}) AS checked_logs ON tp.nanoid = checked_logs.team_nanoid WHERE checked_logs.team_nanoid IS NULL;`
      // Fetch example unchecked teams - optimized approach
      const checkedTeamNanoids = new Set(logs.map((log) => log.team_nanoid))
      const allTeams = await db
        .selectFrom('rise.team_payroll as tp')
        .leftJoin('rise.teams_data as td', 'td.nanoid', 'tp.nanoid')
        .select(['tp.nanoid', 'td.name'])
        .execute()

      const uncheckedTeamResults = allTeams
        .filter((team) => !checkedTeamNanoids.has(team.nanoid as TeamNanoid))
        .slice(0, HEALTHCHECK_CONFIG.UNCHECKED_TEAMS_EXAMPLE_LIMIT)

      exampleUncheckedTeams = uncheckedTeamResults.map((r) => ({
        nanoid: r.nanoid as TeamNanoid,
        name: r.name ?? null,
      }))
    }
  } else {
    totalTeamsChecked = 0
    teamsWithProblems = 0
    healthyTeams = 0
    uncheckedTeams = totalSystemTeams
    hasProblemsInChecked = false
    hasUncheckedItems = totalSystemTeams > 0
    teamsWithDirectProblems = 0
    teamsWithOnlyEmployeeProblems = 0
    exampleUncheckedTeams = []

    if (totalSystemTeams === 0) {
      overallStatusEmoji = '🤔'
      overallStatusText = 'No Data'
      detailsQueryTitle = 'System Status'
      detailsQuerySQL =
        'No payroll healthcheck logs found and no teams configured in the system.'
    } else {
      overallStatusEmoji = '⚠️'
      overallStatusText = 'Unchecked Teams Detected'
      detailsQueryTitle = 'Identify Unchecked Teams (No Logs Found for Cycle)'
      detailsQuerySQL =
        'SELECT tp.nanoid, tp.name FROM rise.team_payroll tp; -- Lists all expected teams'
    }
  }

  return {
    payCycle,
    payCycleString: currentPayCycleString,
    environment,
    runId,
    logsFound,
    totalSystemTeams,
    totalTeamsChecked,
    healthyTeams,
    teamsWithProblems,
    uncheckedTeams,
    teamsWithDirectProblems,
    teamsWithOnlyEmployeeProblems,
    totalUniqueEmployeesWithProblems: allProblematicEmployeeEntities.size,
    hasProblemsInChecked,
    hasUncheckedItems,
    overallStatusEmoji,
    overallStatusText,
    topProblemSummary,
    totalErrorTypes,
    detailsQueryTitle,
    detailsQuerySQL,
    uncheckedTeamsQuerySQL,
    exampleUncheckedTeams,
  }
}

export const generatePayrollHealthcheckSlackBlocks = (
  reportDataItems: PayrollHealthcheckReportData[],
): Record<string, unknown>[] => {
  const blocks: Record<string, unknown>[] = []

  if (reportDataItems.length === 0) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: 'No payroll healthcheck data available to report.',
      },
    })
    return blocks
  }

  // Overall Header from the first item (assuming runId and environment are the same for all items)
  const firstItem = reportDataItems[0]!
  blocks.push({
    type: 'header',
    text: {
      type: 'plain_text',
      text: 'Payroll Healthcheck Report',
      emoji: true,
    },
  })
  blocks.push({
    type: 'context',
    elements: [
      { type: 'mrkdwn', text: `*Environment:* ${firstItem.environment}` },
      { type: 'mrkdwn', text: `*Run ID:* ${firstItem.runId}` },
    ],
  })

  for (const reportData of reportDataItems) {
    blocks.push({ type: 'divider' })

    // Pay Cycle Header
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Report for Pay Cycle: ${reportData.payCycleString}*`,
      },
    })

    // Overall Status for this cycle
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Overall Status:* ${reportData.overallStatusEmoji} ${reportData.overallStatusText}`,
      },
    })

    // Payroll Health Breakdown
    const checkedPercentage =
      reportData.totalSystemTeams > 0
        ? `(${((reportData.totalTeamsChecked / reportData.totalSystemTeams) * 100).toFixed(1)}%)`
        : '(N/A)'
    const healthyPercentage =
      reportData.totalTeamsChecked > 0
        ? `(${((reportData.healthyTeams / reportData.totalTeamsChecked) * 100).toFixed(1)}%)`
        : '(N/A)'
    const breakdownItems = [
      `• *Teams Checked:* ${reportData.totalTeamsChecked}/${reportData.totalSystemTeams} ${checkedPercentage}`,
      `• *Healthy Payrolls:* ${reportData.healthyTeams}/${reportData.totalTeamsChecked} ${healthyPercentage}${reportData.healthyTeams > 0 && reportData.logsFound && !reportData.hasProblemsInChecked ? ' ✅' : ''}`,
      `• *Payrolls with Problems (Overall):* ${reportData.teamsWithProblems}${reportData.teamsWithProblems > 0 ? ' ❌' : ''}`,
      `     • *Teams with Direct Issues:* ${reportData.teamsWithDirectProblems}`,
      `     • *Teams with only Employee Issues:* ${reportData.teamsWithOnlyEmployeeProblems}`,
      `     • *Unique Employees with Problems:* ${reportData.totalUniqueEmployeesWithProblems}`,
      `• *Unchecked Payrolls:* ${reportData.uncheckedTeams}${reportData.uncheckedTeams > 0 ? ' ⚠️' : ''}`,
    ]

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Payroll Health Breakdown:*\n${breakdownItems.join('\n')}`,
      },
    })

    // Problem Summary
    if (
      reportData.hasProblemsInChecked &&
      reportData.topProblemSummary.length > 0
    ) {
      let problemSummaryTitle = ''
      const maxErrorsToShow = HEALTHCHECK_CONFIG.MAX_ERROR_TYPES_DISPLAYED
      if (reportData.totalErrorTypes > maxErrorsToShow) {
        problemSummaryTitle = `*Problem Summary (Top ${maxErrorsToShow} of ${reportData.totalErrorTypes} types):*`
      } else if (reportData.totalErrorTypes > 0) {
        problemSummaryTitle = `*Problem Summary (${reportData.totalErrorTypes} type${reportData.totalErrorTypes > 1 ? 's' : ''}):*`
      }

      const problemDetails = reportData.topProblemSummary
        .slice(0, maxErrorsToShow)
        .map(
          (error) =>
            `• \`${error.name}\`: ${error.count} occurrence${error.count > 1 ? 's' : ''} (affecting ${error.affectedTeamsCount} team${error.affectedTeamsCount === 1 ? '' : 's'} and ${error.affectedEmployeeEntitiesCount} employee entity${error.affectedEmployeeEntitiesCount === 1 ? '' : 's'})`,
        )

      if (reportData.totalErrorTypes > maxErrorsToShow) {
        problemDetails.push(
          `• _(And ${reportData.totalErrorTypes - maxErrorsToShow} more error types)_`,
        )
      }
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `${problemSummaryTitle}\n${problemDetails.join('\n')}`,
        },
      })
    } else if (reportData.hasProblemsInChecked) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Problem Summary:*\nIssues detected affecting ${reportData.teamsWithProblems} teams, but specific error types were not identified. Please check logs.`,
        },
      })
    }

    // Need More Details?
    if (reportData.detailsQuerySQL) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Need More Details?*\n${reportData.detailsQueryTitle}:\n\`\`\`${reportData.detailsQuerySQL}\n\`\`\``,
        },
      })
      if (reportData.uncheckedTeamsQuerySQL) {
        let uncheckedTeamsText = `_To identify all unchecked teams, use this query:_\n\`\`\`${reportData.uncheckedTeamsQuerySQL}\n\`\`\``
        if (reportData.exampleUncheckedTeams.length > 0) {
          const examples = reportData.exampleUncheckedTeams
            .map(
              (team) =>
                `     • ${team.name || 'Unnamed Team'} (ID: ${team.nanoid})`,
            )
            .join('\n')
          uncheckedTeamsText += `\n\n*Examples of Unchecked Teams:*\n${examples}`
        }
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: uncheckedTeamsText,
          },
        })
      }
    } else if (!reportData.logsFound && reportData.totalSystemTeams > 0) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Need More Details?*\nTo investigate why teams were not checked, review the healthcheck process logs and verify team configurations. No specific query available due to no logs for this cycle.',
        },
      })
    }
  }
  if (reportDataItems.length > 0) {
    blocks.push({ type: 'divider' })
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `Report generated at: ${new Date().toUTCString()}`,
        },
      ],
    })
  }

  return blocks
}

export const readAndFormatPayments = async (params: {
  incoming: {
    account: string
    groupId: string
  }
  outgoing: {
    account: string
    groupId: string
  }
}) => {
  const { incoming, outgoing } = params

  const [incomingPayments, outgoingPayments] = await Promise.all([
    groupPaymentsRead(
      incoming.account,
      incoming.groupId,
      ['Scheduled', 'Complete'],
      'arbitrum',
    ),
    groupPaymentsRead(
      outgoing.account,
      outgoing.groupId,
      ['Scheduled', 'Complete'],
      'arbitrum',
    ),
  ])

  const storageTypes = {
    COMPLETE: 1n,
    SCHEDULED: 2n,
  }

  const incomingComplete = incomingPayments.filter(
    (p) =>
      p.storage === storageTypes.COMPLETE && p.recipient === outgoing.account,
  )
  const incomingScheduled = incomingPayments.filter(
    (p) =>
      p.storage === storageTypes.SCHEDULED && p.recipient === outgoing.account,
  )
  const outgoingComplete = outgoingPayments.filter(
    (p) => p.storage === storageTypes.COMPLETE,
  )
  const outgoingScheduled = outgoingPayments.filter(
    (p) => p.storage === storageTypes.SCHEDULED,
  )

  const formatPayments = (payments: typeof incomingPayments) => {
    return payments.map((p) =>
      friendlyFormatPayment(p, {
        concise: false,
        amountFormat: 'raw_bigint',
      }),
    )
  }

  return {
    incomingScheduled: formatPayments(incomingScheduled),
    incomingComplete: formatPayments(incomingComplete),
    outgoingScheduled: formatPayments(outgoingScheduled),
    outgoingComplete: formatPayments(outgoingComplete),
  }
}

export const validateCalculatedPaymentAmountMismatch = (params: {
  employeePayroll: unknown
  teamNanoid: string
  payCycle: PayCycle
  payrollProgram: string
  outgoingPayments: FormattedPayment<bigint>[]
}): PayrollHealthcheckError | null => {
  return null

  // TODO: finish implementation for this error
}

export const validatePaymentFlowBalance = (payments: {
  incoming: FormattedPayment<bigint>[]
  outgoing: FormattedPayment<bigint>[]
}): PayrollHealthcheckError | null => {
  const getDetails = (
    payments: FormattedPayment<bigint>[],
  ): PaymentImbalanceDetails => {
    const totalAmount = payments.reduce((acc, p) => acc + p.amount, 0n)

    const toDollars = (amount: bigint) => (Number(amount) / 1e6).toFixed(2)

    return {
      total_amount_dollars: toDollars(totalAmount),
      total_amount_bigint: String(totalAmount),
      payments: payments.map((p) => ({
        id: p.id,
        amount_dollars: toDollars(p.amount),
        amount_bigint: String(p.amount),
        pay_type: p.payType,
        pay_at_time: p.payAtTime,
        recipient: p.recipient,
      })),
    }
  }

  const incoming = getDetails(payments.incoming)
  const outgoing = getDetails(payments.outgoing)

  if (incoming.total_amount_bigint !== outgoing.total_amount_bigint) {
    return {
      name: 'PAYMENT_FLOW_IMBALANCE_ERROR',
      data: {
        incoming_payments: incoming,
        outgoing_payments: outgoing,
      },
    }
  }

  return null
}

export const validatePaymentTiming = (
  scheduledPayments: Array<{ payAtTime: string }>,
): PayrollHealthcheckError | null => {
  const delayedPayments: unknown[] = []

  for (const payment of scheduledPayments) {
    const payAtTime = new Date(payment.payAtTime)
    const now = new Date()
    const diffHours = (now.getTime() - payAtTime.getTime()) / (1000 * 60 * 60)

    if (diffHours > HEALTHCHECK_CONFIG.PAST_DUE_THRESHOLD_HOURS) {
      delayedPayments.push(payment)
    }
  }

  if (delayedPayments.length > 0) {
    return {
      name: 'PAST_DUE_SCHEDULED_PAYMENT_ERROR',
      data: { delayed_payments: delayedPayments },
    }
  }

  return null
}

export const parsePayrollHealthcheckErrors = (
  errorJson: unknown,
  logId: number,
): PayrollHealthcheckError[] => {
  try {
    const parsedErrors = errorJson as PayrollHealthcheckError[]
    if (Array.isArray(parsedErrors)) {
      return parsedErrors
    }
    return []
  } catch (e) {
    console.error(`Failed to parse errors JSON for log id ${logId}:`, e)
    return [
      {
        name: 'ERROR_PARSING_FAILED',
        data: {
          error: e instanceof Error ? e.message : 'Unknown parsing error',
        },
      },
    ]
  }
}

export const paymentAmountsToBigInt = (
  payments: FormattedPayment<string>[],
): FormattedPayment<bigint>[] => {
  return payments.map((p) => ({ ...p, amount: BigInt(p.amount) }))
}
