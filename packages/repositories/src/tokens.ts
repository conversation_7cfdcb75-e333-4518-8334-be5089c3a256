/**
 *
 * This file is a lift and shift from v1 Rise pay app (pay.riseworks.io).
 * Funcationality is the same, but the code is not identical.
 * We should audit this file for a more v2 appropriate approach.
 *
 */

import { getContext } from 'utils/src/common/requestContext.js'
import { ethers } from 'ethers'
import { arbTokenList } from './arb-token-list.js'

const layerIds: Record<string, number> = {
  sepolia: 11155111,
  arbitrum: 42161,
  'arbitrum-sepolia': 421614,
  mainnet: 1,
}

const NoImageTokens = [
  'agve',
  'bark',
  'cnt',
  'defi5',
  'degen',
  'dfyn',
  'dog',
  'dsu',
  'dvf',
  'ess',
  'eux',
  'flux',
  'fst',
  'gmx',
  'imx',
  'land',
  'magic',
  'mal',
  'nec',
  'nfd',
  'pl2',
  'route',
  'sdt',
  'spa',
  'strp',
  'sum',
  'swpr',
  'tac',
  'tcr',
  'unt',
  'usx',
  'valx',
  'wchi',
  'ibbtc',
  'kusdc',
]

export const getTokens = (network = 'arbitrum') => {
  using ctx = getContext()
  const { logger } = ctx

  let assignedNetwork = network

  try {
    const blacklistSymbols = ['USDC']

    if (!['mainnet', 'arbitrum'].includes(assignedNetwork)) {
      logger.warn('Invalid Network')
      return null
    }

    const isDev = 'development,staging,localhost'.includes(process.env.NODE_ENV)

    if (assignedNetwork === 'mainnet' && isDev) {
      assignedNetwork = 'sepolia'
    }

    if (assignedNetwork === 'arbitrum' && isDev) {
      assignedNetwork = 'arbitrum-sepolia'
    }

    const chainId = layerIds[assignedNetwork]

    const tokenList = { ...arbTokenList }

    tokenList.tokens = tokenList.tokens
      .filter(
        (token) =>
          !(
            NoImageTokens.includes(token.symbol.toLowerCase()) ||
            blacklistSymbols.includes(token.symbol)
          ) && token.chainId === chainId,
      )
      .map((token) => {
        if (token?.extensions?.l1Address) {
          const realL1Token = tokenList.tokens.find(
            (t) => t.symbol === token.symbol && t.chainId === 1,
          )
          const address = ethers.getAddress(
            realL1Token?.address || token.extensions.l1Address,
          )
          token.logoURI = `https://assets.trustwalletapp.com/blockchains/ethereum/assets/${address}/logo.png`
        } else if (chainId === 1) {
          const address = ethers.getAddress(token.address)
          token.logoURI = `https://assets.trustwalletapp.com/blockchains/ethereum/assets/${address}/logo.png`
        }
        return token
      })
      .sort((a, b) => {
        const as = (a.symbol || a.name).toLowerCase()
        const bs = (b.symbol || b.name).toLowerCase()
        return as.localeCompare(bs)
      })
    return tokenList
  } catch (e) {
    logger.warn('Failed to parse tokens list', e)
    return null
  }
}
