import createClient from 'openapi-fetch'
import assert from 'utils/src/common/assertHTTP.js'
import { isProduction, isStaging } from 'utils/src/common/env.js'
import { getContext } from 'utils/src/common/requestContext.js'

const devEndpoint = 'https://api-uniswap-841056449880.us-central1.run.app'
const stagingEndpoint = 'https://api-uniswap-103829732552.us-central1.run.app'
const prodEndpoint = 'https://api-uniswap-23957376505.us-central1.run.app'
const endpoint = isProduction
  ? prodEndpoint
  : isStaging
    ? stagingEndpoint
    : devEndpoint

interface paths {
  '/quote': {
    post: {
      requestBody: {
        content: {
          'application/json': {
            inputTokenAddress: string
            outputTokenAddress: string
            inputAmountCents: number
            providerUrl: string
            rampAddress: string
          }
        }
      }
      responses: {
        200: {
          content: {
            'application/json': {
              inputAmount: string
              outputAmount: string
              priceImpact: string
              minimumOutput: string
              executionPrice: string
              quote: string
            }
          }
        }
      }
    }
  }
  '/swapConfig': {
    post: {
      requestBody: {
        content: {
          'application/json': {
            inputTokenAddress: string
            outputTokenAddress: string
            inputAmountCents: number
            receiverAddress: string
            offChainReference: string
            providerUrl: string
            rampAddress: string
          }
        }
      }
      responses: {
        200: {
          content: {
            'application/json': {
              amount: string
              transferType: string
              fixedOrPercent: string
              ramp: string
              source: string
              destination: string
              offChainReference: string
              data: string
            }
          }
        }
      }
    }
  }
}

const client = createClient<paths>({
  baseUrl: endpoint,
  headers: {
    'Content-Type': 'application/json',
  },
})

export const getUniswapQuoteV4 = async ({
  inputTokenAddress,
  outputTokenAddress,
  inputAmountCents,
  providerUrl,
  rampAddress,
}: {
  inputTokenAddress: string
  outputTokenAddress: string
  inputAmountCents: number
  providerUrl: string
  rampAddress: string
}) => {
  using _ = getContext()
  const { error, data } = await client.POST('/quote', {
    headers: {
      'x-rise-internal-request-id': _.requestId,
    },
    body: {
      inputTokenAddress,
      outputTokenAddress,
      inputAmountCents,
      providerUrl,
      rampAddress,
    },
  })
  assert(!error, `Error returned from Uniswap API: ${error}`)
  assert(data, 'No data returned from Uniswap API')
  return data
}

export const getUniswapV4Config = async ({
  inputTokenAddress,
  outputTokenAddress,
  inputAmountCents,
  receiverAddress,
  offChainReference,
  providerUrl,
  rampAddress,
}: {
  inputTokenAddress: string
  outputTokenAddress: string
  inputAmountCents: number
  receiverAddress: string
  offChainReference: string
  providerUrl: string
  rampAddress: string
}) => {
  using _ = getContext()
  const { error, data } = await client.POST('/swapConfig', {
    headers: {
      'x-rise-internal-request-id': _.requestId,
    },
    body: {
      inputTokenAddress,
      outputTokenAddress,
      inputAmountCents,
      receiverAddress,
      offChainReference,
      providerUrl,
      rampAddress,
    },
  })
  assert(!error, `Error returned from Uniswap API: ${error}`)
  assert(data, 'No data returned from Uniswap API')
  return data
}
