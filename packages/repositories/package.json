{"name": "repositories", "version": "1.0.1", "description": "Rise Common Model Queries", "license": "UNLICENSED", "main": "./src/index.js", "types": "./src/index.ts", "private": true, "type": "module", "scripts": {"lint": "biome lint --no-errors-on-unmatched --write src/*.ts --config-path ../../", "typecheck": "tsc -b --emitDeclarationOnly", "typecheck:watch": "tsc -b --emitDeclarationOnly --watch", "generate": "pnpm run --parallel \"/^generate:.*/\"", "generate:klaviyo": "openapi-typescript ./src/oas/klaviyo.json --output ./src/oas/klaviyo.ts", "generate:orum-auth": "openapi-typescript https://github.com/orum-io/openapi/releases/download/v1.0.0/auth.yaml --output ./src/oas/orum-auth.ts", "generate:orum-deliver": "openapi-typescript https://github.com/orum-io/openapi/releases/download/v1.0.0/deliver.yaml --output ./src/oas/orum-deliver.ts", "generate:orum-verify": "openapi-typescript https://github.com/orum-io/openapi/releases/download/v1.0.0/verify.yaml --output ./src/oas/orum-verify.ts", "build": "swc ./src -s --ignore **/*.js -d .", "clean": "rimraf \"dist\" \"./src/**/*.js\" \"./src/**/*.d.ts\" \"./src/**/*.d.ts.map\" \"./src/**/*.js.map\" -g"}, "dependencies": {"@anatine/zod-mock": "^3.14.0", "@clerk/express": "^1.6.0", "@faker-js/faker": "^9.8.0", "@google-cloud/storage": "^7.16.0", "@googleapis/drive": "^13.0.1", "@integration-app/sdk": "^1.13.0", "@riseworks/contracts": "workspace:*", "@segment/analytics-node": "^2.2.1", "@uniswap/sdk-core": "^7.7.2", "@uniswap/smart-order-router": "^4.21.18", "@uniswap/v3-sdk": "^3.25.2", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "country-code-lookup": "^0.1.3", "crc": "^4.3.2", "dayjs": "^1.11.13", "db": "workspace:*", "ethers": "^6.14.3", "firebase-admin": "^13.4.0", "form-data": "^4.0.3", "go-go-try": "^6.2.0", "google-auth-library": "^10.1.0", "jsbi": "3.2.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "kysely": "^0.28.2", "memfs": "^4.17.2", "openapi-fetch": "0.13.1", "remeda": "^2.23.0", "siwe": "^3.0.0", "us-state-codes": "^1.1.2", "us-state-converter": "^1.0.8", "utils": "workspace:*", "uuid": "^11.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.25.63"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.31", "openapi-typescript": "^7.8.0", "rimraf": "^6.0.1", "typescript": "^5.8.3"}}