# Forked Network Setup

This guide explains how to set up and use a local Anvil instance that forks the Arbitrum Sepolia network.

## Overview

We're using Anvil to fork the Arbitrum Sepolia network. This gives us access to all contracts already deployed on that network, allowing us to interact with them in a local environment without spending real ETH or tokens.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Node.js and pnpm installed
- Access to the project repository

## Setup Instructions

### 1. Start the Forked Network

Run the following command to start Anvil with the correct fork configuration:

```bash
pnpm -w script setupForkedNetwork
```

This script will:
- Check if Anvil is running with the correct fork configuration
- If not, start or restart Anvil with the proper settings
- Connect to the forked network and verify it's working
- Save network information to `forked-network-info.json`

### 2. Query Contracts on the Forked Network

To query information about known contracts on the forked network:

```bash
pnpm -w script queryForkedContracts
```

This script will:
- Connect to the forked network
- Query information about known contract addresses
- Save contract information to `forked-contracts-info.json`

## Configuration Details

The forked network is configured with the following settings:

- **RPC URL**: http://localhost:8547
- **Chain ID**: 421614 (Arbitrum Sepolia)
- **Fork URL**: https://arbitrum-sepolia.infura.io/v3/[INFURA_KEY]

## Interacting with the Forked Network

You can interact with the forked network using:

1. **Web3.js or ethers.js**: Connect to http://localhost:8547
2. **Metamask**: Add a custom network with RPC URL http://localhost:8547 and Chain ID 421614
3. **Hardhat or Foundry**: Configure your project to use the local RPC URL
4. **Local otterscan**: Check transactions at http://localhost:5100

## Troubleshooting

If you encounter issues:

1. **Anvil not starting**: Check Docker logs with `docker compose logs rise-anvil`
2. **Connection issues**: Ensure port 8545 is not being used by another application
3. **Contract interaction failures**: Verify the contract addresses are correct for Arbitrum Sepolia

## Notes

- The forked network is a local copy of Arbitrum Sepolia at a specific block number
- Any changes you make to the forked network are local only and do not affect the actual Arbitrum Sepolia network
- The local accounts have test ETH that can be used for transactions
