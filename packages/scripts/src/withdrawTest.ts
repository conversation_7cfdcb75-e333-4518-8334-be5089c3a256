import type {
  UserNanoid,
  WithdrawAccountNanoid,
} from '@riseworks/contracts/src/brands.js'
import { ethers } from 'ethers'
import {
  withdrawTransaction,
  withdrawTransactionRequest,
} from 'repositories/src/withdrawals.js'

const withdrawAccount = 'wa-iWcGpQKBSCWa' as WithdrawAccountNanoid
const privateKey =
  '0x5c9125e97342b51209e99eb16b547561af58cd6ba525220a5e7f9dc6581a383f'
const userNanoid = 'us-BTSwkYfzxt1K' as UserNanoid

export default async () => {
  const {
    tokenTransfer: { domain, types, primary_type, typed_data },
  } = await withdrawTransaction({
    nanoid: userNanoid,
    from: userNanoid,
    account_nanoid: withdrawAccount,
    amount_cents: 100,
    network: 'arbitrum',
  })

  console.log({ domain, types, primary_type, typed_data })

  const wallet = new ethers.Wallet(privateKey)
  const signature = await wallet.signTypedData(domain, types, typed_data)

  console.log({ signature })

  const tx = await withdrawTransactionRequest(
    wallet.address,
    {
      nanoid: userNanoid,
      from: userNanoid,
      account_nanoid: withdrawAccount,
      amount_cents: 100,
      network: 'arbitrum',
    },
    typed_data,
    signature,
  )

  console.log({ tx })
}
