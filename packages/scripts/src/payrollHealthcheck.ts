import type { TeamNanoid } from '@riseworks/contracts/src/brands.js'
import { employeePayrollGroupId } from 'repositories/src/payroll.js'
import { teamPayrollCashRequirementPaymentId } from 'repositories/src/payroll.js'
import {
  readAndFormatPayments,
  validatePaymentFlowBalance,
} from 'repositories/src/payrollHealthcheck.js'
import { getTeamPayrollAccounts } from 'repositories/src/teams.js'
import assert from 'utils/src/common/assertHTTP.js'

const team_nanoid = 'te-71zZ9p8XHyHH' as TeamNanoid

const pay_cycle = { year: 2025, month: 5, period: 2 }

export default async function payslip() {
  console.time()

  const accounts = await getTeamPayrollAccounts(team_nanoid, 'riseworks_eor_us')
  assert(accounts, 'Accounts not found')
  console.log(accounts)

  const payments = await readAndFormatPayments({
    incoming: {
      account: accounts.team_account,
      groupId: teamPayrollCashRequirementPaymentId(team_nanoid, pay_cycle),
    },
    outgoing: {
      account: accounts.team_payroll_account,
      groupId: employeePayrollGroupId(team_nanoid, pay_cycle),
    },
  })

  const {
    incomingScheduled,
    incomingComplete,
    outgoingScheduled,
    outgoingComplete,
  } = payments

  const paymentFlowData = {
    incoming: [...incomingScheduled, ...incomingComplete],
    outgoing: [...outgoingScheduled, ...outgoingComplete],
  }

  const result = validatePaymentFlowBalance(paymentFlowData)

  console.dir(result, { depth: null })

  console.timeEnd()
}
