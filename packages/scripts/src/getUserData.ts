import minimist from 'minimist'
import { argv, exit } from 'node:process'
import {
  getCompaniesByFounder,
  getCompaniesByUser,
} from 'repositories/src/companies.js'
import { getPrivateData } from 'repositories/src/privateData.js'
import { getUserByNanoid } from 'repositories/src/users.js'
import { getOnboard } from 'repositories/src/usersOnboarding.js'
const args = minimist(argv)

function printUsage(message?: string) {
  console.log(
    [
      `${argv.slice(2).join(' ')}`,
      '\nUsage:\tpnpm script getUserData --nanoid <nanoid> --private-data',
      '\t--nanoid <nanoid>: User Nanoid',
      '\t--private-data: Print Private Data (default: false)',
      message,
    ].join('\n'),
  )
  exit(1)
}

function log(item: Parameters<typeof console.dir>[0]) {
  console.dir(item, {
    depth: Number.POSITIVE_INFINITY,
    colors: true,
    numericSeperator: true,
  })
}

export default async () => {
  if (!args.nanoid) {
    printUsage()
    return
  }

  const user = await getUserByNanoid(args.nanoid)

  if (!user) {
    printUsage(`Unable to find user with nanoid ${args.nanoid}`)
    return
  }

  const data: Record<string, unknown> = { getUserByNanoid: user }

  data.getCompaniesByFounder = await getCompaniesByFounder(user.nanoid)
  data.getOnboard = await getOnboard(user.nanoid)

  if (args['private-data']) {
    data.getPrivateData = await getPrivateData(user.nanoid)
    data.getCompaniesByUser = await getCompaniesByUser(user.nanoid)
  }

  log(data)

  exit(0)
}
