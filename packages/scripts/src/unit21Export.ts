import { appendFileSync, writeFileSync } from 'node:fs'
import type { DB } from '@riseworks/contracts/src/codegen/db/all.js'
import dayjs from 'dayjs'
import { dbInstance } from 'db/src/index.js'
import { sql } from 'kysely'
import { jsonBuildObject } from 'kysely/helpers/mysql'
import minimist from 'minimist'
import pRetry from 'p-retry'
import {
  chunk,
  difference,
  entries,
  filter,
  fromEntries,
  isPlainObject,
} from 'remeda'
import {
  sendTransaction,
  upsertBusiness,
  upsertInstrument,
  upsertUser,
} from 'repositories/src/unit21.js'
import assert from 'utils/src/common/assertHTTP.js'
import { startProgress } from 'utils/src/common/cliProgress.js'
import { run } from 'utils/src/common/run.js'
import { z } from 'zod'

const dbSettings = {
  host: process.env.DB_HOST ?? 'localhost',
  port: +(process.env.DB_PORT ?? 5000),
  user: process.env.DB_USER ?? 'root',
  password: process.env.DB_PW ?? 'local',
  ...(process.env.SOCKET_PATH
    ? { socketPath: process.env.SOCKET_PATH }
    : undefined),
}

const db = dbInstance<DB>(dbSettings)

const isCI = process.env.IS_CI === 'true'

const argv = minimist(process.argv.slice(2))

const shouldSave = !!argv.save
const startDate = z
  .string()
  .date('Invalid startDate, please use YYYY-MM-DD')
  .optional()
  .parse(argv.startDate)

const entities = [
  'users',
  'companies',
  'user_instruments',
  'company_instruments',
  'transactions',
] as const
const skipList = argv.skip
  ? Array.isArray(argv.skip)
    ? argv.skip
    : [argv.skip]
  : []
assert(
  difference(skipList, entities).length === 0,
  `--skip must be a subset of the valid entities: ${JSON.stringify(entities)}, example: --skip users --skip companies`,
)
const CONCURRENCY = 5
const pRetryOptions = {
  retries: 3,
}
const logFile = `${process.cwd()}/scripts/txn.log`
const errorFile = `${process.cwd()}/scripts/txn.errors.log`
if (!isCI) writeFileSync(logFile, '')
if (!isCI) writeFileSync(errorFile, '')

let usersLength = 0
const failedUsers: Parameters<typeof upsertUser>[0][] = []
const failedUserInstruments: Parameters<typeof upsertInstrument>[0][] = []
let companiesLength = 0
const failedCompanies: Parameters<typeof upsertBusiness>[0][] = []
const failedCompanyInstruments: Parameters<typeof upsertInstrument>[0][] = []
let userInstrumentsLength = 0
let companyInstrumentsLength = 0
let transactionsLength = 0
const failedTransactions: Parameters<typeof sendTransaction>[0][] = []

if (!skipList.includes('users')) {
  if (!isCI) appendFileSync(logFile, 'ADDING USERS\n\n')

  const users = await db
    .selectFrom('strapi.users-permissions_user as u')
    .leftJoin('private_data.address as a', 'a.id', 'u.addressId')
    .leftJoin('private_data.countries_risk as c', 'c.code', 'a.country')
    .leftJoin('strapi.private_compliance as pc', 'pc.userOrCompanyId', 'u.id')
    .select((eb) => [
      'u.id',
      'u.email',
      'u.firstname',
      'u.lastname',
      'u.middlename',
      'u.phone',
      'a.city',
      'a.address1',
      'a.address2',
      'a.country',
      'a.state',
      'a.zip',
      eb
        .fn<string | null>('DATE_FORMAT', ['u.birthdate', eb.val('%Y-%m-%d')])
        .as('birthdate'),
      'u.riseId',
      'u.created_at',
      'pc.compliance_id',
      eb
        .selectFrom('strapi.private_user_ip_records as r')
        .select(['r.ip'])
        .whereRef('r.userId', '=', 'u.id')
        .orderBy('r.created_at', 'desc')
        .limit(1)
        .as('ip'),
      eb
        .case()
        .when('c.risk_level', 'is not', null)
        .then(eb.ref('c.risk_level'))
        .else(0)
        .end()
        .as('country_risk_score'),
      eb
        .selectFrom('private_data.entity_risk_scores as e')
        .select(['e.risk_level'])
        .whereRef('e.entity_id', '=', 'u.id')
        .where('e.entity_type', '=', 'user')
        .orderBy('e.id', 'desc')
        .limit(1)
        .as('customer_risk_score'),
      sql.lit('Contractor').as('role_type'),
    ])
    .where('u.role', '=', 4)
    .where('u.riseId', 'is not', null)
    .$if(!!startDate, (qb) =>
      qb.where('u.created_at', '>=', dayjs(startDate).toDate()),
    )
    .$narrowType<{
      country_risk_score: number
    }>()
    .execute()
  usersLength = users.length
  await startProgress('Users', usersLength, async (increment) => {
    for (const c of chunk(users, CONCURRENCY)) {
      const userObjs: Parameters<typeof upsertUser>[0][] = []
      const instrumentObjs: Parameters<typeof upsertInstrument>[0][] = []
      const initialTime = Date.now()
      for (const user of c) {
        const [year, month, day] = user.birthdate
          ? user.birthdate.split('-').map(Number)
          : [null, null, null]
        const user_riseid = user.riseId
        assert(user.created_at, 'Missing created_at')
        assert(user_riseid, 'Missing user_riseid')
        const userObj = {
          id: `user-${user.id}` as const,
          subtype: 'payee',
          registered_at: Math.round(user.created_at.getTime() / 1000),
          user_data: {
            first_name: user.firstname ?? '',
            middle_name: user.middlename ?? '',
            last_name: user.lastname ?? '',
            ...(day && month && year
              ? {
                  day_of_birth: day,
                  month_of_birth: month,
                  year_of_birth: year,
                }
              : {}),
          },
          location_data: {
            city: user.city ?? '',
            country: user.country ?? '',
            state: user.state ?? '',
            street_name: `${user.address1}${user.address2 ? ` ${user.address2}` : ''}`,
            postal_code: user.zip ?? '',
          },
          custom_data: {
            country_risk_score: user.country_risk_score,
            customer_risk_score: user.customer_risk_score ?? 0,
            email_risk_level: '',
            country_of_incorporation: '',
            identification_number: '',
            intercom_link: '',
            ip_risk_level: '',
            kyc_profile: user.compliance_id
              ? `https://cockpit.sumsub.com/checkus#/applicant/${user.compliance_id}/client/basicInfo`
              : '',
          },
          communication_data: {
            email: user.email,
            phone_number: user.phone ?? '',
          },
          ip_address: user.ip ?? '',
        } as const
        const instrumentObj = {
          instrument_id: `crypto-${user_riseid}` as const,
          name: `riseid-${user_riseid}`,
          instrument_type: 'eth_address',
          custom_data: {
            address: user_riseid,
          },
          entities: [
            {
              entity_id: `user-${user.id}` as const,
              entity_type: 'user' as 'business' | 'user',
            },
          ],
        }
        if (!(shouldSave || isCI)) {
          appendFileSync(logFile, `${JSON.stringify(userObj, null, 2)}\n`)
          appendFileSync(logFile, `${JSON.stringify(instrumentObj, null, 2)}\n`)
        }
        userObjs.push(userObj)
        instrumentObjs.push(instrumentObj)
      }
      if (shouldSave) {
        const userPromises = await Promise.allSettled(userObjs.map(upsertUser))
        const instrumentPromises = await Promise.allSettled(
          instrumentObjs.map(upsertInstrument),
        )
        for (const [i, p] of userPromises.entries()) {
          if (p.status === 'rejected') {
            console.log(p.reason)
            failedUsers.push(userObjs[i]!)
          }
        }
        for (const [i, p] of instrumentPromises.entries()) {
          if (p.status === 'rejected') {
            console.log(p.reason)
            failedUserInstruments.push(instrumentObjs[i]!)
          }
        }
      }
      increment(CONCURRENCY, initialTime, Date.now())
    }
  })
  console.log(`${usersLength} USERS ADDED`)
}

if (!skipList.includes('companies')) {
  const companies = await db
    .selectFrom('strapi.companies as c')
    .innerJoin('strapi.users-permissions_user as u', 'u.id', 'c.owner')
    .leftJoin('private_data.countries_risk as cr', 'cr.code', 'c.country')
    .leftJoin('strapi.private_compliance as pc', (join) =>
      join
        .onRef('pc.userOrCompanyId', '=', 'c.id')
        .on('pc.userType', '=', 'business'),
    )
    .select((eb) => [
      'c.id',
      'c.name',
      'c.tax_id',
      'c.country',
      'c.state',
      'c.address1',
      'c.address2',
      'c.city',
      'c.zip',
      'c.incorporation',
      'c.phone',
      'c.website',
      'c.created_at',
      'u.id as user_id',
      eb
        .fn<string | null>('DATE_FORMAT', ['u.birthdate', eb.val('%Y-%m-%d')])
        .as('user_birthdate'),
      'u.email as user_email',
      'u.firstname as user_firstname',
      'u.lastname as user_lastname',
      'u.middlename as user_middlename',
      'u.phone as user_phone',
      'c.riseId',
      'u.riseId as user_riseid',
      eb
        .case()
        .when('c.isPayeeCompany', '=', true)
        .then('payee')
        .else('payer')
        .end()
        .as('company_type'),
      eb
        .case()
        .when('c.isPayeeCompany', '=', false)
        .then(eb.ref('c.riseId'))
        .else(eb.ref('u.riseId'))
        .end()
        .as('company_riseid'),
      eb
        .selectFrom('strapi.private_user_ip_records as r')
        .select(['r.ip'])
        .whereRef('r.userId', '=', 'c.id')
        .orderBy('r.created_at', 'desc')
        .limit(1)
        .as('ip'),
      eb
        .case()
        .when('cr.risk_level', 'is not', null)
        .then(eb.ref('cr.risk_level'))
        .else(0)
        .end()
        .as('country_risk_score'),
      eb
        .selectFrom('private_data.entity_risk_scores as e')
        .select(['e.risk_level'])
        .whereRef('e.entity_id', '=', 'c.id')
        .where('e.entity_type', '=', 'company')
        .orderBy('e.id', 'desc')
        .limit(1)
        .as('customer_risk_score'),
      eb
        .fn<string>('CONCAT', ['u.firstname', eb.val(' '), 'u.lastname'])
        .as('account_holder_name'),
      'pc.compliance_id',
    ])
    .where('u.registrationType', '=', 'business')
    .where((eb) =>
      eb.or([eb('c.riseId', 'is not', null), eb('u.riseId', 'is not', null)]),
    )
    .$if(!!startDate, (qb) =>
      qb.where('c.created_at', '>=', dayjs(startDate).toDate()),
    )
    .$narrowType<{
      country_risk_score: number
      company_type: 'payer' | 'payee'
    }>()
    .execute()

  if (!isCI) {
    appendFileSync(
      logFile,
      `\n\n${usersLength} USERS ADDED\n\nADDING COMPANIES\n\n`,
    )
  }

  companiesLength = companies.length

  await startProgress('Companies', companiesLength, async (increment) => {
    for (const c of chunk(companies, CONCURRENCY)) {
      const initialTime = Date.now()
      const companyObjs: Parameters<typeof upsertBusiness>[0][] = []
      const instrumentObjs: Parameters<typeof upsertInstrument>[0][] = []
      for (const company of c) {
        const riseid = company.riseId ?? company.user_riseid
        assert(riseid, 'Missing company_riseid')
        const [year, month, day] = company.user_birthdate
          ? company.user_birthdate.split('-').map(Number)
          : [null, null, null]
        const companyObj = {
          id: `company-${company.id}` as const,
          subtype: company.company_type,
          owner_id: `user-${company.user_id}` as const,
          user_data: {
            first_name: company.user_firstname ?? '',
            last_name: company.user_lastname ?? '',
            middle_name: company.user_middlename ?? '',
            ...(day && month && year
              ? {
                  day_of_birth: day,
                  month_of_birth: month,
                  year_of_birth: year,
                }
              : {}),
          },
          business_data: {
            business_name: company.name ?? '',
            corporate_tax_id: company.tax_id ?? '',
            account_holder_name: company.account_holder_name ?? '',
            registered_state: company.state ?? '',
            registered_country: company.country ?? '',
            website: company.website ?? '',
          },
          location_data: {
            city: company.city ?? '',
            country: company.country ?? '',
            state: company.state ?? '',
            street_name: `${company.address1}${company.address2 ? ` ${company.address2}` : ''}`,
            postal_code: company.zip ?? '',
          },
          custom_data: {
            country_risk_score: company.country_risk_score,
            customer_risk_score: company.customer_risk_score ?? 0,
            country_of_incorporation: company.incorporation ?? '',
            email_risk_level: '',
            identification_number: '',
            intercom_link: '',
            ip_risk_level: '',
            kyb_profile: company.compliance_id
              ? `https://cockpit.sumsub.com/checkus#/applicant/${company.compliance_id}/client/basicInfo`
              : '',
          },
          communication_data: {
            phone_number: company.phone ?? '',
          },
          ip_address: company.ip ?? '',
        } as const
        const instrumentObj = {
          instrument_id: `crypto-${riseid}` as const,
          name: `riseid-${riseid}`,
          instrument_type: 'eth_address',
          instrument_subtype: 'eth_address',
          custom_data: {
            address: riseid,
          },
          entities: [
            {
              entity_id: `company-${company.id}` as const,
              entity_type: 'business' as 'business' | 'user',
            },
          ],
        }
        if (!(shouldSave || isCI)) {
          appendFileSync(logFile, `${JSON.stringify(companyObj, null, 2)}\n`)
          appendFileSync(logFile, `${JSON.stringify(instrumentObj, null, 2)}\n`)
        }
        companyObjs.push(companyObj)
        instrumentObjs.push(instrumentObj)
      }
      if (shouldSave) {
        const companyPromises = await Promise.allSettled(
          companyObjs.map(upsertBusiness),
        )
        const instrumentPromises = await Promise.allSettled(
          instrumentObjs.map(upsertInstrument),
        )
        for (const [i, p] of companyPromises.entries()) {
          if (p.status === 'rejected') {
            console.log(p.reason)
            failedCompanies.push(companyObjs[i]!)
          }
        }
        for (const [i, p] of instrumentPromises.entries()) {
          if (p.status === 'rejected') {
            console.log(p.reason)
            failedCompanyInstruments.push(instrumentObjs[i]!)
          }
        }
      }
      increment(CONCURRENCY, initialTime, Date.now())
    }
  })

  console.log(`${companiesLength} COMPANIES ADDED`)
}

if (!skipList.includes('user_instruments')) {
  const userInstruments = await db
    .selectFrom('strapi.pay_external_accounts as p')
    .select((eb) => [
      sql.lit('crypto').as('entity'),
      'p.id',
      'p.name',
      'p.type',
      'p.type as subtype',
      eb.fn('CONVERT', ['p.data', sql`JSON`]).as('data'),
      sql<string>`CAST(p.user AS CHAR)`.as('user_id'),
    ])
    .innerJoin('strapi.users-permissions_user as u', 'u.id', 'p.user')
    .where('p.company', 'is', null)
    .where('u.role', '=', 4)
    .where('u.riseId', 'is not', null)
    .$if(!!startDate, (qb) =>
      qb.where('p.created_at', '>=', dayjs(startDate).toDate()),
    )
    .$narrowType<{
      entity: 'bank_account' | 'crypto'
      data: {
        accountNumber: string | null
        routingNumber: string | null
        iban: string | null
        beneficiaryName: string | null
        beneficiaryCountryCode: string | null
      }
    }>()
    .union(
      db
        .selectFrom('private_data.bank_accounts as b')
        .innerJoin(
          'strapi.pay_external_accounts as p',
          'p.id',
          'b.strapi_external_account_id',
        )
        .innerJoin('strapi.users-permissions_user as u', 'u.id', 'b.user_id')
        .where('b.strapi_company_id', 'is', null)
        .where('u.role', '=', 4)
        .where('u.riseId', 'is not', null)
        .select((eb) => [
          sql.lit('bank_account').as('entity'),
          'b.id',
          eb
            .case()
            .when('b.bank_name', 'is not', null)
            .then(eb.ref('b.bank_name'))
            .else(eb.ref('p.type'))
            .end()
            .as('name'),
          sql.lit('bank_account').as('type'),
          'p.type as subtype',
          jsonBuildObject({
            accountNumber: eb.ref('b.account_number'),
            routingNumber: eb.ref('b.routing_number'),
            iban: eb.ref('b.iban'),
            beneficiaryName: eb.ref('b.beneficiary_name'),
            beneficiaryCountryCode: eb.ref('b.beneficiary_country_code'),
          }).as('data'),
          sql<string>`CAST(b.user_id AS CHAR)`.as('user_id'),
        ])
        .$if(!!startDate, (qb) =>
          qb.where('b.created_at', '>=', dayjs(startDate).toDate()),
        )
        .$narrowType<{
          entity: 'bank_account' | 'crypto'
        }>(),
    )
    .execute()

  if (!isCI) {
    appendFileSync(
      logFile,
      `\n\n${companiesLength} COMPANIES ADDED\n\nADDING USER INSTRUMENTS\n\n`,
    )
  }

  userInstrumentsLength = userInstruments.length
  await startProgress(
    'User Instruments',
    userInstrumentsLength,
    async (increment) => {
      for (const c of chunk(userInstruments, CONCURRENCY)) {
        const initialTime = Date.now()
        const instrumentObjs: Parameters<typeof upsertInstrument>[0][] = []
        for (const instrument of c) {
          type CustomData = {
            [K in keyof typeof instrument.data]: NonNullable<
              (typeof instrument.data)[K]
            >
          }
          const custom_data = isPlainObject(instrument.data)
            ? (fromEntries(
                filter(entries(instrument.data), ([key, value]) => !!value),
              ) as CustomData)
            : {}
          const instrumentObj = {
            instrument_id: `${instrument.entity}-${instrument.id}` as const,
            name: instrument.name ?? '',
            instrument_type: instrument.type ?? '',
            instrument_subtype: instrument.subtype ?? '',
            custom_data,
            entities: [
              {
                entity_id: `user-${+instrument.user_id}` as const,
                entity_type: 'user' as 'user' | 'business',
              },
            ],
          }
          if (!(shouldSave || isCI)) {
            appendFileSync(
              logFile,
              `${JSON.stringify(instrumentObj, null, 2)}\n`,
            )
          }
          instrumentObjs.push(instrumentObj)
        }
        if (shouldSave) {
          const promises = await Promise.allSettled(
            instrumentObjs.map(upsertInstrument),
          )
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected') {
              console.log(p.reason)
              failedUserInstruments.push(instrumentObjs[i]!)
            }
          }
        }
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )

  console.log(`${userInstrumentsLength} USER INSTRUMENTS ADDED`)
}

if (!skipList.includes('company_instruments')) {
  const companyInstruments = await db
    .selectFrom('strapi.pay_external_accounts as p')
    .innerJoin('strapi.companies as c', 'c.id', 'p.company')
    .innerJoin('strapi.users-permissions_user as u', 'u.id', 'c.owner')
    .select((eb) => [
      sql.lit('crypto').as('entity'),
      'p.id',
      'p.name',
      'p.type',
      'p.type as subtype',
      eb.fn('CONVERT', ['p.data', sql`JSON`]).as('data'),
      'p.company',
    ])
    .where('p.type', 'in', ['dedicated_fund_account', 'eth_address'])
    .where('u.registrationType', '=', 'business')
    .where((eb) =>
      eb.or([eb('c.riseId', 'is not', null), eb('u.riseId', 'is not', null)]),
    )
    .$if(!!startDate, (qb) =>
      qb.where('p.created_at', '>=', dayjs(startDate).toDate()),
    )
    .$narrowType<{
      entity: 'bank_account' | 'crypto'
      company: number
      data: {
        accountNumber: string | null
        routingNumber: string | null
        iban: string | null
        beneficiaryName: string | null
        beneficiaryCountryCode: string | null
      }
    }>()
    .union(
      db
        .selectFrom('private_data.bank_accounts as b')
        .innerJoin(
          'strapi.pay_external_accounts as p',
          'p.id',
          'b.strapi_external_account_id',
        )
        .innerJoin('strapi.companies as c', 'c.id', 'b.strapi_company_id')
        .innerJoin('strapi.users-permissions_user as u', 'u.id', 'c.owner')
        .where('u.registrationType', '=', 'business')
        .where((eb) =>
          eb.or([
            eb('c.riseId', 'is not', null),
            eb('u.riseId', 'is not', null),
          ]),
        )
        .select((eb) => [
          sql.lit('bank_account').as('entity'),
          'b.id',
          eb
            .case()
            .when('b.bank_name', 'is not', null)
            .then(eb.ref('b.bank_name'))
            .else(eb.ref('p.type'))
            .end()
            .as('name'),
          sql.lit('bank_account').as('type'),
          'p.type as subtype',
          jsonBuildObject({
            accountNumber: eb.ref('b.account_number'),
            routingNumber: eb.ref('b.routing_number'),
            iban: eb.ref('b.iban'),
            beneficiaryName: eb.ref('b.beneficiary_name'),
            beneficiaryCountryCode: eb.ref('b.beneficiary_country_code'),
          }).as('data'),
          'b.strapi_company_id as company',
        ])
        .$if(!!startDate, (qb) =>
          qb.where('b.created_at', '>=', dayjs(startDate).toDate()),
        )
        .$narrowType<{
          company: number
          entity: 'bank_account' | 'crypto'
        }>(),
    )
    .execute()

  if (!isCI) {
    appendFileSync(
      logFile,
      `\n\n${userInstrumentsLength} USER INSTRUMENTS ADDED\n\nADDING COMPANY INSTRUMENTS\n\n`,
    )
  }

  companyInstrumentsLength = companyInstruments.length
  await startProgress(
    'Company Instruments',
    companyInstrumentsLength,
    async (increment) => {
      for (const c of chunk(companyInstruments, CONCURRENCY)) {
        const initialTime = Date.now()
        const instrumentObjs: Parameters<typeof upsertInstrument>[0][] = []
        for (const instrument of c) {
          type CustomData = {
            [K in keyof typeof instrument.data]: NonNullable<
              (typeof instrument.data)[K]
            >
          }
          const custom_data = isPlainObject(instrument.data)
            ? (fromEntries(
                filter(entries(instrument.data), ([key, value]) => !!value),
              ) as CustomData)
            : {}
          const instrumentObj = {
            instrument_id: `${instrument.entity}-${instrument.id}` as const,
            name: instrument.name ?? '',
            instrument_type: instrument.type ?? '',
            instrument_subtype: instrument.subtype ?? '',
            custom_data,
            entities: [
              {
                entity_id: `company-${instrument.company}` as const,
                entity_type: 'business' as 'user' | 'business',
              },
            ],
          }
          if (!(shouldSave || isCI)) {
            appendFileSync(
              logFile,
              `${JSON.stringify(instrumentObj, null, 2)}\n`,
            )
          }
          instrumentObjs.push(instrumentObj)
        }
        if (shouldSave) {
          const promises = await Promise.allSettled(
            instrumentObjs.map(upsertInstrument),
          )
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected') {
              console.log(p.reason)
              failedCompanyInstruments.push(instrumentObjs[i]!)
            }
          }
        }
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
  console.log(`${companyInstrumentsLength} COMPANY INSTRUMENTS ADDED`)
  if (!isCI) {
    appendFileSync(
      logFile,
      `\n\n${companyInstrumentsLength} COMPANY INSTRUMENTS ADDED\n\n`,
    )
  }
}

if (!skipList.includes('transactions')) {
  if (!isCI) appendFileSync(logFile, 'ADDING TRANSACTIONS\n\n')

  const transactions = await db
    .selectFrom('pay.ledger as le')
    .leftJoin('remittance.withdrawals as wi', 'wi.tx_hash', 'le.tx_hash')
    .leftJoin('strapi.companies as co', 'co.riseId', 'le.source')
    .leftJoin('strapi.companies as co2', 'co2.riseId', 'le.destination')
    .leftJoin(
      'strapi.users-permissions_user as pu2',
      'pu2.riseId',
      'le.destination',
    )
    .leftJoin(
      'strapi.pay_external_accounts as pe',
      'pe.id',
      'wi.selected_account',
    )
    .leftJoin(
      'private_data.bank_accounts as ba',
      'ba.strapi_external_account_id',
      'pe.id',
    )
    .leftJoin('strapi.companies as co3', 'co3.id', 'pe.company')
    .leftJoin('strapi.users-permissions_user as pu3', (join) =>
      join.onRef('pu3.id', '=', 'pe.user').on('pu3.riseId', 'is not', null),
    )
    .select((eb) => [
      'le.tx_hash',
      'le.source',
      'le.destination',
      'le.tx_timestamp',
      'le.type as ledger_type',
      'le.ramp',
      sql<number>`le.amount_unit DIV 1e6`.as('amount'),
      eb.fn.coalesce('wi.currency', eb.val('RISEPAY')).as('currency'),
      'co.owner as sender_id',
      'co.id as sender_company',
      'pu2.id as receiver_id',
      'co2.id as receiver_company',
      'pe.id as external_account',
      'ba.id as bank_account_id',
      'co3.id as external_company',
      'pu3.id as external_user',
    ])
    .where('le.type', 'not in', ['fee', 'SUBSCRIPTION_FLAT_COUNT'])
    .whereRef('le.rise_id', '!=', 'le.destination')
    .$if(!!startDate, (qb) =>
      qb.where('le.tx_timestamp', '>=', dayjs(startDate).toDate()),
    )
    .execute()

  // need to check for fiat payments and funds, withdrawals

  const rampCurrencies: Record<string, string> = {
    RisePayRampUSDC: 'USDC',
    RisePayRampUSDInternational: 'USD',
  }
  const fiatRamps = [
    'RisePayRampUSDUS',
    'RisePayRampUSDInternational',
    'RisePayRampUSDInternationalAuto',
    'RisePayRampForEx',
    'RisePayRampUSDWire',
    'RisePayRampEURGBP',
    'RisePayRampUSDACH',
    'RisePayRampNGN',
    'RisePayRampUSDCircleWire',
    'RisePayRampUSDCircleACH',
  ]
  const filteredTransactions = transactions.filter(
    (t) =>
      t.receiver_id ||
      t.receiver_company ||
      t.external_company ||
      t.external_user,
  )
  transactionsLength = filteredTransactions.length
  await startProgress('Transactions', transactionsLength, async (increment) => {
    for (const c of chunk(filteredTransactions, CONCURRENCY)) {
      const initialTime = Date.now()
      const txnObjs: Parameters<typeof sendTransaction>[0][] = []
      for (const t of c) {
        assert(t.tx_timestamp, 'Missing tx_timestamp')
        const currency =
          (t.ramp && rampCurrencies[t.ramp]) ?? t.currency ?? 'USD'
        const txnType = t.ramp && fiatRamps.includes(t.ramp) ? 'fiat' : 'crypto'
        const receiver_instrument_entity = t.bank_account_id
          ? 'bank_account'
          : 'crypto'
        const receiver_instrument_id =
          `${receiver_instrument_entity}-${t.bank_account_id ?? t.external_account ?? t.destination}` as const
        const sender_instrument_id = `crypto-${t.source}` as const
        const receiver_entity_type = (
          (t.receiver_company ?? t.external_company) ? 'business' : 'user'
        ) as 'user' | 'business'
        if (
          !(
            t.receiver_company ||
            t.external_company ||
            t.receiver_id ||
            t.external_user
          )
        )
          continue
        const receiver_entity_id = (
          (t.receiver_company ?? t.external_company)
            ? `company-${t.receiver_company ?? t.external_company}`
            : `user-${t.receiver_id ?? t.external_user}`
        ) as `user-${number}` | `company-${number}`
        const sender_entity_id = run(() => {
          if (t.sender_company) {
            return `company-${t.sender_company}` as const
          }
          if (t.sender_id) {
            return `user-${t.sender_id}` as const
          }
          return 'company-RISE' as const
        })
        const sender_entity_type = run(() => {
          if (t.sender_company) {
            return 'business' as const
          }
          if (t.sender_id) {
            return 'user' as const
          }
          return 'business' as const
        })
        const txnObj = {
          event_time: Math.round(t.tx_timestamp.getTime() / 1000),
          type: `${txnType}_${t.ledger_type}`,
          id: t.tx_hash,
          transaction_data: {
            amount: Math.abs(t.amount),
            sent_amount: Math.abs(t.amount),
            sent_currency: currency,
            sender_entity_id,
            sender_instrument_id,
            sender_entity_type,
            received_amount: Math.abs(t.amount),
            received_currency: currency,
            receiver_entity_id,
            receiver_entity_type,
            receiver_instrument_id,
            transaction_hash: t.tx_hash,
          },
        }
        if (!(shouldSave || isCI)) {
          appendFileSync(logFile, `${JSON.stringify(txnObj, null, 2)}\n`)
        }
        txnObjs.push(txnObj)
      }
      if (shouldSave) {
        const promises = await Promise.allSettled(txnObjs.map(sendTransaction))
        for (const [i, p] of promises.entries()) {
          if (p.status === 'rejected') {
            console.log(p.reason)
            failedTransactions.push(txnObjs[i]!)
          }
        }
      }
      const endTime = Date.now()
      increment(CONCURRENCY, initialTime, endTime)
    }
  })
  console.log(`${transactionsLength} TRANSACTIONS ADDED`)
  if (!isCI) {
    appendFileSync(logFile, `\n\n${transactionsLength} TRANSACTIONS ADDED\n\n`)
  }
}

if (failedTransactions.length > 0) {
  console.log(`Failed Transactions: ${failedTransactions.length}, retrying...`)
  if (!isCI) {
    appendFileSync(
      errorFile,
      `\n\nFailed Transactions: ${failedTransactions.length}\n\n`,
    )
  }
  await startProgress(
    'Retrying Failed Transactions',
    failedTransactions.length,
    async (increment) => {
      for (const txs of chunk(failedTransactions, CONCURRENCY)) {
        const initialTime = Date.now()
        await pRetry(async (attemptCount) => {
          const promises = await Promise.allSettled(txs.map(sendTransaction))
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected' && attemptCount === 3) {
              if (isCI) {
                console.log(p.reason)
                continue
              }
              appendFileSync(
                errorFile,
                `FAILED: ${JSON.stringify(txs[i]!, null, 2)}\n`,
              )
            }
          }
        }, pRetryOptions)
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
}
if (failedUsers.length > 0) {
  console.log(`Failed Users: ${failedUsers.length}, retrying...`)
  if (!isCI) {
    appendFileSync(errorFile, `\n\nFailed Users: ${failedUsers.length}\n\n`)
  }
  await startProgress(
    'Retrying Users',
    failedUsers.length,
    async (increment) => {
      for (const c of chunk(failedUsers, CONCURRENCY)) {
        const initialTime = Date.now()
        await pRetry(async (attemptCount) => {
          const promises = await Promise.allSettled(c.map(upsertUser))
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected' && attemptCount === 3) {
              if (isCI) {
                console.log(p.reason)
                continue
              }
              appendFileSync(
                errorFile,
                `FAILED: ${JSON.stringify(c[i]!, null, 2)}\n`,
              )
            }
          }
        }, pRetryOptions)
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
}
if (failedCompanies.length > 0) {
  console.log(`Failed Companies: ${failedCompanies.length}, retrying...`)
  await startProgress(
    'Retrying Companies',
    failedCompanies.length,
    async (increment) => {
      if (!isCI) {
        appendFileSync(
          errorFile,
          `\n\nFailed Companies: ${failedCompanies.length}\n\n`,
        )
      }
      for (const c of chunk(failedCompanies, CONCURRENCY)) {
        const initialTime = Date.now()
        await pRetry(async (attemptCount) => {
          const promises = await Promise.allSettled(c.map(upsertBusiness))
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected' && attemptCount === 3) {
              if (isCI) {
                console.log(p.reason)
                continue
              }
              appendFileSync(
                errorFile,
                `FAILED: ${JSON.stringify(c[i]!, null, 2)}\n`,
              )
            }
          }
        }, pRetryOptions)
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
}
if (failedUserInstruments.length > 0) {
  console.log(
    `Failed User Instruments: ${failedUserInstruments.length}, retrying...`,
  )
  await startProgress(
    'Retrying User Instruments',
    failedUserInstruments.length,
    async (increment) => {
      if (!isCI) {
        appendFileSync(
          errorFile,
          `\n\nFailed User Instruments: ${failedUserInstruments.length}\n\n`,
        )
      }
      for (const c of chunk(failedUserInstruments, CONCURRENCY)) {
        const initialTime = Date.now()
        await pRetry(async (attemptCount) => {
          const promises = await Promise.allSettled(c.map(upsertInstrument))
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected' && attemptCount === 3) {
              if (isCI) {
                console.log(p.reason)
                continue
              }
              appendFileSync(
                errorFile,
                `FAILED: ${JSON.stringify(c[i]!, null, 2)}\n`,
              )
            }
          }
        }, pRetryOptions)
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
}
if (failedCompanyInstruments.length > 0) {
  console.log(
    `Failed Company Instruments: ${failedCompanyInstruments.length}, retrying...`,
  )
  await startProgress(
    'Retrying Company Instruments',
    failedCompanyInstruments.length,
    async (increment) => {
      if (!isCI) {
        appendFileSync(
          errorFile,
          `\n\nFailed Company Instruments: ${failedCompanyInstruments.length}\n\n`,
        )
      }
      for (const c of chunk(failedCompanyInstruments, CONCURRENCY)) {
        const initialTime = Date.now()
        await pRetry(async (attemptCount) => {
          const promises = await Promise.allSettled(c.map(upsertInstrument))
          for (const [i, p] of promises.entries()) {
            if (p.status === 'rejected' && attemptCount === 3) {
              if (isCI) {
                console.log(p.reason)
                continue
              }
              appendFileSync(
                errorFile,
                `FAILED: ${JSON.stringify(c[i]!, null, 2)}\n`,
              )
            }
          }
        }, pRetryOptions)
        increment(CONCURRENCY, initialTime, Date.now())
      }
    },
  )
}
process.exit(0)
