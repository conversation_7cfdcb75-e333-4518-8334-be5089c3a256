import { ExecuteUniswapQuoteV4 } from '../../univ4/executeUniswapV4.js'

export default async function main() {
  try {
    //lint-disable-next-line
    let configs: any

    configs = await ExecuteUniswapQuoteV4(
      '0x2f2a2543B76A4166549F7aaB2e75Bef0aefC5B0f',
      '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
      '1',
    )

    // Get the quote

    // Display results
    console.log('\nQuote Details:')
    console.log('Configs:', JSON.stringify(configs.riseConfig, null, 2))
  } catch (error) {
    console.error('Error getting quote:')
    console.error(error)
  }
}
