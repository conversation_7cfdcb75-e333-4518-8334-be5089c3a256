import { execSync } from 'node:child_process'
import fs from 'node:fs'
import path from 'node:path'
import { ethers } from 'ethers'
import { logger } from 'utils/src/common/logger.js'

const CHAIN_ID = 421614
const LOCAL_RPC_URL = 'http://127.0.0.1:8547'

const restartAnvil = () => {
  try {
    // Stop any running Anvil instances
    try {
      execSync('docker compose down rise-anvil', { stdio: 'inherit' })
    } catch (error) {
      // Ignore errors if the container doesn't exist
    }

    // Start Anvil with the correct configuration
    execSync('docker compose up -d rise-anvil', { stdio: 'inherit' })
    logger.info('Waiting for Anvil to start...')

    // Wait for Anvil to start
    execSync('sleep 5')

    // Verify that Anvil is running with the correct configuration
    const anvilCheck = execSync(
      'curl -s -X POST -H "Content-Type: application/json" --data \'{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":"1"}\' http://127.0.0.1:8547',
      { encoding: 'utf-8' },
    )

    const chainIdResponse = JSON.parse(anvilCheck)
    const chainId = Number.parseInt(chainIdResponse.result, 16)

    if (chainId === CHAIN_ID) {
      logger.info(
        `Anvil successfully started and forked from Arbitrum Sepolia (Chain ID: ${CHAIN_ID})`,
      )
    } else {
      throw new Error(
        `Anvil started with incorrect chain ID: ${chainId}. Expected: ${CHAIN_ID}`,
      )
    }
  } catch (error) {
    logger.error(`Failed to start Anvil: ${error}`)
    throw new Error('Could not start Anvil. Please start it manually.')
  }
}

const getAccounts = async (provider: ethers.JsonRpcProvider) => {
  try {
    // Get accounts from the provider
    const accounts = await provider.send('eth_accounts', [])
    return accounts
  } catch (error) {
    logger.error('Failed to get accounts', { error })
    return []
  }
}

logger.info('Setting up forked Arbitrum Sepolia network...')

// Check if Anvil is running and properly forked
logger.info('Checking if Anvil is running with the correct fork...')
try {
  // Try to connect to Anvil
  const anvilCheck = execSync(
    'curl -s -X POST -H "Content-Type: application/json" --data \'{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":"1"}\' http://localhost:8547',
    { encoding: 'utf-8' },
  )

  const chainIdResponse = JSON.parse(anvilCheck)
  const chainId = Number.parseInt(chainIdResponse.result, 16)

  if (chainId === CHAIN_ID) {
    logger.info(
      `Anvil is running and forked from Arbitrum Sepolia (Chain ID: ${CHAIN_ID})`,
    )
  } else {
    logger.warn(
      `Anvil is running but with incorrect chain ID: ${chainId}. Expected: ${CHAIN_ID}`,
    )
    logger.info('Restarting Anvil with the correct configuration...')
    restartAnvil()
  }
} catch (error) {
  logger.warn('Anvil does not appear to be running or is not responding')
  logger.info('Starting Anvil via Docker...')
  restartAnvil()
}

// Connect to the forked network
const provider = new ethers.JsonRpcProvider(LOCAL_RPC_URL)

// Get network information
const network = await provider.getNetwork()
logger.info(
  `Connected to network: ${network.name} (Chain ID: ${network.chainId})`,
)

// Get latest block
const blockNumber = await provider.getBlockNumber()
logger.info(`Latest block number: ${blockNumber}`)

// Get accounts
const accounts: string[] = await getAccounts(provider)
logger.info(`Available accounts: ${accounts.length}`)
accounts.slice(0, accounts.length - 1).forEach((account, index) => {
  logger.info(`Account ${index}: ${account}`)
})

// Save network information to a file for easy reference
const networkInfo = {
  name: 'Arbitrum Sepolia (Forked)',
  chainId: CHAIN_ID,
  rpcUrl: LOCAL_RPC_URL,
  blockNumber,
  accounts: accounts.slice(0, 10),
}

const infoPath = path.resolve('./forked-network-info.json')
fs.writeFileSync(infoPath, JSON.stringify(networkInfo, null, 2))
logger.info(`Network information saved to ${infoPath}`)

logger.info('Forked network setup complete!')
logger.info(
  `You can now interact with the forked Arbitrum Sepolia network at ${LOCAL_RPC_URL}`,
)

process.exit()
