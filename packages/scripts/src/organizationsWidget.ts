import type {
  CompanyNanoid,
  TeamNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { getCompanyStats } from 'repositories/src/companies.js'

const company_nanoid = 'co-mkEVAumUERR4' as CompanyNanoid
const team_nanoid = 'te-JgkVz7bCTYdd' as TeamNanoid
// const team_nanoid = 'te-yI6eZNmB2mVp' as TeamNanoid
const igor_employee_2 = 'us-jPS7WAatJEnl' as UserNanoid
const igor_employee_3 = 'us-zs-VHZWo34aC' as UserNanoid
const igor_employee_4 = 'us-3dHAEmCc7Z33' as UserNanoid
const igor_employee_5 = 'us-PQFS0q1ztnUm' as UserNanoid

export default async function organizationsWidget() {
  const result = await getCompanyStats(company_nanoid)
  console.log(result)
}
