import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import {
  getEmployeePayrollSettings,
  getEmployeePayrollSettingsByTeam,
} from 'repositories/src/employeePayrollSettings.js'
import {
  getEmployeePaymentsByCategory,
  getEmployeePaymentsByCategoryWithYTD,
  getPayrollPeriodTable,
} from 'repositories/src/payroll.js'
import { getUsersByNanoids } from 'repositories/src/users.js'
import { mapAsync } from 'utils/src/common/array.js'
import assert from 'utils/src/common/assertHTTP.js'
import { getPayCycleDates } from 'utils/src/common/payCycles.js'

const team_nanoid = 'te-JgkVz7bCTYdd' as TeamNanoid
// const team_nanoid = 'te-yI6eZNmB2mVp' as TeamNanoid
const igor_employee_2 = 'us-jPS7WAatJEnl' as UserNanoid
const igor_employee_3 = 'us-zs-VHZWo34aC' as UserNanoid
const igor_employee_4 = 'us-3dHAEmCc7Z33' as UserNanoid
const igor_employee_5 = 'us-PQFS0q1ztnUm' as UserNanoid

const payCycle = { year: 2025, month: 4, period: 2 }

export default async function payslip() {
  console.time()

  const employeePayroll = await getEmployeePayrollSettings({
    user_nanoid: igor_employee_5,
    team_nanoid,
    pay_cycle: payCycle,
  })
  assert(employeePayroll, 'Employee payroll not found')

  const result = await getEmployeePaymentsByCategoryWithYTD(
    employeePayroll,
    payCycle,
  )
  console.dir(result, { depth: null })
  // await testEmployeesPayslips({
  //   employee_nanoids: [igor_employee_3, igor_employee_4],
  //   team_nanoid,
  // })

  // await testTeamPayslipAggregate(team_nanoid)

  console.timeEnd()
}

/**
 * Returns employee's payslips for a given pay cycle
 */
async function testEmployeesPayslips({
  employee_nanoids,
  team_nanoid,
}: {
  employee_nanoids: UserNanoid[]
  team_nanoid: TeamNanoid
}) {
  const payslips = await mapAsync(employee_nanoids, async (employee_nanoid) => {
    const employeePayroll = await getEmployeePayrollSettings({
      user_nanoid: employee_nanoid,
      team_nanoid,
      pay_cycle: payCycle,
    })
    assert(employeePayroll, 'Employee payroll not found')
    console.log(employeePayroll.payroll_program)

    return getEmployeePaymentsByCategory(employeePayroll, payCycle)
  })

  console.dir(payslips, { depth: null })
}
