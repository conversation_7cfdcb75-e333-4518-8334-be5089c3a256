// testPaymentHandlerForwarder.ts
import { ethers } from 'ethers'
import { getContract, getProvider } from 'repositories/src/smartContracts.js'

const provider = await getProvider('arbitrum')
const privateKey = ''
const wallet = new ethers.Wallet(privateKey, provider)

const paymentHandlerAddress = '******************************************'
const tokenAddress1 = '******************************************'
const tokenAddress2 = '******************************************' // Arbitrum WETH
const rampAddress = '******************************************'

const forwarder = await getContract(
  'RisePaymentHandlerForwarder_impl',
  'arbitrum',
)
const forwarderWithSigner = forwarder.connect(wallet)

const request = {
  from: wallet.address,
  to: paymentHandlerAddress,
  data: [
    {
      token: tokenAddress1,
      configs: [
        {
          amount: ethers.parseEther('1000').toString(),
          transferType: '0',
          fixedOrPercent: '3',
          ramp: rampAddress,
          source: tokenAddress1,
          destination: paymentHandlerAddress,
          offChainReference:
            '******************************************000000000000000000000000',
          data: '0x',
        },
      ],
    },
    {
      token: tokenAddress2,
      configs: [
        {
          amount: ethers.parseEther('5').toString(), // 5 WETH
          transferType: '0',
          fixedOrPercent: '3',
          ramp: rampAddress,
          source: tokenAddress2,
          destination: paymentHandlerAddress,
          offChainReference:
            '******************************************000000000000000000000001',
          data: '0x',
        },
      ],
    },
  ],
  salt: ethers.hexlify(ethers.randomBytes(8)),
  expires: (Math.floor(Date.now() / 1000) + 3600).toString(),
}

console.log('Request:', JSON.stringify(request, null, 2))

const domain = {
  name: 'RisePaymentHandlerForwarder',
  version: '1',
  chainId: 421614,
  verifyingContract: forwarder.target.toString(),
}

const types = {
  SetTransferRulesForwardRequest: [
    { name: 'from', type: 'address' },
    { name: 'to', type: 'address' },
    { name: 'data', type: 'RisePaymentHandlerConfigRequest[]' },
    { name: 'salt', type: 'uint64' },
    { name: 'expires', type: 'uint48' },
  ],
  RisePaymentHandlerConfigRequest: [
    { name: 'token', type: 'address' },
    { name: 'configs', type: 'RisePaymentHandlerConfig[]' },
  ],
  RisePaymentHandlerConfig: [
    { name: 'amount', type: 'uint256' },
    { name: 'transferType', type: 'uint8' },
    { name: 'fixedOrPercent', type: 'uint8' },
    { name: 'ramp', type: 'address' },
    { name: 'source', type: 'address' },
    { name: 'destination', type: 'address' },
    { name: 'offChainReference', type: 'bytes32' },
    { name: 'data', type: 'bytes' },
  ],
}

const signature = await wallet.signTypedData(domain, types, request)

const formattedRequest = {
  from: request.from,
  to: request.to,
  data: request.data.map((dataItem) => ({
    token: dataItem.token,
    configs: dataItem.configs.map((config) => ({
      amount: ethers.toBigInt(config.amount),
      transferType: ethers.toBigInt(config.transferType),
      fixedOrPercent: ethers.toBigInt(config.fixedOrPercent),
      ramp: config.ramp,
      source: config.source,
      destination: config.destination,
      offChainReference: config.offChainReference,
      data: config.data,
    })),
  })),
  salt: ethers.toBigInt(request.salt),
  expires: ethers.toBigInt(request.expires),
}

console.log(
  'Formatted Request:',
  JSON.stringify(
    formattedRequest,
    (key, value) => (typeof value === 'bigint' ? value.toString() : value),
    2,
  ),
)

const abiCoder = new ethers.AbiCoder()
const encodedData = abiCoder.encode(
  [
    'tuple(address from, address to, uint64 salt, uint48 expires, tuple(address token, tuple(uint256 amount, uint8 transferType, uint8 fixedOrPercent, address ramp, address source, address destination, bytes32 offChainReference, bytes data)[] configs)[] data)',
  ],
  [formattedRequest],
)

console.log('Encoded Data:', encodedData)

try {
  const result = await forwarderWithSigner.setTransferRules(
    formattedRequest,
    signature,
  )

  console.log('Transaction hash:', result.hash)

  const receipt = await result.wait()
  console.log('Transaction confirmed:', receipt)
} catch (error: unknown) {
  console.log('Error:', (error as Error).message)
}
