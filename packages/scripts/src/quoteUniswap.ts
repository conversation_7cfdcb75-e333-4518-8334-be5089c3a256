import type { WithdrawAccountNanoid } from '@riseworks/contracts/src/brands.js'
import { getUniswapQuoteV4 } from 'repositories/src/uniswapApi.js'
import {
  getUniswapConfig,
  getUniswapQuote,
  // getUniswapV4Config,
} from 'repositories/src/withdrawUniswap.js'

export default async function main() {
  const r = await getUniswapQuoteV4({
    inputTokenAddress: '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
    outputTokenAddress: '0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
    inputAmountCents: 1_00,
    providerUrl:
      'https://arbitrum-mainnet.infura.io/v3/********************************',
    rampAddress: '0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
  })
  console.log('QUOTE', r)
  // const _r = await getUniswapV4Config({
  //   inputTokenAddress: '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
  //   outputTokenAddress: '0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
  //   inputAmountCents: 1_00,
  //   receiverAddress: '0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
  //   offChainReference: '0x42f0d4ecd230a72ba196709705d90ef3d3c901ee79485448ef3366bc73dfb720',
  //   rampAddress: '0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
  //   rpcUrl: 'https://arbitrum-mainnet.infura.io/v3/********************************',
  // })
  // console.log('CONFIG', _r)
  return
  const quote = await getUniswapQuote(
    'wa-gAHST7aOtqZO' as WithdrawAccountNanoid,
    1_00,
  )
  console.log(quote)

  const config = await getUniswapConfig(
    'wa-gAHST7aOtqZO' as WithdrawAccountNanoid,
    1_00,
    'arbitrum',
  )
  console.log(config)
}
