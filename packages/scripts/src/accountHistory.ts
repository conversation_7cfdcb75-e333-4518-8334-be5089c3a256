import type { TeamNanoid, UserNanoid } from '@riseworks/contracts/src/brands.js'
import { getEmployeePayrollSettings } from 'repositories/src/employeePayrollSettings.js'
import {
  getTeamAccountLedgerHistory,
  getUserAccountLedgerHistory,
} from 'repositories/src/ledger.js'
import { employeePayrollGroupId } from 'repositories/src/payroll.js'
import { groupPaymentsRead } from 'repositories/src/riseAccounts.js'
import assert from 'utils/src/common/assertHTTP.js'
import { friendlyFormatPayment } from 'utils/src/common/payments.js'
import { conciseString } from 'utils/src/common/string.js'

const team_nanoid = 'te-JgkVz7bCTYdd' as TeamNanoid
// const team_nanoid = 'te-yI6eZNmB2mVp' as TeamNanoid
const igor_employee_2 = 'us-jPS7WAatJEnl' as UserNanoid
const igor_employee_3 = 'us-zs-VHZWo34aC' as UserNanoid
const igor_employee_4 = 'us-3dHAEmCc7Z33' as UserNanoid
const igor_employee_5 = 'us-PQFS0q1ztnUm' as UserNanoid

const targetEmployee = igor_employee_5
const targetPayCycle = { year: 2025, month: 4, period: 2 }

export default async function accountHistory() {
  const employee_payroll = await getEmployeePayrollSettings({
    user_nanoid: targetEmployee,
    team_nanoid,
    pay_cycle: targetPayCycle,
  })
  assert(employee_payroll, 'Employee payroll not found')

  const payments = await groupPaymentsRead(
    employee_payroll.rise_account,
    employeePayrollGroupId(employee_payroll.team_nanoid, targetPayCycle),
    ['Scheduled', 'Complete'],
    'arbitrum',
  )

  console.dir(payments, { depth: null })

  // console.log(payments.map(friendlyPrintPayment))

  // console.log('employee_payroll', conciseString(employee_payroll.rise_account))

  // const result = await getUserAccountLedgerHistory({
  //   user_nanoid: targetEmployee,
  //   team_nanoid,
  //   query: {
  //     start_date: new Date('2025-03-19'),
  //   },
  // })
}
