import {
  createFakeClient,
  generateAuthorizationHeader,
} from 'backend/src/testing/client.js'
import { app } from 'dev/app.js'
import assert from 'node:assert'
import util from 'node:util'

const client = createFakeClient(app)
const auth = generateAuthorizationHeader('us-K0eW2VjRjsJq')

async function get<Endpoint extends string, Query>(
  endpoint: Endpoint,
  query?: Query,
) {
  const response = await client[endpoint].get({
    headers: {
      Authorization: auth,
    },
    query,
  })

  return {
    response: await response.json(),
    endpoint,
    query,
  }
}

export default async function run() {
  const tests = await Promise.all([
    get('/dashboard/contractors/withdraw_accounts/countries', {
      ramp: 'international_exchange',
    }),
    get('/dashboard/contractors/withdraw_accounts/countries', {
      ramp: 'international_usd',
    }),
    get('/dashboard/contractors/withdraw_accounts/forex/countries'),
    get('/dashboard/contractors/withdraw_accounts/forex/currencies', {
      country: 'AR',
    }),
    get('/dashboard/contractors/withdraw_accounts/currencies', {
      ramp: 'international_exchange',
      country: 'AR',
    }),
  ])

  for (const test of tests) {
    console.log('START TEST: ', test.endpoint, test.query)
    console.log(util.inspect(test.response.data, { depth: null, colors: true }))

    assert(test.response.data.length)
    assert(test.response.success)
    console.log('END TEST: ', test.endpoint, test.query)
  }
}
