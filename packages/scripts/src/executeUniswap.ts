import { executeUniswapWithdraw } from 'repositories/src/quoteUniswap.js'

export default async function main() {
  try {
    // Get RPC URL from environment variables or us

    const inputToken = '0x910581AE553698622b9b4f6041dFB53AD751e691' // USDC

    const outputToken = '0xCbeDa8AC683c65ca88C670932d71FC98cA255f91' // USDT

    const feeTier = 3000

    const configs = await executeUniswapWithdraw(
      inputToken,
      outputToken,
      '1',
      feeTier,
    )

    // Get the quote

    // Display results
    console.log('\nQuote Details:')
    console.log('Configs:', JSON.stringify(configs.riseConfig, null, 2))
  } catch (error) {
    console.error('Error getting quote:')
    console.error(error)
  }
}
