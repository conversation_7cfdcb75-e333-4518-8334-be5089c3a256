import { execSync } from 'node:child_process'
import fs from 'node:fs'
import os from 'node:os'
import path from 'node:path'
import minimist from 'minimist'
import assert from 'utils/src/common/assertHTTP.js'
import { logger } from 'utils/src/common/requestContext.js'

const COPY_SCHEMAS = ['rise', 'rise_private']

interface DatabaseConfig {
  host: string
  port: number
  user: string
  password: string
}
interface CopyOptions {
  schemas?: string[]
  tables?: string[]
  excludeTables?: string[]
  skipData?: boolean
  compress?: boolean
  verbose?: boolean
}

const getDatabaseConfig = (isSource: boolean) => {
  if (isSource) {
    const host = process.env.SOURCE_DB_HOST
    const user = process.env.SOURCE_DB_USER
    const password = process.env.SOURCE_DB_PW
    const port = Number(process.env.SOURCE_DB_PORT || 3306)
    assert(host, 'SOURCE_DB_HOST environment variable is required')
    assert(user, 'SOURCE_DB_USER environment variable is required')
    assert(password, 'SOURCE_DB_PW environment variable is required')
    return {
      host,
      port,
      user,
      password,
    }
  }
  const host = process.env.DB_HOST || 'localhost'
  const port = Number(process.env.DB_PORT || 3306)
  const user = process.env.DB_USER || 'root'
  const password = process.env.DB_PW || ''
  return {
    host,
    port,
    user,
    password,
  }
}

const mydumperImage = 'mydumper/mydumper'

const copySingleSchema = async (
  schema: string,
  sourceConfig: DatabaseConfig,
  targetConfig: DatabaseConfig,
  options: CopyOptions,
) => {
  const { tables, excludeTables, skipData, verbose, compress } = options

  logger.info(`Copying schema ${schema} from source to local...`)
  logger.info(`Source: ${sourceConfig.host}:${sourceConfig.port}/${schema}`)
  logger.info(`Target: ${targetConfig.host}:${targetConfig.port}/${schema}`)

  try {
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'mysql-dump-'))
    let tableList = ''
    if (tables && tables.length > 0) {
      tableList = tables.map((table) => `--tables ${table}`).join(' ')
    }
    let excludeList = ''
    if (excludeTables && excludeTables.length > 0) {
      excludeList = excludeTables
        .map((table) => `--regex '^${table}$'`)
        .join(' ')
    }
    try {
      execSync(`docker image inspect ${mydumperImage}`, { stdio: 'ignore' })
    } catch (error) {
      logger.info('Pulling mydumper Docker image...')
      execSync(`docker pull ${mydumperImage}`, { stdio: 'inherit' })
    }
    const dumpCmd = [
      'docker run --rm',
      `-v ${tempDir}:/backup`,
      '--network=host',
      mydumperImage,
      'mydumper',
      '--clear',
      '-v 3',
      `-h ${sourceConfig.host}`,
      `-P ${sourceConfig.port}`,
      `-u ${sourceConfig.user}`,
      `-p '${sourceConfig.password}'`,
      `-B ${schema}`,
      '-o /backup',
      excludeList,
      tableList,
      skipData ? '--no-data' : '',
      compress ? '-C' : '',
      '--threads 8',
    ].join(' ')
    logger.info(`Executing dump command for schema ${schema}:`)
    if (verbose) logger.info(dumpCmd)
    try {
      logger.info(`Full dump command: ${dumpCmd}`)
      execSync(dumpCmd, { stdio: 'inherit' })
      const backupFiles = fs
        .readdirSync(tempDir)
        .filter(
          (file) =>
            file.endsWith('.sql') ||
            file.endsWith('.sql.gz') ||
            file === 'metadata',
        )
      assert(
        backupFiles.length > 0,
        `No backup files were created in ${tempDir}`,
      )
      assert(
        backupFiles.includes('metadata'),
        `Metadata file not found in ${tempDir}. The dump may be incomplete.`,
      )
      logger.info(`Created ${backupFiles.length} backup files in ${tempDir}`)
      const importCmd = [
        'docker run --rm',
        `-v ${tempDir}:/backup`,
        '--network=host',
        mydumperImage,
        'myloader',
        '--directory /backup',
        `--host ${targetConfig.host}`,
        `--port ${targetConfig.port}`,
        `--user ${targetConfig.user}`,
        `--password "${targetConfig.password}"`,
        `--database ${schema}`,
        compress ? '-C' : '',
        '--threads 8',
        '--overwrite-tables',
        '--verbose 3',
      ].join(' ')
      logger.info(`Executing import command for schema ${schema}:`)
      if (verbose) logger.info(importCmd)
      execSync(importCmd, { stdio: 'inherit' })
      logger.info(`Schema ${schema} imported successfully!`)
    } catch (error) {
      logger.error(`Command failed for schema ${schema}`, error)
      throw error
    }
  } catch (error) {
    logger.error(`Failed to copy schema ${schema}`, error)
    throw error
  }
}

async function copyDatabase(options: CopyOptions): Promise<void> {
  const {
    schemas = COPY_SCHEMAS,
    tables,
    excludeTables,
    skipData,
    compress,
    verbose,
  } = options
  logger.info(`Schemas to copy: ${schemas.join(', ')}`)
  const sourceConfig = getDatabaseConfig(true)
  const targetConfig = getDatabaseConfig(false)
  for (const schema of schemas) {
    try {
      await copySingleSchema(schema, sourceConfig, targetConfig, {
        tables,
        excludeTables,
        skipData,
        compress,
        verbose,
      })
    } catch (error) {
      logger.error(`Failed to copy schema ${schema}`, error)
    }
  }
}

const args = minimist(process.argv.slice(2), {
  string: ['schemas', 'tables', 'exclude'],
  boolean: ['skip-data', 'compress', 'verbose', 'help'],
  alias: {
    s: 'schemas',
    h: 'help',
    v: 'verbose',
  },
  default: {
    compress: true,
    verbose: true,
  },
})

if (args.help) {
  console.log(`
Usage: pnpm db:copy [options]

Options:
  -s, --schemas     Comma-separated list of schemas to copy [default: ${COPY_SCHEMAS.join(',')}]
  --tables          Comma-separated list of tables to include
  --exclude         Comma-separated list of tables to exclude
  --skip-data       Only copy database structure, not data
  --compress        Use compression for faster transfer [default: true]
  -v, --verbose     Show verbose output [default: true]
  -h, --help        Show this help message

Note: This script requires either MySQL client or Docker to be installed.
The following environment variables are required:

  SOURCE_DB_HOST    Hostname of the source database
  SOURCE_DB_PORT    Port of the source database (default: 3306)
  SOURCE_DB_USER    Username for the source database
  SOURCE_DB_PW      Password for the source database

  DB_HOST           Hostname of the target database (default: localhost)
  DB_PORT           Port of the target database (default: 3306)
  DB_USER           Username for the target database (default: root)
  DB_PW             Password for the target database
    `)
  process.exit(0)
}

const schemas = args.schemas ? args.schemas.split(',') : undefined
const tables = args.tables ? args.tables.split(',') : undefined
const excludeTables = args.exclude ? args.exclude.split(',') : undefined

try {
  await copyDatabase({
    schemas,
    tables,
    excludeTables,
    skipData: args['skip-data'],
    compress: args.compress,
    verbose: args.verbose,
  })
  process.exit(0)
} catch (error) {
  logger.error('Database copy failed:', error)
  process.exit(1)
}
