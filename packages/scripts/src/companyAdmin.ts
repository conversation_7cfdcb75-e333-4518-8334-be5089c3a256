import type {
  CompanyNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { validateUserIsCompanyAdminOrOwner } from 'repositories/src/validations.js'

const company_nanoid = 'co-mkEVAumUERR4' as CompanyNanoid
const igor_employee_3 = 'us-zs-VHZWo34aC' as UserNanoid
const igor_employer = 'us-It37qXrCVTqW' as UserNanoid

export default async () => {
  // this shouldn't throw
  await validateUserIsCompanyAdminOrOwner({
    user_nanoid: igor_employer,
    company_nanoid,
  })

  // this should throw
  await validateUserIsCompanyAdminOrOwner({
    user_nanoid: igor_employee_3,
    company_nanoid,
  })
}
