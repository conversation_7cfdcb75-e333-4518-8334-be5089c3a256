import fs from 'node:fs'
import path from 'node:path'
import { db } from 'db/src/index.js'
import { ethers } from 'ethers'
import { logger } from 'utils/src/common/logger.js'

const LOCAL_RPC_URL = 'http://localhost:8547'

const contracts = (
  await db
    .selectFrom('rise.smart_contracts')
    .selectAll()
    .where('environment', '=', 'development')
    .execute()
).map((c) => ({ name: c.name, address: c.address }))

logger.info('Querying contracts on forked Arbitrum Sepolia network...')

// Check if Anvil is running
try {
  // Try to connect to Anvil
  const provider = new ethers.JsonRpcProvider(LOCAL_RPC_URL)

  // Get network information
  const network = await provider.getNetwork()
  logger.info(
    `Connected to network: ${network.name} (Chain ID: ${network.chainId})`,
  )

  // Get latest block
  const blockNumber = await provider.getBlockNumber()
  logger.info(`Latest block number: ${blockNumber}`)

  // Query each known contract
  logger.info('Querying known contracts:')
  const contractsInfo: {
    name: string
    address: string
    codeSize: number
    balance: string
  }[] = []

  for (const contract of contracts) {
    try {
      logger.info(`Checking contract: ${contract.name} (${contract.address})`)

      // Check if the address has code (is a contract)
      const code = await provider.getCode(contract.address)

      if (code === '0x') {
        logger.warn(`No contract found at address: ${contract.address}`)
        continue
      }

      // Get contract balance
      const balance = await provider.getBalance(contract.address)
      const balanceEth = ethers.formatEther(balance)

      // Store contract information
      contractsInfo.push({
        name: contract.name,
        address: contract.address,
        codeSize: (code.length - 2) / 2,
        balance: balanceEth,
      })

      logger.info(
        `Contract ${contract.name} found with code size: ${(code.length - 2) / 2} bytes, balance: ${balanceEth} ETH`,
      )
    } catch (error) {
      logger.error(`Error querying contract ${contract.name}:`, error)
    }
  }

  // Save contract information to a file
  const infoPath = path.resolve('./forked-contracts-info.json')
  fs.writeFileSync(infoPath, JSON.stringify(contractsInfo, null, 2))
  logger.info(`Contract information saved to ${infoPath}`)

  logger.info('Contract query complete!')
} catch (error) {
  logger.error('Failed to connect to Anvil', { error })
  logger.info('Make sure Anvil is running with the correct fork configuration')
  logger.info('Run "pnpm run setupForkedNetwork" to set up the forked network')
}

process.exit()
