import fs from 'node:fs'
import type {
  CompanyNanoid,
  UserNanoid,
} from '@riseworks/contracts/src/brands.js'
import { accountHistoryQueryPending } from '../../repositories/src/ledger.js'

export default async function main() {
  const history = await accountHistoryQueryPending(
    'us-wWuX9ZX5Q4b-' as UserNanoid,
    // 'us-BTSwkYfzxt1K' as UserNanoid,
    {
      status: 'waiting',
    },
  )
  fs.writeFileSync('history.json', JSON.stringify(history, null, 2))
}
